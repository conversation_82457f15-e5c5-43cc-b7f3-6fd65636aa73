/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-19 12:36:37
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-08 16:22:42
 */

import Vue from 'vue'
import Element from 'element-ui'
import '@/styles/element-variables.scss'
import '@fortawesome/fontawesome-free/js/all.js'
import './utils/global'
import 'fix-date'

Element.Dialog.props.closeOnClickModal.default = false

Vue.use(Element, { size: 'small' })
