[{"id": 1, "name": "综合险1", "display_name": "航空一切险", "additionals": [{"main_id": 1, "uuid": "0cf3298b-8a2e-4c61-b6fa-53171c8ab9ea", "name": "测试1", "products": [{"main_id": 1, "clause_uuid": "0cf3298b-8a2e-4c61-b6fa-53171c8ab9ea", "product_id": 19, "company_branch": "测试1", "packing_methods": [{"id": 1, "name": "31313", "display_name": "3131"}], "loading_methods": [{"id": 1, "name": "222", "display_name": "3333"}], "transport_methods": [{"id": 1, "name": "313131", "display_name": "414141"}], "goods_types": [{"id": 2, "parent_id": -1, "name": "ceshi", "display_name": "ceshi"}, {"id": 3, "parent_id": 2, "name": "ceshi3", "display_name": "ceshi4"}], "additional": {"id": 13, "deductible": "1", "special_agreement": "2", "disabled_regions": null}, "user_rate": "2.00", "minimum_premium": 0.1}]}, {"main_id": 1, "uuid": "4239fae6-b72d-456f-ac85-a612224d4c70", "name": "测试2", "products": [{"main_id": 1, "clause_uuid": "4239fae6-b72d-456f-ac85-a612224d4c70", "product_id": 17, "company_branch": "测试1", "packing_methods": [{"id": 1, "name": "31313", "display_name": "3131"}], "loading_methods": [{"id": 1, "name": "222", "display_name": "3333"}], "transport_methods": [{"id": 1, "name": "313131", "display_name": "414141"}], "goods_types": [{"id": 2, "parent_id": -1, "name": "ceshi", "display_name": "ceshi"}, {"id": 3, "parent_id": 2, "name": "ceshi3", "display_name": "ceshi4"}], "additional": {"id": 11, "deductible": "1", "special_agreement": "2", "disabled_regions": null}, "user_rate": "2.00", "minimum_premium": 0.1}]}]}, {"id": 2, "name": "综合险2", "display_name": "航空一切险", "additionals": [{"main_id": 2, "uuid": "0cf3298b-8a2e-4c61-b6fa-53171c8ab9e", "name": "测试3", "products": [{"main_id": 2, "clause_uuid": "0cf3298b-8a2e-4c61-b6fa-53171c8ab9e", "product_id": 19, "company_branch": "测试2-1-1", "packing_methods": [{"id": 1, "name": "31313", "display_name": "3131"}], "loading_methods": [{"id": 1, "name": "222", "display_name": "3333"}], "transport_methods": [{"id": 1, "name": "313131", "display_name": "414141"}], "goods_types": [{"id": 2, "parent_id": -1, "name": "ceshi", "display_name": "ceshi"}, {"id": 3, "parent_id": 2, "name": "ceshi3", "display_name": "ceshi4"}], "additional": {"id": 13, "deductible": "1", "special_agreement": "2", "disabled_regions": null}, "user_rate": "2.00", "minimum_premium": 0.1}]}, {"main_id": 2, "uuid": "4239fae6-b72d-456f-ac85-a612224d4c7", "name": "测试4", "products": [{"main_id": 2, "clause_uuid": "4239fae6-b72d-456f-ac85-a612224d4c7", "product_id": 17, "company_branch": "测试2-2-1", "packing_methods": [{"id": 1, "name": "31313", "display_name": "3131"}], "loading_methods": [{"id": 1, "name": "222", "display_name": "3333"}], "transport_methods": [{"id": 1, "name": "313131", "display_name": "414141"}], "goods_types": [{"id": 2, "parent_id": -1, "name": "ceshi", "display_name": "ceshi"}, {"id": 3, "parent_id": 2, "name": "ceshi3", "display_name": "ceshi4"}], "additional": {"id": 11, "deductible": "1", "special_agreement": "2", "disabled_regions": null}, "user_rate": "2.00", "minimum_premium": 0.1}]}]}]