/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-21 09:54:14
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-22 09:41:21
 */
export default {
  id: 2,
  name: 'operator',
  display_name: '运营',
  is_platform: 0,
  is_platform_default: 0,
  permissions: [
    {
      id: 1,
      parent_id: -1,
      name: 'permissions',
      guard_name: 'jwt',
      display_name: '系统管理',
      is_menu: 1,
      is_universal: 1,
      icon: null,
      route_name: 'Prmissions',
      menu_name: '系统管理',
      created_at: '2021-01-07T09:49:55.000000Z',
      updated_at: '2021-01-07T09:49:55.000000Z',
      pivot: {
        role_id: 2,
        permission_id: 1
      }
    },
    {
      id: 1.1,
      parent_id: 1,
      name: 'permissions',
      guard_name: 'jwt',
      display_name: '权限管理',
      is_menu: 1,
      is_universal: 1,
      icon: null,
      route_name: 'Permissions',
      menu_name: '权限管理',
      created_at: '2021-01-07T09:49:55.000000Z',
      updated_at: '2021-01-07T09:49:55.000000Z',
      pivot: {
        role_id: 2,
        permission_id: 1
      }
    },
    {
      id: 1.2,
      parent_id: 1,
      name: 'role',
      guard_name: 'jwt',
      display_name: '角色管理',
      is_menu: 1,
      is_universal: 1,
      icon: null,
      route_name: 'Role',
      menu_name: '角色管理',
      created_at: '2021-01-07T09:49:55.000000Z',
      updated_at: '2021-01-07T09:49:55.000000Z',
      pivot: {
        role_id: 2,
        permission_id: 1
      }
    },
    {
      id: 2,
      parent_id: -1,
      name: 'example',
      guard_name: 'jwt',
      display_name: '示例页面',
      is_menu: 1,
      is_universal: 1,
      icon: null,
      route_name: 'Example',
      menu_name: '示例页面',
      created_at: '2021-01-07T09:49:55.000000Z',
      updated_at: '2021-01-07T09:49:55.000000Z',
      pivot: {
        role_id: 2,
        permission_id: 1
      }
    },
    {
      id: 3,
      parent_id: 2,
      name: 'exampleOne',
      guard_name: 'jwt',
      display_name: '国内货运险 列表',
      is_menu: 1,
      is_universal: 1,
      icon: null,
      route_name: 'ExampleOne',
      menu_name: '国内货运险',
      created_at: '2021-01-07T09:49:55.000000Z',
      updated_at: '2021-01-07T09:49:55.000000Z',
      pivot: {
        role_id: 2,
        permission_id: 1
      }
    },
    {
      id: 4,
      parent_id: 2,
      name: 'exampleThree',
      guard_name: 'jwt',
      display_name: '国内货运险',
      is_menu: 1,
      is_universal: 1,
      icon: null,
      route_name: 'ExampleThree',
      menu_name: '国内货运险 投保',
      created_at: '2021-01-07T09:49:55.000000Z',
      updated_at: '2021-01-07T09:49:55.000000Z',
      pivot: {
        role_id: 2,
        permission_id: 1
      }
    },
    {
      id: 5,
      parent_id: 2,
      name: 'exampleTwo',
      guard_name: 'jwt',
      display_name: '国内货运险 详情',
      is_menu: 1,
      is_universal: 1,
      icon: null,
      route_name: 'ExampleTwo',
      menu_name: '国内货运险',
      created_at: '2021-01-07T09:49:55.000000Z',
      updated_at: '2021-01-07T09:49:55.000000Z',
      pivot: {
        role_id: 2,
        permission_id: 1
      }
    },
    {
      id: 6,
      parent_id: 1,
      name: 'permissions.create',
      guard_name: 'jwt',
      display_name: '添加权限',
      is_menu: 0,
      is_universal: 1,
      icon: null,
      route_name: null,
      menu_name: null,
      created_at: '2021-01-07T09:51:48.000000Z',
      updated_at: '2021-01-07T10:11:59.000000Z',
      pivot: {
        role_id: 2,
        permission_id: 3
      }
    }
  ]
}
