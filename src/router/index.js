/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-19 12:30:41
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-10 17:11:29
 */

import Vue from 'vue'
import VueRouter from 'vue-router'
import routes from './routes'
Vue.use(VueRouter)

const router = new VueRouter({
  base: '/',
  mode: 'history',
  routes: routes
})

router.beforeEach((to, from, next) => {
  const meta = to.meta
  if (meta.dynamicTitle) {
    const _temp = meta.titles.find((item) => item.current)
    if (_temp) {
      _temp.label = to.params[meta.dynamicTitle]
    } else {
      meta.titles.push({
        label: to.params[meta.dynamicTitle],
        current: true
      })
    }
  }
  next()
})

export default router
