/*
 * @Descripttion:
 * @Author: Mr. z<PERSON>
 * @Date: 2020-10-19 12:30:41
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-02-02 15:49:23
 */
//  引入拆分后的 route  S
// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context('./modules', true, /\.js$/)
// you do not need `import app from './modules/system'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // set './system.js' => 'system'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

// 二级路由
let secondaryRouting = []
for (const key in modules) {
  secondaryRouting.push.apply(secondaryRouting, modules[key])
}
//  引入拆分后的 route
const routes = [...secondaryRouting, { path: '*', component: () => import('@/views/404.vue') }]

export default routes
