export default [
  {
    title: '理赔服务',
    path: '/claim',
    name: 'Claim',
    component: () => import('@/layouts'),
    meta: {
      icon: 'fas fa-headset',
      feature: 'claims',
      isMenu: true
    },
    children: [
      {
        path: 'submission',
        name: 'ClaimSubmission',
        component: () => import('@/views/claim/Submission.vue'),
        title: '我要报案',
        meta: {
          isMenu: true,
          titles: [{ label: '理赔服务' }, { label: '我要报案' }]
        }
      },
      {
        path: 'progress',
        name: 'ClaimProgress',
        component: () => import('@/views/claim/Progress.vue'),
        title: '理赔进度查询',
        meta: {
          isMenu: true,
          titles: [{ label: '理赔服务' }, { label: '理赔进度查询' }]
        }
      }
    ]
  }
]
