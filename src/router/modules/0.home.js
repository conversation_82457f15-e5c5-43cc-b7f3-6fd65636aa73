export default [
  {
    title: '首页',
    path: '',
    component: () => import('@/layouts'),
    meta: {
      icon: 'fas fa-home',
      hasDropdown: false,
      redirect: 'HomeIndex',
      isMenu: true
    },
    children: [
      {
        path: '',
        name: 'HomeIndex',
        component: () => import('@/views/home/<USER>'),
        meta: {
          hideBreadcrumb: true,
          titles: [{ label: '首页' }]
        }
      },
      {
        path: 'announcements',
        name: 'Announcements',
        component: () => import('@/views/home/<USER>'),
        meta: {
          titles: [{ label: '系统通知' }]
        }
      },
      {
        path: 'premium-calculator',
        name: 'PremiumCalculator',
        component: () => import('@/views/home/<USER>'),
        meta: {
          titles: [{ label: '保费试算' }]
        }
      }
    ]
  }
]
