export default [
  {
    title: '资料中心',
    path: '/documents',
    name: 'Documents',
    component: () => import('@/layouts'),
    meta: {
      feature: 'documents',
      icon: 'fas fa-download',
      hasDropdown: false,
      redirect: 'DocumentIndex',
      isMenu: true
    },
    children: [
      {
        path: 'index',
        name: 'DocumentIndex',
        component: () => import('@/views/documents/Index.vue'),
        meta: {
          hideBreadcrumb: true,
          titles: [{ label: '资料中心' }]
        }
      }
    ]
  }
]
