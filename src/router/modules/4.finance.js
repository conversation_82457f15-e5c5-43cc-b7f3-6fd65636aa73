export default [
  {
    title: '财务管理',
    path: '/finance',
    name: 'Finance',
    component: () => import('@/layouts'),
    meta: {
      icon: 'fas fa-money-check',
      feature: 'finance',
      isMenu: true
    },
    children: [
      {
        path: 'income',
        title: '我的收益',
        name: 'FinanceIncome',
        component: () => import('@/views/finance/Income.vue'),
        meta: {
          feature: 'finance.income',
          isMenu: true,
          requiresAgent: true,
          titles: [{ label: '财务管理' }, { label: '我的收益', name: 'FinanceIncome', current: true }]
        }
      },
      {
        path: 'income/apply-withdraw',
        title: '我的收益',
        name: 'FinanceIncomeApplyWithdraw',
        component: () => import('@/views/finance/IncomeApplyWithdraw.vue'),
        meta: {
          isMenu: false,
          active: 'FinanceIncome',
          titles: [
            { label: '财务管理' },
            { label: '我的收益', name: 'FinanceIncome' },
            { label: '申请提现', current: true }
          ]
        }
      },
      {
        path: 'income/withdraw',
        title: '提现记录',
        name: 'FinanceIncomeWithdraw',
        component: () => import('@/views/finance/IncomeWithdraw.vue'),
        meta: {
          isMenu: false,
          active: 'FinanceIncome',
          titles: [
            { label: '财务管理' },
            { label: '我的收益', name: 'FinanceIncome' },
            { label: '提现记录', current: true }
          ]
        }
      },
      {
        path: 'transactions',
        title: '交易流水',
        name: 'FinanceTransactions',
        component: () => import('@/views/finance/Transactions.vue'),
        meta: {
          feature: 'finance.transactions',
          isMenu: true,
          titles: [{ label: '财务管理' }, { label: '交易流水', name: 'FinanceTransactions', current: true }]
        }
      },
      {
        path: 'invoices',
        title: '发票管理',
        name: 'FinanceInvoices',
        component: () => import('@/views/finance/Invoices.vue'),
        meta: {
          feature: 'finance.invoices',
          isMenu: true,
          titles: [{ label: '我的客户' }, { label: '发票管理', name: 'FinanceInvoices', current: true }]
        }
      },
      {
        path: 'invoices/records',
        title: '开票记录',
        name: 'FinanceInvoiceRecords',
        component: () => import('@/views/finance/InvoiceRecords.vue'),
        meta: {
          isMenu: false,
          active: 'FinanceInvoices',
          titles: [
            { label: '我的客户' },
            { label: '发票管理', name: 'FinanceInvoices' },
            { label: '开票历史', name: 'FinanceInvoiceRecords', current: true }
          ]
        }
      },
      {
        path: 'invoices/:id',
        title: '发票详情',
        name: 'FinanceInvoiceDetail',
        component: () => import('@/views/finance/InvoiceDetail.vue'),
        meta: {
          isMenu: false,
          active: 'FinanceInvoices',
          titles: [
            { label: '我的客户' },
            { label: '发票管理', name: 'FinanceInvoices' },
            { label: '开票历史', name: 'FinanceInvoiceRecords' },
            { label: '发票详情', name: 'FinanceInvoiceDetail', current: true }
          ]
        }
      }
    ]
  }
]
