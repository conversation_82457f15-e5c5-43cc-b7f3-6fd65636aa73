/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-22 09:27:52
 * @LastEditors: yanb
 * @LastEditTime: 2024-04-26 11:32:11
 */

export default [
  {
    title: '我要投保',
    path: '/policies/insure',
    name: 'PoliciesInsure',
    component: () => import('@/layouts'),
    meta: {
      feature: 'insure',
      icon: 'fas fa-shopping-basket',
      isMenu: true
    },
    children: [
      {
        title: '国内货运险',
        path: 'domestic/:id?',
        name: 'PoliciesInsureDomestic',
        component: () => import('@/views/policies/InsureDomestic.vue'),
        meta: {
          feature: 'insure.domestic',
          isMenu: true,
          titles: [{ label: '我要投保' }, { label: '国内货运险', current: true }]
        }
      },
      {
        title: '国际货运险',
        path: 'intl/:id?',
        name: 'PoliciesInsureIntl',
        component: () => import('@/views/policies/InsureIntl.vue'),
        meta: {
          feature: 'insure.intl',
          isMenu: true,
          titles: [{ label: '我要投保' }, { label: '国际货运险', current: true }]
        }
      },
      {
        title: '跨境电商货运险',
        path: 'cbec/:id?',
        name: 'PoliciesInsureCbec',
        component: () => import('@/views/policies/InsureCbec.vue'),
        meta: {
          feature: 'insure.cbec',
          isMenu: true,
          titles: [{ label: '我要投保' }, { label: '跨境电商货运险', current: true }]
        }
      },
      {
        title: '单车责任险',
        path: 'lbt/:id?',
        name: 'PoliciesInsureLbt',
        component: () => import('@/views/policies/InsureLbt.vue'),
        meta: {
          feature: 'insure.lbt',
          isMenu: true,
          titles: [{ label: '我要投保' }, { label: '单车责任险', current: true }]
        }
      },
      {
        title: '雇主责任险',
        path: 'group/:policyGroupId?/:step?',
        name: 'PoliciesInsureGroup',
        component: () => import('@/views/policies/InsureGroup.vue'),
        meta: {
          feature: 'insure.group',
          isMenu: true,
          titles: [{ label: '我要投保' }, { label: '雇主责任险', current: true }]
        }
      },
      {
        title: '其他险种',
        path: 'other',
        name: 'PoliciesInsureOther',
        component: () => import('@/views/policies/InsuranceOther.vue'),
        meta: {
          feature: 'insure.general',
          isMenu: true,
          titles: [{ label: '我要投保' }, { label: '其他险种', current: true }]
        }
      },
      {
        title: '其他险种详情',
        path: 'otherInsuranceDetail/:id',
        name: 'PoliciesInsureOtherDeatil',
        component: () => import('@/views/policies/InsureOtherDeatil.vue'),
        meta: {
          isMenu: false,
          active: 'PoliciesInsureOther',
          titles: [
            { label: '我要投保' },
            { label: '其他险种', name: 'PoliciesInsureOther' },
            { label: '其他险种详情', current: true }
          ]
        }
      },
      {
        title: '立即投保',
        path: 'form/:id',
        name: 'InsureOtherForm',
        component: () => import('@/views/policies/InsureOtherForm.vue'),
        meta: {
          isMenu: false,
          active: 'PoliciesInsureOther',
          titles: [
            { label: '我要投保' },
            { label: '其他险种', name: 'PoliciesInsureOther' },
            { label: '填写保单', current: true }
          ]
        }
      },
      {
        title: '支付信息',
        path: 'pay/:id',
        name: 'PayInfoPage',
        component: () => import('@/views/policies/PayInfoPage.vue'),
        meta: {
          isMenu: false,
          active: 'PoliciesInsureOther',
          titles: [
            { label: '我要投保' },
            { label: '其他险种', name: 'PoliciesInsureOther' },
            { label: '填写保单', name: 'InsureOtherForm' },
            { label: '支付信息', current: true }
          ]
        }
      }
      // {
      //   title: '保单详情',
      //   path: 'policyDetail/:id',
      //   name: 'policyDetail',
      //   component: () => import('@/views/policies/policyDetail.vue'),
      //   meta: {
      //     isMenu: false,
      //     titles: [
      //       { label: '我要投保' },
      //       { label: '其他险种', name: 'PoliciesInsureOther' },
      //       { label: '保单详情', current: 'true' }
      //     ]
      //   }
      // }
    ]
  },
  {
    title: '保单管理',
    path: '/policies',
    name: 'Policies',
    component: () => import('@/layouts'),
    meta: {
      feature: 'insure',
      icon: 'fas fa-paperclip',
      isMenu: true
    },
    children: [
      {
        title: '国内货运险',
        path: 'domestic',
        name: 'PoliciesDomestic',
        component: () => import('@/views/policies/PoliciesDomestic.vue'),
        meta: {
          isMenu: true,
          feature: 'insure.domestic',
          titles: [{ label: '保单管理' }, { label: '国内货运险', current: true }]
        }
      },
      {
        title: '国内货运险详情',
        path: 'domestic/:id',
        name: 'PoliciesDomesticDetails',
        component: () => import('@/views/policies/PoliciesDomesticDetails.vue'),
        meta: {
          isMenu: false,
          hideBreadcrumb: true,
          active: 'PoliciesDomestic',
          titles: [
            { label: '保单管理' },
            { label: '国内货运险', name: 'PoliciesDomestic' },
            { label: '保单详情', current: true }
          ]
        }
      },
      {
        title: '国际货运险',
        path: 'intl',
        name: 'PoliciesIntl',
        component: () => import('@/views/policies/PoliciesIntl.vue'),
        meta: {
          feature: 'insure.intl',
          isMenu: true,
          titles: [{ label: '保单管理' }, { label: '国际货运险', current: true }]
        }
      },
      {
        title: '国际货运险详情',
        path: 'intl/:id',
        name: 'PoliciesIntlDetails',
        component: () => import('@/views/policies/PoliciesIntlDetails.vue'),
        meta: {
          isMenu: false,
          hideBreadcrumb: true,
          active: 'PoliciesIntl',
          titles: [
            { label: '保单管理' },
            { label: '国际货运险', name: 'PoliciesIntl' },
            { label: '保单详情', current: true }
          ]
        }
      },
      {
        title: '跨境电商货运险',
        path: 'cbec',
        name: 'PoliciesCbec',
        component: () => import('@/views/policies/PoliciesCbec.vue'),
        meta: {
          feature: 'insure.cbec',
          isMenu: true,
          titles: [{ label: '保单管理' }, { label: '跨境电商货运险', current: true }]
        }
      },
      {
        title: '跨境电商货运险详情',
        path: 'cbec/:id',
        name: 'PoliciesCbecDetails',
        component: () => import('@/views/policies/PoliciesCbecDetails.vue'),
        meta: {
          isMenu: false,
          hideBreadcrumb: true,
          active: 'PoliciesCbec',
          titles: [
            { label: '保单管理' },
            { label: '跨境电商货运险', name: 'PoliciesCbec' },
            { label: '保单详情', current: true }
          ]
        }
      },
      {
        title: '单车责任险',
        path: 'lbt',
        name: 'PoliciesLbt',
        component: () => import('@/views/policies/PoliciesLbt.vue'),
        meta: {
          feature: 'insure.lbt',
          isMenu: true,
          titles: [{ label: '保单管理' }, { label: '单车责任险', current: true }]
        }
      },
      {
        title: '雇主责任险',
        path: 'group',
        name: 'PoliciesGroup',
        component: () => import('@/views/policies/PoliciesGroup.vue'),
        meta: {
          feature: 'insure.group',
          isMenu: true,
          titles: [{ label: '保单管理' }, { label: '雇主责任险', current: true }]
        }
      },
      {
        title: '雇主责任险',
        path: 'group/:id',
        name: 'PoliciesGroupDetails',
        component: () => import('@/views/policies/PoliciesGroupDetails.vue'),
        meta: {
          isMenu: false,
          hideBreadcrumb: true,
          active: 'PoliciesGroup',
          titles: [
            { label: '保单管理' },
            { label: '雇主责任险', name: 'PoliciesGroup' },
            { label: '保单详情', current: true }
          ]
        }
      },
      {
        title: '雇主责任险',
        path: 'group/:id/employee',
        name: 'PoliciesGroupEmployee',
        component: () => import('@/views/policies/PoliciesGroupEmployee.vue'),
        meta: {
          isMenu: false,
          hideBreadcrumb: true,
          active: 'PoliciesGroup',
          titles: [
            { label: '保单管理' },
            { label: '雇主责任险', name: 'PoliciesGroup' },
            { label: '保单详情', name: 'PoliciesGroupDetails' },
            { label: '人员管理', current: true }
          ]
        }
      },
      {
        title: '雇主责任险',
        path: 'group/:id/payments',
        name: 'PoliciesGroupPayments',
        component: () => import('@/views/policies/PoliciesGroupPayments.vue'),
        meta: {
          isMenu: false,
          active: 'PoliciesGroup',
          titles: [
            { label: '保单管理' },
            { label: '雇主责任险', name: 'PoliciesGroup' },
            { label: '保单详情', name: 'PoliciesGroupDetails' },
            { label: '支付记录', current: true }
          ]
        }
      },
      {
        title: '雇主责任险',
        path: 'group/:id/endorse',
        name: 'PoliciesGroupEndorse',
        component: () => import('@/views/policies/PoliciesGroupEndorse.vue'),
        meta: {
          isMenu: false,
          active: 'PoliciesGroup',
          titles: [
            { label: '保单管理' },
            { label: '雇主责任险', name: 'PoliciesGroup' },
            { label: '保单详情', name: 'PoliciesGroupDetails' },
            { label: '人员管理', name: 'PoliciesGroupEmployee' },
            { label: '批单管理', current: true }
          ]
        }
      },
      {
        title: '单车责任险详情',
        path: 'lbt/:id',
        name: 'PoliciesLbtDetails',
        component: () => import('@/views/policies/PoliciesLbtDetails.vue'),
        meta: {
          isMenu: false,
          hideBreadcrumb: true,
          active: 'PoliciesLbt',
          titles: [
            { label: '保单管理' },
            { label: '单车责任险', name: 'PoliciesLbt' },
            { label: '保单详情', current: true }
          ]
        }
      },
      {
        title: '其他险种',
        path: 'others',
        name: 'PolicyOther',
        component: () => import('@/views/policies/PolicyOther.vue'),
        meta: {
          feature: 'insure.general',
          isMenu: true,
          titles: [{ label: '保单管理' }, { label: '其他险种', current: true }]
        }
      },
      {
        title: '其他险种详情页',
        path: 'mod/:id',
        name: 'ManageOtherDetail',
        component: () => import('@/views/policies/ManageOtherDetail.vue'),
        meta: {
          isMenu: false,
          hideBreadcrumb: true,
          active: 'PolicyOther',
          titles: [
            { label: '保单管理' },
            { label: '其他险种', name: 'PolicyOther' },
            { label: '其他险种详情', current: true }
          ]
        }
      },
      {
        title: '修改',
        path: 'EditPage/:id',
        name: 'EditPage',
        component: () => import('@/views/policies/EditPage.vue'),
        meta: {
          isMenu: false,
          hideBreadcrumb: false,
          active: 'PolicyOther',
          titles: [
            { label: '保单管理' },
            { label: '其他险种', name: 'PolicyOther' },
            { label: '其他险种详情', name: 'ManageOtherDetail' },
            { label: '修改', current: true }
          ]
        }
      },
      {
        title: '支付记录',
        path: 'ph/:id',
        name: 'PayHistory',
        component: () => import('@/views/policies/PayHistory.vue'),
        meta: {
          isMenu: false,
          active: 'PolicyOther',
          titles: [
            { label: '保单管理' },
            { label: '支付详情', name: 'PolicyOther' },
            { label: '支付记录', current: true }
          ]
        }
      },
      {
        title: '历史保单',
        path: 'histories',
        name: 'PoliciesHistory',
        component: () => import('@/views/policies/PoliciesHistory.vue'),
        meta: {
          feature: 'insure.history',
          isMenu: true,
          titles: [{ label: '保单管理' }, { label: '历史保单', current: true }]
        }
      },
      {
        title: '历史保单详情',
        path: 'histories/:id',
        name: 'PoliciesHistoryDetails',
        component: () => import('@/views/policies/PoliciesHistoryDetails.vue'),
        meta: {
          isMenu: false,
          titles: [
            { label: '保单管理' },
            { label: '历史保单', name: 'PoliciesHistory' },
            { label: '保单详情', current: true }
          ]
        }
      },
      {
        title: '线下录入保单',
        path: 'offline',
        name: 'PoliciesOffline',
        component: () => import('@/views/policies/PoliciesOffline.vue'),
        meta: {
          isMenu: true,
          feature: 'insure.offline',
          titles: [{ label: '保单管理' }, { label: '线下保单', current: true }]
        }
      },
      {
        title: '线下保单详情',
        path: 'offline/:id',
        name: 'PoliciesOfflineDetails',
        component: () => import('@/views/policies/PoliciesOfflineDetails.vue'),
        meta: {
          isMenu: false,
          hideBreadcrumb: true,
          active: 'PoliciesOffline',
          titles: [
            { label: '保单管理' },
            { label: '线下保单', name: 'PoliciesOffline' },
            { label: '保单详情', current: true }
          ]
        }
      }
    ]
  }
]
