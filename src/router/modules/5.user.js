export default [
  {
    title: '我的客户',
    path: '/users',
    name: 'Users',
    component: () => import('@/layouts'),
    meta: {
      icon: 'fas fa-users',
      hasDropdown: false,
      redirect: 'UsersIndex',
      isMenu: true,
      feature: 'customers',
      requiresAgent: true
    },
    children: [
      {
        path: 'index',
        name: 'UsersIndex',
        component: () => import('@/views/users/Index.vue'),
        meta: {
          hideBreadcrumb: true,
          titles: [{ label: '我的客户' }]
        }
      },
      {
        path: ':id/products',
        name: 'UsersProducts',
        component: () => import('@/views/users/products'),
        meta: {
          hideBreadcrumb: false,
          titles: [
            { label: '我的客户', name: 'UsersIndex' },
            { label: '费率配置', name: 'UsersProducts', current: true }
          ]
        }
      }
    ]
  }
]
