import { findByDomain as apiFindByDomain } from '@/apis/platform'
import { Loading, Message } from 'element-ui'
import { platformKey } from '@/config'

const state = {
  platform: JSON.stringify(window.localStorage.getItem(platformKey)) || {}
}

const mutations = {}

const getters = {
  logo: (state) => state.platform.logo,
  logoWhite: (state) => state.platform.logo_white,
  title: (state) => state.platform.title,
  slogan: (state) => state.platform.slogan,
  description: (state) => state.platform.description,
  favicon: (state) => state.platform.favicon,
  bgImage: (state) => state.platform.bg_image,
  primaryColor: (state) => state.platform.primary_color,
  customFooter: (state) => state.platform.custom_footer,
  styles: (state) => state.platform.styles,
  features: (state) => state.platform.features,
  officialSite: (state) => state.platform.official_website,
  online_payment_is_enabled: (state) => state.platform.online_payment_is_enabled
}

const actions = {
  findByDomain({ state }) {
    const loading = Loading.service()
    apiFindByDomain(window.location.hostname)
      .then((r) => {
        state.platform = r.data

        window.localStorage.setItem(platformKey, JSON.stringify(state.platform))

        document.querySelector('link[rel=icon]').setAttribute('href', r.data.favicon)
        document.title = r.data.title
        loading.close()
      })
      .catch(() => {
        Message.error('未授权的域名')
      })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
}
