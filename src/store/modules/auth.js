import { token<PERSON><PERSON>, user<PERSON><PERSON> } from '@/config'
import { getUser } from '@/apis/user'

const state = {
  user: JSON.parse(window.localStorage.getItem(userKey)) || {},
  token: ''
}

const getters = {
  user: (state) => state.user
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token

    window.localStorage.setItem(tokenKey, token)
  },
  SET_USER: (state, user) => {
    state.user = user

    window.localStorage.setItem(userKey, JSON.stringify(user))
  }
}

const actions = {
  setToken({ commit }, data) {
    commit('SET_TOKEN', data.access_token)

    getUser().then((r) => {
      commit('SET_USER', r.data)
    })
  },
  refreshUser({ commit }) {
    getUser().then((r) => {
      commit('SET_USER', r.data)
    })
  },
  removeToken() {
    window.localStorage.removeItem(tokenKey)
    window.localStorage.removeItem(userKey)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
