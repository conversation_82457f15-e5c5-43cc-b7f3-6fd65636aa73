/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-07 21:36:30
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 18:08:36
 */
/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-07 21:36:30
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 10:59:35
 */
const state = {
  announcementVisible: true
}
const mutations = {
  setAnnouncementHasRead(state, announcementId) {
    state.announcementVisible = true

    window.localStorage.setItem('announcementHasRead:' + announcementId, 1)
  }
}
const getters = {
  announcementVisible: (state) => (announcementId) => {
    const visible = window.localStorage.getItem('announcementHasRead:' + announcementId)
    state.announcementVisible = visible === null ? true : false
    return state.announcementVisible
  }
}
const actions = {
  setAnnouncementHasRead({ commit }, announcementId) {
    commit('setAnnouncementHasRead', announcementId)
  }
}

const app = {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
export default app
