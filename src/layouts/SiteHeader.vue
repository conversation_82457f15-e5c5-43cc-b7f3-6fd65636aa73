<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 10:05:33
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-08 15:58:10
-->
<template>
  <el-header class="site-container__header">
    <i class="site-collapsed font-20" @click="handleChangeCollapse" :class="collapseIcon"></i>
    <div class="announcements">
      <span
        class="title text-danger"
        style="cursor: pointer; font-weight: bold"
        @click="$router.push({ name: 'Announcements' })"
      >
        {{ title }}
        <el-link v-if="title" type="primary" style="margin-top: -2px" @click="$router.push({ name: 'Announcements' })">
          更多通知
        </el-link>
      </span>
    </div>
    <div class="right-actions">
      <el-button
        size="mini"
        type="primary"
        icon="fas fa-calculator"
        @click="$router.push({ name: 'PremiumCalculator' })"
      >
        保费试算
      </el-button>
      <el-popover width="300" trigger="click">
        <span slot="reference">
          <el-badge :value="messages.length" class="item">
            <i class="el-icon-chat-dot-round font-20"></i>
          </el-badge>
        </span>
        <div class="messages">
          <template v-if="messages.length <= 0">
            <p style="text-align: center">暂无未读消息</p>
          </template>
          <template v-else>
            <ul class="list">
              <li v-for="message in messages" :key="message.id">
                <span class="text-ellipsis" style="width: 238px; display: inline-block">
                  <el-tooltip class="item" effect="dark" :content="message.message" placement="top-start">
                    <span :class="`text-${message.level}`">{{ message.message }}</span>
                  </el-tooltip>
                </span>
                <el-button
                  class="read-btn"
                  type="text"
                  size="mini"
                  circle
                  icon="fas fa-check"
                  @click="$emit('readMessage', message)"
                >
                  已读
                </el-button>
              </li>
            </ul>
          </template>
        </div>
      </el-popover>
      <div class="logged-user">
        <span class="nick-name not-space">{{ userName }}</span>
        <el-divider direction="vertical" style="margin-left: -5px; margin-right: -5px"></el-divider>
        <el-link type="primary" :underline="false" @click="handleSignOut">退出</el-link>
      </div>
      <div class="balance">
        <div class="icon">
          <i class="fas fa-yen-sign" />
        </div>
        <label class="label"> {{ balance }} </label>
      </div>
    </div>
  </el-header>
</template>

<script>
export default {
  name: 'SiteHeader',
  props: {
    // 余额
    balance: {
      type: [String, Number],
      default: 0.0
    },
    // 用户名称
    userName: {
      type: String,
      default: ''
    },
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 菜单是否折叠
    isCollapse: {
      type: Boolean,
      deault: false
    },
    messages: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      hasTriggeredBalanceNotify: false
    }
  },
  watch: {
    balance: {
      deep: true,
      immediate: true,
      handler(value) {
        const balance = parseFloat(value)
        if (balance < 500 && balance > 0 && !this.hasTriggeredBalanceNotify && this.userName) {
          this.$notify({
            type: 'warning',
            title: '余额不足',
            message: '您的余额已不足 500 元，为了不影响您的正常投保，请及时充值',
            duration: 0
          })

          this.hasTriggeredBalanceNotify = true
        }
      }
    }
  },
  computed: {
    collapseIcon() {
      return this.isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'
    }
  },
  methods: {
    handleChangeCollapse() {
      this.$emit('update:isCollapse', !this.isCollapse)
    },
    handleSignOut() {
      this.$emit('signOut')
    }
  }
}
</script>

<style lang="scss" scoped>
.messages {
  .read-all-btn {
    text-align: right;
  }
  .list {
    padding: 0;
    li {
      list-style: none;
      height: 40px;
      border-bottom: 1px solid #eee;
      line-height: 40px;

      .read-btn {
        float: right;
        height: 40px;
        line-height: 20px;
      }
    }
  }
}
</style>
