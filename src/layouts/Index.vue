<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-07 20:24:41
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 15:15:28
-->
<template>
  <el-container class="site-container">
    <el-aside
      :collapse-transition="false"
      unique-opened
      class="site-container__aside"
      :class="{ collapsed: asideIsCollapse }"
    >
      <div class="site-logo">
        <img :src="logoSrc" :alt="title" />
      </div>
      <div class="flex-fill o-hidden">
        <el-scrollbar style="height: 100%">
          <AsideMenu :menus="menus" :collapse="asideIsCollapse" :is-agent="user.is_agent" />
        </el-scrollbar>
      </div>
    </el-aside>
    <el-container class="site-container__main">
      <site-header
        :isCollapse.sync="asideIsCollapse"
        :title="announcement?.content"
        :userName="user.name"
        :messages="messages"
        :balance="user.balance"
        @signOut="handleSignOut"
        @readMessage="handleReadMessage"
      ></site-header>
      <site-breadcrumb v-if="!$route.meta.hideBreadcrumb" />
      <el-main class="site-container__content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import AsideMenu from './AsideMenu'
import SiteHeader from './SiteHeader'
import SiteBreadcrumb from './SiteBreadcrumb'
import { mapGetters } from 'vuex'
import './style.scss'
import { getUnreadMessages, markMessageAsRead } from '@/apis/user'
import { latestAnnouncement } from '@/apis/announcement'
import { tokenKey } from '../config'

export default {
  name: 'SiteLayout',
  components: { AsideMenu, SiteHeader, SiteBreadcrumb },
  data() {
    return {
      asideIsCollapse: false,
      announcement: {},
      announcementVisible: true,
      announcementWaitForRead: false,
      messages: []
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    ...mapGetters('platform', ['title', 'logo', 'favicon']),
    menus() {
      return this.$router.options.routes
    },
    logoSrc() {
      return this.asideIsCollapse ? this.favicon : this.logo
    },
    collapseIcon() {
      return this.asideIsCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'
    }
  },
  created() {
    this.loadAnnouncement()
    setInterval(() => {
      if (Object.keys(this.user).length > 0 && window.localStorage.getItem(tokenKey)) {
        this.loadMessages()
        this.loadAnnouncement()
        this.$store.dispatch('auth/refreshUser')
      }
    }, 30000)
  },
  methods: {
    loadAnnouncement() {
      // 1: 全站通知
      latestAnnouncement(1).then((r) => {
        this.announcement = r.data

        const visible = this.$store.getters['app/announcementVisible'](this.announcement?.id)
        if (this.announcement?.id && visible && !this.announcementWaitForRead) {
          this.announcementWaitForRead = true
          this.$alert(this.announcement?.content, '公告', {
            confirmButtonText: '确定',
            dangerouslyUseHTMLString: true,
            callback: () => {
              this.announcementVisible = false

              this.$store.dispatch('app/setAnnouncementHasRead', this.announcement?.id, true)
            }
          })
        }
      })
    },
    loadMessages() {
      getUnreadMessages().then((r) => {
        this.messages = r.data
      })
    },
    handleReadMessage(message) {
      markMessageAsRead(message.id).then(() => {
        this.loadMessages()
      })
    },
    /** 退出处理函数 */
    handleSignOut() {
      this.$confirm('是否确认退出登录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$store.dispatch('auth/removeToken')

          this.$message.success('退出成功')
          this.$router.push({ name: 'Login' })

          window.location.reload()
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-menu-item,
/deep/ .el-submenu {
  .svg-inline--fa {
    vertical-align: middle;
    margin-right: 5px;
    width: 24px;
    text-align: center;
  }
}
</style>
