/*
 * @Descripttion:生成菜单
 * @Author: Mr. zhu
 * @Date: 2021-01-08 00:04:31
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 17:25:23
 */

import { mapGetters } from 'vuex'

export default {
  name: 'AsideMenu',
  props: {
    menus: {
      type: Array,
      default: () => []
    },
    defaultActive: {
      type: String,
      default: ''
    },
    collapse: {
      type: Boolean,
      default: false
    },
    isAgent: {
      type: [Boolean, Number],
      default: false
    }
  },
  computed: {
    ...mapGetters('platform', ['features'])
  },
  data() {
    return {
      defaultOpen: []
    }
  },
  methods: {
    handleSelect(key, keyPath) {
      this.defaultOpen = [keyPath[0]]
      this.$router
        .push({
          name: key
        })
        .catch((e) => { })
    }
  },
  render() {
    const that = this
    function renderMenu(arr = []) {
      return arr.map((item) => {
        if (item?.meta?.isMenu) {
          if (item?.meta?.requiresAgent && !that.isAgent) {
            return ''
          }

          if (item?.meta?.feature && !that.features.includes(item?.meta?.feature)) {
            return ''
          }

          if (item.children && item.children.length > 0 && item?.meta?.hasDropdown !== false) {
            return (
              <el-submenu index={item.name}>
                <template slot="title">
                  <i class={item?.meta?.icon}></i>
                  <span>{item?.title}</span>
                </template>
                {renderMenu(item.children)}
              </el-submenu>
            )
          } else {
            return (
              <el-menu-item key={item?.name} index={item?.meta?.redirect ? item.meta.redirect : item?.name}>
                <i class={item?.meta?.icon}></i>
                <span slot="title">{item.title}</span>
                <i class="border"></i>
              </el-menu-item>
            )
          }
        }
      })
    }
    return (
      <el-menu
        collapse={this.collapse}
        default-active={this.$route?.meta?.active || this.$route.name}
        default-openeds={this.defaultOpen}
        unique-opened={true}
        class="site-container__asid-menu"
        background-color="#364666"
        text-color="#ccc"
        active-text-color="#fff"
        style="border-right: 0;"
        vOn:select={this.handleSelect}
      >
        {renderMenu(this.menus)}
      </el-menu>
    )
  }
}
