.site-container {
  display: flex;
  background-color: $site-app-main-bg;
  width: 100vw;
  height: 100vh;
  min-width: 1366px;
  .site-container__aside {
    display: flex;
    flex-direction: column;
    width: $site-app-aside-width !important;
    border-right: $site-app-aside-border-right;
    background-color: $site-app-aside-bg;
    z-index: 1;
    transition: 0.2s ease-in-out;
    overflow: hidden;
    .site-logo {
      min-height: $site-app-header-hight;
      display: flex;
      align-items: center;
      background-color: $site-app-header-bg;
      padding: 14px 0 14px $app-space-extra-large;
      box-sizing: border-box;
      img {
        height: $site-app-header-hight - 28px;
      }
    }
    &.collapsed {
      width: $site-app-aside-width-collapsed !important;
      transition: 0.2s ease-in-out;
    }
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
    .el-scrollbar__bar.is-horizontal {
      display: none;
    }
    .site-container__asid-menu {
      width: $site-app-aside-width !important;
      border: none;
      background-color: $site-app-aside-bg;
      transition: 0.2s ease-in-out;
      .el-menu-item {
        position: relative;
        &.is-active .border {
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          width: 4px;
          background-color: $app-color-primary;
        }
      }
      .el-submenu.is-opened .el-menu,
      .el-submenu.is-opened .el-menu-item {
        background-color: $site-app-aside-bg-opened !important;
        transition: all 0.2s;
        .el-menu-item:hover,
        .el-menu-item.is-active {
          transition: all 0.2s;
          background-color: $site-app-aside-menu-item-active-bg !important;
        }
      }
      &.el-menu--collapse {
        width: $site-app-aside-width-collapsed !important;
      }
    }
  }

  .site-container__main {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-bottom: $app-space-extra-large !important;
    .site-container__header {
      display: flex;
      align-items: center;
      min-height: $site-app-header-hight !important;
      background-color: $site-app-header-bg;
      padding: $app-space-extra-large;
      & > *:not(.el-divider, ) {
        margin-right: $app-space-extra-large;
      }
      .not-space,
      & > *:last-child {
        margin-right: 0;
      }
      .site-collapsed {
        font-size: $app-space-extra-large;
      }
      .announcements {
        flex: 1;
      }
      .right-actions {
        display: flex;
        align-items: center;
        column-gap: $app-space-extra-large;
      }
      .balance {
        display: flex;
        border-radius: $site-app-header-balance-radius;
        border: 1px solid $app-color-primary;
        .icon {
          width: $site-app-header-balance-icon-size;
          height: $site-app-header-balance-icon-size;
          line-height: $site-app-header-balance-icon-size;
          text-align: center;
          color: white;
          background-color: $app-color-primary;
        }
        .label {
          padding: 0 $site-app-header-balance-label-padding;
          line-height: $site-app-header-balance-icon-size;
          color: $app-color-primary;
        }
      }
    }
    .site-container__content {
      display: flex;
      padding: 0 !important;
    }
  }
}
