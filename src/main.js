/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-19 12:26:38
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2020-10-19 12:48:00
 */
import Vue from 'vue'
import router from '@/router'
import store from '@/store'
import '@/plugins'
import '@/components'
import App from '@/App.vue'

if (
  /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) &&
  window.location.pathname.indexOf('/mobile') === -1
) {
  window.location.href = `${window.location.origin}/mobile`
}

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: (h) => h(App)
}).$mount('#baoya')
