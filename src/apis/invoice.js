/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-06-21 09:57:49
 * @LastEditors: yanb
 * @LastEditTime: 2022-11-29 16:24:15
 */
import { get, post } from '../utils/axios'
import { tokenKey } from '@/config'
import qs from 'qs'

// 可开票保单.
export const getInovices = (data) => get('users/me/invoices', data)

// 获取发票.
export const getInvoiceRecords = (data) => get('users/me/invoices/records', data)

// 获取发票详情.
export const getInvoice = (id) => get(`users/me/invoices/${id}`)

// 开票.
export const makeInvoice = (data) => post('users/me/invoices', data)

// 全部开票.
export const makeInvoiceAll = (data) => post('users/me/invoices/make-invoice-all', data)

// 导出保单.
export const exportPolicy = (id) => {
  const token = window.localStorage.getItem(tokenKey)

  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  return `${baseUrl}users/me/invoices/${id}/export-policies` + '?' + qs.stringify({ token })
}

// 获取推荐信息.
export const fetchSuggestion = (data) => get('users/me/invoices/suggestion', data)
