import { get, post, postFormData, del } from '../utils/axios'
import { tokenKey } from '@/config'

// 获取雇主产品列表
export const getGroupProducts = () => get('group-products')

// 获取套餐工种
export const getGroupProductPlan = (planId) => get(`group-products/plans/${planId}`)

// 雇主保单暂存
export const insureGroupPolicy = (data) => {
  if (data.product_platform !== undefined) {
    return post('group-policies', data)
  } else {
    return postFormData('group-policies', data)
  }
}

// 获取雇主保单列表
export const getGroupPolicies = (data) => get('group-policies', data)

// 获取雇主保单详情
export const getGroupPolicy = (policyGroupId) => get(`group-policies/${policyGroupId}`)

// 获取保单未提交人员列表
export const getUnsubmitEmployee = (policyGroupId, data) =>
  get(`group-policies/employees/${policyGroupId}/endorse`, data)

// 暂存单作废
export const invalidGroupPolicy = (policyGroupId) => post(`group-policies/${policyGroupId}/invalid`)

// 保单人员批改
export const modifyInsuredEmployee = (data) => post('group-policies/employees/modify', data)

// 保单人员撤消
export const deleteUnsubmitEmployee = (employeeId) => del(`group-policies/employees/modify/${employeeId}`)

// 人员导入
export const importEmployeesList = (policyGroupId, data) =>
  postFormData(`group-policies/employees/import/${policyGroupId}`, data)

// 计算保费
export const clacEmployeePremium = (policyGroupId) => get(`group-policies/employees/${policyGroupId}/premium`)

// 保单人员提交
export const submitEmployee = (policyGroupId, data) =>
  postFormData(`group-policies/employees/${policyGroupId}/submit`, data)

// 获取参保人员列表
export const getInsuredEmployee = (policyGroupId, data) => get(`group-policies/employees/${policyGroupId}`, data)

// 获取批单列表
export const getGroupEndorses = (policyGroupId) => get(`group-policies/endorses/${policyGroupId}`)

// 获取保单支付记录
export const getGroupPayments = (policyGroupId) => get(`group-policies/transactions/${policyGroupId}`)

// 获取保单产品信息
export const getGroupPolicyProduct = (policyGroupId) => get(`group-products/policy/${policyGroupId}`)

// 获取发票历史信息
export const getInsuredInvoiceHistory = (data) => post(`group-policies/invoice`, data)

// 导出连接
export const buildEndorseDownloadHref = (id) => {
  const token = window.localStorage.getItem(tokenKey)

  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  return `${baseUrl}group-policies/endorses/detail/${id}/download?token=${token}`
}
