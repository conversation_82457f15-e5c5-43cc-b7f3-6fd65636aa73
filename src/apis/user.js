/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-11-04 15:10:37
 * @LastEditors: yanb
 * @LastEditTime: 2021-11-26 15:41:06
 */
import { del, get, patch, post, put } from '../utils/axios'
import { tokenKey } from '@/config'
import qs from 'qs'

// 获取用户列表.
export const getUsers = (data) => get(`users`, data)

// 获取用户字典.
export const getUsersDict = () => get(`users/dict`)

// 获取当前登录用户.
export const getUser = (id = 'me') => get(`users/${id}`)

// 更新用户信息
export const updateUser = (id = 'me', data) => put(`users/${id}`, data)

// 创建用户.
export const createUser = (data) => post('users', data)

// 更新用户密码
export const updatePassword = (id = 'me', data) => patch(`users/${id}/password`, data)

/*- 以下用 me 代替 id -*/
// 获取银行卡
export const getBankcards = () => get('users/me/bankcards')

// 获取银行卡详情
export const getBankcard = (id) => get(`users/me/bankcards/${id}`)

// 创建银行账户
export const createBankcard = (data) => post('users/me/bankcards', data)

// 更新银行账户信息
export const updateBankcard = (id, data) => put(`users/me/bankcards/${id}`, data)

// 删除银行账户信息
export const deleteBankcard = (id) => del(`users/me/bankcards/${id}`)

// 获取发票信息
export const getInvoiceInformation = () => get('users/me/invoice')

// 更新开票信息
export const updateInvoiceInformation = (data) => post('users/me/invoice', data)

// 获取入账记录.
export const getIncomeTransactions = (id = 'me', data) => get(`users/${id}/transactions/income`, data)

// 获取充值记录.
export const getPayments = (data) => get(`payments`, data)

//在线充值
export const onlineRecharge = (data) => post(`payments/online-recharge`, data)

// 取消充值
export const cancelRecharge = (id) => del(`payments/${id}/cancel-recharge`)

// 获取交易流水.
export const getTransactions = (id = 'me', data) => get(`users/${id}/transactions`, data)

// 导出交易流水
export const buildTransactionsExportHref = (id = 'me', data) => {
  data.token = window.localStorage.getItem(tokenKey)

  return process.env.VUE_APP_BASE_API + `users/${id}/transactions/export` + '?' + qs.stringify(data)
}

// 获取用户产品.
export const getUserProducts = (id = 'me', data) => get(`users/${id}/products`, { filter: data })

// 设置用户产品.
export const assignProduct = (id, userProductId, data) => post(`users/${id}/products/${userProductId}/assignment`, data)

// 更新用户产品状态.
export const updateProductStatus = (id, userProductId, status) =>
  patch(`users/${id}/products/${userProductId}/status`, { status })

// 未读消息.
export const getUnreadMessages = () => get(`users/me/messages`)

// 标记为已读.
export const markMessageAsRead = (id) => patch(`users/me/messages/${id}`)

// 获取用户收益
export const getIncomes = (data) => get('users/me/incomes', data)

// 导出收益
export const buildIncomesExportHref = (data) => {
  data.token = window.localStorage.getItem(tokenKey)

  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  return `${baseUrl}users/me/incomes/export` + '?' + qs.stringify(data)
}

// 充值
export const charge = (id, data) => patch(`users/${id}/charge`, data)

// 充值
export const pay = (id, data) => patch(`users/${id}/pay`, data)

// 充值记录
export const getUserPayments = (id, data) => get(`users/${id}/payments`, data)
