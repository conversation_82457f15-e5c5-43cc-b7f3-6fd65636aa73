import * as _ from '../utils/axios'
import { tokenKey } from '@/config/index'
import qs from 'qs'

// 我要投保-其他险种 列表接口
export const otherInsurance = (data) => _.get(`/general-products`, data)
// 我要投保-其他险种 详情
export const otherInsuranceDetail = (id) => _.get(`general-products/${id}`)

//保险分类下拉选项
export const categoriesList = () => _.get('general-products/categories')

// 填写表单接口
export const writeTable = (data) => _.postFormData('policies', data)

// 上传支付凭证
export const uploadFile = (id, data) => _.postFormData(`general-policies/${id}/premium`, data)
// 查询支付金额
// export const checkPay = (id) => _.get(``)

// 保单管理
// 列表
// export const manageList = (data) => _.get(`general-policies`, data)
export const manageList = (data) => _.get(`policies`, data)
// 详情
export const checkDetail = (id) => _.get(`policies/${id}`)
// 保险公司
export const companies = () => _.get('companies')

// 导出连接
export const buildExportHref = (data) => {
  data.token = window.localStorage.getItem(tokenKey)

  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  return `${baseUrl}policies/export` + '?' + qs.stringify(data)
}
// 支付记录
export const recordpay = (id) => _.get(`general-policies/${id}/transaction`)
// 修改接口
export const editFrom = (id, data) => _.postFormData(`policies/${id}/modify`, data)
