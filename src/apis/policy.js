/*
 * @Descripttion:
 * @Author: Mr. <PERSON>hu
 * @Date: 2020-10-22 17:26:55
 * @LastEditors: yanb
 * @LastEditTime: 2022-03-10 15:56:08
 */
import { get, patch, post, postFormData } from '../utils/axios'
import { tokenKey } from '@/config'
import qs from 'qs'

/**
 * 保单列表
 * @param { Object } data  { filter: { type: [ '1(国内)', '2(国际)' ]  } }
 */
export const getPolicies = (data) => get('policies', data)

/**
 * 保单列表
 * @param { Object } data
 */
export const getHistoryPolicies = (data) => get('policies/histories', data)

/* 获取历史保单详情 */
export const getHistoryPolicyDetail = (id) => get(`policies/histories/${id}`)

// 导出连接
export const buildExportHistoryPolicyHref = (data) => {
  data.token = window.localStorage.getItem(tokenKey)

  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  return `${baseUrl}policies/histories/export` + '?' + qs.stringify(data)
}

/**
 * 保单详情
 * @param { String || Number } id  数据id
 */
export const getPolicyDetails = (id) => get(`policies/${id}`)

/* 检查指定信息的保单是否存在 */
export const checkIfExist = (data) => get('policies/exist', data)

/* 获取保单预览信息 */
export const previewPolicy = (data) => postFormData('policies/preview', data)

/* 投保 */
export const insurePolicy = (data) => postFormData('policies', data)

// 保存为草稿
export const saveAsDraft = (data) => postFormData('policies/draft', data)

// 修改保单
export const updatePolicy = (id, data) => postFormData(`policies/${id}/modify`, data)

// 下载保单地址
export const buildDownloadHref = (id) => {
  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  return `${baseUrl}policies/${id}/download?token=` + window.localStorage.getItem(tokenKey)
}

// 导出连接
export const buildExportHref = (data) => {
  data.token = window.localStorage.getItem(tokenKey)

  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  return `${baseUrl}policies/export` + '?' + qs.stringify(data)
}

// 导出连接
export const buildDownloadFilesHref = (data) => {
  data.token = window.localStorage.getItem(tokenKey)

  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  return `${baseUrl}policies/download-zip` + '?' + qs.stringify(data)
}

// 退保
export const policySurrender = (id, data) => patch(`policies/${id}/surrender`, data)

// 获取纸质保单申请记录
export const getPolicyPapers = () => get(`policies/papers`)

// 申请纸质保单
export const applyPolicyPaper = (id, data) => post(`policies/${id}/papers`, data)

// 作废暂存单
export const destroyDraft = (id) => patch(`policies/${id}/drop`)

// 未支付的订单.
export const unpaidPayments = (id) => get(`policies/${id}/payments/unpaid`)

// 支付订单
export const getUnpaidPayment = (id, paymentId, data) => get(`policies/${id}/payments/${paymentId}/pay`, data)

// 获取建议.
export const getSuggestions = (companyId, column, value) =>
  get('policies/suggestions', { company_id: companyId, column, value })

// 检查货物名称
export const getGoodsSuggestion = (data) => get('policies/goods-suggestion', data)

// 可理赔的保单
export const fetchClaimableList = (params) => get('policies/claimable', params)

// 投保单确认件
export const downloadConfirmationLetter = (data) => post(`policies/confirmation-letter`, data)

// 下载 CBEC 导入模板
export const downloadCbecImportTemplate = (data) => {
  data.token = window.localStorage.getItem(tokenKey)

  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  return `${baseUrl}policies/cbec-import-template` + '?' + qs.stringify(data)
}

// CBEC 批量投保
export const importCbec = (data) => postFormData('policies/cbec-import', data)

// 暂存单批量提交.
export const batchSubmit = (data) => post('policies/batch-submit', data)

// 暂存单批量作废
export const batchDestroy = (data) => post('policies/batch-destroy', data)

// 下载导入模板
export const downloadImportTemplate = (data) => {
  data.token = window.localStorage.getItem(tokenKey)

  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  return `${baseUrl}policies/import-template` + '?' + qs.stringify(data)
}

// 批量投保
export const importXlsx = (data) => postFormData('policies/import', data)
