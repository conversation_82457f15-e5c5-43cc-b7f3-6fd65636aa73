import { get, post } from '../utils/axios'

// 登录系统
export const login = (data) => post(`auth/login`, data)

// 获取验证码
export const captchaSrc = () => get('captcha')

// 获取获取手机验证码
export const getPhoneNumberVerificationCode = (data) => post(`verification-code`, data)

// 提交注册申请
export const register = (data) => post(`auth/registration`, data)

// 忘记密码验证吗
export const sendForgotPasswordCaptcha = (data) => get(`auth/forgot-password-captcha`, data)

// 重置密码
export const resetPassword = (data) => post(`auth/reset-password`, data)
