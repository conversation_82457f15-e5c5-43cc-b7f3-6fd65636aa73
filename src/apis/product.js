import { get } from '../utils/axios'

// 获取产品.
export const getProducts = (data) => get(`products`, data)

// 获取产品货物类别.
export const getProductGoodsTypes = (id) => get(`products/${id}/goods-types`)

// 获取产品包装方式.
export const getProductPackingMethods = (id) => get(`products/${id}/packing-methods`)

// 获取产品装载方式.
export const getProductLoadingMethods = (id) => get(`products/${id}/loading-methods`)

// 获取产品运输方式.
export const getProductTransportMethods = (id) => get(`products/${id}/transport-methods`)

// 获取已经配置的保险公司.
export const getProductsCompanies = () => get(`products/companies`)
