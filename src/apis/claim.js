import * as axios from '@/utils/axios'

export const fetchSubmittedClaims = (params) => axios.get('/claims', params)

export const fetchCaseDetail = (id) => axios.get(`/claims/${id}`)

export const cancelClaim = (id) => axios.patch(`/claims/${id}/cancellation`)

export const createClaim = (data) => axios.post('/claims', data)

export const uploadAttachment = (id, data) => axios.postFormData(`claims/${id}/attachments`, data)

export const hurryUp = (id) => axios.patch(`claims/${id}/hurry-up`)
