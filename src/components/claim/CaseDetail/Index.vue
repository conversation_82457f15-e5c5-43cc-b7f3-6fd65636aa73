<template>
  <el-dialog :visible.sync="visible" width="850px" destroy-on-close :before-close="handleClose">
    <el-alert
      v-if="data?.hurry_up_at"
      class="m-extra-large-t"
      :closable="false"
      type="success"
      title="催办提醒"
      :description="`您已于 ${data?.hurry_up_at} 申请催办`"
    >
    </el-alert>
    <el-alert
      v-if="data?.apply_cancellation_at"
      class="m-extra-large-t"
      :closable="false"
      type="success"
      title="撤案提醒"
      :description="`您已于 ${data?.apply_cancellation_at} 申请撤案`"
    >
    </el-alert>
    <define-details :data="caseData"></define-details>
    <define-table :cols="cols" :data="data?.attachments || []"></define-table>
    <div class="__pending-upload-box" v-if="fileList.length > 0">
      <p class="__title">待上传</p>
      <ul class="__ua-file-list">
        <li v-for="(f, idx) in fileList" :key="idx" class="__ua-file-item">
          <span>{{ f.categoryName }} - {{ f.name }}</span>
          <el-button type="danger" icon="fas fa-times" size="mini" @click="fileList.splice(idx, 1)"> 删除 </el-button>
        </li>
      </ul>
    </div>
    <template #footer>
      <el-button
        type="primary"
        icon="fas fa-upload"
        @click="showUploadAttachmentDialog = true"
        v-if="![0, 4, 5, 7].includes(data?.status)"
      >
        添加附件
      </el-button>
      <el-button
        v-if="![0, 4, 5, 7].includes(data?.status)"
        type="primary"
        icon="fas fa-check"
        :disabled="fileList.length < 1"
        @click="handleSubmit"
      >
        保存
      </el-button>
      <el-button
        type="danger"
        icon="fas fa-arrow-left"
        v-if="[-1, 1, 2, 3].includes(data?.status) && !data?.apply_cancellation_at"
        @click="handleCancel"
      >
        撤销报案
      </el-button>
      <el-button type="plain" icon="fas fa-times" @click="handleClose">关闭</el-button>
    </template>

    <upload-attachment
      :visible.sync="showUploadAttachmentDialog"
      :category-options="fileCategories"
      @succeed="(fs) => fileList.push(...fs)"
    />
  </el-dialog>
</template>

<script>
import UploadAttachment from '@/components/claim/UploadAttachment'
import { Loading } from 'element-ui'
import * as claimApi from '@/apis/claim'

export default {
  name: 'CaseDetail',
  components: {
    UploadAttachment
  },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      showUploadAttachmentDialog: false,
      fileList: [],
      cols: [
        { label: '一级分类', prop: 'primary_category', width: 150 },
        { label: '二级分类', prop: 'secondary_category', width: 150 },
        { label: '文件名', prop: 'name' },
        { label: '上传时间', prop: 'created_at', width: 150 },
        {
          label: '操作',
          width: 60,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link type="primary" href={scoped.row.href} download={scoped.row.name}>
                    下载
                  </el-link>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  computed: {
    fileCategories() {
      switch (this.data?.policy_type) {
        case 1:
        case 2:
        case 3:
          return [
            {
              value: '运输单证',
              children: [
                { value: '提单' },
                { value: '运输合同/运单' },
                { value: '装车清单' },
                { value: '运输经营许可证' },
                { value: '驾驶证' },
                { value: '行驶证' },
                { value: '车辆道路运输证' },
                { value: '监装报告' },
                { value: '设备交接单EIR' },
                { value: '签收交接单' }
              ]
            },
            {
              value: '货物单证',
              children: [
                { value: '购货合同' },
                { value: '形式发票' },
                { value: '装箱单' },
                { value: '报关单' },
                { value: '其他' }
              ]
            },
            {
              value: '现场照片视频类',
              children: [
                { value: '运输工具（全貌、船舱、车厢的密封情况、受损状况、船名、车牌号）' },
                { value: '集装箱（全貌、集装箱号、内外状况、受损细节）' },
                { value: '标的货物（全貌、包装、运输标志、防震动、防倾、温控标签、绑扎和积载、铭牌、受损细节）' },
                { value: '发货现场（装箱情况、标的物状态）' },
                { value: '其他' }
              ]
            },
            {
              value: '定责类',
              children: [
                { value: '交通责任认定书' },
                { value: '火灾证明' },
                { value: '破损单' },
                { value: '理货报告' },
                { value: '承运人出具的事故说明' },
                { value: '其他' }
              ]
            },
            {
              value: '定损类',
              children: [{ value: '报损清单' }, { value: '核验报告' }, { value: '鉴定报告' }, { value: '其他' }]
            }
          ]
        // 其他
        case 4:
          return [
            {
              value: '定责类',
              children: [{ value: '火灾证明' }, { value: '交通事故责任认定书' }, { value: '其他' }]
            },
            {
              value: '定损类',
              children: [
                { value: '报损明细' },
                { value: '损失证明' },
                { value: '检查报告' },
                { value: '维修报价' },
                { value: '其他' }
              ]
            }
          ]
        // 雇主
        case 5:
          return [
            {
              value: '定责类',
              children: [
                { value: '受伤员工劳动合同（注明工种）' },
                { value: '病历、检验报告、出院小结' },
                { value: '工伤认定书、伤残鉴定书、死亡证明' },
                { value: '交通责任认定书、交通事故调解书' },
                { value: '事故情况说明' },
                { value: '其他' }
              ]
            },
            {
              value: '定损类',
              children: [{ value: '医疗费用明细' }, { value: '医疗费用发票/收据' }, { value: '其他' }]
            },
            {
              value: '现场照片视频类',
              children: [{ value: '事故现场' }, { value: '伤者情况' }, { value: '其他' }]
            }
          ]
        default:
          return []
      }
    },
    caseData() {
      return {
        title: '理赔详情',
        data: [
          {
            title: `案件信息(理赔当前进度：${this.data?.status_text?.slice(
              this.data?.status_text?.lastIndexOf('>') + 1
            )})`,
            groups: [
              { label: '险种', value: this.data?.policy_type_text },
              { label: '保单号', value: this.data?.policy_no },
              { label: '理赔编号', value: this.data?.case_no },
              { label: '保司案件号', value: this.data?.external_case_no || '-' },
              { label: '出险原因', value: this.data?.loss_reason },
              { label: '出险时间', value: this.data?.date_of_loss },
              { label: '出险地点', value: `${this.data?.loss_location} ${this.data?.loss_address || ''}` },
              { label: '损失类型', value: this.data?.loss_category_text },
              { label: '损失金额', value: this.data?.loss_amount },
              { label: '损失金额币种', value: this.data?.loss_amount_currency },
              { label: '损失经过', value: this.data?.loss_detail, row: true }
            ]
          },
          {
            title: `报案人`,
            groups: [
              { label: '报案人', value: this.data?.claimant },
              { label: '报案人邮箱', value: this.data?.claimant_email },
              { label: '报案人电话', value: this.data?.claimant_phone_number },
              { label: '报案时间', value: this.data?.created_at }
            ]
          },
          {
            title: `附件`,
            groups: []
          }
        ]
      }
    }
  },
  methods: {
    async handleSubmit() {
      const loading = Loading.service()
      try {
        for (const f of this.fileList) {
          await claimApi.uploadAttachment(this.data.id, {
            primary_category: f.category.primary,
            secondary_category: f.category.secondary,
            name: f.name,
            file: f.raw
          })
        }

        this.$message.success('上传附件成功')
      } catch {
        this.$message.error('上传附件失败')
      }
      loading.close()
      this.$emit('updated')
      this.handleClose()
    },
    async handleCancel() {
      try {
        await this.$confirm('确定撤销报案吗')
        const loading = Loading.service()
        try {
          await claimApi.cancelClaim(this.data.id)
          this.$message.success('撤销报案成功')
          this.$emit('updated')
          this.handleClose()
        } catch (e) {
          // this.$message.error('撤销报案失败')
        }
        loading.close()
      } catch (e) {
        // this.handleClose()
      }
    },
    handleClose() {
      this.$emit('update:visible', false)

      this.fileList = []
    }
  }
}
</script>

<style>
.el-dialog__body {
  padding-top: 0;
}
</style>

<style scoped>
.__pending-upload-box {
  width: 100%;
  .__title {
    font-weight: bold;
  }
  .__ua-file-list {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    margin: 0;
    padding: 0;
    list-style: none;

    .__ua-file-item {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:not(:last-child) {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
