<template>
  <el-dialog
    :visible.sync="visible"
    title="上传附件"
    :before-close="handleClose"
    destroy-on-close
    width="600px"
    append-to-body
  >
    <div class="w-100">
      <div class="__ua-choose-box">
        <el-cascader
          class="w-100"
          v-model="fileCategory"
          :options="categoryOptions"
          filterable
          placeholder="请选择文件类别"
          popper-class="__cascader-full-height"
          :props="{
            expandTrigger: 'hover',
            filterable: true,
            label: 'value'
          }"
        ></el-cascader>
        <el-upload
          action=""
          :auto-upload="false"
          :on-change="handleChange"
          :show-file-list="false"
          multiple
          :file-list="fileList"
          accept=".jpeg, .png, .pdf, .jpg, .doc, .docx, .xlsx, .xls, .zip, .rar, .mp4, .mp3, .wav, .txt, .ppt, .pptx, .avi, .mov, .mkv"
        >
          <el-button
            type="primary"
            icon="fas fa-plus"
            class="m-mini-l"
            slot="trigger"
            :disabled="fileCategory.length == 0"
          >
            添加附件
          </el-button>
        </el-upload>
      </div>
      <div slot="tip" class="el-upload__tip">单个文件大小不超过 {{ maxSize }}MB</div>
    </div>
    <div class="w-100 m-mini-t">
      <ul class="__ua-file-list">
        <li v-for="(f, idx) in fileList" :key="idx" class="__ua-file-item">
          <span>{{ f.categoryName }} - {{ f.name }}</span>
          <el-button type="plain" icon="fas fa-times" size="mini" @click="handleRemove(idx)">删除</el-button>
        </li>
      </ul>
    </div>
    <div slot="footer" class="dialog-footer m-mini-t">
      <div class="d-flex __ua-actions">
        <el-button
          type="primary"
          @click="handleUpload"
          :loading="uploading"
          :disabled="fileList.length == 0"
          icon="fas fa-upload"
        >
          {{ caseId ? '全部上传' : '确认添加' }}
        </el-button>
        <el-button type="danger" @click="handleRemoveAll" :disabled="fileList.length == 0" icon="fas fa-trash">
          全部删除
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import * as claimApi from '@/apis/claim'

export default {
  props: {
    caseId: {
      type: Number,
      required: false
    },
    visible: {
      type: Boolean,
      default: false
    },
    categoryOptions: {
      type: Array,
      default: () => []
    },
    maxSize: {
      type: Number,
      default: 8
    }
  },
  data() {
    return {
      uploading: false,
      fileCategory: [],
      fileList: []
    }
  },
  methods: {
    handleRemove(fileIndex) {
      this.fileList.splice(fileIndex, 1)
    },
    handleChange(file) {
      if (file.size / 1024 / 1024 > this.maxSize) {
        this.$message.error(`文件大小不能超过 ${this.maxSize} MB`)
        return
      }

      if (this.fileCategory.length === 0) {
        this.$message.error('请先选择文件类别')
        return
      }

      const fileType = this.fileCategory.join('/')
      file.category = {
        primary: this.fileCategory[0],
        secondary: this.fileCategory[1]
      }
      file.categoryName = fileType
      this.fileList.push(file)
    },
    async handleRemoveAll() {
      try {
        await this.$confirm('确认删除所有附件吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.fileList = []
        this.fileCategory = []
      } catch (error) {
        //
      }
    },
    async handleUpload() {
      if (this.fileList.length === 0) {
        this.$message.error('请先添加附件')
        return
      }

      if (this.caseId) {
        this.uploading = true
        for (const f in this.fileList) {
          try {
            await claimApi.uploadAttachment(this.caseId, {
              primary_category: f.category.primary,
              secondary_category: f.category.secondary,
              name: f.name,
              file: f.raw
            })
          } catch (e) {
            this.uploading = false
          }
        }

        this.uploading = false
        this.$message({
          type: 'success',
          message: '上传成功'
        })
      }

      this.$emit('succeed', this.fileList)
      this.handleClose()
    },
    handleClose() {
      this.fileList = []
      this.fileCategory = []
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="scss">
.__cascader-full-height .el-cascader-menu__wrap {
  height: 360px !important;
}
</style>

<style scoped lang="scss">
.__ua-choose-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.__ua-file-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  list-style: none;

  .__ua-file-item {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:not(:last-child) {
      margin-bottom: 10px;
    }
  }
}

.__ua-actions {
  justify-content: center;
}
</style>
