<template>
  <el-dialog :visible.sync="visible" title="提交报案" width="850px" destroy-on-close :before-close="handleClose">
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px">
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item prop="loss_reason" label="出险原因">
            <el-cascader
              class="w-100"
              v-model="form.loss_reason"
              :options="reasons"
              :props="{
                expandTrigger: 'hover',
                filterable: true,
                label: 'value',
                value: 'value',
                children: 'options'
              }"
            ></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="date_of_loss" label="出险时间">
            <el-date-picker
              v-model="form.date_of_loss"
              type="datetime"
              placeholder="选择日期"
              format="yyyy-MM-dd HH:mm:ss"
              :picker-options="pickerOptions"
              style="width: 100% !important"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item prop="loss_location" label="出险地">
            <div class="d-flex">
              <el-cascader
                class="w-100"
                v-model="form.loss_location"
                :options="locations"
                filterable
                :props="{
                  expandTrigger: 'hover',
                  filterable: true,
                  value: 'value',
                  label: 'value',
                  children: isDomestic ? 'city' : 'children'
                }"
              ></el-cascader>
              <el-input
                class="m-mini-l"
                type="textarea"
                rows="1"
                cols="1"
                autosize
                v-model="form.loss_address"
                placeholder="出险地详细地址"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="loss_category" label="损失类型">
            <el-select class="w-100" v-model="form.loss_category" placeholder="损失类型">
              <el-option
                v-for="item in lossCategories"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item prop="loss_amount">
            <template #label>
              <span>报损金额</span>
              <small class="text-danger">（损失情况不明时请填写 1）</small>
            </template>
            <el-input type="number" clearable v-model="form.loss_amount" placeholder="请输入报损金额"> </el-input>
            <span>{{ form.loss_amount | chineseAmount }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="loss_amount_currency_id" label="币种">
            <el-select v-model="form.loss_amount_currency_id" placeholder="请选择币种" class="w-100">
              <el-option
                v-for="currency in currencies"
                :value="currency.id"
                :key="currency.value"
                :label="currency.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="48">
        <el-col :span="24">
          <el-form-item prop="loss_detail" label="事故经过">
            <el-input
              type="textarea"
              minlength="8"
              maxlength="2000"
              show-word-limit
              v-model="form.loss_detail"
              rows="5"
              :placeholder="lossDetailPlaceholder"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="48">
        <el-col :span="8">
          <el-form-item prop="claimant" label="报案人">
            <el-input v-model="form.claimant" placeholder="报案人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="claimant_email" label="报案人邮箱">
            <el-input v-model="form.claimant_email" placeholder="报案人邮箱"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="claimant_phone_number" label="报案人电话">
            <el-input v-model="form.claimant_phone_number" placeholder="报案人电话"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="48" v-if="fileList.length > 0">
        <el-col :span="24">
          <el-form-item label="附件">
            <ul class="__ua-file-list">
              <li v-for="(f, idx) in fileList" :key="idx" class="__ua-file-item">
                <span>{{ f.categoryName }} - {{ f.name }}</span>
                <el-button type="danger" icon="fas fa-times" size="mini" @click="fileList.splice(idx, 1)">
                  删除
                </el-button>
              </li>
            </ul>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="plain" icon="fas fa-times" @click="handleClose">取消报案</el-button>
      <el-button type="primary" icon="fas fa-upload" @click="showUploadAttachmentDialog = true">添加附件</el-button>
      <el-button type="primary" icon="fas fa-check" @click="handleSubmit">提交报案</el-button>
    </template>

    <upload-attachment
      :visible.sync="showUploadAttachmentDialog"
      :category-options="fileCategories"
      @succeed="(fs) => fileList.push(...fs)"
    />
  </el-dialog>
</template>

<script>
import chinaAreadata from '@/utils/areadata.json'
import overseaAreadata from '@/utils/regions'
import { digitUppercase } from '@/utils'
import * as currencyApi from '@/apis/currency'
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'
import UploadAttachment from '@/components/claim/UploadAttachment'

export default {
  components: {
    UploadAttachment
  },
  name: 'SubmissionForm',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    policy: {
      type: Object,
      required: true
    }
  },
  filters: {
    chineseAmount(value) {
      return digitUppercase(value)
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    isDomestic() {
      return this.policy.type !== 2
    },
    locations() {
      if (this.isDomestic) {
        return chinaAreadata
      }

      return [
        {
          value: '中国大陆',
          children: chinaAreadata.map((item) => {
            return {
              value: item.value,
              children: item.city.map((city) => {
                return {
                  value: city.value
                }
              })
            }
          })
        },
        {
          value: '境外地区',
          children: Object.keys(this.overseaAreadata).map((k) => {
            return { value: k }
          })
        }
      ]
    },
    lossCategories() {
      switch (this.policy?.type) {
        case 1:
        case 2:
        case 3:
          return [{ label: '物损', value: 2 }]
        case 5:
          return [{ label: '人伤', value: 1 }]
        default:
          return [
            { label: '人伤', value: 1 },
            { label: '物损', value: 2 },
            { label: '人伤和物损', value: 3 }
          ]
      }
    },
    currencies() {
      if (this.isDomestic) {
        return [{ id: -1, label: '人民币', value: 'CNY' }]
      }

      return this.rawCurrencies
    },
    fileCategories() {
      switch (this.policy?.type) {
        case 1:
        case 2:
        case 3:
          return [
            {
              value: '运输单证',
              children: [
                { value: '提单' },
                { value: '运输合同/运单' },
                { value: '装车清单' },
                { value: '运输经营许可证' },
                { value: '驾驶证' },
                { value: '行驶证' },
                { value: '车辆道路运输证' },
                { value: '监装报告' },
                { value: '设备交接单EIR' },
                { value: '签收交接单' }
              ]
            },
            {
              value: '货物单证',
              children: [
                { value: '购货合同' },
                { value: '形式发票' },
                { value: '装箱单' },
                { value: '报关单' },
                { value: '其他' }
              ]
            },
            {
              value: '现场照片视频类',
              children: [
                { value: '运输工具（全貌、船舱、车厢的密封情况、受损状况、船名、车牌号）' },
                { value: '集装箱（全貌、集装箱号、内外状况、受损细节）' },
                { value: '标的货物（全貌、包装、运输标志、防震动、防倾、温控标签、绑扎和积载、铭牌、受损细节）' },
                { value: '发货现场（装箱情况、标的物状态）' },
                { value: '其他' }
              ]
            },
            {
              value: '定责类',
              children: [
                { value: '交通责任认定书' },
                { value: '火灾证明' },
                { value: '破损单' },
                { value: '理货报告' },
                { value: '承运人出具的事故说明' },
                { value: '其他' }
              ]
            },
            {
              value: '定损类',
              children: [{ value: '报损清单' }, { value: '核验报告' }, { value: '鉴定报告' }, { value: '其他' }]
            }
          ]
        // 其他
        case 4:
          return [
            {
              value: '定责类',
              children: [{ value: '火灾证明' }, { value: '交通事故责任认定书' }, { value: '其他' }]
            },
            {
              value: '定损类',
              children: [
                { value: '报损明细' },
                { value: '损失证明' },
                { value: '检查报告' },
                { value: '维修报价' },
                { value: '其他' }
              ]
            }
          ]
        // 雇主
        case 5:
          return [
            {
              value: '定责类',
              children: [
                { value: '受伤员工劳动合同（注明工种）' },
                { value: '病历、检验报告、出院小结' },
                { value: '工伤认定书、伤残鉴定书、死亡证明' },
                { value: '交通责任认定书、交通事故调解书' },
                { value: '事故情况说明' },
                { value: '其他' }
              ]
            },
            {
              value: '定损类',
              children: [{ value: '医疗费用明细' }, { value: '医疗费用发票/收据' }, { value: '其他' }]
            },
            {
              value: '现场照片视频类',
              children: [{ value: '事故现场' }, { value: '伤者情况' }, { value: '其他' }]
            }
          ]
        default:
          return []
      }
    },
    reasons() {
      // 货运险
      switch (this.policy?.type) {
        case 1:
        case 2:
        case 3:
          return [
            {
              value: '自然灾害',
              options: [
                { value: '恶劣气候' },
                { value: '雷电' },
                { value: '海啸' },
                { value: '地震' },
                { value: '洪水' },
                { value: '暴风' },
                { value: '暴雨' },
                { value: '其他' }
              ]
            },
            {
              value: '意外事故',
              options: [
                { value: '运输工具意外' },
                { value: '火灾' },
                { value: '爆炸' },
                { value: '装卸货意外' },
                { value: '震动碰撞挤压' },
                { value: '水湿' },
                { value: '其他' }
              ]
            },
            {
              value: '共同海损',
              options: [{ value: '共同海损牺牲分摊' }, { value: '救助费用分摊' }, { value: '其他' }]
            },
            {
              value: '货物丢失',
              options: [{ value: '货物丢失' }]
            }
          ]
        case 4:
          return [
            {
              value: '自然灾害',
              options: [
                { value: '恶劣气候' },
                { value: '雷电' },
                { value: '海啸' },
                { value: '地震' },
                { value: '洪水' },
                { value: '暴风' },
                { value: '暴雨' },
                { value: '其他' }
              ]
            },
            {
              value: '意外事故',
              options: [{ value: '交通事故' }, { value: '火灾' }, { value: '爆炸' }, { value: '其他' }]
            }
          ]
        case 5:
          return [
            {
              value: '受伤',
              options: [
                { value: '因工作原因受到事故伤害' },
                { value: '因履行工作职责受到暴力等意外伤害' },
                { value: '工作原因受到伤害或者发生事故下落不明' },
                { value: '上下班途中，受到非本人主要责任的交通事故伤害' },
                { value: '其他原因受伤' }
              ]
            },
            {
              value: '死亡',
              options: [
                { value: '工作期间突发疾病死亡' },
                { value: '在48小时之内经抢救无效死亡' },
                { value: '其他原因死亡' }
              ]
            },
            {
              value: '其他',
              options: [{ value: '其他' }]
            }
          ]
        default:
          return []
      }
    },
    lossDetailPlaceholder() {
      if ([1, 2, 3].includes(this.policy.type)) {
        return '例：XYZ公司从上海出口标的货物空气开关3箱到英国伦敦ABC客户，委托DDFF承运，运单号为DDFF111111，运输方式为航空运输。2023年1月1日，在伦敦卸离飞机后陆运到买家仓库途中发生交通事故，经初步清点，事故造成10只空气开关受损。'
      }

      if (this.policy.type === 5) {
        return '例：2023年9月1日10：00时左右，上海某机械有限公司员工张某在车床加工零件作业时，车床在无指令的状况下突然启动，导致张某右手受伤，事发后立即送往上海第六人民医院治疗，经诊断为手指骨折，在手术治疗中。'
      }

      return '请填写事故经过'
    }
  },
  data() {
    return {
      chinaAreadata,
      overseaAreadata,
      rawCurrencies: [],
      fileList: [],
      showUploadAttachmentDialog: false,
      pickerOptions: {
        disabledDate(t) {
          return t.getTime() > Date.now()
        }
      },
      form: {
        loss_reason: '',
        date_of_loss: '',
        loss_location: '',
        loss_address: '',
        loss_category: '',
        loss_amount: '',
        loss_amount_currency_id: '',
        loss_detail: '',
        claimant: '',
        claimant_email: '',
        claimant_phone_number: ''
      },
      rules: {
        loss_reason: [{ required: true, message: '请选择出险原因', trigger: 'change' }],
        date_of_loss: [{ required: true, message: '请选择出险时间', trigger: 'change' }],
        loss_location: [{ required: true, message: '请选择出险地点', trigger: 'change' }],
        loss_category: [{ required: true, message: '请选择损失类别', trigger: 'change' }],
        loss_amount: [{ required: true, message: '请输入报损金额', trigger: 'blur' }],
        loss_amount_currency_id: [{ required: true, message: '请选择币种', trigger: 'change' }],
        loss_detail: [
          { required: true, message: '请输入事故经过', trigger: 'blur' },
          {
            min: 8,
            max: 2000,
            message: '长度在 8 到 2000 个字符',
            trigger: 'blur'
          }
        ],
        claimant: [{ required: true, message: '请输入报案人', trigger: 'blur' }],
        claimant_email: [{ required: true, message: '请输入报案人邮箱', trigger: 'blur' }],
        claimant_phone_number: [{ required: true, message: '请输入报案人电话', trigger: 'blur' }]
      }
    }
  },
  watch: {
    policy() {
      if (!this.isDomestic) {
        this.fetchCurrencies()
      }
    },
    'policy.type'(policyType) {
      // 雇主默认人伤
      if (policyType === 5) {
        this.form.loss_category = 1
      } else if ([1, 2, 3, 7].includes(policyType)) {
        this.form.loss_category = 2
      }
    },
    visible(value) {
      if (value) {
        // 暂时去掉默认填写邮箱信息等
        // this.form.claimant = this.user.name
        // this.form.claimant_email = this.user.email
        // this.form.claimant_phone_number = this.user.phone_number
      }
    }
  },
  methods: {
    async fetchCurrencies() {
      const currencies = await currencyApi.getCurrencies()

      this.rawCurrencies = currencies.data.map((item) => {
        return {
          id: item.id,
          label: item.name,
          value: item.code
        }
      })
    },
    handleClose() {
      this.$emit('update:visible', false)

      this.form = {
        loss_reason: '',
        date_of_loss: '',
        loss_location: '',
        loss_address: '',
        loss_category: '',
        loss_amount: '',
        loss_amount_currency_id: '',
        loss_detail: '',
        claimant: '',
        claimant_email: '',
        claimant_phone_number: ''
      }

      this.fileList = []
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const data = Object.assign({}, this.form)
          data.date_of_loss = dayjs(data.date_of_loss).format('YYYY-MM-DD HH:mm:ss')
          data.loss_reason = data.loss_reason.join('/')
          data.loss_location = data.loss_location.join('/')

          this.$emit('submit', data, this.fileList)
          this.handleClose()
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.__ua-file-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  list-style: none;

  .__ua-file-item {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:not(:last-child) {
      margin-bottom: 10px;
    }
  }
}

.__ua-actions {
  justify-content: center;
}
</style>
