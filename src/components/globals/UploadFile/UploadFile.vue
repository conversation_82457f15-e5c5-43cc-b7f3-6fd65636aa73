<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-10 15:26:43
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-10 16:06:11
-->
<template>
  <el-upload
    class="upload-component d-flex o-hidden"
    ref="upload"
    :multiple="false"
    action=""
    :on-preview="handleUploadPreview"
    :file-list="fileList"
    :show-file-list="false"
    :on-change="handleUploadChange"
    :auto-upload="false"
    :accept="accept"
  >
    <el-button slot="trigger" icon="el-icon-upload" type="primary" :disabled="disabled">{{ buttonText }}</el-button>
    <div class="d-flex o-hidden" style="align-items: center" v-if="fileList.length > 0">
      <span class="flex-fill m-mini-x text-ellipsis">{{ fileList[0].name }}</span>
      <span class="text-info size m-mini-r">{{ convertSize(fileList[0].size) }}</span>
      <!-- <el-link class="text-blue m-mini-r" @click="handleUploadPreview">查看</el-link> -->
      <el-link class="text-primary" @click="handleUploadRemove">删除</el-link>
    </div>
  </el-upload>
</template>

<script>
import { convertSize } from '@/utils'
export default {
  name: 'UploadFile',
  props: {
    buttonText: {
      type: String,
      default: '点击上传'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    accept: {
      type: String,
      default: '.jpeg,.png,.pdf,.jpg,.doc,.docx,.xlsx,.xls'
    },
    limitSize: {
      type: Number,
      default: 4
    }
  },
  data() {
    return {
      fileList: [],
      convertSize
    }
  },
  methods: {
    handleUploadRemove() {
      this.fileList = []
      this.$emit('input', null)
      this.$forceUpdate()
    },
    handleUploadPreview() {
      this.$message.success(`预览文件: ${this.fileList[0].name}`)
    },
    handleUploadChange(file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1)
      }
      this.fileList = fileList
      let isValid = true
      let limitSize = Number(this.limitSize)
      if (!isNaN(limitSize) && limitSize > 0) {
        limitSize = limitSize * 1024 * 1024
        let fileSize = fileList[0].size
        if (fileSize > limitSize) {
          this.$message.error(`文件超过上传大小(${this.limitSize}MB)限制!`)
          this.fileList = []
          isValid = false
        }
      }
      if (isValid) {
        this.$emit('input', file.raw)
      }
      this.$forceUpdate()
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-component {
  /deep/ .el-link,
  /deep/ .size {
    min-width: max-content;
  }
}
</style>
