<!--
 * @Descripttion: 
 * @Author: Mr. zhu
 * @Date: 2020-10-19 12:30:41
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2020-10-19 12:36:02
-->

<template>
  <el-container class="simple-container">
    <el-header class="simple-container__header" height="36px">
      <div class="title">
        <label class="title-text">{{ title }}</label>
        <span class="title-badge"></span>
      </div>
      <div class="actions">
        <slot name="actions"></slot>
      </div>
    </el-header>
    <el-main class="simple-container__mian">
      <slot></slot>
    </el-main>
  </el-container>
</template>

<script>
export default {
  name: 'SimpleContainer',
  props: {
    title: {
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.simple-container {
  padding: 24px;
  min-height: 100vh;
  #{&}__header {
    margin-bottom: 24px;
    padding: 0 !important;
    display: flex;
    justify-content: space-between;
    .title {
      display: flex;
      flex-direction: column;
      .title-text {
        color: #212529;
        font-size: 14px;
      }
      .title-badge {
        display: block;
        width: 25px;
        height: 5px;
        border-radius: 3px;
        background: #177eec;
        margin-top: 10px;
      }
    }
    .actions {
      display: flex;
    }
  }
  #{&}__mian {
    padding: 0 !important;
  }
}
</style>
