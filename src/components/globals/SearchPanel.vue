<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-19 12:30:41
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2020-10-19 12:35:58
-->
<script>
import { isObj, isStr } from '../../utils'
const customItemTypes = [
  'input',
  'textarea',
  'select',
  'date',
  'dates',
  'daterange',
  'time',
  'datetime',
  'year',
  'week',
  'month'
]
export default {
  name: 'SearchPanel',
  props: {
    // 布局大小
    size: {
      type: String,
      default: 'medium',
      validator: (val) => ['mini', 'small', 'medium'].indexOf(val) !== -1
    },
    /**
     * 自定义 搜索框
     * 支持 input|select|date|dates|time|datetime|year|week|month
     * 数据格式:
     * [{
     *   type: String('input|select|date|dates|time|datetime|year|week|month') 类型,
     *   hintText:String 提示文本,
     *   valKey:String 绑定值字段，最终获取的值对应的key
     * }]
     * Example
            <SearchPanel
                :custom="[{
                    type: 'input',
                    valKey: 'customInput',
                    hintText: '自定义输入框'
                },{
                    type: 'select',
                    valKey: 'customSelect',
                    hintText: '下拉框',
                    options: [
                        { label: '选项1', value: 1},
                        { label: '选项2', value: 2},
                    ]
                },{
                    type: 'date',
                    valKey: 'customDate',
                    hintText: '自定义日期'
                },{
                    type: 'datetime',
                    valKey: 'customDateTime',
                    hintText: '自定义时间日期'
                },{
                    type: 'time',
                    valKey: 'customTime',
                    hintText: '自定义时间'
                },{
                    type: 'dates',
                    valKey: 'customDates',
                    hintText: '多个时间'
                },{
                    type: 'year',
                    valKey: 'customYear',
                    hintText: '选择年'
                },{
                    type: 'month',
                    valKey: 'customMonth',
                    hintText: '选择月'
                },{
                    type: 'week',
                    valKey: 'customWeek',
                    hintText: '选择周'
                }]"
            />
         */
    custom: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      searchData: {}
    }
  },
  computed: {
    getCustomLayout() {
      // 校验数据
      this.validationCustomData(this.custom)
      // 绑定数据
      this.bindCustomData(this.custom)

      // 一行四个
      return Array(Math.ceil(this.custom.length / 4))
        .fill()
        .map((_, i) => this.custom.slice(i * 4, i * 4 + 4))
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler() {
        const data = Object.assign({}, this.searchData)

        this.$emit('change', data)
      }
    }
  },
  methods: {
    handlerFunc(command) {
      // 如果是重置
      // 清空每一项
      if (command === 'reset') {
        for (let key in this.searchData) {
          this.searchData[key] = ''
        }
      } else {
        let data = Object.assign({}, this.searchData)
        this.$emit('command', command, data)
      }
    },
    onSelectChange(node) {
      if (node.onChangeReset !== undefined) {
        this.$set(this.searchData, node.onChangeReset, '')
      }
    },
    /*
     * build 方法集 S
     * zhuzhaofeng
     */
    buildFormItemInput(key, hintText) {
      return (
        <el-form-item>
          <el-input clearable vModel={this.searchData[key]} placeholder={hintText} />
        </el-form-item>
      )
    },
    buildFormItemTextarea(key, rows, hintText, autosize = true) {
      return (
        <el-form-item>
          <el-input
            type="textarea"
            rows={rows}
            autosize={autosize}
            clearable
            vModel={this.searchData[key]}
            placeholder={hintText}
            style="min-height:35px !important"
          />
        </el-form-item>
      )
    },
    buildFormItemDate(key, hintText, type = 'date', format = 'yyyy-MM-dd') {
      return (
        <el-form-item>
          <el-date-picker
            format={format}
            value-format={format}
            vModel={this.searchData[key]}
            type={type}
            placeholder={hintText}
          />
        </el-form-item>
      )
    },
    buildFormItemDateRange(key, hintText) {
      return (
        <el-form-item>
          <el-date-picker
            vModel={this.searchData[key]}
            type="daterange"
            align="right"
            unlink-panels
            range-separator="至"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder={hintText + '起始日期'}
            end-placeholder={hintText + '结束日期'}
          ></el-date-picker>
        </el-form-item>
      )
    },
    // build 方法集 E
    /**
     * 校验自定义数据 数据类型
     */
    validationCustomData(arr) {
      // 遍历custom
      arr.map((item) => {
        // 1. custom 每一项必须是一个对象
        // 使用Object.prototype.toString.call()检测
        if (!isObj(item)) throw new Error('Custom item should be an <Object>')
        // 2. custom Item 必须存在 type 和 valKey 这两个参数
        // type指定 需要渲染的类型 valKey 指定返回数据的key (type 可以默认 input)
        let keys = Object.keys(item)
        if (keys.indexOf('type') < 0 || keys.indexOf('valKey') < 0)
          throw new Error("Custom item must have 'type' and 'valkey'")
        // 3. 上述条件成立后，检测每一项的数据类型
        // 只检测有用参数
        // 防止非法数据绑定
        // 第二步已经检测过type 和 valKey 是否存在了
        if (!isStr(item.type) || item.type.length < 1) throw new Error(`"Custom > item > type" should be an <String>`)
        if (customItemTypes.indexOf(item.type) < 0)
          throw new Error(`"Custom > item > type" type must be ${customItemTypes.join('|')}`)
        // 当类型为select时，options属性必须存在
        if (item.type === 'select') {
          if (!item.options) throw new Error('The options property must exist when the type is select')
        }

        if (!isStr(item.valKey) || item.valKey.length < 1)
          throw new Error(`"Custom > item > valKey" should be an <String>`)

        if (keys.indexOf('hintText') > -1 && !isStr(item.hintText))
          throw new Error(`"Custom > item > hintText" should be an <String>`)
      })
    },
    /**
     * 绑定自定义数据到 searchData
     */
    bindCustomData(val) {
      // 使用 $set 或者 Vue.set
      // Please Look: https://cn.vuejs.org/v2/guide/reactivity.html#对于对象
      val.map((item) =>
        this.$set(
          this.searchData,
          item.valKey,
          this.searchData[item.valKey] !== undefined || this.searchData[item.valKey] ? this.searchData[item.valKey] : ''
        )
      )
    },
    readerCustomItem(item) {
      switch (item.type) {
        case 'input':
          return this.buildFormItemInput(item.valKey, item.hintText || '')
        case 'textarea':
          return this.buildFormItemTextarea(item.valKey, item.rows || 1, item.hintText || '', item.autosize || true)
        case 'date':
          return this.buildFormItemDate(item.valKey, item.hintText || '', 'yyyy-MM-dd')
        case 'daterange':
          return this.buildFormItemDateRange(item.valKey, item.hintText || '')
        case 'time':
          return (
            <el-time-picker
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              size={this.size}
              vModel={this.searchData[item.valKey]}
              placeholder={item.hintText}
            />
          )
        case 'dates':
          return this.buildFormItemDate(item.valKey, item.hintText || '', item.type)
        case 'datetime':
          return this.buildFormItemDate(item.valKey, item.hintText || '', item.type, 'yyyy-MM-dd HH:mm:ss')
        case 'year':
          return this.buildFormItemDate(item.valKey, item.hintText || '', item.type, 'yyyy')
        case 'month':
          return this.buildFormItemDate(item.valKey, item.hintText || '', item.type, 'MM')
        case 'week':
          return (
            <el-form-item>
              <el-date-picker
                clearable
                format="yyyy 第 WW 周"
                value-format="yyyy-WW"
                vModel={this.searchData[item.valKey]}
                type={item.type}
                placeholder={item.hintText}
              />
            </el-form-item>
          )
        case 'select':
          return (
            <el-select
              onChange={(value) => this.onSelectChange(item, value)}
              clearable
              filterable
              size={this.size}
              vModel={this.searchData[item.valKey]}
              placeholder={item.hintText}
            >
              {item.options.map((option, index) => (
                <el-option key={index} label={option.label} value={option.value} />
              ))}
            </el-select>
          )
        default:
          return <i></i>
      }
    }
  },
  render(h) {
    return (
      <el-form
        inline={true}
        label-width="0"
        vModel={this.searchData}
        ref="searchForm"
        class="search_form"
        size={this.size}
      >
        <el-card shadow="never">
          <template slot="header">
            {this.getCustomLayout.map((items, index) => {
              return (
                <el-row gutter={15}>
                  {items.map((item) => {
                    return <el-col span={6}>{this.readerCustomItem(item, index + 'custom')}</el-col>
                  })}
                </el-row>
              )
            })}
          </template>
          <el-row gutter={15}>
            <el-col>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-search"
                  vOn:click={() => this.handlerFunc('submit')}
                >
                  搜索
                </el-button>
                <el-button size="small" icon="el-icon-refresh" vOn:click={() => this.handlerFunc('reset')}>
                  清空
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        {/** 渲染查询重置按钮  */}
      </el-form>
    )
  }
}
</script>

<style lang="scss" scoped>
.search_form {
  .el-col {
    margin: 5px 0;
    .el-form-item {
      margin: 0;
      display: flex;
      width: 100%;
      /deep/ .el-form-item__content {
        flex: 1;
        overflow: hidden;
      }
    }
  }
  .el-select,
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 100%;
  }
}
</style>
