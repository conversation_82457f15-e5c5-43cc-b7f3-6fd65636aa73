<!--
 * @Descripttion: 保单详情
 * @Author: Mr. zhu
 * @Date: 2020-10-22 16:06:44
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-08 14:43:19
-->
<template>
  <el-card shadow="never" v-if="data && data.title" class="define-details">
    <el-form label-position="left">
      <div v-for="(row, index) in data.data" :key="`row_${index}`" class="group">
        <h6 class="clearfix title p-mini-l">
          <label>
            <span v-html="row.title"></span>
          </label>
          <i class="flex-fill"></i>
          <template v-if="row.actions">
            <el-button
              @click="handleRowTitleClick(row)"
              v-for="(action, _i) in row.actions"
              :key="`action_${_i}`"
              :icon="action.icon"
            >
              {{ action.text }}
            </el-button>
          </template>
        </h6>
        <section class="data-content">
          <div v-for="(rows, i) in marshalRows(row.groups)" :key="`row_${index}_${i}`" class="data-row">
            <div
              v-for="(row, key) in rows"
              :key="`sub_row_${key}`"
              class="data-col"
              :class="{
                'data-full-col': rows.length === 1,
                hidden: rows.filter((r) => !r?.hide).length === 0
              }"
            >
              <template v-if="!(row?.hide || false)">
                <div class="data-label-text" :style="`width: ${data.label_width || '150px'}`">{{ row.label }}</div>
                <div class="data-label-content">
                  <template v-if="row.isLink">
                    <!-- 如果是链接 -->
                    <el-link type="primary" v-if="row.target && row.target === '_blank'" :href="row.to" target="_blank">
                      <!-- 如果是链接 并且是 target="_blank" -->
                      {{ row.value }}
                    </el-link>
                    <el-link type="primary" v-else :href="row.to">
                      {{ row.value }}
                    </el-link>
                  </template>
                  <template v-else>
                    <div v-html="row.value"></div>
                  </template>
                </div>
              </template>
            </div>
          </div>
        </section>
      </div>
    </el-form>
  </el-card>
  <center v-else>
    <el-link :underline="false" type="danger">详情数据格式错误</el-link>
  </center>
</template>

<script>
export default {
  name: 'DefineDetails',
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    handleRowTitleClick(row) {
      const _temp = JSON.parse(JSON.stringify(row))
      const _dataTemp = JSON.parse(JSON.stringify(this.data))
      this.$emit('row-title', _temp, _dataTemp)
    },
    marshalRows(rows) {
      const dstRows = []
      for (let index = 0; index < rows.length; index++) {
        const row = rows[index]
        if (row.row) {
          dstRows.push([row])
        } else {
          if (rows[index + 1] !== undefined) {
            dstRows.push([row, rows[index + 1]])
          } else {
            dstRows.push([row])
          }
          index++
        }
      }
      return dstRows
    }
  }
}
</script>

<style>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>

<style lang="scss" scoped>
$__base-space: 12px;
$__base-border: 1px solid #dee2e6;
$__base-height: 45px;
$__title-height: 60px;
$__base-title-width: 150px;

.define-details {
  font-size: 14px;

  border: none;
  /deep/ .el-card__header {
    font-weight: bolder;
    border-bottom: none;
    padding: $__base-space;
  }
  /deep/ .el-card__body {
    padding: 0;
    .title {
      font-size: 16px;
      font-weight: 500;
      margin: 0;
      height: $__title-height;
      box-sizing: border-box;
      position: relative;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        display: block;
        height: 15px;
        width: 3px;
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        background-color: $app-color-primary;
      }
    }
  }

  .data-content {
    width: 100%;

    .data-row {
      display: grid;
      grid-template-columns: 50% 50%;

      .data-col {
        display: flex;
        border-left: $__base-border;
        border-top: $__base-border;
        border-right: $__base-border;
        // min-height: $__base-height;
        line-height: $__base-height;

        .data-label-text,
        .data-label-content {
          padding-left: $__base-space;
          padding-right: $__base-space;
          width: fit-content;
          overflow: hidden;
        }

        .data-label-text {
          flex-shrink: 0;
          min-width: $__base-title-width;
          border-right: $__base-border;
        }

        &:nth-child(2) {
          border-left: none;
        }
      }
      .data-full-col {
        grid-column: 1 / 4;
      }

      &:last-child {
        border-bottom: $__base-border;
      }
    }
  }

  .hidden {
    display: none !important;
  }
}
</style>
