<template>
  <el-dialog :visible.sync="visible" :title="title" :before-close="handleClose" destroy-on-close width="600px">
    <el-form ref="form" :model="form" :rules="rules" label-suffix=":" width="520px">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item prop="type" label="用户类型">
            <br />
            <el-radio v-model="form.type" :label="1">个人用户</el-radio>
            <el-radio v-model="form.type" :label="2">企业用户</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item prop="username" label="用户名">
            <el-input
              v-model="form.username"
              placeholder="请输入用户名 5到16位"
              minlength="5"
              maxlength="16"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="email" label="邮箱">
            <el-input v-model="form.email" placeholder="请输入邮箱"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item prop="password" label="密码">
            <el-input
              type="password"
              v-model="form.password"
              placeholder="请输入密码"
              show-password
              auto-complete="new-password"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="name" :label="form.type === 1 ? '联系人名称' : '企业名称'">
            <el-input
              v-model="form.name"
              :placeholder="`请输入${form.type === 1 ? '联系人名称' : '企业名称'}`"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item prop="phone_number" label="电话号">
            <el-input v-model="form.phone_number" placeholder="请输入电话号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="idcard_no" :label="form.type === 1 ? '身份证号' : '统一社会信用代码'">
            <el-input
              v-model="form.idcard_no"
              :placeholder="`请输入${form.type === 1 ? '身份证号' : '统一社会信用代码'}`"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item prop="address" label="地址">
        <el-input type="textarea" autoresize :rows="3" v-model="form.address" placeholder="请输入地址"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleClose" icon="fas fa-times">取消</el-button>
        <el-button type="primary" @click="handleSubmit" icon="fas fa-check">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'UserForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(value) {
        if (Object.keys(value).length > 0) {
          this.form = Object.assign({}, value)
        }
      }
    },
    form: {
      immediate: true,
      handler() {
        if (this.form.id === undefined) {
          this.rules.password = [
            { required: true, message: '请输入密码', trigger: 'blur' },
            {
              min: 5,
              max: 16,
              message: '密码长度最低不能低于 5 位最长不超过 16 位',
              trigger: 'blur'
            }
          ]
        } else {
          delete this.rules.password
        }
      }
    }
  },
  data() {
    return {
      form: {
        type: 1,
        name: '',
        email: '',
        username: '',
        password: '',
        phone_number: '',
        idcard_no: '',
        address: ''
      },
      rules: {
        type: [{ required: true, message: '请选择账户类型', trigger: ['change'] }],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          {
            min: 5,
            max: 16,
            message: '用户长度最低不能低于 5 位最长不超过 16 位',
            trigger: 'blur'
          }
        ],
        phone_number: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        idcard_no: [{ required: true, message: '请输入身份证号/统一社会信用代码', trigger: 'blur' }],
        address: [{ required: true, message: '请输入地址', trigger: 'blur' }]
      }
    }
  },
  computed: {
    nameLabel() {
      return this.form.type === '1' ? '姓名' : '企业名称'
    },
    title() {
      return this.form.id === undefined ? '添加客户' : '编辑客户'
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)

      this.$refs.form.resetFields()
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('update:visible', false)

          this.$emit('submit', this.form)

          this.handleClose()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
