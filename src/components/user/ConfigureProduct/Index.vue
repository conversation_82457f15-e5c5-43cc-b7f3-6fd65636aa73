<template>
  <el-dialog
    :visible.sync="visible"
    :title="`费率设置 - ${data.name}`"
    :before-close="handleClose"
    destroy-on-close
    width="520px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100" label-suffix=":">
      <el-form-item prop="user_rate" label="用户费率(‱)">
        <el-input v-model="form.user_rate" placeholder="请输入用户费率" />
      </el-form-item>
      <el-form-item prop="minimum_premium" label="最低保费">
        <el-input v-model="form.minimum_premium" placeholder="请输入最低保费" />
      </el-form-item>
      <el-form-item prop="is_enabled" label="是否启用" v-if="data.is_enabled !== undefined">
        <el-radio-group v-model="form.is_enabled">
          <el-radio :label="0">禁用</el-radio>
          <el-radio :label="1">启用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="is_allowed_invoice" label="是否允许开票" v-if="!form.disabled_invoice_checkbox">
        <el-radio-group v-model="form.is_allowed_invoice">
          <el-radio :label="0">禁用</el-radio>
          <el-radio :label="1">启用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'ConfigureProduct',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    data(value) {
      if (Object.keys(value).length > 0) {
        this.form = Object.assign({}, value)
      }
    }
  },
  data() {
    return {
      form: {
        user_rate: '',
        minimum_premium: '',
        is_enabled: 1,
        is_allowed_invoice: 0
      },
      rules: {
        user_rate: [{ required: true, message: '请输入用户费率', trigger: 'blur' }],
        minimum_premium: [{ required: true, message: '请输入最小费率', trigger: 'blur' }],
        is_enabled: [{ required: true, message: '请选择是否启用', trigger: ['blur', 'change'] }],
        is_allowed_invoice: [{ required: true, message: '请选择是否允许开票', trigger: ['blur', 'change'] }]
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)

      this.$refs.form.resetFields()
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', Object.assign({}, this.form))

          this.handleClose()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
