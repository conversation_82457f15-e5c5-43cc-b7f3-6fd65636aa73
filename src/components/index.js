/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-17 23:17:06
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2020-10-18 00:06:49
 */
// 此文件用于注册全局 组件
import Vue from 'vue'

const requireComponent = require.context(
  // 其组件目录的相对路径
  './globals',
  // 是否查询其子目录
  true,
  // 匹配基础组件文件名的正则表达式
  /\w+\.vue|\w+\.jsx$/
)
let GlobalsComponents = []
requireComponent.keys().forEach((fileName) => {
  // 获取组件配置
  const componentConfig = requireComponent(fileName)
  // 格式化组件名称
  const name = fileName
    .split('/')
    .pop()
    .replace(/\.\w+$/, '')
  // 过滤下划线开头的  (私有)
  if (name.indexOf('_') !== 0) {
    GlobalsComponents.push(name)
    // 全局注册组件
    Vue.component(
      name,
      // 通过 `export default` 导出 使用 `.default`，
      componentConfig.default
    )
  }
})
console.log(`%c全局已注册组件:[${GlobalsComponents.join(',')}]\n文档请查看README.md文件`, 'color: #67C23A')
