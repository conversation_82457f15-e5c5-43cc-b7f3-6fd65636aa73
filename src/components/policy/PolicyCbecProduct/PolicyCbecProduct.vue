<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-11 11:06:11
 * @LastEditors: yanb
 * @LastEditTime: 2023-07-03 09:43:57
-->
<template>
  <el-card shadow="never">
    <el-form class="policy-product bg-white" label-position="top" :disabled="isDisabled">
      <template v-if="products?.length > 0">
        <el-form-item required label="请选择保障范围">
          <el-checkbox-group v-model="modelCoverageScope">
            <el-checkbox v-for="product in products" :key="product.value" :true-label="product.value" :false-label="-1">
              {{ product.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item required label="请选择保险公司">
          <template v-if="modelCoverageScope === -1">
            <el-alert title="请选择保障范围" type="warning" :closable="false" />
          </template>
          <template v-else>
            <el-checkbox-group v-model="modelCompanyId">
              <el-checkbox v-for="company in companies" :key="company.id" :true-label="company.id" :false-label="-1">
                {{ company.name }}
              </el-checkbox>
            </el-checkbox-group>
          </template>
        </el-form-item>
      </template>
      <template v-else>
        <el-alert title="当前暂无可投保的产品，请联系管理员开通。" type="warning" :closable="false" />
      </template>
    </el-form>
  </el-card>
</template>
<script>
export default {
  name: 'PolicyCbecProduct',
  props: {
    products: {
      required: true,
      type: Array
    },
    companyId: {
      required: true,
      type: Number
    },
    coverageScope: {
      required: true,
      type: Number
    }
  },
  computed: {
    isDisabled() {
      return this.$route.query.from === 'edit'
    },
    companies() {
      return this.products?.find((item) => item.value === this.modelCoverageScope)?.companies || []
    }
  },
  data() {
    return {
      modelCoverageScope: -1,
      modelCompanyId: -1
    }
  },
  watch: {
    coverageScope(value) {
      if (value !== this.modelCoverageScope) {
        this.modelCoverageScope = value
      }
    },
    companyId(value) {
      if (value !== this.modelCompanyId) {
        this.modelCompanyId = value
      }
    },
    modelCompanyId(val) {
      this.$emit('update:companyId', val)

      this.$emit(
        'update:company',
        this.products
          ?.find((item) => item.value === this.modelCoverageScope)
          ?.companies?.find((item) => item.id === val)
      )
    },
    modelCoverageScope(val, oldVal) {
      if (val !== oldVal && oldVal !== -1) {
        this.modelCompanyId = -1
        this.$emit('update:companyId', -1)
        this.$emit('update:company', null)
      }

      this.$emit('update:coverageScope', val)
    },
    products() {
      this.$emit(
        'update:company',
        this.products
          ?.find((item) => item.value === this.coverageScope)
          ?.companies?.find((item) => item.id === this.companyId)
      )
    }
  },
  created() {
    this.modelCompanyId = this.companyId
    this.modelCoverageScope = this.coverageScope
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-alert__content {
  width: 100% !important;
}

.policy-product {
  .selected-content {
    .el-alert__content {
      width: 100%;
    }
    padding: 0 15px 15px;
    .field {
      @extend .d-flex;
      font-size: 14px;
      margin-bottom: $app-size-mini;
      label {
        width: 75px;
        min-width: 75px;
        font-weight: bold;
      }
      span {
        @extend .flex-fill;
      }
    }
  }
}
</style>
