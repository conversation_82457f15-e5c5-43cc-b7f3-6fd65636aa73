<template>
  <el-select placeholder="请选择货币" v-model="currencyId" :disabled="disabled" class="w-100">
    <el-option v-for="c in currencies" :key="c.id" :value="c.id" :label="`${c.name}(${c.rate})`"></el-option>
  </el-select>
</template>

<script>
import { getCurrencies } from '@/apis/currency'

export default {
  name: 'Currencies',
  props: {
    value: {
      type: [Number, String],
      default: -1
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currencies: [],
      currencyId: ''
    }
  },
  watch: {
    value(value, oldValue) {
      if (value) {
        this.currencyId = value
      }

      if (value && (oldValue === '' || oldValue === -1) && this.$route.query.from !== undefined) {
        this.fetchCurrencies()
      }
    },
    currencyId(value) {
      this.$emit('input', value)
      this.$emit(
        'update:currency',
        this.currencies.find((c) => c.id === value)
      )
    }
  },
  created() {
    if (this.$route.query.from === undefined || this.$route.query.from === 'copy') {
      this.fetchCurrencies()
    }
  },
  methods: {
    fetchCurrencies() {
      getCurrencies({
        with: this.currencyId
      }).then((r) => {
        this.currencies = r.data

        this.$emit(
          'update:currency',
          this.currencies.find((c) => c.id === this.currencyId)
        )
      })
    }
  }
}
</script>
