<template>
  <div class="d-flex m-extra-large-b">
    <el-button type="primary" class="m-extra-large-b" icon="fas fa-download" @click="download">
      下载导入模板
    </el-button>
    <upload-file class="m-extra-large-l" accept="xlsx" v-model="file" button-text="从模板导入" :disabled="importable" />
  </div>
</template>

<script>
import * as policyApi from '@/apis/policy'
import { Loading } from 'element-ui'

const PRODUCT_TYPE = 2

export default {
  props: {
    productId: {
      required: true,
      type: Number
    },
    importable: {
      required: true,
      type: Boolean
    }
  },
  data() {
    return {
      file: ''
    }
  },
  watch: {
    file(val) {
      this.import(val)
    }
  },
  methods: {
    async import() {
      const loading = Loading.service()
      try {
        const { data } = await policyApi.importXlsx({
          type: PRODUCT_TYPE,
          file: this.file,
          product_id: this.productId
        })

        this.$message.success('导入成功')

        this.$router.push({ name: 'PoliciesIntlDetails', params: { id: data.id } })
      } finally {
        loading.close()
      }
    },
    download() {
      const href = policyApi.downloadImportTemplate({ type: PRODUCT_TYPE })
      window.open(href, '_blank')
    }
  }
}
</script>
