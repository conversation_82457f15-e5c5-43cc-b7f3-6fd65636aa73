<template>
  <el-select placeholder="请选择包装方式" v-model="packingMethodId" class="w-100">
    <el-option v-for="m in packingMethods" :key="m.id" :value="m.id" :label="m.display_name"></el-option>
  </el-select>
</template>

<script>
import { getProductPackingMethods } from '@/apis/product'

export default {
  name: 'ComponentPackingMethods',
  props: {
    value: {
      require: true,
      type: [String, Number]
    },
    productId: {
      require: false,
      type: [String, Number]
    },
    values: {
      require: false,
      type: Array
    }
  },
  data() {
    return {
      packingMethods: [],
      packingMethodId: ''
    }
  },
  watch: {
    value(value) {
      this.packingMethodId = value
    },
    packingMethodId(value) {
      this.$emit('input', value)
    },
    productId(value) {
      if (value) {
        this.fetchProductPackingMethods()
      }
    },
    values(values) {
      if (values.length > 0) {
        this.packingMethods = values
      }
    }
  },
  methods: {
    fetchProductPackingMethods() {
      if (this.productId <= 0) {
        return
      }

      getProductPackingMethods(this.productId).then((r) => {
        this.packingMethods = r.data
      })
    }
  }
}
</script>
