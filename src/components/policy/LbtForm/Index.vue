<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-10 00:05:29
 * @LastEditors: yanb
 * @LastEditTime: 2024-07-08 19:27:30
-->
<template slot="label">
  <div>
    <policy-product
      :products="products"
      @product-change="(p) => (product = p)"
      :product="form.product_id"
      class="m-extra-large-b"
    ></policy-product>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" :disabled="product?.id === undefined">
      <form-block class="m-extra-large-b" title="投保人/被保人信息">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="policyholder" label="投保人(同开票抬头)">
              <el-autocomplete
                v-model="form.policyholder"
                :fetch-suggestions="(value, cb) => fetchSuggestions('policyholder', value, cb)"
                @select="selectSuggestion"
                @blur="() => checkBlacklist('投保人', 'name', form.policyholder)"
                placeholder="请输入投保人"
                value="form.policyholder"
                clearable
                class="w-100"
              >
              </el-autocomplete>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="insured">
              <template slot="label">
                被保人
                <span class="m-mini-l text-danger">
                  <el-checkbox v-model="isSame">同投保人</el-checkbox>
                </span>
              </template>
              <el-autocomplete
                v-model="form.insured"
                :fetch-suggestions="(value, cb) => fetchSuggestions('insured', value, cb)"
                @select="selectSuggestion"
                @blur="() => checkBlacklist('被保人', 'name', form.insured)"
                placeholder="请输入被保人"
                clearable
                class="w-100"
              ></el-autocomplete>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="policyholder_phone_number" label="投保人电话">
              <el-input v-model="form.policyholder_phone_number" placeholder="请输入投保人电话" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="insured_phone_number" label="被保人电话">
              <el-input v-model="form.insured_phone_number" placeholder="请输入被保人电话" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="policyholder_address" label="投保人地址">
              <el-input v-model="form.policyholder_address" placeholder="请输入投保人地址" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="insured_address" label="被保人地址">
              <el-input v-model="form.insured_address" placeholder="请输入被保人地址" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" title="货物信息">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="goods_type_id" label="货物分类">
              <goods-types :productId.sync="product.id" v-model="form.goods_type_id" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="packing_method_id" label="包装方式">
              <packing-methods :productId.sync="product.id" v-model="form.packing_method_id" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="goods_name" label="货物名称">
              <el-input
                v-model="form.goods_name"
                placeholder="请输入货物名称"
                type="textarea"
                autoresize
                :rows="3"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="goods_amount" label="包装件数">
              <el-input
                v-model="form.goods_amount"
                placeholder="请输入包装件数"
                type="textarea"
                autoresize
                :rows="3"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b">
        <template #title>
          <span>运输信息</span>
          <el-popover placement="top-start" title="投保提醒" width="520" trigger="hover">
            <template>
              <p>
                （1）船名航次/航班号/车牌号：根据运输工具填写，例如：船名/航次 ABB CCC/088E，航班号 MU8888，车牌号
                沪A8G888。若暂无运输工具信息，先填写“待定”；后续运输工具信息确定后，请联系业务人员批改承保信息。
              </p>
              <p>（2）起运地/目的地描述：如门到门，填写仓库所在城市名称即可，如上海、洛杉矶(Los Angeles)。</p>
              <p>
                （3）起运时间：按运输条款的起运时间填写。如仓至仓条款，起运时间填写工厂或仓库装车起运时间；如港至港，则填写大船起运时间。
              </p>
            </template>
            <span slot="reference" style="margin-left: 5px; font-size: 12px; color: #ff7f4c">
              如何填写<i class="fas fa-question"></i>
            </span>
          </el-popover>
        </template>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="transport_method_id" label="运输方式">
              <transport-methods
                :productId.sync="product.id"
                @loadingMethods="(m) => (loadingMethods = m)"
                v-model="form.transport_method_id"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="loading_method_id" label="装载方式">
              <loading-methods
                :productId.sync="product.id"
                :loading-methods="loadingMethods"
                v-model="form.loading_method_id"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="departure" label="起运地">
              <div class="d-flex w-100">
                <el-cascader
                  v-model="form.departure"
                  :style="{ width: '500px' }"
                  :options="regions"
                  :props="{
                    expandTrigger: 'hover',
                    filterable: true,
                    value: 'value',
                    label: 'value',
                    children: 'city'
                  }"
                ></el-cascader>
                <el-input v-model="form.departure_addr" class="flex-fill m-mini-l" clearable></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="destination" label="目的地">
              <div class="d-flex w-100">
                <el-cascader
                  v-model="form.destination"
                  :style="{ width: '500px' }"
                  :options="regions"
                  :props="{
                    expandTrigger: 'hover',
                    filterable: true,
                    value: 'value',
                    label: 'value',
                    children: 'city'
                  }"
                ></el-cascader>
                <el-input v-model="form.destination_addr" class="flex-fill m-mini-l" clearable></el-input>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="transmit" label="中转地">
              <div class="d-flex w-100">
                <el-cascader
                  :style="{ width: '500px' }"
                  v-model="form.transmit"
                  :options="regions"
                  :props="{
                    expandTrigger: 'hover',
                    filterable: true,
                    value: 'value',
                    label: 'value',
                    children: 'city'
                  }"
                ></el-cascader>
                <el-input v-model="form.transmit_addr" class="flex-fill m-mini-l" clearable></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="transport_no" label="车牌号">
              <el-input v-model="form.transport_no" placeholder="请输入车牌号" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="invoice_no">
              <template slot="label">
                <span>发票号</span>
                <span class="text-danger">（运单号和发票号必选填一个）</span>
              </template>
              <el-input v-model="form.invoice_no" placeholder="请输入发票号" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="waybill_no">
              <template slot="label">
                <span>运单号</span>
                <span class="text-danger">（运单号和发票号必选填一个）</span>
              </template>
              <el-input v-model="form.waybill_no" placeholder="请输入运单号" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="shipping_date" label="起运日期">
              <el-date-picker
                :picker-options="shippingDateOptions"
                v-model="form.shipping_date"
                class="w-100"
                type="date"
                placeholder="请选择起运日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item prop="anti_dated_file" v-if="showAntiDatedFileUploader">
              <template slot="label">
                <span>倒签保函</span>
                <span class="text-danger">
                  <el-link class="m-mini-l" :href="product?.additional?.anti_dated_file">
                    <small style="color: red">点击下载倒签保函</small>
                  </el-link>
                </span>
              </template>
              <div class="d-flex">
                <upload-file v-model="form.anti_dated_file"></upload-file>
                <el-link
                  v-if="typeof form.anti_dated_file === 'string' && form.anti_dated_file"
                  class="m-extra-large-l"
                  :href="form.anti_dated_file"
                >
                  点击查看
                </el-link>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" title="费用信息">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="coverage" label="运费（元）">
              <el-input clearable type="number" v-model="form.coverage" placeholder="请输入运费"> </el-input>
              <span>{{ form.coverage | chineseCoverage }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" title="其他信息">
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="sticky_note" label="工作编号">
              <el-input
                v-model="form.sticky_note"
                placeholder="此内容不作为投保使用，仅方便您核对账单使用"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="custom_file" label="投保附件">
              <div class="d-flex">
                <upload-file v-model="form.custom_file"></upload-file>
                <el-link
                  v-if="typeof form.custom_file === 'string' && form.custom_file"
                  class="m-extra-large-l"
                  :href="form.custom_file"
                >
                  点击查看
                </el-link>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="remark">
              <template slot="label">
                <span>备注</span>
                <span class="text-danger">（以下内容不展示在保单上）</span>
              </template>
              <el-input
                v-model="form.remark"
                type="textarea"
                autoresize
                :rows="3"
                placeholder="备注不作为投保依据"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" center>
        <div class="m-extra-large-b">
          <el-checkbox @change="disabledSubmit = !disabledSubmit" slot="title"></el-checkbox>
          <span style="cursor: pointer" @click="showNotice = !showNotice"> &nbsp;我已详细阅读《投保须知》的内容 </span>
        </div>

        <template v-if="showNotice">
          <el-alert title="投保须知" type="warning" class="m-extra-large-b" :closable="false">
            <template v-if="product.additional !== undefined">
              <div style="text-align: left" v-html="product.additional.notice"></div>
            </template>
          </el-alert>
        </template>
        <el-button icon="far fa-save" @click="handleSave" v-if="$route.query.from !== 'edit'">暂存</el-button>
        <el-button type="primary" icon="el-icon-document-checked" :disabled="disabledSubmit" @click="submitPreview">
          提交预览
        </el-button>
      </form-block>
    </el-form>
    <inform :visible.sync="informVisible" :detail="product?.additional?.inform || ''" @checking="checkingInform" />
  </div>
</template>

<script>
import areadata from '@/utils/areadata.json'
import PolicyProduct from '@/components/policy/PolicyLbtProduct'
import GoodsTypes from '@/components/policy/GoodsTypes'
import PackingMethods from '@/components/policy/PackingMethods'
import LoadingMethods from '@/components/policy/LoadingMethods'
import TransportMethods from '@/components/policy/TransportMethods'
import Inform from '@/components/policy/Inform'
import { getProducts } from '@/apis/product'
import { checkIfExist, getSuggestions } from '@/apis/policy'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'
import { digitUppercase } from '@/utils'
import AddressParser from '@/utils/addressparser'
import * as blacklistApi from '@/apis/blacklist'

const PRODUCT_TYPE = 3

export default {
  name: 'DomesticForm',
  components: { PolicyProduct, GoodsTypes, PackingMethods, LoadingMethods, TransportMethods, Inform },
  props: {
    policy: {
      type: Object,
      default: () => {}
    }
  },
  filters: {
    chineseCoverage(value) {
      return digitUppercase(value)
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    showAntiDatedFileUploader() {
      const antiDatedDays = this.antiDatedDays
      const shippingDate = dayjs(this.form.shipping_date)
      if (antiDatedDays > 0 && shippingDate.isValid()) {
        return dayjs().isAfter(shippingDate.add(antiDatedDays, 'day'))
      }

      return false
    },
    shippingDateOptions() {
      const antiDatedDays = this.antiDatedDays
      return {
        disabledDate(time) {
          if (antiDatedDays === undefined || antiDatedDays <= 0) {
            return time.getTime() <= dayjs(Date.now()).subtract(1, 'day').toDate()
          }
        }
      }
    },
    regions() {
      const disabledRegions = this.product?.additional?.disabled_regions?.split('|') || []
      return areadata.filter((d) => {
        return !disabledRegions.includes(d.value)
      })
    },
    antiDatedDays() {
      let date = this?.product?.additional?.anti_dated_days
      if (this?.product?.additional?.anti_date_is_in_transports == 1) {
        date = this?.product?.additional?.anti_date_transport_data.find(
          (item) => item.transport_id == this.form.transport_method_id
        )?.value
      }
      return date
    }
  },
  data() {
    return {
      isSame: false,
      showNotice: false,
      products: {},
      product: {},
      disabledSubmit: true,
      informVisible: false,
      loadingMethods: [],
      form: {
        type: PRODUCT_TYPE,
        product_id: -1,
        policyholder: '',
        policyholder_address: '',
        policyholder_phone_number: '',
        insured: '',
        insured_address: '',
        insured_phone_number: '',
        sticky_note: '',
        goods_type_id: '',
        packing_method_id: '',
        goods_name: '',
        goods_amount: '',
        transport_method_id: '',
        loading_method_id: '',
        departure: [],
        departure_addr: '',
        destination: [],
        destination_addr: '',
        transmit: [],
        transmit_addr: '',
        transport_no: '',
        invoice_no: '',
        waybill_no: '',
        anti_dated_file: '',
        coverage: '',
        shipping_date: '',
        remark: '',
        custom_file: ''
      },
      rules: {
        policyholder: [
          { required: true, message: '请输入投保人姓名', trigger: 'blur' },
          { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' }
        ],
        insured: [
          { required: true, message: '请输入被保人姓名', trigger: 'blur' },
          { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' }
        ],
        insured_address: [
          {
            validator: (rule, value, callback) => {
              if (new TextEncoder().encode(`${value}${this.form.insured_phone_number}`).length > 150) {
                callback(new Error('被保人地址+电话长度不能超过150个字符'))
              } else {
                callback()
              }
            }
          }
        ],
        insured_phone_number: [
          {
            validator: (rule, value, callback) => {
              if (new TextEncoder().encode(`${value}${this.form.insured_address}`).length > 150) {
                callback(new Error('被保人地址+电话长度不能超过150个字符'))
              } else {
                callback()
              }
            }
          }
        ],
        policyolder_phone_number: [
          {
            validator: (rule, value, callback) => {
              if (new TextEncoder().encode(`${value}${this.form.policyholder_address}`).length > 150) {
                callback(new Error('投保人地址+电话长度不能超过150个字符'))
              } else {
                callback()
              }
            }
          }
        ],
        policyholder_address: [
          {
            validator: (rule, value, callback) => {
              if (new TextEncoder().encode(`${value}${this.form.policyholder_phone_number}`).length > 150) {
                callback(new Error('投保人地址+电话长度不能超过150个字符'))
              } else {
                callback()
              }
            }
          }
        ],
        goods_type_id: [{ required: true, message: '请选择货物类别', trigger: ['change', 'blur'] }],
        packing_method_id: [{ required: true, message: '请选择包装方式', trigger: 'change' }],
        goods_name: [{ required: true, message: '请输入货物名称', trigger: 'blur' }],
        goods_amount: [{ required: true, message: '请输入包装件数', trigger: 'blur' }],
        transport_method_id: [{ required: true, message: '请选择运输方式', trigger: 'change' }],
        loading_method_id: [{ required: true, message: '请选择装载方式', trigger: 'change' }],
        departure: [{ required: true, message: '请选择起运地', trigger: 'change' }],
        destination: [{ required: true, message: '请选择目的地', trigger: 'change' }],
        transport_no: [
          { required: true, message: '请输入车牌号', trigger: 'blur' },
          { min: 1, max: 32, message: '车牌号长度错误', trigger: 'blur' }
        ],
        shipping_date: [{ required: true, type: 'date', message: '请选择起运日期', trigger: 'change' }],
        coverage: [{ required: true, message: '请输入保险金额', trigger: 'blue' }],
        invoice_no: [
          { min: 2, message: '发票号长度错误', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (this.form.waybill_no === '' && this.form.invoice_no === '') {
                callback(new Error('当运单号为空时发票号必填'))
              } else {
                if (value) {
                  checkIfExist({
                    type: PRODUCT_TYPE,
                    policy_id: this.$route.query.from !== 'copy' ? this.$route.params.id : null,
                    company: this.product?.company?.identifier,
                    column: 'invoice_no',
                    value: value
                  }).then((r) => {
                    r.is_exist ? callback(new Error('发票号已经存在')) : callback()
                  })
                } else {
                  callback()
                }

                this.$refs.form.clearValidate('waybill_no')
              }
            },
            trigger: 'blur'
          }
        ],
        waybill_no: [
          { min: 2, message: '运单号长度错误', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (this.form.invoice_no === '' && this.form.waybill_no === '') {
                callback(new Error('当发票号为空时运单号必填'))
              } else {
                if (value) {
                  checkIfExist({
                    type: PRODUCT_TYPE,
                    policy_id: this.$route.query.from !== 'copy' ? this.$route.params.id : null,
                    column: 'waybill_no',
                    company: this.product?.company?.identifier,
                    value: value
                  }).then((r) => {
                    r.is_exist ? callback(new Error('运单号已经存在')) : callback()
                  })
                } else {
                  callback()
                }

                this.$refs.form.clearValidate('invoice_no')
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    policy: {
      deep: true,
      immediate: true,
      handler(value) {
        if (Object.keys(value).length > 0) {
          this.form = Object.assign({}, this.form, value)
        }
      }
    },
    // hack: 线上环境不知道为什么 subject_id 变更的时候 product_id 还是 1 按理来说应该是同步的
    'form.product_id'(value, oldValue) {
      if (this.$route.query.from === 'edit' && oldValue === -1) {
        this.fetchProducts()
      }
    },
    product(value) {
      if (value.id !== this.form.product_id) {
        this.form.transport_method_id = ''
        this.form.packing_method_id = ''
        this.form.loading_method_id = ''
        this.form.goods_type_id = ''
        this.form.product_id = value.id
      }
    },
    'form.shipping_date': {
      deep: true,
      immediate: true,
      handler(value) {
        if (this.antiDatedDays > 0 && this.$route.query.from !== 'edit') {
          if (dayjs().isAfter(dayjs(value).add(this.antiDatedDays, 'day'))) {
            this.$set(this.rules, 'anti_dated_file', [
              { required: true, message: '请上传倒签保函', trigger: ['blur', 'change'] }
            ])
          } else {
            this.$delete(this.rules, 'anti_dated_file')
          }

          this.$refs.form.clearValidate('anti_dated_file')
        }
      }
    },
    'form.policyholder': {
      deep: true,
      immediate: true,
      handler(value) {
        if (this.isSame) {
          this.form.insured = value
        }
      }
    },
    'form.policyholder_phone_number': {
      deep: true,
      immediate: true,
      handler(value) {
        if (this.isSame) {
          this.form.insured_phone_number = value
        }
      }
    },
    'form.policyholder_address': {
      deep: true,
      immediate: true,
      handler(value) {
        if (this.isSame) {
          this.form.insured_address = value
        }
      }
    },
    isSame(value) {
      if (value) {
        this.form.insured = this.form.policyholder
        this.form.insured_phone_number = this.form.policyholder_phone_number
        this.form.insured_address = this.form.policyholder_address
      }
    },
    'form.departure_addr'(value, oldValue) {
      if (!oldValue && this.$route.params.from !== undefined) {
        return
      }

      const { province, city } = AddressParser.parse(value)
      if (city && this.form.departure[1] !== city) {
        this.form.departure = [province, city]
      }
    },
    'form.destination_addr'(value, oldValue) {
      if (!oldValue && this.$route.params.from !== undefined) {
        return
      }

      const { province, city } = AddressParser.parse(value)
      if (city && this.form.destination[1] !== city) {
        this.form.destination = [province, city]
      }
    },
    'form.transmit_addr'(value, oldValue) {
      if (!oldValue && this.$route.params.from !== undefined) {
        return
      }
      const { province, city } = AddressParser.parse(value)
      if (city && this.form.transmit[1] !== city) {
        this.form.transmit = [province, city]
      }
    }
  },
  created() {
    if (this.$route.query.from === undefined) {
      this.form.policyholder = this.user.name
      this.form.shipping_date = Date.now()
    }

    this.fetchProducts()
  },
  methods: {
    async checkBlacklist(name, column, value) {
      try {
        const { data } = await blacklistApi.check({
          column,
          value,
          product_type: PRODUCT_TYPE
        })

        if (data.is_blocked) {
          this.$alert(
            `<b class="text-danger">${name}为黑名单用户，请立即停止投保！</b><br />  任何试图规避规则或利用系统漏洞的行为均将被系统完整记录并追踪。对于经核实的恶意投保行为，本公司保留依法追责的权利。如需正常投保，请联系客服核实相关信息。`,
            `信息异常`,
            {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定'
            }
          )
        }
      } catch (e) {
        console.log(e)
      }
    },
    fetchSuggestions(column, value, cb) {
      getSuggestions(this.product?.company?.id, column, value).then((r) => {
        const data = r.data.map((item) => {
          item.value = item[column]
          return item
        })

        cb(data)
      })
    },
    selectSuggestion(value) {
      delete value.value

      Object.assign(this.form, {}, value)

      this.$refs.form.clearValidate(Object.keys(value))
    },
    handlePreview() {
      this.$refs.form.validate((valid, errors) => {
        if (valid) {
          const data = Object.assign({}, this.form)

          this.$emit(
            'on-preview',
            this.product,
            this.marshalData(Object.assign({}, this.policy)),
            this.marshalData(data)
          )
        } else {
          window.alert_validate_errors(errors)
        }
      })
    },
    handleSave() {
      const data = Object.assign({}, this.form)

      this.$emit('on-save', this.product, this.marshalData(data))
    },
    marshalData(data) {
      if (Object.keys(data).length <= 0) {
        return data
      }

      for (const fileField of ['anti_dated_file', 'custom_file']) {
        if (typeof data[fileField] === 'string') {
          data[fileField] = ''
        }
      }

      data.shipping_date = dayjs(data.shipping_date).isValid() ? dayjs(data.shipping_date).format('YYYY-MM-DD') : ''
      data.departure = data.departure_addr
        ? data.departure.join('-') + ':' + data.departure_addr
        : data.departure.join('-')

      data.transmit = data.transmit_addr
        ? data.transmit?.join('-') + ':' + data.transmit_addr
        : data.transmit?.join('-')

      data.destination = data.destination_addr
        ? data.destination.join('-') + ':' + data.destination_addr
        : data.destination.join('-')

      data.product_id = this.product.id

      return data
    },
    fetchProducts() {
      let filter = {
        filter: {
          type: PRODUCT_TYPE
        }
      }

      if (this.$route.params.id !== undefined && this.$route.query.from === 'edit') {
        filter.id = this.form.product_id

        // hack: 编辑模式阻止 id 为 -1 的请求
        if (filter.id === -1) {
          return
        }
      }

      getProducts(filter).then((r) => {
        this.products = r.data
      })
    },
    submitPreview() {
      if (
        this?.product?.channel?.platform?.includes(this.user.platform_id) &&
        this?.product?.additional?.inform?.length > 1
      ) {
        this.informVisible = true
        return
      }
      this.handlePreview()
    },
    checkingInform() {
      this.informVisible = false
      this.handlePreview()
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-form {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}
</style>
