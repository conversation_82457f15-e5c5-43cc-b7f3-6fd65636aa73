<!--
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-11-04 15:10:37
 * @LastEditors: yanb
 * @LastEditTime: 2024-04-22 09:54:56
-->
<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="申请纸质保单-保单打印时间为：工作日09：00-17：30"
    destory-on-close
    width="800px"
    :before-close="handleClose"
  >
    <el-alert
      v-if="announcement?.content"
      class="m-extra-large-b"
      type="warning"
      :closable="false"
      show-icon
      title="通知"
      :description="announcement?.content"
    ></el-alert>

    <ul class="detail">
      <li>
        <div>投保人</div>
        <div>{{ policy.policyholder }}</div>
      </li>
      <li>
        <div>被保人</div>
        <div>{{ policy.insured }}</div>
      </li>
      <li>
        <div>投保人地址</div>
        <div>{{ policy.policyholder_address || '-' }}</div>
      </li>
      <li>
        <div>被保人地址</div>
        <div>{{ policy.insured_address || '-' }}</div>
      </li>
      <li>
        <div>保单号</div>
        <div>{{ policy.policy_no }}</div>
      </li>
      <li>
        <div>起运日期打印格式</div>
        <div>{{ shippingDatePrintFormat }}</div>
      </li>
    </ul>
    <el-form ref="form" :model="form" :rules="rules" label-position="left" label-suffix=":" label-width="100">
      <el-form-item label="历史申请记录" prop="salesman_id">
        <el-select placeholder="请选择历史申请记录" v-model="paperId" @change="useSuggestion" class="w-100">
          <el-option v-for="s in suggestions" :key="s.id" :label="s.recipient" :value="s.id" />
        </el-select>
      </el-form-item>
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item prop="recipient" label="收件人">
            <el-input v-model="form.recipient" placeholder="请输入收件人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="phone" label="联系电话">
            <el-input v-model="form.phone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item prop="address" label="收件地址">
            <el-input v-model="form.address" placeholder="请输入收件地址" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="type" label="快递类型">
            <el-select v-model="form.type" placeholder="请选择快递类型" class="w-100">
              <el-option :value="1" label="普通快递" />
              <el-option :value="2" label="顺丰到付" />
              <el-option :value="4" label="顺丰预付" />
              <el-option :value="3" label="自取保单" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item prop="remark" label="备注">
        <el-input type="textarea" autoresize :rows="3" v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button icon="fas fa-times" @click="handleClose">取消</el-button>
      <el-button type="primary" icon="fas fa-check" @click="handleSubmit">提交</el-button>
    </template>
  </el-dialog>
</template>

<script>
import dayjs from 'dayjs'
import { latestAnnouncement } from '@/apis/announcement'

export default {
  name: 'PolicyPaper',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    policy: {
      type: Object,
      default: () => {}
    },
    suggestions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        recipient: '',
        address: '',
        type: '',
        phone: '',
        remark: ''
      },
      rules: {
        recipient: [{ required: true, message: '请输入收件人', trigger: 'blur' }],
        address: [{ required: true, message: '请输入收件人地址', trigger: 'blur' }],
        type: [{ required: true, message: '请选择快递类型', trigger: ['blur', 'change'] }],
        phone: [{ required: true, message: '请输入收件人电话', trigger: 'blur' }]
      },
      paperId: '',
      announcement: {}
    }
  },
  computed: {
    shippingDatePrintFormat() {
      return parseInt(this.policy?.detail?.shipping_date_print_format, 10) === 1
        ? dayjs(this.policy?.detail?.shipping_date).format('MMM.DD, YYYY')
        : 'AS PER B/L'
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        latestAnnouncement(2).then((r) => (this.announcement = r.data))
      }
    }
  },
  created() {
    this.dialogVisible = this.visible
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.dialogVisible = false
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)
          this.handleClose()
        }
      })
    },
    useSuggestion() {
      let paper = this.suggestions.find((e) => e.id === this.paperId)
      this.form = {
        recipient: paper?.recipient,
        address: paper?.address,
        type: paper?.type,
        phone: paper?.phone,
        remark: paper?.remark
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;

  .el-alert__description {
    color: red;
  }
}

.detail {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  padding: 0;
  margin: 0 0 20px 0;
  border: 1px solid #eee;
  border-right: 0;
  border-bottom: 0;

  li {
    list-style: none;
    margin: 0;
    padding: 5px 10px 5px 10px;
    border-bottom: 1px solid #eee;
    border-right: 1px solid #eee;

    div {
      &:first-child {
        font-weight: bold;
      }
    }
  }
}
</style>
