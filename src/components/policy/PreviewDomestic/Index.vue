<template>
  <div id="preview-body" class="policy-wrap">
    <el-card
      shadow="never"
      class="define-card my-4 shadow-sm"
      :body-style="{ padding: '0' }"
      v-if="product.id !== undefined"
    >
      <div class="preview-header">
        <el-row class="flex heading">
          <el-col>保险公司</el-col>
          <el-col>保险金额</el-col>
          <el-col>费率（‱万分之）</el-col>
          <el-col>保费（元）</el-col>
        </el-row>
        <el-row class="flex">
          <el-col>{{ product.company_branch }}</el-col>
          <el-col v-html="viewData.coverage"></el-col>
          <el-col v-html="viewData.user_rate"></el-col>
          <el-col>{{ data?.user_premium?.toFixed(2) }}</el-col>
        </el-row>
      </div>
    </el-card>
    <div class="p-extra-large m-extra-large-t bg-white flex-fill o-hidden o-y-auto">
      <define-details :data="detail" />
    </div>
    <el-card shadow="never" class="define-card my-4 shadow-sm m-extra-large-t">
      <center>
        <el-row>
          <el-button icon="el-icon-back" @click="$emit('on-back')">返回修改</el-button>
          <el-button icon="el-icon-download" @click="handleDownloadConfirmationPdf()">
            {{ $route.query.from !== 'edit' ? '下载投保单确认件并暂存' : '下载投保单确认件' }}
          </el-button>
          <el-button type="primary" icon="el-icon-circle-check" @click="handleSubmit()">
            {{ $route.query.from === 'edit' ? '提交批改' : '立即投保' }}
          </el-button>
        </el-row>
      </center>
    </el-card>
  </div>
</template>

<script>
import { downloadConfirmationLetter } from '@/apis/policy'
import { mapGetters } from 'vuex'

export default {
  name: 'PreviewDomestic',
  props: {
    product: {
      type: Object,
      default: () => {}
    },
    originData: {
      type: Object,
      default: () => {}
    },
    data: {
      type: Object,
      default: () => {}
    },
    paymentMethod: {
      type: Number,
      default: 1
    }
  },
  computed: {
    ...mapGetters('platform', ['online_payment_is_enabled']),
    ...mapGetters('auth', ['user']),
    canOnlinePayment() {
      return (
        this.online_payment_is_enabled == 1 && this.user.is_online_payment == 1 && Number(this.data.subject_id) !== 3
      )
    },
    viewData() {
      if (this.$route.query.from !== 'edit') {
        return this.data
      }

      let viewData = {}
      Object.keys(this.data).forEach((key) => {
        if (!(typeof this.data[key] === 'object')) {
          if (this.data[key] !== this.originData[key]) {
            viewData[key] = `<span style="color: red;">${this.data[key]}</span>`
          } else {
            viewData[key] = this.data[key]
          }
        } else {
          if (this.data[key]?.id !== this.originData[key]?.id) {
            viewData[key] = {
              id: this.data[key].id,
              name: `<span style="color: red;">${this.data[key]?.name}</span>`
            }
          } else {
            viewData[key] = this.data[key]
          }
        }
      })

      return viewData
    },
    detail() {
      let clientData = [
        { label: '投保人', value: this.viewData.policyholder },
        { label: '被保人', value: this.viewData.insured },
        { label: '投保人地址', value: this.viewData.policyholder_address },
        { label: '被保人地址', value: this.viewData.insured_address },
        { label: '投保人电话', value: this.viewData.policyholder_phone_number },
        { label: '被保人电话', value: this.viewData.insured_phone_number }
      ]

      if (['TPIC', 'PICC', 'CPIC'].includes(this.product.company.identifier)) {
        clientData.push(
          { label: '投保人类型', value: this.data.policyholder_type === 1 ? '个人客户' : '团体客户' },
          { label: '被保人类型', value: this.data.insured_type === 1 ? '个人客户' : '团体客户' },
          { label: '投保人证件号', value: this.viewData.policyholder_idcard_no },
          { label: '被保人证件号', value: this.viewData.insured_idcard_no }
        )
      }

      return {
        title: ' ',
        data: [
          {
            title: '保险内容',
            groups: [
              { label: '主条款', value: this.viewData?.clauses?.main, row: true },
              { label: '附加险', value: this.viewData?.clauses?.additional, row: true },
              {
                label: '免赔',
                value: this.viewData?.deductible ? this.viewData.deductible : this.product.additional?.deductible,
                row: true
              },
              {
                label: '特别约定',
                value: this.viewData?.special_agreement
                  ? this.viewData.special_agreement
                  : this.product.additional?.special_agreement,
                row: true
              }
            ]
          },
          {
            title: '基本信息',
            groups: clientData
          },
          {
            title: '货物信息',
            groups: [
              { label: '货物类别', value: this.viewData.goods_type.name },
              { label: '装载方式', value: this.viewData.loading_method.name },
              { label: '运输方式', value: this.viewData.transport_method.name },
              { label: '包装方式', value: this.viewData.packing_method.name },
              { label: '货物名称', value: `<pre>${this.viewData.goods_name}</pre>` },
              { label: '包装件数', value: `<pre>${this.viewData.goods_amount}</pre>` }
            ]
          },
          {
            title: '运输信息',
            groups: [
              { label: '运单号', value: this.viewData.waybill_no },
              { label: '发票号', value: this.viewData.invoice_no },
              { label: '运输工具号', value: this.viewData.transport_no },
              { label: '起运日期', value: this.viewData.shipping_date },
              {
                label: '起运地',
                value: this.data.departure_port
                  ? `${this.viewData.departure?.replace(':', '-')} (${this.viewData.departure_port})`
                  : this.viewData.departure?.replace(':', '-')
              },
              {
                label: '中转地',
                value: this.data.transmit_port
                  ? `${this.viewData.transmit?.replace(':', '-')} (${this.viewData.transmit_port})`
                  : this.viewData.transmit?.replace(':', '-')
              },
              {
                label: '目的地',
                value: this.data.destination_port
                  ? `${this.viewData.destination?.replace(':', '-')} (${this.viewData.destination_port})`
                  : this.viewData.destination?.replace(':', '-'),
                row: true
              }
            ]
          }
        ]
      }
    },
    confirmationPdfData() {
      return [
        {
          title: '被保险人信息',
          dataset: [
            { label: '被保险人', subtitle: 'Insured', value: this.viewData?.insured },
            { label: '被保险人通讯地址', subtitle: 'Address', value: this.viewData?.insured_address },
            { label: '被保险人电话', subtitle: 'Office Phone or Mobile', value: this.viewData?.insured_phone_number }
          ]
        },
        {
          title: '货物信息',
          dataset: [
            {
              label: '货物名称',
              subtitle: 'Description',
              value: this.viewData?.goods_name,
              wrap: true
            },
            {
              label: '数量单位',
              subtitle: 'Quantity',
              value: this.viewData?.goods_amount,
              wrap: true
            },
            {
              label: '保险金额',
              subtitle: 'Amount Insured',
              value: this.viewData?.coverage
            },
            { label: '发票号', subtitle: 'Invoice Number', value: this.viewData?.invoice_no },
            { label: '提单/运单号', subtitle: 'WAYBILL NO OR B/L NO', value: this.viewData?.waybill_no },
            { label: '运输工具号', subtitle: 'Per Conveyance S.S.', value: this.viewData?.transport_no },
            {
              label: '起运地',
              subtitle: 'From',
              value: this.data.departure_port
                ? `${this.viewData.departure?.replace(':', '-')} (${this.viewData.departure_port})`
                : this.viewData.departure?.replace(':', '-')
            },

            {
              label: '目的地',
              subtitle: 'To',
              value: this.data.destination_port
                ? `${this.viewData.destination?.replace(':', '-')} (${this.viewData.destination_port})`
                : this.viewData.destination?.replace(':', '-')
            },

            {
              label: '转运地',
              subtitle: 'Via',
              value: this.data.transmit_port
                ? `${this.viewData.transmit?.replace(':', '-')} (${this.viewData.transmit_port})`
                : this.viewData.transmit?.replace(':', '-')
            },
            { label: '起运日期', subtitle: 'Slg. on or abt', value: this.viewData?.shipping_date }
          ]
        },
        {
          title: '保险条件',
          dataset: [
            {
              label: '主条款',
              subtitle: 'Conditions',
              value: this.viewData?.clauses?.main,
              wrap: true
            },
            {
              label: '附加条款',
              subtitle: 'Additional Conditions',
              value: this.viewData?.clauses?.additional,
              wrap: true
            },

            {
              label: '免赔',
              subtitle: 'Deductible',
              value: this.product.additional?.deductible || '-',
              wrap: true
            },

            {
              label: '特别约定',
              subtitle: 'Special Agreement',
              value: this.product.additional?.special_agreement,
              wrap: true
            }
          ]
        }
      ]
    }
  },
  methods: {
    async handleDownloadConfirmationPdf() {
      const data = await downloadConfirmationLetter({
        data: this.confirmationPdfData
      })
      const linkSource = `data:application/pdf;base64,${data.data}`
      const downloadLink = document.createElement('a')
      downloadLink.href = linkSource
      downloadLink.download = `${this.data?.insured}.pdf`
      downloadLink.click()

      if (this.$route.query.from !== 'edit') {
        this.$emit('on-save', {}, this.data)
      }
    },
    handleSubmit() {
      // 修改和不支持在线支付时,直接走余额支付流程
      if (this.$route.query.from === 'edit' || !this.canOnlinePayment) {
        this.$emit('on-submit')
        return
      }
      this.$confirm('请选择支付方式', '选择支付方式', {
        distinguishCancelAndClose: true,
        confirmButtonText: '余额支付',
        cancelButtonText: '在线支付'
      })
        .then(() => {
          this.$emit('update:paymentMethod', 1)
          this.$emit('on-submit')
        })
        .catch((action) => {
          if (action === 'cancel') {
            this.$emit('update:paymentMethod', 2)
            this.$emit('on-submit')
          }
        })
      // this.$emit('on-submit')
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-header {
  .flex {
    display: flex;
    flex: 1;
    text-align: left;
    height: 40px;
    line-height: 40px;

    .el-col {
      padding-left: 10px;
    }
  }

  .heading {
    background-color: #4aa0b5;
    color: #fff;
    font-weight: bold;
  }
}
</style>
