<template>
  <el-select placeholder="请选择装载方式" v-model="loadingMethodId" class="w-100">
    <el-option v-for="m in loadingMethods" :key="m.id" :value="m.id" :label="m.display_name"></el-option>
  </el-select>
</template>

<script>
export default {
  name: 'ComponentLoadingMethods',
  props: {
    value: {
      require: true,
      type: [String, Number]
    },
    productId: {
      require: true,
      type: [String, Number]
    },
    loadingMethods: {
      require: true,
      type: Array
    }
  },
  data() {
    return {
      loadingMethodId: ''
    }
  },
  watch: {
    value(value) {
      this.loadingMethodId = value
    },
    loadingMethods(value) {
      if (value.length > 0 && !value.some((m) => m.id === this.loadingMethodId)) {
        this.loadingMethodId = ''
      }

      if (value.length > 0 && !this.loadingMethodId) {
        this.loadingMethodId = value[0].id
      }
    },
    loadingMethodId(value) {
      this.$emit('input', value)
    }
  }
}
</script>
