<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 15:20:51
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-11 09:52:20
-->
<template>
  <div>
    <search-panel
      size="small"
      :custom="searchFields"
      @change="(data) => (searchData = data)"
      @command="handleSearch"
    ></search-panel>
  </div>
</template>

<script>
import { getProductsCompanies } from '@/apis/product'
import { getUsersDict } from '@/apis/user'
import { mapGetters } from 'vuex'

export default {
  name: 'PolicySearch',
  props: {
    statuses: {
      type: Array,
      default: () => []
    },
    hideCargoFields: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      baseSearchFields: [
        {
          type: 'daterange',
          valKey: 'submitted_at_range',
          hintText: '投保'
        },
        {
          type: 'daterange',
          valKey: 'issued_at_range',
          hintText: '出单'
        },
        {
          type: 'input',
          valKey: 'order_no',
          hintText: '流水号'
        },
        {
          type: 'input',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'series_no',
          hintText: '发票号/提(运)单号'
        },
        {
          type: 'input',
          valKey: 'sticky_note',
          hintText: '工作编号'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保险人'
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '保单状态',
          options: this.statuses
        }
      ],
      rawCompanies: [],
      searchData: {}
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    searchFields() {
      const fields = this.baseSearchFields
      if (this.user.is_agent && fields.findIndex((e) => e.valKey === 'user_id') === -1) {
        fields.push({
          type: 'select',
          valKey: 'user_id',
          hintText: '投保用户',
          options: []
        })

        this.fetchUsers()
      }

      if (this.hideCargoFields) {
        ;['series_no', 'sticky_note'].forEach((key) => {
          const index = fields.findIndex((e) => e.valKey === key)
          if (index !== -1) {
            fields.splice(index, 1)
          }
        })
      }

      return fields
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    },
    statuses: {
      deep: true,
      immediate: true,
      handler(value) {
        this.assignSelectOptions('status', value)
      }
    }
  },
  created() {
    getProductsCompanies().then((r) => {
      this.rawCompanies = r.data
      this.loadCompanies()
      this.loadCompanyBranches()
    })

    if (this.user.is_agent) {
      this.fetchUsers()
    }
  },
  methods: {
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '') {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleSearch(command, data) {
      this.searchData = data

      this.$emit('search', Object.assign({}, this.searchData))
    },
    fetchUsers() {
      getUsersDict().then((r) => {
        const users = r.data.map((item) => {
          return {
            value: item.id,
            label: item.name
          }
        })

        users.push({
          value: this.user.id,
          label: this.user.name
        })

        this.assignSelectOptions('user_id', users)
      })
    }
  }
}
</script>
