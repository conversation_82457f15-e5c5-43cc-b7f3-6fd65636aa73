<!--
 * @Descripttion:
 * @Author: Mr. z<PERSON>
 * @Date: 2021-01-11 11:06:11
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-11 14:40:02
-->
<template>
  <el-form class="policy-product bg-white" label-position="top">
    <div class="p-extra-large-x p-extra-large-y">
      <template v-if="mainClauses.length > 0">
        <el-form-item required label="请选择主险">
          <el-checkbox-group :disabled="disabled" v-model="mainClauseId" @change="handleSelectMainClause">
            <el-checkbox v-for="(product, idx) in mainClauses" :key="idx" :true-label="product.id" :false-label="0">
              <el-popover width="300" placement="top-start" title="条款内容" trigger="hover">
                <p>{{ product.content }}</p>
                <p v-if="product.clause_file">
                  <el-link :href="product.clause_file" target="_blank" download>下载条款文件</el-link>
                </p>
                <span slot="reference">{{ product.name }}</span>
              </el-popover>
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="请选择附加险" required>
          <el-checkbox-group
            v-if="additionalClauses.length > 0"
            :disabled="!mainClauseId || disabled"
            v-model="additionalClauseIds"
            @change="handleSelectAdditionalClause"
          >
            <el-checkbox
              v-for="(clause, idx) in additionalClauses"
              :key="`additional_clause_${idx}_${mainClauseId}_${clause.id}`"
              :label="clause.id"
            >
              <el-popover width="300" placement="top-start" title="条款内容" trigger="hover">
                <p>{{ clause.content }}</p>
                <p v-if="clause.clause_file">
                  <el-link :href="clause.clause_file" target="_blank" download>下载条款文件</el-link>
                </p>
                <span slot="reference">{{ clause.name }}</span>
              </el-popover>
            </el-checkbox>
          </el-checkbox-group>
          <template v-else>
            <el-alert title="当前主附险下暂无可选择附加险，请联系管理员开通。" type="warning" :closable="false" />
          </template>
        </el-form-item>

        <el-form-item required label="请选择出单公司">
          <el-checkbox-group
            v-if="companyBranches.length > 0"
            :disabled="!mainClauseId || additionalClauseIds.length <= 0 || disabled"
            v-model="productId"
          >
            <el-checkbox
              v-for="(com, idx) in companyBranches"
              :key="`com_${idx}_${mainClauseId}_${com.id}`"
              :true-label="com.id"
              :false-label="0"
            >
              {{ com.company_branch }}
            </el-checkbox>
          </el-checkbox-group>
          <template v-else>
            <el-alert title="当前主附险下暂无可投保的产品，请联系管理员开通。" type="warning" :closable="false" />
          </template>
        </el-form-item>
      </template>
      <template v-else>
        <el-alert title="当前暂无可投保的产品，请联系管理员开通。" type="warning" :closable="false" />
      </template>
    </div>

    <div class="selected-content" v-if="mainClauseId && productId && currentProduct !== undefined">
      <el-alert type="warning" :closable="false">
        <el-row>
          <el-col :span="12">
            <div class="field">
              <label>费率</label>
              <span>{{ currentProduct.user_rate }}‱</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field">
              <label>最低保费</label>
              <span>{{ currentProduct.minimum_premium }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <div class="field">
              <label>最高保险金额</label>
              <span>{{ currentProduct.additional.coverage }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field">
              <label>禁用区域</label>
              <span>{{ currentProduct.additional.disabled_regions || '无' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="field">
              <label>免赔</label>
              <span>{{ currentProduct.additional.deductible || '无' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="field">
              <label>特别约定</label>
              <span>
                {{ currentProduct.additional.special_agreement || '无' }}
              </span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="field">
              <label>除外货物</label>
              <span>
                {{ currentProduct.additional.except_subject || '无' }}
              </span>
            </div>
          </el-col>
        </el-row>
      </el-alert>
    </div>
  </el-form>
</template>

<script>
import { arraysEqual } from '@/utils'

export default {
  name: 'PolicyLbtProduct',
  props: {
    products: {
      require: true,
      type: Object,
      default: () => {}
    },
    product: {
      type: Number,
      require: true,
      default: -1
    }
  },
  data() {
    return {
      // 主险
      mainClauseId: '',
      // 附加险
      additionalClauseIds: [],
      // 产品
      productId: ''
    }
  },
  computed: {
    disabled() {
      return this.$route.query.from === 'edit'
    },
    currentProduct() {
      return this.products?.products?.find((e) => e.id === this.productId)
    },
    mainClauses() {
      return this.products.main_clauses || []
    },
    // 附加险
    additionalClauses() {
      let additionalClauses = this.products.additional_clauses
      // // 如果主险为空 显示所有附加险
      if (!this.mainClauseId) {
        return additionalClauses
      }

      const mainClause = this.mainClauses.find((e) => e.id === this.mainClauseId)

      return additionalClauses.filter((item) => item.parent_ids.some((r) => mainClause.ids.includes(r))) || []
    },
    // 保险公司
    companyBranches() {
      let products = this.products.products
      // 如果主险为空 显示所有保险公司, 并去重
      if (!this.mainClauseId && this.additionalClauseIds.length <= 0) {
        return products.filter(
          (value, index) => index === products.findIndex((e) => e.company_branch === value.company_branch)
        )
      }

      // 如果主险不为空 附加险为空 显示当前主险下的所有保险公司
      if (this.mainClauseId && this.additionalClauseIds.length <= 0) {
        let coms = products.filter((item) => item.new_main_clause_id === this.mainClauseId) || []
        coms = coms.filter((value, index) => index === coms.findIndex((e) => e.company_branch === value.company_branch))

        return coms
      }

      // 如果附加险不为空 显示当前附加险下的所有保险公司
      if (this.additionalClauseIds.length > 0) {
        return (
          products.filter(
            (item) =>
              arraysEqual(item.new_additional_clause_ids, this.additionalClauseIds) &&
              item.new_main_clause_id === this.mainClauseId
          ) || []
        )
      }

      return products
    }
  },
  watch: {
    product(value) {
      if (value !== -1 && value !== this.productId) {
        this.productId = value
      }
    },
    productId: {
      immediate: true,
      handler(value) {
        if (value && this.additionalClauses !== undefined && (this.additionalClauses.length || 0) > 0) {
          const product = this.products?.products?.find((e) => e.id === value)
          if (product.new_additional_clause_id !== undefined) {
            this.additionalClauseId = product?.new_additional_clause_id
          }

          this.mainClauseId = product?.new_main_clause_id
        }
      }
    },
    products: {
      immediate: true,
      handler() {
        if (this.product !== -1 && this.products?.products?.find((e) => e.id === this.product)) {
          this.productId = this.product

          this.mainClauseId = this.currentProduct.new_main_clause_id
          this.additionalClauseIds = this.currentProduct.new_additional_clause_ids
        }
      }
    },
    currentProduct(value) {
      this.$emit('product-change', Object.assign({}, value))
    }
  },
  methods: {
    handleSelectMainClause() {
      this.productId = ''
      this.additionalClauseId = []
    },
    //选择附加险
    handleSelectAdditionalClause() {
      this.productId = ''
    }
  },
  mounted() {
    this.handleSelectMainClause()
    this.handleSelectAdditionalClause()
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-alert__content {
  width: 100% !important;
}

.policy-product {
  .selected-content {
    .el-alert__content {
      width: 100%;
    }
    padding: 0 15px 15px;
    .field {
      @extend .d-flex;
      font-size: 14px;
      margin-bottom: $app-size-mini;
      label {
        width: 75px;
        min-width: 75px;
        font-weight: bold;
      }
      span {
        @extend .flex-fill;
      }
    }
  }
}
</style>
