<template>
  <div>
    <el-button type="primary" icon="fas fa-upload" @click="bulkDialogVisible = true">批量投保</el-button>

    <el-dialog
      :visible.sync="bulkDialogVisible"
      title="批量投保"
      width="520px"
      @before-close="handleCloseDialog"
      destroy-on-close
    >
      <el-select v-model="companyId" placeholder="请选择保司" class="w-100">
        <el-option v-for="company in companies" :key="company.id" :label="company.name" :value="company.id" />
      </el-select>

      <div class="m-extra-large-t buttons">
        <el-button type="primary" icon="fas fa-download" @click="handleDownloadTemplate">下载模板</el-button>
        <upload-file v-model="uploadFile"></upload-file>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as policyApi from '@/apis/policy'
import { Loading } from 'element-ui'

export default {
  name: 'CbecBulk',
  props: {
    products: {
      type: Array,
      default: () => []
    },
    productType: {
      required: true,
      type: Number
    }
  },
  data() {
    return {
      bulkDialogVisible: false,
      companyId: '',
      uploadFile: ''
    }
  },
  computed: {
    companies() {
      const companies = {}
      this.products.forEach((product) => {
        product.companies.forEach((company) => {
          companies[company.id] = {
            id: company.id,
            name: company.name
          }
        })
      })

      return Object.values(companies)
    }
  },
  watch: {
    uploadFile(value) {
      if (!value) return

      this.upload(value)
    }
  },
  methods: {
    upload(file) {
      if (!this.companyId) return this.$message.error('请选择保司')
      this.$confirm('确定要上传吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const loading = Loading.service({ lock: true, text: '上传中...' })
        try {
          const data = await policyApi.importCbec({ type: this.productType, company_branch_id: this.companyId, file })
          if (data.data.successful) {
            this.$message.success('导入成功')
            loading.close()
            this.$emit('success')
            this.handleCloseDialog()
          } else {
            loading.close()
            this.$message.error(
              '导入数据失败，已为您自动下载包含错误信息的导入文件，请根据返回的错误信息修改后重新上传'
            )

            const linkSource = `data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,${data.data.error_file}`
            const downloadLink = document.createElement('a')
            downloadLink.href = linkSource
            downloadLink.download = '批量投保导入数据含错误信息.xlsx'
            downloadLink.click()
          }
        } catch (error) {
          this.$message.error(error.message)
          this.handleCloseDialog()
        }
      })
    },
    handleDownloadTemplate() {
      if (!this.companyId) return this.$message.error('请选择保司')

      const href = policyApi.downloadCbecImportTemplate({
        company_branch_id: this.companyId,
        type: this.productType
      })

      window.open(href)
    },
    handleCloseDialog() {
      this.companyId = ''
      this.uploadFile = ''
      this.bulkDialogVisible = false
      this.$forceUpdate()
    }
  }
}
</script>

<style lang="scss">
.buttons {
  display: flex;
  gap: 20px;
}
</style>
