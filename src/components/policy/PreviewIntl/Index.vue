<template>
  <div id="preview-body" class="policy-wrap">
    <el-card
      shadow="never"
      class="define-card my-4 shadow-sm"
      :body-style="{ padding: '0' }"
      v-if="product.id !== undefined"
    >
      <div class="preview-header">
        <el-row class="flex heading">
          <el-col>保险公司</el-col>
          <el-col>保险产品</el-col>
          <el-col>保险金额 ({{ data.coverage_currency.name }})</el-col>
          <el-col>费率（‱万分之）</el-col>
          <el-col>保费（元）</el-col>
        </el-row>
        <el-row class="flex">
          <el-col>{{ product.company_branch }}</el-col>
          <el-col>{{ product.name }}</el-col>
          <el-col v-html="viewData.coverage"></el-col>
          <el-col v-html="viewData.user_rate"></el-col>
          <el-col>{{ data?.user_premium?.toFixed(2) }}</el-col>
        </el-row>
      </div>
    </el-card>

    <div class="p-extra-large m-extra-large-t bg-white flex-fill o-hidden o-y-auto">
      <define-details :data="detail" />
    </div>
    <el-card shadow="never" class="define-card my-4 shadow-sm m-extra-large-t">
      <center>
        <el-row>
          <el-button icon="el-icon-back" @click="$emit('on-back')">返回修改</el-button>
          <el-button icon="el-icon-download" @click="handleDownloadConfirmationPdf()">
            {{ $route.query.from !== 'edit' ? '下载投保单确认件并暂存' : '下载投保单确认件' }}
          </el-button>
          <el-button type="primary" icon="el-icon-circle-check" @click="handleSubmit()">
            {{ $route.query.from === 'edit' ? '提交批改' : '立即投保' }}
          </el-button>
        </el-row>
      </center>
    </el-card>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { downloadConfirmationLetter } from '@/apis/policy'
import { mapGetters } from 'vuex'

export default {
  name: 'PreviewIntl',
  props: {
    product: {
      type: Object,
      default: () => {}
    },
    data: {
      type: Object,
      default: () => {}
    },
    originData: {
      type: Object,
      default: () => {}
    },
    paymentMethod: {
      type: Number,
      default: 1
    }
  },
  computed: {
    ...mapGetters('platform', ['online_payment_is_enabled']),
    ...mapGetters('auth', ['user']),
    canOnlinePayment() {
      return (
        this.online_payment_is_enabled == 1 && this.user.is_online_payment == 1 && Number(this.data.subject_id) !== 3
      )
    },
    shippingDatePrintFormat() {
      return parseInt(this.data?.shipping_date_print_format, 10) === 1
        ? dayjs(this.data?.shipping_date).format('MMM.DD, YYYY')
        : 'AS PER B/L'
    },
    viewData() {
      if (this.$route.query.from !== 'edit') {
        return this.data
      }

      let viewData = {}
      Object.keys(this.data).forEach((key) => {
        if (!(typeof this.data[key] === 'object')) {
          if (this.data[key] !== this.originData?.[key]) {
            viewData[key] = `<span style="color: red;">${this.data[key]}</span>`
          } else {
            viewData[key] = this.data[key]
          }
        } else {
          if (this.data[key]?.id !== this.originData?.[key]?.id) {
            viewData[key] = {
              id: this.data[key].id,
              name: `<span style="color: red;">${this.data[key]?.name}</span>`
            }
          } else {
            viewData[key] = this.data[key]
          }
        }
      })

      return viewData
    },
    detail() {
      let clientData = [
        { label: '投保人', value: this.viewData.policyholder },
        { label: '被保人', value: this.viewData.insured },
        { label: '投保人地址', value: this.viewData.policyholder_address },
        { label: '被保人地址', value: this.viewData.insured_address },
        { label: '投保人电话', value: this.viewData.policyholder_phone_number },
        { label: '被保人电话', value: this.viewData.insured_phone_number }
      ]
      if (['PICC', 'CPIC'].includes(this.product.company.identifier)) {
        clientData.push(
          { label: '投保人类型', value: this.data.policyholder_type === 1 ? '个人客户' : '团体客户' },
          { label: '被保人类型', value: this.data.insured_type === 1 ? '个人客户' : '团体客户' },
          { label: '投保人证件号', value: this.viewData.policyholder_idcard_no },
          { label: '被保人证件号', value: this.viewData.insured_idcard_no }
        )
      }
      return {
        title: ' ',
        data: [
          {
            title: '保险内容',
            groups: [
              { label: '主条款', value: this.viewData?.clauses?.main, row: true },
              { label: '附加险', value: this.viewData?.clauses?.additional, row: true },
              {
                label: '免赔',
                value: this.viewData?.deductible ? this.viewData.deductible : this.product.additional?.deductible,
                row: true
              },
              {
                label: '特别约定',
                value: this.viewData?.special_agreement
                  ? this.viewData.special_agreement
                  : this.product.additional?.special_agreement,
                row: true
              },
              { label: '是否做信用证', value: parseInt(this.data?.is_credit, 10) ? '是' : '否' },
              { label: '信用证号', value: this.viewData?.credit_no },
              {
                label: '条款内容',
                value: `<pre>${this.viewData?.clause_content}</pre>`,
                row: true
              },
              {
                label: '制裁国家/地区条款',
                row: true,
                hide: !this.viewData?.is_sanctionist,
                value: this.product.additional?.sanctionist_clause
              },
              {
                label: '保障到港条款',
                row: true,
                hide: !this.viewData?.is_only_port,
                value: this.product.additional?.only_port_clause
              }
            ]
          },
          {
            title: '基本信息',
            groups: clientData
          },
          {
            title: '货物信息',
            groups: [
              { label: '货物类别', value: this.viewData?.goods_type.name },
              { label: '装载方式', value: this.viewData?.loading_method.name },
              { label: '运输方式', value: this.viewData?.transport_method.name },
              { label: '包装方式', value: this.viewData?.packing_method.name },
              { label: '货物名称', value: `<pre>${this.viewData?.goods_name}</pre>` },
              { label: '包装件数', value: `<pre>${this.viewData?.goods_amount}</pre>` },
              { label: '唛头', value: `<pre>${this.viewData?.shipping_mark || ''}</pre>`, row: true }
            ]
          },
          {
            title: '运输信息',
            groups: [
              { label: '提/运单号', value: this.viewData?.waybill_no },
              { label: '发票号', value: this.viewData?.invoice_no },
              { label: '合同号', value: this.viewData?.contract_no },
              { label: '信用证号', value: this.viewData?.credit_no, hide: !this.viewData?.is_credit },
              { label: '运输工具号', value: this.viewData?.transport_no },
              { label: '起运日期', value: this.viewData?.shipping_date },
              { label: '起运日期打印格式', value: this.shippingDatePrintFormat },
              { label: '起运地(国/地区)', value: this.viewData?.departure },
              { label: '起运地', value: this.viewData?.departure_port },
              { label: '目的地(国/地区)', value: this.viewData?.destination },
              { label: '目的地', value: this.viewData?.destination_port },
              { label: '中转地', value: this.viewData?.transmit },
              { label: '赔付地', value: this.viewData?.payable_at }
            ]
          }
        ]
      }
    },
    confirmationPdfData() {
      return [
        {
          title: '被保险人信息',
          dataset: [
            { label: '被保险人', subtitle: 'Insured', value: this.viewData?.insured },
            { label: '被保险人通讯地址', subtitle: 'Address', value: this.viewData?.insured_address },
            { label: '被保险人电话', subtitle: 'Office Phone or Mobile', value: this.viewData?.insured_phone_number }
          ]
        },
        {
          title: '货物信息',
          dataset: [
            { label: '唛头', subtitle: 'Marks', value: this.viewData?.shipping_mark || '', wrap: true },
            {
              label: '货物名称',
              subtitle: 'Description',
              value: this.viewData?.goods_name,
              wrap: true
            },
            {
              label: '数量单位',
              subtitle: 'Quantity',
              value: this.viewData?.goods_amount,
              wrap: true
            },
            {
              label: '发票金额',
              subtitle: 'Invoice Amount',
              value: this.viewData?.invoice_amount + this.data.coverage_currency.name
            },
            {
              label: '保险金额',
              subtitle: 'Amount Insured',
              value: this.viewData?.coverage + this.data.coverage_currency.name
            },
            { label: '发票号', subtitle: 'Invoice Number', value: this.viewData?.invoice_no },
            { label: '提单/运单号', subtitle: 'WAYBILL NO OR B/L NO', value: this.viewData?.waybill_no },
            { label: '合同号', subtitle: 'Contract No', value: this.viewData?.contract_no },
            { label: '信用证号', subtitle: 'Credit No', value: this.viewData?.credit_no },
            { label: '船名/车号', subtitle: 'Per Conveyance S.S.', value: this.viewData?.transport_no },
            { label: '起运地', subtitle: 'From', value: this.viewData?.departure_port },
            { label: '转运地', subtitle: 'Via', value: this.viewData?.transmit },
            { label: '目的地', subtitle: 'To', value: this.viewData?.destination_port },
            { label: '赔付地点', subtitle: 'Claim Payable at', value: this.viewData?.payable_at },
            { label: '起运日期', subtitle: 'Slg. on or abt', value: this.viewData?.shipping_date }
          ]
        },
        {
          title: '保险条件',
          dataset: [
            {
              label: '免赔',
              subtitle: 'Deductible',
              value: this.viewData?.deductible,
              wrap: true
            },
            {
              label: '特别约定',
              subtitle: 'Special Agreement',
              value: this.viewData?.special_agreement,
              wrap: true
            },
            {
              label: '条款内容',
              subtitle: 'Conditions',
              value: this.viewData?.clause_content,
              wrap: true
            },
            {
              label: '制裁国家/地区条款',
              subtitle: 'Sanctionist Conditions',
              value: this.viewData?.is_sanctionist ? this.product.additional?.sanctionist_clause : ''
            },
            {
              label: '保障到港条款',
              subtitle: 'Port Conditions',
              value: this.viewData?.is_only_port ? this.product.additional?.only_port_clause : ''
            }
          ]
        }
      ]
    }
  },
  methods: {
    async handleDownloadConfirmationPdf() {
      const data = await downloadConfirmationLetter({
        data: this.confirmationPdfData
      })
      const linkSource = `data:application/pdf;base64,${data.data}`
      const downloadLink = document.createElement('a')
      downloadLink.href = linkSource
      downloadLink.download = `${this.data?.insured}.pdf`
      downloadLink.click()

      if (this.$route.query.from !== 'edit') {
        this.$emit('on-save', {}, this.data)
      }
    },
    handleSubmit() {
      // 修改和不支持在线支付时,直接走余额支付流程
      if (this.$route.query.from === 'edit' || !this.canOnlinePayment) {
        this.$emit('on-submit')
        return
      }
      this.$confirm('请选择支付方式', '选择支付方式', {
        distinguishCancelAndClose: true,
        confirmButtonText: '余额支付',
        cancelButtonText: '在线支付'
      })
        .then(() => {
          this.$emit('update:paymentMethod', 1)
          this.$emit('on-submit')
        })
        .catch((action) => {
          if (action === 'cancel') {
            this.$emit('update:paymentMethod', 2)
            this.$emit('on-submit')
          }
        })
      // this.$emit('on-submit')
    }
  }
}
</script>

<style lang="scss" scoped>
.confirmation-pdf-content {
  /deep/ .data-label-text {
    width: 350px !important;
  }
}
.preview-header {
  .flex {
    display: flex;
    flex: 1;
    text-align: left;
    height: 40px;
    line-height: 40px;

    .el-col {
      padding-left: 10px;
    }
  }

  .heading {
    background-color: #4aa0b5;
    color: #fff;
    font-weight: bold;
  }
}
</style>
