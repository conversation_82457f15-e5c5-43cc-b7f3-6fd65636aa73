<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-11 11:06:11
 * @LastEditors: yanb
 * @LastEditTime: 2024-07-17 10:01:56
-->
<template>
  <el-form class="policy-product bg-white" label-position="top">
    <div style="padding: 1rem">
      <el-form-item required>
        <template #label>
          <!-- // eslint-disable-next-line prettier/prettier -->
          <span>货物类别（<span class="text-danger">请正确选择货物类别。若不属于下列货物，请选择“人工审核”投保或联系业务人员</span>）</span>
        </template>
        <el-checkbox-group v-model="subjectId">
          <el-checkbox v-for="s in subjects" :key="s.id" :true-label="s.id" :false-label="0">
            <el-popover v-if="s.description" width="300" placement="top-start" :title="s.name" trigger="hover">
              <p>{{ s.description }}</p>
              <span slot="reference" :class="{ 'text-danger': s.id === 3 }">{{ s.name }}</span>
            </el-popover>
            <template v-else>
              <span :class="{ 'text-danger': s.id === 3 }">{{ s.name }}</span>
            </template>
          </el-checkbox>
        </el-checkbox-group>

        <el-alert v-if="subjectDescription" type="error" :title="subjectDescription" :closable="false" />
      </el-form-item>

      <template v-if="products.length > 0">
        <template v-if="subjectId === 3">
          <el-form-item required label="请选择人工审核原因">
            <el-checkbox-group :disabled="disabled" v-model="subjectCategories">
              <el-checkbox
                v-for="(m, idx) in subjects?.find((e) => e.id === subjectId)?.categories"
                :key="idx"
                :label="m.id"
              >
                {{ m.name }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item required label="请填写承保费率条件">
            <el-form-item prop="manualConditionsContent">
              <el-input
                v-model="manualConditionsContent"
                placeholder="请输入承保费率条件"
                type="textarea"
                autoresize
                :rows="3"
                clearable
              ></el-input>
            </el-form-item>
          </el-form-item>
        </template>

        <el-form-item required label="请选择出单公司">
          <el-checkbox-group v-model="productId" :disabled="disabled">
            <el-checkbox
              border
              :label="product.id"
              v-for="(product, idx) in products"
              :key="idx"
              :true-label="product.id"
              :false-label="0"
            >
              {{ product.company_branch }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </template>
      <template v-if="products.length === 0 && subjectId !== -1">
        <el-alert title="当前标的下暂无可投保的产品，请联系管理员开通。" type="warning" :closable="false" />
      </template>
    </div>

    <div class="selected-content" v-if="productId && currentProduct !== undefined">
      <el-alert type="warning" :closable="false">
        <el-row>
          <el-col :span="12">
            <div class="field">
              <label>费率</label>
              <span>{{ currentProduct.user_rate }}‱</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field">
              <label>最低保费</label>
              <span>{{ currentProduct.minimum_premium | toLocaleCurrency }}元</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <div class="field">
              <label>最高保险金额</label>
              <span>{{ currentProduct.additional.coverage | toLocaleCurrency }}元</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field">
              <label>禁用区域</label>
              <span>{{ currentProduct.additional.disabled_regions || '无' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="field">
              <label>免赔</label>
              <span>{{ currentProduct.additional.deductible || '无' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="field">
              <label>特别约定</label>
              <span>
                {{ currentProduct.additional.special_agreement || '无' }}
              </span>
            </div>
          </el-col>
        </el-row>
        <el-row v-if="subjectId !== 2">
          <el-col :span="24">
            <div class="field">
              <label>除外货物</label>
              <span>
                {{ currentProduct.additional.except_subject || '无' }}
              </span>
            </div>
          </el-col>
        </el-row>
      </el-alert>
      <!-- 人工审核产品不需要 -->
      <template v-if="subjectId === 2">
        <div class="m-extra-large-t">
          <label class="p-alert text-danger">
            本投保界面仅适用于普通货物，若为下列特殊货物投保，请选择其他货物类别或选择人工审核方式为货物投保。
          </label>
        </div>
        <el-form-item label="投保货物是否为下列特殊货物" style="margin-top: 0; margin-left: 10px">
          <el-radio :disabled="disabled" v-model="isExceptGoods" :label="1">是</el-radio>
          <el-radio :disabled="disabled" v-model="isExceptGoods" :label="0">否</el-radio>
        </el-form-item>
        <div style="font-size: 14px; color: red">{{ currentProduct.additional.except_subject || '无' }}</div>
      </template>
    </div>
  </el-form>
</template>

<script>
export default {
  name: 'PolicyIntlProduct',
  props: {
    subjects: {
      require: true,
      type: Array,
      default: () => []
    },
    products: {
      require: true,
      type: Array,
      default: () => []
    },
    product: {
      type: Number,
      require: true,
      default: -1
    },
    subject: {
      type: Number,
      default: -1
    },
    subjectCategoryIds: {
      type: Array,
      default: () => []
    },
    manualConditions: {
      type: String,
      default: ''
    }
  },
  filters: {
    toLocaleCurrency(currency) {
      return parseFloat(currency).toLocaleString('zh-CN', {
        style: 'currency',
        currency: 'CNY'
      })
    }
  },
  data() {
    return {
      // 标的
      subjectId: -1,
      // 人工审核原因
      subjectCategories: [],
      // 产品
      productId: -1,
      // 承保费率条件
      manualConditionsContent: '',
      // 是否除外货物
      isExceptGoods: ''
    }
  },
  computed: {
    subjectDescription() {
      if (this.subjectId === -1) {
        return ''
      }
      const subject = this.subjects.find((s) => s.id === this.subjectId)
      if (subject && subject.description) {
        return subject.name + '：' + subject.description
      }
      return ''
    },
    disabled() {
      return this.$route.query.from === 'edit'
    },
    currentProduct() {
      return this.products?.find((e) => e.id === this.productId)
    }
  },
  watch: {
    product(value) {
      if (value !== -1 && value !== this.productId) {
        this.productId = value
      }
    },
    productId(value, oldValue) {
      if ((oldValue !== -1 && value !== oldValue) || !['edit', 'continue'].includes(this.$route.query.from)) {
        if (this.subjectId !== 2 && this.subjectId !== -1) {
          this.isExceptGoods = 0
        } else {
          this.isExceptGoods = ''
        }
      }
    },
    subject(value) {
      if (value !== -1 && value !== this.subjectId) {
        this.subjectId = value
      }
    },
    subjectId(value) {
      if (value !== 2 && value !== -1) {
        this.isExceptGoods = 0
      }
      //获取产品选项卡的id
      this.$emit('subject-change', value)
    },
    currentProduct(value) {
      this.$emit('product-change', Object.assign({}, value))
    },
    isExceptGoods(value) {
      if (value) {
        this.$message.error('请正确选择货物类别投保或联系业务人员')
      }
      this.$emit('update:isExceptGoods', value)
    },
    subjectCategories(value) {
      this.$emit('update:subjectCategoryIds', value)
    },
    subjectCategoryIds(value) {
      if (JSON.stringify(value) !== JSON.stringify(this.subjectCategories)) {
        this.subjectCategories = value
      }
    },
    manualConditionsContent(value) {
      this.$emit('update:manualConditions', value)
    },
    manualConditions(value) {
      if (value !== this.manualConditionsContent) {
        this.manualConditionsContent = value
      }
    }
  },
  mounted() {
    if ((this.subjectId !== 2 && this.subjectId !== -1) || ['edit', 'continue'].includes(this.$route.query.from)) {
      this.isExceptGoods = 0
    }

    this.subjectCategories = this.subjectCategoryIds

    this.$emit('update:isExceptGoods', this.isExceptGoods)
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-alert__content {
  width: 100% !important;
}

/deep/ .el-tabs__item {
  padding: 0 15px;
}

.p-alert::before {
  content: '*';
  color: #f56c6c;
}

.policy-product {
  border: 1px solid #ebeef5;
  /deep/ .el-tabs {
    background-color: transparent;
    box-shadow: none;
    border: none;
    .el-checkbox-group {
      .el-checkbox.is-bordered {
        margin-right: 0;
        .el-checkbox__input {
          display: none;
        }
        .el-checkbox__label {
          padding-left: 5px;
        }
      }
    }
  }
  .selected-content {
    padding: 0 15px 15px;
    .el-alert__content {
      width: 100%;
    }

    .field {
      @extend .d-flex;
      font-size: 14px;
      margin-bottom: $app-size-mini;
      label {
        width: 100px;
        min-width: 100px;
        font-weight: bold;
      }
      span {
        @extend .flex-fill;
      }
    }
  }
}
</style>
