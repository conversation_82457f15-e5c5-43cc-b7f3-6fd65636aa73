<template>
  <el-dialog
    :visible.sync="visible"
    title="批量导入"
    destory-on-close
    :before-close="() => $emit('update:visible', false)"
    width="520px"
    :close-on-click-modal="false"
  >
    <el-form ref="form" :model="form" :rules="rules" label-suffix=":" label-position="top">
      <p>请先 <el-link type="primary" :href="download">下载人员模板</el-link> 进行填写</p>
      <el-form-item prop="staffs" label="人员模板">
        <upload-file v-model="form.staffs" />
      </el-form-item>
      <el-form-item>
        <el-button @click="() => $emit('update:visible', false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script>
export default {
  name: 'EmployeeImportForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    download: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      form: {
        staffs: ''
      },
      rules: {
        staffs: [{ required: true, message: '请上传文件', trigger: ['change', 'blur'] }]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)
          this.$emit('update:visible', false)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
