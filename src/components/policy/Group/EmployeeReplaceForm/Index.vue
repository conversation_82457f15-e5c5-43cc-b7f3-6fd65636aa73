<template>
  <el-dialog
    :visible.sync="visible"
    title="人员替换"
    destory-on-close
    :before-close="() => $emit('update:visible', false)"
    width="520px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-suffix=":">
      <el-form-item prop="name" label="姓名">
        <el-input v-model="form.name" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item prop="idcard_no" label="身份证号码">
        <el-input v-model="form.idcard_no" placeholder="请输入请输入身份证号码" />
      </el-form-item>
      <el-form-item prop="mobile" label="手机号码">
        <el-input v-model="form.mobile" placeholder="请输入请输入手机号码" />
      </el-form-item>
      <el-form-item>
        <el-button @click="() => $emit('update:visible', false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script>
export default {
  name: 'EmployeeReplaceForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        name: '',
        mobile: '',
        idcard_no: ''
      },
      rules: {
        name: [{ required: true, message: '请填写姓名', trigger: 'blur' }],
        mobile: [{ required: false, message: '请填写手机号码', trigger: 'blur' }],
        idcard_no: [{ required: true, message: '请填写身份证号码', trigger: 'blur' }]
      }
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(value) {
        if (value !== undefined) {
          this.form = Object.assign({}, value)
        }
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)
          this.$emit('update:visible', false)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
