<template>
  <el-dialog
    :visible.sync="visible"
    title="添加人员"
    destory-on-close
    :before-close="() => $emit('update:visible', false)"
    width="520px"
    :close-on-click-modal="false"
  >
    <el-form ref="form" :model="form" :rules="rules" label-suffix=":">
      <el-form-item prop="name" label="姓名">
        <el-input v-model="form.name" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item prop="idcard_no" label="身份证号码">
        <el-input v-model="form.idcard_no" placeholder="请输入请输入身份证号码" />
      </el-form-item>
      <el-form-item prop="mobile" label="手机号码">
        <el-input v-model="form.mobile" placeholder="请输入请输入手机号码" />
      </el-form-item>
      <el-form-item prop="job_id" label="职位类别">
        <el-select class="w-100" v-model="form.job_id" placeholder="请选择职位类别" filterable>
          <el-option v-for="job in jobs" :key="job.id" :value="job.id" :label="job.code + '--' + job.name" />
        </el-select>
      </el-form-item>
      <el-form-item prop="job_name" label="职位名称">
        <el-input v-model="form.job_name" placeholder="请输入请输入职位名称" />
      </el-form-item>
      <el-form-item>
        <el-button @click="() => $emit('update:visible', false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script>
export default {
  name: 'EmployeeForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    jobs: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    'form.job_id'(value) {
      if (value) {
        this.form.job_name = this.jobs.find((e) => e.id === value)?.name
      }
    }
  },
  data() {
    return {
      form: {
        action: 'append',
        name: '',
        mobile: '',
        idcard_no: '',
        job_name: '',
        job_id: ''
      },
      rules: {
        name: [{ required: true, message: '请填写姓名', trigger: 'blur' }],
        mobile: [{ required: false, message: '请填写手机号码', trigger: 'blur' }],
        idcard_no: [{ required: true, message: '请填写身份证号码', trigger: 'blur' }],
        job_name: [{ required: true, message: '请填写职位', trigger: 'blur' }],
        job_id: [{ required: true, message: '请选择职位类别', trigger: ['change', 'blur'] }]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)
        }
      })
    },
    resetFields() {
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
