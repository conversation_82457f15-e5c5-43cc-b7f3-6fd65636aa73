<template>
  <el-dialog
    :visible.sync="visible"
    title="计算保费"
    destory-on-close
    :before-close="() => $emit('update:visible', false)"
    width="600px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-suffix=":" label-position="top">
      <p>
        需要{{ premium >= 0 ? '支付' : '退回' }}保费: <b>{{ premium }}</b> 元
      </p>
      <el-form-item prop="file" label="支付凭证" v-if="premium > 0 && paymentType !== 2">
        <upload-file v-model="form.transaction_file" />
      </el-form-item>
      <el-form-item v-if="paymentType === 2">
        <el-alert
          v-if="premium > 0"
          title="温馨提示"
          description="该产品为见费出单，提交审核通过后需要完成在线支付方可生效。"
          type="warning"
          :closable="false"
        ></el-alert>
        <template v-if="premium < 0">
          <el-alert
            title="温馨提示"
            :description="paymentMethod !== 1 ? '请输入投保人或被保人收款信息' : '请输入原付款人信息'"
            show-icon
            type="warning"
            :closable="false"
          ></el-alert>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="收款人" prop="accountName">
                <el-input v-model="form.accountName" placeholder="请输入收款人名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="银行卡号" prop="accountcode">
                <el-input v-model="form.accountcode" placeholder="请输入银行卡号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="收款银行" prop="bankcode">
                <el-select v-model="form.bankcode" placeholder="请选择收款银行" class="w-100" filterable>
                  <el-option v-for="(bank, bankCode) in banks" :key="bankCode" :value="bankCode" :label="bank" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户地址" prop="bankAddress">
                <div class="d-flex w-100">
                  <el-cascader
                    filterable
                    v-model="form.bankAddress"
                    :style="{ width: '500px' }"
                    :options="regions"
                    :props="{
                      expandTrigger: 'hover',
                      filterable: true,
                      value: 'code',
                      label: 'value',
                      children: 'city'
                    }"
                  ></el-cascader>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="开户行名称" prop="customBankName">
                <el-input v-model="form.customBankName" placeholder="请输入开户行"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="银联行号" prop="customBankCode">
                <el-input v-model="form.customBankCode" placeholder="请输入银联行号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="付款备注" prop="compensateRemark">
            <el-input v-model="form.compensateRemark" placeholder="请输入付款备注"></el-input>
          </el-form-item>
        </template>
      </el-form-item>
      <el-form-item prop="endorse_stamp_file" v-if="isEndorse">
        <template #label>
          <span>批单盖章文件:</span>
          <a
            style="margin-left: 10px"
            v-if="revisionTemplateFile"
            :href="revisionTemplateFile"
            target="_blank"
            download
          >
            <el-button type="primary" plain size="mini" icon="fas fa-download"> 下载批单申请书模板 </el-button>
          </a>
        </template>
        <upload-file v-model="form.endorse_stamp_file" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="() => $emit('update:visible', false)" icon="fas fa-times">取消</el-button>
      <el-button type="primary" @click="handleSubmit" icon="fas fa-check">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
import banks from './banks'
import regions from '@/utils/areadata'

export default {
  name: 'PremiumForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    premium: {
      type: String,
      default: () => ''
    },
    isEndorse: {
      type: Boolean,
      default: false
    },
    paymentType: {
      type: Number,
      default: 2
    },
    paymentMethod: {
      type: Number,
      default: -1
    },
    revisionTemplateFile: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      banks,
      regions,
      form: {
        transaction_file: '',
        endorse_stamp_file: '',
        /* 中意退款 */
        accountName: '',
        accountcode: '',
        bankcode: '',
        bankname: '',
        customBankCode: '',
        customBankName: '',
        bankAddress: '',
        bankProvince: '',
        bankCity: '',
        unionBank: '',
        compensateRemark: '批单退款',
        recareaCode: '',
        reccityName: ''
      },
      rules: {
        accountName: [{ required: true, message: '请输入收款账户开户名', trigger: 'blur' }],
        accountcode: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }],
        bankcode: [{ required: true, message: '请选择收款银行', trigger: ['blur', 'change'] }],
        customBankCode: [{ required: true, message: '请输入银行联行号', trigger: 'blur' }],
        customBankName: [{ required: true, message: '请输入开户行名称', trigger: 'blur' }],
        compensateRemark: [{ required: true, message: '请输入付款备注', trigger: 'blur' }],
        bankAddress: [{ required: true, message: '请选择开户行地址', trigger: ['blur', 'change'] }]
      }
    }
  },
  watch: {
    'form.accountcode'(value) {
      value = value.replace(' ', '')
      if (value != this.form.accountcode) {
        this.form.accountcode = value
      }
    },
    'form.bankcode'() {
      this.form.bankname = this.banks[this.form.bankcode]
    },
    'form.customBankCode'(value) {
      this.form.unionBank = value
    },
    'form.bankAddress'(value) {
      const specialCity = ['110000', '310000', '120000', '500000']
      this.form.bankProvince = value[1].slice(0, 2)

      if (specialCity.includes(value[0])) {
        this.form.bankCity = value[0].slice(0, 4)
        this.form.recareaCode = this.form.bankCity
      } else {
        this.form.bankCity = value[1].slice(0, 4)
        this.form.recareaCode = this.form.bankCity
      }

      const province = this.regions.find((item) => item.code === value[0])
      const city = province?.city?.find((item) => item.code === value[1])

      this.form.reccityName = city?.value
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)
          this.$emit('update:visible', false)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
