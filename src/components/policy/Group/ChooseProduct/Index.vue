<template>
  <div>
    <template>
      <el-card shadow="never" v-if="data.length > 0">
        <el-form class="bg-white choose-product" label-position="top">
          <el-form-item required label="请选择保险公司">
            <el-radio-group v-model="companyId">
              <el-radio v-for="com in data" border :label="com.id" :key="'com_' + com.id">
                {{ com.name }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item required label="请选择产品">
            <el-radio-group v-model="productId">
              <el-radio v-for="product in products" border :label="product.id" :key="'prod_' + product.id">
                {{ product.name }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item required label="请选择套餐">
            <el-radio-group v-model="planId" v-if="plans.length > 0">
              <el-radio v-for="plan in plans" border :label="plan.id" :key="'plan_' + plan.id">
                {{ plan.title }}
              </el-radio>
            </el-radio-group>
            <el-alert v-else title="当前产品暂无可选择的套餐，请联系管理员开通。" type="warning" :closable="false" />
          </el-form-item>
          <template v-if="product">
            <el-form-item label="保险产品资料">
              <el-link @click="downloadEmployeeTemplateExcel()"> 《人员清单模板》下载 </el-link>
              <el-link
                style="margin-left: 10px"
                v-if="product.additional.clause_file"
                :href="product.additional.clause_file"
              >
                《保险条款》下载
              </el-link>
              <el-link
                style="margin-left: 10px"
                v-if="product.additional.insured_file"
                :href="product.additional.insured_file"
              >
                《投保资料》下载
              </el-link>
              <el-link
                style="margin-left: 10px"
                v-if="product.additional.job_category_file"
                :href="product.additional.job_category_file"
              >
                《职业分类表》下载
              </el-link>
            </el-form-item>
            <el-form-item label="责任免除内容">
              <el-alert type="info" :closable="false">
                <pre class="html-body" v-html="product.additional.description || '无'"></pre>
              </el-alert>
            </el-form-item>
            <el-form-item label="产品备注">
              <el-alert type="info" :closable="false">
                <pre v-html="product.additional.remark || '无'"></pre>
              </el-alert>
            </el-form-item>
            <el-form-item v-if="hasFaqContent">
              <span slot="label">
                常见问答&nbsp;<el-button type="text" @click="showFaq = !showFaq">点击查看</el-button>
              </span>
              <template v-if="showFaq && hasFaqContent">
                <el-alert title="常见问题" type="warning" class="m-extra-large-b" :closable="false">
                  <pre v-html="product.additional.faq"></pre>
                </el-alert>
              </template>
            </el-form-item>
          </template>
        </el-form>
      </el-card>
      <el-alert v-else title="当前暂无可投保的产品，请联系管理员开通。" type="warning" :closable="false" />
    </template>

    <el-card shadow="never" class="m-extra-large-t" v-if="plan">
      <table class="product-details">
        <tr>
          <td>类目名称</td>
          <td></td>
        </tr>
        <tr>
          <td>意外身故/残疾</td>
          <td>
            <div class="html-body" v-html="plan.accidental_death"></div>
          </td>
        </tr>
        <tr>
          <td>意外医疗</td>
          <td>
            <div class="html-body" v-html="plan.accidental_medical"></div>
          </td>
        </tr>
        <tr v-if="plan.accidental_injury">
          <td>附加24小时意外伤害</td>
          <td>
            <div class="html-body" v-html="plan.accidental_injury"></div>
          </td>
        </tr>
        <tr v-if="plan.lost_wages">
          <td>误工费</td>
          <td>{{ plan.lost_wages }}</td>
        </tr>
        <tr>
          <td>意外伤害住院津贴</td>
          <td>{{ plan.accidental_allowance }}</td>
        </tr>
        <tr>
          <td>雇主法律责任</td>
          <td>{{ plan.legal_liability }}</td>
        </tr>
        <tr>
          <td>累计赔偿限额</td>
          <td>{{ plan.total_indemnity }}</td>
        </tr>
      </table>
    </el-card>

    <el-card shadow="never" class="m-extra-large-t" v-if="plan">
      <template slot="header">
        <el-input v-model="jobKeyword" placeholder="请输入关键字搜索" style="width: 200px" />
      </template>
      <define-table :attrs="{ boder: true }" :cols="cols" :data="displayJobs" />
      <div style="width: 100%; text-align: center; cursor: pointer" v-if="jobs.length > 10 && !showFullJobs">
        <a @click="showFullJobs = true">查看全部 <i icon="fas fa-arrow"></i></a>
      </div>
    </el-card>

    <el-card shadow="never" class="m-extra-large-t">
      <p class="text-center">
        <el-checkbox v-model="agreed" />&nbsp;我已详细阅读
        <el-button type="text" @click="showNotice = !showNotice">投保须知及除外事项的内容</el-button>
      </p>
      <template v-if="showNotice && product">
        <el-alert title="投保须知" type="warning" class="m-extra-large-b" :closable="false">
          <div v-html="product.additional.notice" />
        </el-alert>
      </template>
      <p class="text-center">
        <el-button type="primary" icon="fas fa-check" :disabled="!agreed" @click="submitPreview" v-if="plan">
          填写投保信息
        </el-button>
      </p>
    </el-card>
    <inform :visible.sync="informVisible" :detail="product?.additional?.inform || ''" @checking="checkingInform" />
  </div>
</template>
<script>
import { tokenKey } from '@/config'
import { mapGetters } from 'vuex'
import Inform from '@/components/policy/Inform'

export default {
  name: 'ChooseProduct',
  components: {
    Inform
  },
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      companyId: '',
      productId: '',
      planId: null,
      agreed: false,
      showNotice: false,
      showFaq: false,
      jobKeyword: '',
      showFullJobs: false,
      informVisible: false,
      cols: []
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    products() {
      return this.data.find((e) => e.id === this.companyId)?.products || []
    },
    plans() {
      return this.products.find((e) => e.id === this.productId)?.plans || []
    },
    jobs() {
      return this.plans.find((e) => e.id === this.planId)?.jobs || []
    },
    displayJobs() {
      if (this.jobKeyword) {
        return this.jobs.filter((e) => e.name.indexOf(this.jobKeyword) > -1)
      }

      if (this.jobs.length > 10 && !this.showFullJobs) {
        return this.jobs.slice(0, 10)
      }

      return this.jobs
    },
    company() {
      return this.data.find((e) => e.id === this.companyId)
    },
    product() {
      const product = this.products.find((e) => e.id === this.productId)
      const plan = product?.plans[0] || null
      if (plan) {
        this.$set(this, 'planId', plan.id)
      }
      return product
    },
    plan() {
      return this.plans.find((e) => e.id === this.planId)
    },
    hasFaqContent() {
      if (!this.product?.additional?.faq) {
        return false
      }
      return this.product.additional.faq.trim().length > 0
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler() {
        const company = this.data[0]
        if (company) {
          this.companyId = company?.id

          const product = company?.products[0] ?? null
          this.productId = product?.id ?? -1
          if (product) {
            this.planId = product?.plans[0]?.id ?? -1
          }
        }
      }
    },
    jobKeyword() {
      this.showFullJobs = true
    },
    companyId: {
      deep: true,
      immediate: true,
      handler() {
        const company = this.company
        if (company) {
          this.companyId = company?.id

          const product = company.products[0]
          this.productId = product?.id
          if (product) {
            this.planId = product.plans[0]?.id
          }
        }
      }
    },
    productId: {
      deep: true,
      immediate: true,
      handler() {
        if (this.product?.additional?.third_platform === 'API_GROUP_ZY') {
          this.cols = [
            { prop: 'code', label: '职位类别' },
            { prop: 'grade', label: '职位类别' },
            { prop: 'major_class', label: '职业大类' },
            { prop: 'name', label: '职位名称' },
            { prop: 'price', label: '价格 / 元' }
          ]
        } else {
          this.cols = [
            { prop: 'code', label: '职位类别', width: 200 },
            { prop: 'name', label: '职位名称' },
            { prop: 'price', label: '价格 / 元', width: 100 }
          ]
        }
      }
    }
  },
  methods: {
    downloadEmployeeTemplateExcel() {
      if (this.planId) {
        let baseUrl = process.env.VUE_APP_BASE_API
        if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
          baseUrl = `${window.location.origin}${baseUrl}`
        }

        window.open(
          `${baseUrl}group-products/plans/${this.planId}/template?token=` + window.localStorage.getItem(tokenKey),
          '_blank'
        )
      }

      return '#'
    },
    handleNext() {
      if (this.plans.find((e) => e.id === this.planId) === undefined) {
        this.$message.warning('请先选择投保套餐')
      }

      this.$emit('selected', {
        company: this.company,
        product: this.product,
        plan: this.plans.find((e) => e.id === this.planId)
      })
    },
    submitPreview() {
      if (
        this?.product?.channel?.platform?.includes(this.user.platform_id) &&
        this?.product?.additional?.inform?.length > 1
      ) {
        this.informVisible = true
        return
      }
      this.handleNext()
    },
    checkingInform() {
      this.informVisible = false
      this.handleNext()
    }
  }
}
</script>

<style lang="scss" scoped>
.html-body {
  /deep/ tr,
  /deep/ th,
  /deep/ td {
    height: auto;
    border: 1px solid #eee !important;
    border-spacing: 0;
    padding: 0;
    min-height: 30px;
    padding: 5px;
  }

  /deep/ table {
    width: 100%;
    border-collapse: collapse;
  }
}

.product-details {
  border-spacing: 0;
  width: 100%;

  td {
    border: 1px solid #eee;
    border-spacing: 0;
    padding: 0;
    min-height: 30px;
    padding: 5px;
    text-align: center;
  }

  tr:not(:first-child) > td {
    border-top: 0;
  }

  td:first-child {
    font-weight: bold;
  }

  td:not(:first-child) {
    border-left: 0;
  }
}

.choose-product {
  /deep/ {
    background-color: transparent;
    box-shadow: none;
    border: none;

    .el-radio-group {
      .el-radio.is-bordered {
        margin-right: 0;
        padding: 8px 10px 0 10px;

        .el-radio__input {
          display: none;
        }

        .el-radio__label {
          padding-left: 0;
        }
      }
    }
  }

  .selected-content {
    padding: 0 15px 15px;

    .field {
      @extend .d-flex;
      font-size: 14px;
      margin-bottom: $app-size-mini;

      label {
        width: 75px;
        min-width: 75px;
        font-weight: bold;
      }

      span {
        @extend .flex-fill;
      }
    }
  }
}
</style>
