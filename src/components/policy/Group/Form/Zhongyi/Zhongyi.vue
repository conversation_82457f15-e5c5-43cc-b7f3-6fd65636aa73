<template>
  <el-form ref="form" :model="form" :rules="rules" label-position="top">
    <form-block class="m-extra-large-b" title="投保人信息">
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item prop="policyholder" label="企业法人">
            <el-input v-model="form.policyholder" placeholder="请输入企业法人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="policyholder_phone_number" label="手机">
            <el-input v-model="form.policyholder_phone_number" placeholder="请输入手机" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="policyholder_idcard_type" label="证件类型">
            <el-select v-model="form.extra_info.policyholder_idcard_type" placeholder="请选择证件类型" class="w-100">
              <el-option v-for="(name, certType) in certTypes" :key="certType" :value="certType" :label="name" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="policyholder_idcard_no" label="证件号">
            <el-input v-model="form.policyholder_idcard_no" placeholder="请输入证件号" />
          </el-form-item>
        </el-col>
      </el-row>
    </form-block>
    <form-block class="m-extra-large-b" title="被保人信息">
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item prop="insured">
            <span slot="label"> 企业名称 <el-checkbox size="mini" v-model="syncInsured">同投保人</el-checkbox> </span>
            <el-input v-model="form.insured" placeholder="请输入企业名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="insured_phone_number" label="手机">
            <el-input v-model="form.insured_phone_number" placeholder="请输入手机" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="insured_idcard_type" label="证件类型">
            <el-select v-model="form.extra_info.insured_idcard_type" placeholder="请选择证件类型" class="w-100">
              <el-option v-for="(name, certType) in insuredCertTypes" :key="certType" :value="certType" :label="name" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="insured_idcard_no" label="证件号">
            <el-input v-model="form.insured_idcard_no" placeholder="请输入证件号" />
          </el-form-item>
        </el-col>
      </el-row>
    </form-block>
    <form-block class="m-extra-large-b" title="标的信息">
      <el-form-item label="地址" required>
        <div class="d-flex w-100">
          <el-cascader
            :style="{ width: '500px' }"
            v-model="form.extra_info.object_address"
            :options="districts[0].districts"
            :props="{
              expandTrigger: 'hover',
              filterable: true,
              value: 'name',
              label: 'name',
              children: 'districts'
            }"
          />
          <el-input
            v-model="form.extra_info.object_address_detail"
            placeholder="请输入详细地址信息"
            class="flex-fill m-mini-l"
            clearable
          />
        </div>
      </el-form-item>
    </form-block>
    <form-block class="m-extra-large-b" title="保单信息">
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item prop="start_at" label="起保日期">
            <el-date-picker
              class="w-100"
              @change="insureDateChange"
              :picker-options="insureDatePickerOption"
              type="date"
              v-model="form.start_at"
              format="yyyy-MM-dd 00:00:00"
              value-format="yyyy-MM-dd 00:00:00"
              placeholder="请选择日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="end_at" label="终保日期">
            <el-input class="w-100" v-model="form.end_at" placeholder="请选择日期" readonly />
          </el-form-item>
        </el-col>
      </el-row>
    </form-block>
    <form-block class="m-extra-large-b text-center">
      <el-button type="primary" icon="fas fa-check" @click="handleSubmit">确认提交</el-button>
    </form-block>
  </el-form>
</template>

<script>
import districts from '@/utils/zhongyi-areadata.json'
import dayjs from 'dayjs'

export default {
  name: 'GroupFormZhongyi',
  props: {
    policyGroupId: {
      type: Number,
      default: 0
    },
    policy: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      syncInsured: false,
      districts,
      certTypes: {
        '01': '身份证',
        '02': '军官证',
        '03': '学生证',
        '04': '台胞证',
        '06': '护照',
        '07': '港澳返乡证',
        '08': '出生证明（未成年人）',
        '09': '营业执照',
        10: '工商登记号',
        11: '组织机构代码',
        13: '统一社会信用代码',
        14: '港澳台居民居住证',
        99: '其他'
      },
      insuredCertTypes: {
        '01': '身份证',
        '02': '军官证',
        '03': '学生证',
        '04': '台胞证',
        '06': '护照',
        '07': '港澳返乡证',
        '08': '出生证明（未成年人）',
        '09': '营业执照',
        10: '工商登记号',
        11: '组织机构代码',
        13: '统一社会信用代码',
        14: '港澳台居民居住证',
        99: '其他'
      },
      form: {
        policyholder: '',
        policyholder_phone_number: '',
        extra_info: {
          policyholder_idcard_type: '01',
          insured_idcard_type: '13',
          object_address: [],
          object_address_detail: ''
        },
        policyholder_idcard_no: '',
        insured: '',
        insured_phone_number: '',
        insured_idcard_no: '',
        start_at: '',
        end_at: ''
      },
      rules: {
        policyholder: [{ required: true, message: '输入企业法人', trigger: 'blur' }],
        policyholder_phone_number: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        policyholder_idcard_no: [{ required: true, message: '请输证件号码', trigger: 'blur' }],
        // policyholder_idcard_type: [{ required: true, message: '请选择证件类型' }],
        insured: [{ required: true, message: '输入企业名称', trigger: 'blur' }],
        insured_phone_number: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        insured_idcard_no: [{ required: true, message: '请输证件号码', trigger: 'blur' }],
        // insured_idcard_type: [{ required: true, message: '请选择证件类型', trigger: 'change' }],
        object_address: [{ required: true, message: '请输入地址信息', trigger: 'blur' }],
        start_at: [{ required: true, message: '请选择起保时间' }],
        end_at: [{ required: true, message: '请选择起保时间' }]
      },
      insureDatePickerOption: {
        // @todo::投保日期限制
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      }
    }
  },
  watch: {
    policy: {
      deep: true,
      immediate: true,
      handler(value) {
        this.form = Object.assign(this.form, value)
      }
    },
    'form.policyholder'(value) {
      if (this.syncInsured) {
        this.form.insured = value
      }
    },
    'form.extra_info.policyholder_idcard_type'(value) {
      if (this.syncInsured) {
        this.form.extra_info.insured_idcard_type = value
      }
    },
    'form.policyholder_idcard_no'(value) {
      if (this.syncInsured) {
        this.form.insured_idcard_no = value
      }
    },
    'form.policyholder_phone_number'(value) {
      if (this.syncInsured) {
        this.form.insured_phone_number = value
      }
    },
    'form.insured'(value) {
      if (this.syncInsured) {
        this.form.policyholder = value
      }
    },
    'form.extra_info.insured_idcard_type'(value) {
      if (this.syncInsured) {
        this.form.extra_info.policyholder_idcard_type = value
      }
    },
    'form.insured_idcard_no'(value) {
      if (this.syncInsured) {
        this.form.policyholder_idcard_no = value
      }
    },
    'form.insured_phone_number'(value) {
      if (this.syncInsured) {
        this.form.policyholder_phone_number = value
      }
    },
    syncInsured(value) {
      if (value) {
        this.form.insured = this.form.policyholder
        this.form.extra_info.insured_idcard_type = this.form.extra_info.policyholder_idcard_type
        this.form.insured_idcard_no = this.form.policyholder_idcard_no
        this.form.insured_phone_number = this.form.policyholder_phone_number
      } else {
        this.form.insured = this.form.insured_idcard_no = this.form.insured_phone_number = ''
        this.form.extra_info.insured_idcard_type = '13'
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid, errors) => {
        if (valid) {
          const message = this.checkInsuranceLimit()
          if (message !== null) {
            this.$message.error(message)
            return
          }
          // 中意雇主
          this.form.product_platform = 'API_GROUP_ZY'
          this.$emit('submit', this.form)
        } else {
          window.alert_validate_errors(errors)
        }
      })
    },
    insureDateChange() {
      let startDate = new Date(this.form.start_at)

      startDate.setFullYear(startDate.getFullYear() + 1)
      startDate.setDate(startDate.getDate() - 1)

      this.$set(this.form, 'end_at', dayjs(startDate).format('YYYY-MM-DD 23:59:59'))
    },
    checkInsuranceLimit() {
      if (
        parseInt(this.form.extra_info.insured_idcard_type, 10) === 13 &&
        String(this.form.insured_idcard_no).startsWith('92')
      ) {
        return '暂不支持被保险人为个体工商户'
      }

      let blacklistWords = [
        '人力资源',
        '劳务',
        '外包',
        '快递',
        '速运',
        '建筑',
        '工程',
        '工程机械',
        '起重',
        '混凝土',
        '垃圾',
        '建材',
        '钢材',
        '石材',
        '砂石',
        '化工',
        '化学',
        '救援',
        '清障',
        '搬家',
        '牵引车',
        '自卸车',
        '大型货物',
        '渣土',
        '吊机',
        '土石方',
        '石化'
      ]

      for (const idx in blacklistWords) {
        if (
          this.form.policyholder.indexOf(blacklistWords[idx]) !== -1 ||
          this.form.insured.indexOf(blacklistWords[idx]) !== -1
        ) {
          return '您的保单中含有黑名单关键字，请检查后重新提交'
        }
      }

      if (this.form.extra_info.object_address_detail.length === 0) {
        return '标的地址信息必填'
      }

      // [370, 120, 430, 520, 130, 210, 230, 220]
      const provinceBlacklist = ['山东省', '天津市', '湖南省', '贵州省', '河北省', '辽宁省', '黑龙江省', '吉林省']
      if (
        provinceBlacklist.includes(this.form.extra_info.object_address[0]) &&
        ['装卸', '搬运'].some(
          (word) =>
            String(this.form.policyholder).indexOf(word) !== -1 || String(this.form.insured).indexOf(word) !== -1
        )
      ) {
        return '暂不符合承保规则'
      }

      return null
    }
  }
}
</script>
