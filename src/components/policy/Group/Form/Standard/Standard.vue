<template>
  <el-form ref="form" :model="form" :rules="rules" label-position="top">
    <form-block class="m-extra-large-b" title="投保联系人">
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item prop="contact_name" label="姓名">
            <el-input v-model="form.contact_name" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="contact_phone" label="联系电话">
            <el-input v-model="form.contact_phone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>
    </form-block>
    <form-block class="m-extra-large-b" title="单位信息">
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item prop="policyholder" label="投保单位名称">
            <el-input v-model="form.policyholder" placeholder="请输入投保单位名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="policyholder_idcard_no" label="投保单位统一社会信用代码">
            <el-input v-model="form.policyholder_idcard_no" placeholder="请输入投保单位统一社会信用代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="insured">
            <span slot="label">
              被保单位名称 <el-checkbox border size="mini" v-model="syncInsured">同投保人</el-checkbox>
            </span>
            <el-input v-model="form.insured" placeholder="请输入被保单位名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="insured_idcard_no" label="被保单位统一社会信用代码">
            <el-input v-model="form.insured_idcard_no" placeholder="请输入被保单位统一社会信用代码" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="business_license_file">
            <template slot="label">
              <span>被保单位营业执照</span>
              <span class="text-danger"> .jpeg, .png, .pdf, .jpg, .zip 文件，大小不能超过 4M</span>
            </template>
            <upload-file accept=".jpeg,.png,.pdf,.jpg,.zip" :limitSize="4" v-model="form.business_license_file" />
          </el-form-item>
        </el-col>
      </el-row>
    </form-block>
    <form-block class="m-extra-large-b" title="保单相关信息">
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item prop="start_at" label="起保日期">
            <el-date-picker
              class="w-100"
              @change="insureDateChange"
              :picker-options="insureDatePickerOption"
              type="date"
              default-time="00:00:00"
              v-model="form.start_at"
              format="yyyy-MM-dd 00:00:00"
              value-format="yyyy-MM-dd 00:00:00"
              placeholder="请选择日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="end_at" label="终保日期">
            <el-input class="w-100" v-model="form.end_at" placeholder="请选择日期" readonly />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item
            prop="application_file"
            :rules="
              attachments.includes('application_file')
                ? [{ required: true, message: '请上传投保单文件', trigger: ['blur', 'change'] }]
                : []
            "
          >
            <template slot="label">
              <span>投保单文件</span>
              <span class="text-danger"> .doc, .docx, .zip, .rar 文件，大小不能超过 4M</span>
            </template>
            <upload-file accept=".doc,.docx,.zip,.rar" :limitSize="4" v-model="form.application_file" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="application_stamp_file"
            :rules="
              attachments.includes('application_stamp_file')
                ? [{ required: true, message: '请上传投保单盖章文件', trigger: ['blur', 'change'] }]
                : []
            "
          >
            <template slot="label">
              <span>投保单盖章文件</span>
              <span class="text-danger"> .jpeg, .png, .jpg, .pdf, .zip 文件，大小不能超过 4M</span>
            </template>
            <upload-file accept=".jpeg,.png,.jpg,.pdf,.zip" :limitSize="4" v-model="form.application_stamp_file" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item
            prop="staff_list_file"
            :rules="
              attachments.includes('staff_list_file')
                ? [{ required: true, message: '请上传保险公司人员清单', trigger: ['blur', 'change'] }]
                : []
            "
          >
            <template slot="label">
              <span>保险公司人员清单</span>
              <small>
                <a @click="downloadEmployeeTemplateExcel()"> (点击下载模板) </a>
              </small>
              <span class="text-danger"> .xls, .xlsx, .zip 文件，大小不能超过 4M </span>
            </template>
            <upload-file accept=".xls,.xlsx,.zip" :limitSize="4" v-model="form.staff_list_file" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="staff_stamp_list_file"
            :rules="
              attachments.includes('staff_stamp_list_file')
                ? [{ required: true, message: '请上传人员清单盖章文件', trigger: ['blur', 'change'] }]
                : []
            "
          >
            <template slot="label">
              <span>人员清单盖章文件</span>
              <span class="text-danger"> .jpeg, .png, .jpg, .pdf, .zip 文件，大小不能超过 4M</span>
            </template>
            <upload-file accept=".jpeg,.png,.jpg,.pdf,.zip" :limitSize="4" v-model="form.staff_stamp_list_file" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item
            prop="authorization_file"
            :rules="
              attachments.includes('authorization_file')
                ? [{ required: true, message: '请上传委托书', trigger: ['blur', 'change'] }]
                : []
            "
          >
            <template slot="label">
              <span>委托书</span>
              <span class="text-danger"> .jpeg, .png, .jpg, .doc, .docx, .pdf, .zip 文件，大小不能超过 4M</span>
            </template>
            <upload-file
              accept=".jpeg,.png,.jpg,.doc,.docx,.pdf,.zip"
              :limitSize="4"
              v-model="form.authorization_file"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="extra_file"
            :rules="
              attachments.includes('extra_file')
                ? [{ required: true, message: '请上传其他文件', trigger: ['blur', 'change'] }]
                : []
            "
          >
            <template slot="label">
              <span>其他文件</span>
              <span class="text-danger"> .jpeg, .png, .pdf, .jpg, .doc, .docx, .pdf, .zip 文件，大小不能超过 4M</span>
            </template>
            <upload-file accept=".jpeg,.png,.pdf,.jpg,.doc,.docx,.pdf,.zip" :limitSize="4" v-model="form.extra_file" />
          </el-form-item>
        </el-col>
      </el-row>
    </form-block>
    <form-block class="m-extra-large-b" title="发票相关信息">
      <el-row :gutter="48">
        <el-col :span="24">
          <el-form-item prop="invoice_type" label="请选择发票类型">
            <el-radio-group v-model="form.invoice_type">
              <el-radio label="none">不需要发票</el-radio>
              <el-radio label="normal">普通发票</el-radio>
              <el-radio label="special">增值税专用发票</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="form.invoice_type !== 'none'">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item
              prop="invoice_title"
              label="发票抬头"
              :rules="[{ required: true, message: '请输入发票抬头', trigger: 'blur' }]"
            >
              <el-input v-model="form.invoice_title" placeholder="请输入发票抬头"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              prop="invoice_tax_no"
              label="纳税人识别号"
              :rules="[{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }]"
            >
              <el-input v-model="form.invoice_tax_no" placeholder="请输入纳税人识别号"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="form.invoice_type === 'special'">
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item
                prop="invoice_bank_name"
                label="开户行"
                :rules="[{ required: true, message: '请输入开户行', trigger: 'blur' }]"
              >
                <el-input v-model="form.invoice_bank_name" placeholder="请输入开户行"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                prop="invoice_bankcard_no"
                label="账号"
                :rules="[{ required: true, message: '请输入账号', trigger: 'blur' }]"
              >
                <el-input v-model="form.invoice_bankcard_no" placeholder="请输入账号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item
                prop="invoice_registered_addr"
                label="注册地址"
                :rules="[{ required: true, message: '请输入注册地址', trigger: 'blur' }]"
              >
                <el-input v-model="form.invoice_registered_addr" placeholder="请输入注册地址"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                prop="invoice_phone_number"
                label="联系电话"
                :rules="[{ required: true, message: '请输入联系电话', trigger: 'blur' }]"
              >
                <el-input v-model="form.invoice_phone_number" placeholder="请输入联系电话"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </template>
    </form-block>
    <form-block class="m-extra-large-b text-center">
      <el-button type="primary" icon="fas fa-check" @click="handleSubmit">确认提交</el-button>
    </form-block>
  </el-form>
</template>

<script>
import dayjs from 'dayjs'
import { getInsuredInvoiceHistory } from '@/apis/groupProduct'
import { tokenKey } from '@/config'

export default {
  name: 'GroupFormStandard',
  props: {
    policyGroupId: {
      type: Number,
      default: 0
    },
    policy: {
      type: Object,
      default: () => {}
    },
    product: {
      type: Object,
      default: () => {}
    },
    plan: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      syncInsured: false,
      form: {
        contact_name: '',
        contact_phone: '',
        policyholder: '',
        policyholder_idcard_no: '',
        insured: '',
        insured_idcard_no: '',
        business_license_file: '',
        start_at: '',
        end_at: '',
        application_file: '',
        application_stamp_file: '',
        staff_list_file: '',
        staff_stamp_list_file: '',
        authorization_file: '',
        extra_file: '',
        invoice_type: 'none',
        invoice_title: '',
        invoice_tax_no: '',
        invoice_bank_name: '',
        invoice_bankcard_no: '',
        invoice_registered_addr: '',
        invoice_phone_number: ''
      },
      rules: {
        contact_name: [{ required: true, message: '输入姓名', trigger: 'blur' }],
        contact_phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        policyholder: [{ required: true, message: '请输入投保单位名称', trigger: 'blur' }],
        policyholder_idcard_no: [{ required: true, message: '请输入投保单位统一社会编码', trigger: 'blur' }],
        insured: [{ required: true, message: '请输入被保单位名称', trigger: 'blur' }],
        insured_idcard_no: [{ required: true, message: '请输入被保单位统一社会信用代码', trigger: 'blur' }],
        start_at: [{ required: true, message: '请选择起保时间', trigger: ['blur', 'change'] }],
        end_at: [{ required: true, message: '请选择起保时间' }],
        business_license_file: [{ required: true, message: '请上传被保单位营业执照', trigger: ['blur', 'change'] }]
        // application_file: [{ required: false, message: '请上传投保单文件', trigger: ['blur', 'change'] }],
        // application_stamp_file: [{ required: false, message: '请上传投保单盖章文件', trigger: ['blur', 'change'] }],
        // staff_list_file: [{ required: false, message: '请上传保险公司人员清单', trigger: ['blur', 'change'] }],
        // staff_stamp_list_file: [{ required: false, message: '请上传人员清单盖章文件', trigger: ['blur', 'change'] }],
        // authorization_file: [{ required: false, message: '请上传委托书', trigger: ['blur', 'change'] }],
        // extra_file: [{ required: false, message: '请上传其他文件', trigger: ['blur', 'change'] }]
      },
      insureDatePickerOption: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      }
    }
  },
  watch: {
    policy: {
      deep: true,
      immediate: true,
      handler(value) {
        this.form = Object.assign(this.form, value)
      }
    },
    'form.invoice_type'(value) {
      if (value) {
        this.fetchInvoiceInformation(this.form.insured_idcard_no)
      }
    },
    'form.policyholder'(value) {
      if (this.syncInsured) {
        this.form.insured = value
      }
    },
    'form.policyholder_idcard_no'(value) {
      if (this.syncInsured) {
        this.form.insured_idcard_no = value
      }
    },
    'form.insured'(value) {
      if (this.syncInsured) {
        this.form.policyholder = value
      }
    },
    'form.insured_idcard_no'(value) {
      if (this.syncInsured) {
        this.form.policyholder_idcard_no = value
      }
    },
    syncInsured: {
      deep: true,
      immediate: true,
      handler(value) {
        if (value) {
          this.form.insured = this.form.policyholder
          this.form.insured_idcard_no = this.form.policyholder_idcard_no
        } else {
          this.form.insured = this.form.insured_idcard_no = ''
        }
      }
    }
  },
  computed: {
    attachments() {
      let attachments = this.product?.product?.additional?.mail_attachments
      let insured = attachments?.insured ?? []
      let modify = attachments?.modify ?? []

      return [...new Set([...insured, ...modify])]
    }
  },
  methods: {
    fetchInvoiceInformation(insuredIdcardNo) {
      getInsuredInvoiceHistory({
        insured_idcard_no: insuredIdcardNo
      }).then((r) => {
        this.form.invoice_title = r.data.title ?? this.form.policyholder
        this.form.invoice_tax_no = r.data.tax_no ?? this.form.policyholder_idcard_no
        this.form.invoice_bank_name = r.data.bank_name ?? ''
        this.form.invoice_bankcard_no = r.data.bankcard_no ?? ''
        this.form.invoice_registered_addr = r.data.registered_addr ?? ''
        this.form.invoice_phone_number = r.data.phone_number ?? ''
      })
    },
    handleSubmit() {
      let alerts = this.product.product?.alerts
      if (alerts?.enable) {
        this.$alert(alerts.message)
      }
      this.$refs.form.validate((valid, errors) => {
        if (valid) {
          this.$emit('submit', this.form)
        } else {
          window.alert_validate_errors(errors)
        }
      })
    },
    insureDateChange() {
      let startDate = new Date(this.form.start_at)

      startDate.setFullYear(startDate.getFullYear() + 1)
      startDate.setDate(startDate.getDate() - 1)

      this.$set(this.form, 'end_at', dayjs(startDate).format('YYYY-MM-DD 23:59:59'))
    },
    downloadEmployeeTemplateExcel() {
      if (this.plan?.id) {
        let baseUrl = process.env.VUE_APP_BASE_API
        if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
          baseUrl = `${window.location.origin}${baseUrl}`
        }

        window.open(
          `${baseUrl}group-products/plans/${this.plan.id}/template?token=` + window.localStorage.getItem(tokenKey),
          '_blank'
        )
      }

      return '#'
    },
    download(link) {
      window.open(link)
    }
  }
}
</script>
