<template>
  <div>
    <el-card shadow="never">
      <h3>你选择的套餐是:</h3>
      <p v-if="data.product">
        {{ data.product.name }} - {{ data.plan.title }}&nbsp;&nbsp;
        <el-link v-if="selectable" @click="$emit('back')" class="el-link--primary">重新选择</el-link>
      </p>
      <!-- 只有线下支付需要 -->
      <template
        v-if="data.product.additional.third_platform === 'API_GROUP_ZY' && data.product.additional.payment_type === 1"
      >
        <h3>保费收款账户</h3>
        <el-row :gutter="48">
          <el-col :span="8">
            <p>账户名称</p>
            <p>开户行</p>
            <p>账号</p>
          </el-col>
          <el-col :span="16">
            <p>中意财产保险有限公司</p>
            <p>中国工商银行股份有限公司鼓楼外大街支行</p>
            <p>0200225119200028130</p>
          </el-col>
        </el-row>
      </template>
      <template
        v-if="data.product.additional.third_platform === 'API_GROUP_ZY' && data.product.additional.payment_type === 2"
      >
        <el-alert
          title="温馨提示"
          description="该产品为见费出单，提交审核通过后需要完成在线支付方可生效。"
          type="warning"
          :closable="false"
        ></el-alert>
      </template>
    </el-card>
    <div class="m-extra-large-t">
      <zhongyi
        v-if="data.product && data.product.additional.third_platform === 'API_GROUP_ZY'"
        @submit="(data) => handleSubmit('zhongyi', data)"
        :policy="policy"
      />
      <standard
        v-else
        @submit="(data) => handleSubmit('standard', data)"
        :policy="policy"
        :product="product"
        :plan="data.plan"
      />
    </div>

    <el-dialog :visible.sync="visible" title="投保声明" width="520px" :before-close="handleClose">
      <template v-if="data.product && data.product.additional.third_platform === 'API_GROUP_ZY'">
        1、兹双方同意，本保单承保的被保险人员工年龄范围为 16-65 周岁。<br />
        2、被保险人的雇员发生工伤事故后，有工伤保险的应当先行向工伤保险提出索赔请求。<br />
        3、本保单记名承保，投保时需同时提供雇员清单。雇员必须与被保险人存在劳动关系（包括事实劳动关系）并签署劳动合同。本保单不承保仅与被保险人存在劳务派遣关系，而不产生直接劳动关系的雇员。<br />
        4、除紧急抢救外，被保险雇员因遭受保险事故，在二级及以上公立医院治疗，但不包含任何北京平谷区密云、河北省三河市、天津滨海区、天津静海区、辽宁铁岭、河北青龙县、廊坊市、山东禹城、河南信阳下辖的各医疗机构，不在保险公司认可医院范围内，即上述地区医疗机构的发票保险公司不予理赔。对于被保险人在每次意外伤害中所支出的必要且合理的，符合当地政府颁布的基本医疗保险报销范围的医疗费用，按照实际支出的符合当地社会医疗保险规定及用药名录的合理医疗费用扣除免赔额后进行赔付。<br />
        5、被保险人应在发生事故的 48
        小时之内拨打中意财产保险有限公司24小时客服热线4006002700进行报案，如超过上述报案时效保险人有权不承担相应的赔偿。<br />
        6、伤残5%起：一级伤残 100%、二级伤残 80%、三级伤残 70%、四级伤残 60%、五级伤残 50%、六级伤残 40%、七级伤残
        30%、八级伤残 20%、九级伤残 10%、十级伤残 5%； 伤残10%起：一级 100%、二级 90%、三级 80%、四级 70%、五级
        60%、六级 50%、七级 40%、八级 30%、九级 20%、十级 10%。<br />
        7、本保单记名投保，投保人需提供被保险人的姓名及身份证号信息。雇员必须与被保险人存在劳动关系（包括事实劳动关系）并签署劳动合同。本保单不承保仅与被保险人存在劳务派遣关系，而不产生直接劳动关系的雇员。<br />
        8、本保单仅承担中国国籍人员（不包含港澳台人员）。<br />
        9、本保险仅承保普通货物运输企业，本保险不承保营业执照经营范围无货物运输的企业、人力资源公司、劳务派遣公司、劳务外包公司、及个体经营户（统一社会信用代码92开头）。<br />
        10、本保险不承保从事以下职业的人员：运输危险品车辆的司机及装卸工、运输建筑工程相关车辆的司机及装卸工、运输钢材建材相关车辆的司机及装卸工、自卸车辆司机及装卸工、超长超宽大件运输车辆司机及装卸工、特种车辆司机及装卸工（不含集装箱货物运输车辆和冷藏货物厢式货车）、渣土混凝土车辆司机及装卸工、吊机车辆司机及装卸工、重型罐式半挂车司机及装卸工、脚离地距离5米以上高处工作人员、外卖配送人员、送餐员、搬家服务工作人员、道路救援及清障服务工作人员、钢结构工人、路桥建设工人、室外土建人员、渔船及船厂工作人员，码头工作人员、有色金属冶炼及加工业工作人员，化工行业工作人员，矿业开采与加工工作人员、潜水作业人员、玻璃幕墙（外窗）安装与清洁人员、爆破工作人员、采石（砂）凿岩工作人员。<br />
        11、本保险不承保名称中包含以下文字的被保险人:人力资源、劳务、外包、快递、速运、建筑、工程、工程机械、起重、混凝土、垃圾、建材、钢材、石材、砂石、化工、化学、救援、清障、搬家、渣土、吊机、土石方、石化。<br />
        12、伤残鉴定：保险公司接受市级以上司法鉴定机构提供的伤残鉴定。可以在以下网站查询全国各地的司法鉴定机构名录。http://www.sfjdml.com/web/。
      </template>
      <template v-else>
        1. 投保人确认投保前已认真阅读并了解了本声明以及投保须知的内容，并如实按照投保须知的指引进行投保。<br />
        2.本公司及保险公司已向投保人提供并详细介绍了保单所适用的所有保险条款，并对其中免除保险人责任的条款（包括但不限于责任免除、投保人、被保险人义务、赔偿处理、其他事项等），以及保单付费约定和特别约定的内容向投保人做了明确说明，投保人已充分理解并接受上述内容，同意以此作为投保的依据，自愿投保本保险。
        <br />
        3.如投保人违反本声明，未做到如实投保，须自行承担由此引发的任何责任和后果。对于本声明有任何异议，请在确认投保前与本公司联系。
      </template>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleConfirmed">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import Standard from './Standard'
import Zhongyi from './Zhongyi'

export default {
  name: 'GroupForm',
  components: {
    Standard,
    Zhongyi
  },
  props: {
    data: {
      type: Object,
      default: () => {},
      require: true
    },
    policy: {
      type: Object,
      default: () => {}
    },
    product: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    selectable() {
      return [0].includes(this.policy?.status ?? 0)
    }
  },
  data() {
    return {
      from: '',
      form: {},
      visible: false
    }
  },
  methods: {
    handleClose() {
      this.visible = false
    },
    handleConfirmed() {
      this.handleClose()

      let data = Object.assign({}, this.form)
      data.start_at = dayjs(data.start_at).format('YYYY-MM-DD')
      data.end_at = dayjs(data.end_at).format('YYYY-MM-DD')

      this.$emit('submit', {
        from: this.from,
        data
      })
    },
    handleSubmit(from, data) {
      this.visible = true
      this.from = from
      this.form = data
    }
  }
}
</script>
