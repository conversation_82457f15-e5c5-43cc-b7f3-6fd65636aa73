<template>
  <el-input v-model="model" placeholder="请输入地址" v-if="useInput" clearable />
  <el-cascader
    v-else
    class="w-100"
    v-model="model"
    :options="areas"
    :props="{
      expandTrigger: 'hover',
      filterable: true,
      label: 'value',
      value: 'value'
    }"
    clearable
  />
</template>

<script>
import areas from './areas.json'

export default {
  name: 'InputAddress',
  props: {
    value: {
      type: String,
      default: ''
    },
    company: {
      type: String,
      required: false
    }
  },
  data() {
    return {
      areas,
      model: ''
    }
  },
  computed: {
    useInput() {
      return this.company !== 'TPIC'
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (val !== this.model) {
          this.model = !this.useInput ? val.split('-') : val
        }
      }
    },
    model(value) {
      this.$emit('input', !this.useInput ? value.join('-') : value)
    }
  },
  created() {
    if (this.value && this.useInput) {
      this.model = this.value.split('-')
    }
  }
}
</script>
