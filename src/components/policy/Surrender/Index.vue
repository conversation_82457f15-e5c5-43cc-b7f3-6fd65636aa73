<template>
  <el-dialog :visible.sync="visible" title="退回保单" @before-close="handleBeforeClose" destroy-on-close width="520px">
    <el-form ref="form" :model="form" :rules="rules" label-suffix=":">
      <el-form-item prop="reason" label="退保原因">
        <el-input type="textarea" autoresize :rows="3" v-model="form.reason" placeholder="请输入退保原因"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="handleBeforeClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'surrender',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        reason: ''
      },
      rules: {
        reason: [{ required: true, message: '请输入退保原因', trigger: ['blur', 'change'] }]
      }
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)

          this.$emit('update:visible', false)

          this.$refs.form.resetFields()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
