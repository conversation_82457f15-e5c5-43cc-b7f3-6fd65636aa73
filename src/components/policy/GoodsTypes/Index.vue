<template>
  <div>
    <el-select v-model="model" class="el-select w-100" placeholder="请选择货物分类">
      <el-option v-for="item in options" :key="item.id" :value="item.id" :label="item.display_name" />
    </el-select>
  </div>
</template>

<script>
import { getProductGoodsTypes } from '@/apis/product'

export default {
  name: 'ComponentGoodsTypes',
  props: {
    value: {
      type: [String, Number]
    },
    productId: {
      required: false,
      type: [String, Number]
    }
  },
  data() {
    return {
      model: '',
      options: []
    }
  },
  watch: {
    value(value, oldValue) {
      if (value !== oldValue) {
        this.model = value
      }
    },
    productId(value) {
      if (value && value !== -1) {
        this.fetchGoodsTypes()
      }
    },
    model(value) {
      this.$emit('input', value)
    }
  },
  created() {
    this.model = this.value
  },
  methods: {
    async fetchGoodsTypes() {
      if (this.productId <= 0) {
        return
      }
      const { data } = await getProductGoodsTypes(this.productId)
      this.options = data
    }
  }
}
</script>
