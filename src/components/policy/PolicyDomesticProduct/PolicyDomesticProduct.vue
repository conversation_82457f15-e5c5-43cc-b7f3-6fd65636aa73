<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-11 11:06:11
 * @LastEditors: yanb
 * @LastEditTime: 2024-07-17 10:00:10
-->
<template>
  <el-form class="policy-product bg-white" label-position="top">
    <div style="padding: 1rem">
      <el-form-item required>
        <template #label>
          <!-- // eslint-disable-next-line prettier/prettier -->
          <span>货物类别（<span class="text-danger">请正确选择货物类别。若不属于下列货物，请选择“人工审核”投保或联系业务人员</span>）</span>
        </template>
        <el-checkbox-group v-model="subjectId">
          <el-checkbox v-for="s in subjects" :key="s.id" :true-label="s.id" :false-label="0">
            <el-popover v-if="s.description" width="300" placement="top-start" :title="s.name" trigger="hover">
              <p>{{ s.description }}</p>
              <span slot="reference" :class="{ 'text-danger': s.id === 3 }">{{ s.name }}</span>
            </el-popover>
            <template v-else>
              <span :class="{ 'text-danger': s.id === 3 }">{{ s.name }}</span>
            </template>
          </el-checkbox>
        </el-checkbox-group>

        <el-alert v-if="subjectDescription" type="error" :title="subjectDescription" :closable="false" />
      </el-form-item>

      <template v-if="hasProducts">
        <el-form-item
          required
          label="是否为二手货/旧器材/旧货、已受损货物、退运货物、进口转运货（释意：进口货物连续进行国内运输但仅计划投保国内运输段）"
        >
          <el-radio :disabled="disabled" v-model="isNew" :label="0">是</el-radio>
          <el-radio :disabled="disabled" v-model="isNew" :label="1">否</el-radio>
        </el-form-item>
        <template v-if="mainClauses.length > 0">
          <template v-if="subjectId === 3">
            <el-form-item required label="请选择人工审核原因">
              <el-checkbox-group :disabled="disabled" v-model="subjectCategories">
                <el-checkbox
                  v-for="(m, idx) in subjects?.find((e) => e.id === subjectId)?.categories"
                  :key="idx"
                  :label="m.id"
                >
                  {{ m.name }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item required label="请填写承保费率条件">
              <el-form-item prop="manualConditionsContent">
                <el-input
                  v-model="manualConditionsContent"
                  placeholder="请输入承保费率条件"
                  type="textarea"
                  autoresize
                  :rows="3"
                  clearable
                ></el-input>
              </el-form-item>
            </el-form-item>
          </template>

          <el-form-item required>
            <!-- // eslint-disable-next-line prettier/prettier -->
            <span>主险（<span class="text-danger">请点击“资料中心”菜单，查看相关保险条款</span>）</span>
            <el-checkbox-group :disabled="disabled" v-model="mainClauseId" @change="handleClearValues">
              <el-checkbox v-for="(m, idx) in mainClauses" :key="idx" :true-label="m.id" :false-label="0">
                <el-popover width="300" placement="top-start" title="条款内容" trigger="hover">
                  <p>{{ m.content }}</p>
                  <p v-if="m.clause_file">
                    <el-link :href="m.clause_file" target="_blank" download>下载条款文件</el-link>
                  </p>
                  <span slot="reference">{{ m.name }}</span>
                </el-popover>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item required>
            <template #label>
              <!-- // eslint-disable-next-line prettier/prettier -->
              <span> 附加险（<span class="text-danger">请点击“资料中心”菜单，查看相关保险条款</span>） </span>
            </template>
            <el-checkbox-group
              v-if="additionalClauses.length > 0"
              :disabled="!mainClauseId || disabled"
              v-model="additionalClauseIds"
              @change="handleSelectAdditionalClause"
            >
              <el-checkbox
                v-for="(clause, idx) in additionalClauses"
                :key="`additional_clause_${idx}_${mainClauseId}_${clause.id}`"
                :label="clause.id"
              >
                <el-popover width="300" placement="top-start" title="条款内容" trigger="hover">
                  <p>{{ clause.content }}</p>
                  <p v-if="clause.clause_file">
                    <el-link :href="clause.clause_file" target="_blank" download>下载条款文件</el-link>
                  </p>
                  <span slot="reference">{{ clause.name }}</span>
                </el-popover>
              </el-checkbox>
            </el-checkbox-group>
            <template v-else>
              <el-alert title="当前主附险下暂无可选择附加险，请联系管理员开通。" type="warning" :closable="false" />
            </template>
          </el-form-item>

          <el-form-item required label="请选择出单公司">
            <el-checkbox-group
              v-if="companyBranches.length > 0"
              :disabled="!mainClauseId || additionalClauseIds.length <= 0 || disabled"
              v-model="productId"
            >
              <el-checkbox
                v-for="(com, idx) in companyBranches"
                :key="`com_${idx}_${mainClauseId}_${com.id}`"
                :true-label="com.id"
                :false-label="0"
              >
                {{ com.company_branch }}
              </el-checkbox>
            </el-checkbox-group>
            <template v-else>
              <el-alert title="当前主附险下暂无可投保的产品，请联系管理员开通。" type="warning" :closable="false" />
            </template>
          </el-form-item>
        </template>
        <template v-else>
          <el-alert title="当前暂无可投保的产品，请联系管理员开通。" type="warning" :closable="false" />
        </template>
      </template>
      <template v-if="subjectId !== -1 && !hasProducts">
        <el-alert title="当前货物类别下暂无可投保的产品，请联系管理员开通。" type="warning" :closable="false" />
      </template>
    </div>

    <div class="selected-content" v-if="mainClauseId && productId && currentProduct !== undefined">
      <el-alert type="warning" :closable="false">
        <el-row>
          <el-col :span="12">
            <div class="field">
              <label>费率</label>
              <span>{{ currentProduct.user_rate }}‱</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field">
              <label>最低保费</label>
              <span>{{ currentProduct.minimum_premium | toLocaleCurrency }}元</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <div class="field">
              <label>最高保险金额</label>
              <span>{{ currentProduct.additional.coverage | toLocaleCurrency }}元</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field">
              <label>禁用区域</label>
              <span>{{ currentProduct.additional.disabled_regions || '无' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="field">
              <label>免赔</label>
              <span>{{ currentProduct.additional.deductible || '无' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="field">
              <label>特别约定</label>
              <span>
                {{ currentProduct.additional.special_agreement || '无' }}
              </span>
            </div>
          </el-col>
        </el-row>
        <el-row v-if="subjectId !== 2">
          <el-col :span="24">
            <div class="field">
              <label>除外货物</label>
              <span>
                {{ currentProduct.additional.except_subject || '无' }}
              </span>
            </div>
          </el-col>
        </el-row>
      </el-alert>
      <!-- 人工审核产品不需要 -->
      <template v-if="subjectId === 2">
        <div class="m-extra-large-t">
          <label class="p-alert text-danger">
            本投保界面仅适用于普通货物，若为下列特殊货物投保，请选择其他货物类别或选择人工审核方式为货物投保。
          </label>
        </div>
        <el-form-item label="投保货物是否为下列特殊货物" style="margin-top: 0; margin-left: 10px">
          <el-radio :disabled="disabled" v-model="isExceptGoods" :label="1">是</el-radio>
          <el-radio :disabled="disabled" v-model="isExceptGoods" :label="0">否</el-radio>
        </el-form-item>
        <div style="font-size: 14px; color: red">{{ currentProduct.additional.except_subject || '无' }}</div>
      </template>
    </div>
  </el-form>
</template>

<script>
import { arraysEqual } from '@/utils'

export default {
  name: 'PolicyDomesticProduct',
  props: {
    subjects: {
      require: true,
      type: Array,
      default: () => []
    },
    products: {
      require: true,
      type: Object,
      default: () => {}
    },
    product: {
      type: Number,
      require: true,
      default: -1
    },
    subject: {
      type: Number,
      default: -1
    },
    subjectCategoryIds: {
      type: Array,
      default: () => []
    },
    newGoods: {
      type: [Boolean, Number],
      default: true
    },
    manualConditions: {
      type: String,
      default: ''
    }
  },
  filters: {
    toLocaleCurrency(currency) {
      return parseFloat(currency).toLocaleString('zh-CN', {
        style: 'currency',
        currency: 'CNY'
      })
    }
  },
  data() {
    return {
      isNew: -1,
      // 标的
      subjectId: -1,
      // 人工审核原因
      subjectCategories: [],
      // 主险
      mainClauseId: '',
      // 附加险
      additionalClauseIds: [],
      // 产品
      productId: '',
      // 除外货物
      isExceptGoods: -1,
      // 承保费率条件
      manualConditionsContent: ''
    }
  },
  computed: {
    subjectDescription() {
      if (this.subjectId === -1) {
        return ''
      }
      const subject = this.subjects.find((s) => s.id === this.subjectId)
      if (subject && subject.description) {
        return subject.name + '：' + subject.description
      }
      return ''
    },
    disabled() {
      return this.$route.query.from === 'edit'
    },
    hasProducts() {
      const products = this.products?.products !== undefined ? this.products.products : []

      return products.length > 0
    },
    mainClauses() {
      if (this.isNew === 0) {
        return this.products?.main_clauses?.filter((e) => e?.is_allow_used)
      }

      return this.products?.main_clauses || []
    },
    currentProduct() {
      return this.products?.products?.find((e) => e.id === this.productId)
    },
    // 附加险
    additionalClauses() {
      let additionalClauses = this.products.additional_clauses
      // // 如果主险为空 显示所有附加险
      if (!this.mainClauseId) {
        return additionalClauses
      }

      const mainClause = this.mainClauses.find((e) => e.id === this.mainClauseId)

      return additionalClauses.filter((item) => item.parent_ids.some((r) => mainClause.ids.includes(r))) || []
    },
    // 保险公司
    companyBranches() {
      let products = this.products.products
      // 如果主险为空 显示所有保险公司, 并去重
      if (!this.mainClauseId && this.additionalClauseIds.length <= 0) {
        return products.filter(
          (value, index) => index === products.findIndex((e) => e.company_branch === value.company_branch)
        )
      }

      // 如果主险不为空 附加险为空 显示当前主险下的所有保险公司
      if (this.mainClauseId && this.additionalClauseIds.length <= 0) {
        let coms = products.filter((item) => item.new_main_clause_id === this.mainClauseId) || []
        coms = coms.filter((value, index) => index === coms.findIndex((e) => e.company_branch === value.company_branch))

        return coms
      }

      // 如果附加险不为空 显示当前附加险下的所有保险公司
      if (this.additionalClauseIds.length > 0) {
        return (
          products.filter(
            (item) =>
              arraysEqual(item.new_additional_clause_ids, this.additionalClauseIds) &&
              item.new_main_clause_id === this.mainClauseId
          ) || []
        )
      }

      return products
    }
  },
  watch: {
    product(value) {
      if (value !== -1 && value !== this.productId) {
        this.productId = value
      }
    },
    subject(value) {
      if (value !== this.subjectId) {
        this.subjectId = value
      }
    },
    newGoods(value) {
      if (value && this.isNew !== 1) {
        this.isNew = 1
      }
      if (!value && this.isNew !== 0) {
        this.isNew = 0
      }
    },
    isNew(value) {
      if (value !== this.newGoods) {
        this.$emit('update:newGoods', value)
      }

      this.mainClauseId = ''
      this.productId = ''
      this.handleClearValues()
    },
    subjectId: {
      immediate: true,
      handler(value) {
        this.mainClauseId = ''

        if (value !== 2 && value !== -1) {
          this.isExceptGoods = 0
        }

        this.handleClearValues()
        //获取产品选项卡的id
        this.$emit('subject-change', value)
      }
    },
    products: {
      immediate: true,
      handler() {
        if (this.product !== -1 && this.products?.products?.find((e) => e.id === this.product)) {
          this.productId = this.product

          this.mainClauseId = this.currentProduct.new_main_clause_id
          this.additionalClauseIds = this.currentProduct.new_additional_clause_ids
        }
      }
    },
    currentProduct(value) {
      this.$emit('product-change', Object.assign({}, value))
    },
    productId: {
      immediate: true,
      handler(value) {
        if (value && this.additionalClauses !== undefined && this.additionalClauses.length > 0) {
          const product = this.products?.products?.find((e) => e.id === value)
          if (product.new_additional_clause_id !== undefined) {
            this.additionalClauseIds = product?.new_additional_clause_ids
          }

          this.mainClauseId = product?.new_main_clause_id

          this.resetIsExceptGoods()
        }
      }
    },
    additionalClauseIds: {
      immediate: true,
      deep: true,
      handler(newValue, oldValue) {
        if (oldValue !== undefined && newValue !== undefined) {
          const newNoneIdx = newValue.findIndex((e) => parseInt(e, 10) <= 0)
          const oldNoneIdx = oldValue.findIndex((e) => parseInt(e, 10) <= 0)
          // 如果是新选择的无删除其他附件.
          if (newNoneIdx !== -1 && oldNoneIdx === -1) {
            this.additionalClauseIds = [newValue[newNoneIdx]]
          }

          if (oldNoneIdx !== -1 && newNoneIdx !== -1 && newValue.length > 1) {
            delete newValue[newNoneIdx]
            this.additionalClauseIds = newValue.filter((e) => e)
          }
        }
      }
    },
    isExceptGoods(value) {
      if (value) {
        this.$message.error('请正确选择货物类别投保或联系业务人员')
      }
      this.$emit('update:isExceptGoods', value)
    },
    subjectCategories(value) {
      this.$emit('update:subjectCategoryIds', value)
    },
    subjectCategoryIds(value) {
      if (JSON.stringify(value) !== JSON.stringify(this.subjectCategories)) {
        this.subjectCategories = value
      }
    },
    manualConditionsContent(value) {
      this.$emit('update:manualConditions', value)
    },
    manualConditions(value) {
      if (value !== this.manualConditionsContent) {
        this.manualConditionsContent = value
      }
    }
  },
  methods: {
    //选择产品的分类
    handleClearValues() {
      this.additionalClauseIds = []
      this.productId = ''

      this.resetIsExceptGoods()
    },
    resetIsExceptGoods() {
      if (!['edit', 'continue'].includes(this.$route.query.from)) {
        if (this.subjectId !== 2 && this.subjectId !== -1) {
          this.isExceptGoods = 0
        } else {
          this.isExceptGoods = ''
        }
      }
    },
    //选择附加险
    handleSelectAdditionalClause() {
      this.productId = ''

      this.resetIsExceptGoods()
    }
  },
  mounted() {
    this.handleClearValues()
    this.handleSelectAdditionalClause()

    if ((this.subjectId !== 2 && this.subjectId !== -1) || ['edit', 'continue'].includes(this.$route.query.from)) {
      this.isExceptGoods = 0
    }

    this.isNew = this.newGoods
    this.subjectCategories = this.subjectCategoryIds

    this.$emit('update:isExceptGoods', this.isExceptGoods)
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-alert__content {
  width: 100% !important;
}

/deep/ .el-tabs__item {
  padding: 0 15px;
}

.p-alert::before {
  content: '*';
  color: #f56c6c;
}

.policy-product {
  border: 1px solid #ebeef5;
  /deep/ .el-tabs {
    background-color: transparent;
    box-shadow: none;
    border: none;
  }
  .selected-content {
    padding: 0 15px 15px;
    .el-alert__content {
      width: 100% !important;
    }
    .field {
      @extend .d-flex;
      font-size: 14px;
      margin-bottom: $app-size-mini;
      label {
        width: 100px;
        min-width: 100px;
        font-weight: bold;
      }
      span {
        @extend .flex-fill;
      }
    }
  }
}
</style>
