<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2024-01-04 15:29:26
 * @LastEditors: yanb
 * @LastEditTime: 2024-01-09 16:27:44
-->
<template>
  <div>
    <el-select placeholder="请选择港口" v-model="port" class="w-100" filterable required :filter-method="searching">
      <el-option v-for="tp in taipingIntlPorts" :key="tp.code" :value="tp.txt" :label="tp.txt"></el-option>
    </el-select>
  </div>
</template>

<script>
import taipingIntlPortsJson from './tpic-intl-ports'

export default {
  name: 'TpicIntlPorts',
  props: {
    value: {
      require: true,
      type: String
    },
    suggestion: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      taipingIntlPorts: taipingIntlPortsJson.slice(0, 15),
      port: ''
    }
  },
  watch: {
    value(value) {
      this.port = value
    },
    suggestion(value) {
      if (value.length > 1) {
        const suggestionAddr = value[1].slice(0, 2)
        const taipingPort = this.taipingIntlPorts.find((p) => p.txt.indexOf(suggestionAddr) > -1)
        if (taipingPort) {
          this.port = taipingPort.txt
        }
      }
    },
    port(value) {
      this.$emit('input', value)
    }
  },
  mounted() {
    this.port = this.value
  },
  methods: {
    searching(value) {
      if (value.length > 0) {
        this.taipingIntlPorts = taipingIntlPortsJson.filter(
          (data) => !value || (data.txt += '').toLowerCase().includes(value.toLowerCase())
        )
      } else {
        this.taipingIntlPorts = taipingIntlPortsJson.slice(0, 15)
      }
    }
  }
}
</script>
