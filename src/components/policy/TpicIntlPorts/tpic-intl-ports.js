export default [
  {
    txt: 'CNXXF-新香坊-null',
    code: 'CNXXF'
  },
  {
    txt: 'CNBAD-保定-BAODING',
    code: 'CNBAD'
  },
  {
    txt: 'SCZ-山城镇-null',
    code: 'SCZ'
  },
  {
    txt: 'ETDDA-德雷达瓦-DIRE DAWA',
    code: 'ETDDA'
  },
  {
    txt: 'UZSEG-谢尔盖利-SERGELI',
    code: 'UZSEG'
  },
  {
    txt: 'CNPAJ-盘锦-PANJIN',
    code: 'CNPAJ'
  },
  {
    txt: 'CNLYS-涞源-null',
    code: 'CNLYS'
  },
  {
    txt: 'CNQXS-祁县-null',
    code: 'CNQXS'
  },
  {
    txt: 'BSY-白市驿-null',
    code: 'BSY'
  },
  {
    txt: 'YCN-银川南-null',
    code: 'YCN'
  },
  {
    txt: 'GQZ-高桥镇-null',
    code: 'GQZ'
  },
  {
    txt: 'JLT-吉兰泰-JLT',
    code: 'JLT'
  },
  {
    txt: 'CNHUB-随州-SUIZHOU',
    code: 'CNHUB'
  },
  {
    txt: 'UZRAU-劳斯坦-RAUSTAN',
    code: 'UZRAU'
  },
  {
    txt: 'PTBBL-波贝德拉-BOBADELA',
    code: 'PTBBL'
  },
  {
    txt: 'CNHULBR-呼伦贝尔-null',
    code: 'CNHULBR'
  },
  {
    txt: 'N_BX001-Kuala Belait-Kuala Belait',
    code: 'N_BX001'
  },
  {
    txt: 'N_BX002-Lumut (Brunei Darussalam)-Lumut (Brunei Darussalam)',
    code: 'N_BX002'
  },
  {
    txt: 'N_BX003-Seria-Seria',
    code: 'N_BX003'
  },
  {
    txt: 'N_BX004-Tanjong Salirong-Tanjong Salirong',
    code: 'N_BX004'
  },
  {
    txt: 'N_BG001-巴尔奇克-Balchik',
    code: 'N_BG001'
  },
  {
    txt: 'N_BG002-Burgas-Burgas',
    code: 'N_BG002'
  },
  {
    txt: 'N_BG003-Ruse-Ruse',
    code: 'N_BG003'
  },
  {
    txt: 'N_BG004-瓦尔纳-Varna',
    code: 'N_BG004'
  },
  {
    txt: 'N_CAM001-Ebome Marine Terminal-Ebome Marine Terminal',
    code: 'N_CAM001'
  },
  {
    txt: 'N_CAM002-FSO Kome Kribi 1-FSO Kome Kribi 1',
    code: 'N_CAM002'
  },
  {
    txt: 'N_CAM003-Kole Terminal-Kole Terminal',
    code: 'N_CAM003'
  },
  {
    txt: 'N_CAM004-克里比深水港-Kribi Deep Sea Port',
    code: 'N_CAM004'
  },
  {
    txt: 'N_CAM005-利姆波-Limboh Terminal',
    code: 'N_CAM005'
  },
  {
    txt: 'N_CAM006-Moudi Terminal-Moudi Terminal',
    code: 'N_CAM006'
  },
  {
    txt: 'N_CAM007-蒂科-Tiko',
    code: 'N_CAM007'
  },
  {
    txt: 'N_CAM008-Victoria (Cameroon)-Victoria (Cameroon)',
    code: 'N_CAM008'
  },
  {
    txt: 'N_CAN001-Amherst-Amherst',
    code: 'N_CAN001'
  },
  {
    txt: 'N_CAN002-Annapolis Royal-Annapolis Royal',
    code: 'N_CAN002'
  },
  {
    txt: 'N_CAN003-Arichat-Arichat',
    code: 'N_CAN003'
  },
  {
    txt: "N_CAN004-Arnold's Cove-Arnold's Cove",
    code: 'N_CAN004'
  },
  {
    txt: 'N_CAN005-Baddeck-Baddeck',
    code: 'N_CAN005'
  },
  {
    txt: 'N_CAN006-Baie-Comeau-Baie-Comeau',
    code: 'N_CAN006'
  },
  {
    txt: 'N_CAN007-Bathurst-Bathurst',
    code: 'N_CAN007'
  },
  {
    txt: 'N_CAN008-圣斯蒂芬-Bayside',
    code: 'N_CAN008'
  },
  {
    txt: 'N_CAN009-贝布尔士-Bay Bulls',
    code: 'N_CAN009'
  },
  {
    txt: 'N_CAN0010-罗伯特-Bay Roberts',
    code: 'N_CAN0010'
  },
  {
    txt: 'N_CAN0011-Beaver Harbour-Beaver Harbour',
    code: 'N_CAN0011'
  },
  {
    txt: 'N_CAN0012-比坎考尔-Becancour',
    code: 'N_CAN0012'
  },
  {
    txt: 'N_CAN0013-贝尔杜内-Belledune',
    code: 'N_CAN0013'
  },
  {
    txt: 'N_CAN0014-Blanc-Sablon-Blanc-Sablon',
    code: 'N_CAN0014'
  },
  {
    txt: 'N_CAN0015-博特伍德-Botwood',
    code: 'N_CAN0015'
  },
  {
    txt: 'N_CAN0016-鲍曼维尔-Bowmanville',
    code: 'N_CAN0016'
  },
  {
    txt: 'N_CAN0017-Britt-Britt',
    code: 'N_CAN0017'
  },
  {
    txt: 'N_CAN0018-布里奇沃特-Bridgewater',
    code: 'N_CAN0018'
  },
  {
    txt: 'N_CAN0019-Brockville-Brockville',
    code: 'N_CAN0019'
  },
  {
    txt: 'N_CAN0020-布鲁斯闵斯-Bruce Mines',
    code: 'N_CAN0020'
  },
  {
    txt: 'N_CAN0021-Bull Arm-Bull Arm',
    code: 'N_CAN0021'
  },
  {
    txt: 'N_CAN0022-Burgeo-Burgeo',
    code: 'N_CAN0022'
  },
  {
    txt: 'N_CAN0023-Campbellton-Campbellton',
    code: 'N_CAN0023'
  },
  {
    txt: 'N_CAN0024-Campbell River-Campbell River',
    code: 'N_CAN0024'
  },
  {
    txt: 'N_CAN0025-Cambridge Bay-Cambridge Bay',
    code: 'N_CAN0025'
  },
  {
    txt: 'N_CAN0026-Canaport-Canaport',
    code: 'N_CAN0026'
  },
  {
    txt: 'N_CAN0027-Canso-Canso',
    code: 'N_CAN0027'
  },
  {
    txt: 'N_CAN0028-Cape Tormentine-Cape Tormentine',
    code: 'N_CAN0028'
  },
  {
    txt: 'N_CAN0029-Cap-aux-Meules-Cap-aux-Meules',
    code: 'N_CAN0029'
  },
  {
    txt: 'N_CAN0030-Caraquet-Caraquet',
    code: 'N_CAN0030'
  },
  {
    txt: 'N_CAN0031-卡尔顿-Carleton',
    code: 'N_CAN0031'
  },
  {
    txt: 'N_CAN0032-Carbonear-Carbonear',
    code: 'N_CAN0032'
  },
  {
    txt: 'N_CAN0033-Cardinal-Cardinal',
    code: 'N_CAN0033'
  },
  {
    txt: 'N_CAN0034-Carmanville-Carmanville',
    code: 'N_CAN0034'
  },
  {
    txt: 'N_CAN0035-Catalina-Catalina',
    code: 'N_CAN0035'
  },
  {
    txt: 'N_CAN0036-钱德勒-Chandler',
    code: 'N_CAN0036'
  },
  {
    txt: 'N_CAN0037-夏洛特敦-Charlottetown',
    code: 'N_CAN0037'
  },
  {
    txt: 'N_CAN0038-Chatham-Chatham',
    code: 'N_CAN0038'
  },
  {
    txt: 'N_CAN0039-彻梅纳斯-Chemainus',
    code: 'N_CAN0039'
  },
  {
    txt: 'N_CAN0040-Clarkson-Clarkson',
    code: 'N_CAN0040'
  },
  {
    txt: 'N_CAN0041-克拉伦维尔-Clarenville',
    code: 'N_CAN0041'
  },
  {
    txt: 'N_CAN0042-Come By Chance-Come By Chance',
    code: 'N_CAN0042'
  },
  {
    txt: 'N_CAN0043-孔特勒克-Contrecoeur',
    code: 'N_CAN0043'
  },
  {
    txt: 'N_CAN0044-科纳布鲁克-Corner Brook',
    code: 'N_CAN0044'
  },
  {
    txt: 'N_CAN0045-康沃尔-Cornwall',
    code: 'N_CAN0045'
  },
  {
    txt: 'N_CAN0046-Cote-Sainte-Catherine-Cote-Sainte-Catherine',
    code: 'N_CAN0046'
  },
  {
    txt: 'N_CAN0047-莫尔顿-Courtright',
    code: 'N_CAN0047'
  },
  {
    txt: 'N_CAN0048-科威阐湾-Cowichan Bay',
    code: 'N_CAN0048'
  },
  {
    txt: 'N_CAN0049-克罗夫顿-Crofton',
    code: 'N_CAN0049'
  },
  {
    txt: 'N_CAN0050-达尔胡西-Dalhousie',
    code: 'N_CAN0050'
  },
  {
    txt: 'N_CAN0051-迪塞普申湾-Deception Bay',
    code: 'N_CAN0051'
  },
  {
    txt: 'N_CAN0052-迪格比-Digby',
    code: 'N_CAN0052'
  },
  {
    txt: 'SOHAR-索哈港-SOHAR',
    code: 'SOHAR'
  },
  {
    txt: 'GBSSH-南西尔兹-SOUTH SHIELDS',
    code: 'GBSSH'
  },
  {
    txt: 'EGSOS-索思迪-null',
    code: 'EGSOS'
  },
  {
    txt: 'PHSPS-苏比克湾-SUBIC BAY',
    code: 'PHSPS'
  },
  {
    txt: 'CNJZS-金洲-JINZHOU',
    code: 'CNJZS'
  },
  {
    txt: 'GJD-郭家店-null',
    code: 'GJD'
  },
  {
    txt: 'LJP-龙家堡-null',
    code: 'LJP'
  },
  {
    txt: 'BRIOA-伊塔普亚-ITAPOA',
    code: 'BRIOA'
  },
  {
    txt: 'KRPTK-平泽-PYONGTAEK',
    code: 'KRPTK'
  },
  {
    txt: 'MYPTP-丹戎帕拉帕斯港-PELABUHAN TANJUNG PELEPAS',
    code: 'MYPTP'
  },
  {
    txt: 'DEAPEN-阿彭-APEN',
    code: 'DEAPEN'
  },
  {
    txt: 'CNSANGRO-上饶-null',
    code: 'CNSANGRO'
  },
  {
    txt: 'LNHAI-海城市-null',
    code: 'LNHAI'
  },
  {
    txt: 'N_CAN0053-Duncan Bay-Duncan Bay',
    code: 'N_CAN0053'
  },
  {
    txt: 'N_CAN0054-Eastern Passage-Eastern Passage',
    code: 'N_CAN0054'
  },
  {
    txt: 'N_CAN0055-Esquimalt-Esquimalt',
    code: 'N_CAN0055'
  },
  {
    txt: 'N_CAN0056-Fisher Harbour-Fisher Harbour',
    code: 'N_CAN0056'
  },
  {
    txt: 'N_CAN0057-Fortune-Fortune',
    code: 'N_CAN0057'
  },
  {
    txt: 'N_CAN0058-FPSO Terra Nova FPSO-FPSO Terra Nova FPSO',
    code: 'N_CAN0058'
  },
  {
    txt: 'N_CAN0059-FPSO SeaRose-FPSO SeaRose',
    code: 'N_CAN0059'
  },
  {
    txt: 'N_CAN0060-Gananoque-Gananoque',
    code: 'N_CAN0060'
  },
  {
    txt: 'N_CAN0061-加斯佩-Gaspe',
    code: 'N_CAN0061'
  },
  {
    txt: 'N_CAN0062-Georgetown (Canada)-Georgetown (Canada)',
    code: 'N_CAN0062'
  },
  {
    txt: 'N_CAN0063-戈德瑞驰-Goderich',
    code: 'N_CAN0063'
  },
  {
    txt: 'N_CAN0064-Godbout-Godbout',
    code: 'N_CAN0064'
  },
  {
    txt: 'N_CAN0065-金河-Gold River',
    code: 'N_CAN0065'
  },
  {
    txt: 'N_CAN0066-古斯贝-Goose Bay',
    code: 'N_CAN0066'
  },
  {
    txt: 'N_CAN0067-格兰德班克-Grand Bank',
    code: 'N_CAN0067'
  },
  {
    txt: 'N_CAN0068-格罗斯卡科纳港-Gros Cacouna',
    code: 'N_CAN0068'
  },
  {
    txt: 'N_CAN0069-哈利法克斯-Halifax',
    code: 'N_CAN0069'
  },
  {
    txt: 'N_CAN0070-Hamilton (Canada)-Hamilton (Canada)',
    code: 'N_CAN0070'
  },
  {
    txt: 'N_CAN0071-Harbour Breton-Harbour Breton',
    code: 'N_CAN0071'
  },
  {
    txt: 'N_CAN0072-Havre St Pierre-Havre St Pierre',
    code: 'N_CAN0072'
  },
  {
    txt: 'N_CAN0073-霍利鲁德-Holyrood',
    code: 'N_CAN0073'
  },
  {
    txt: 'N_CAN0074-Iona-Iona',
    code: 'N_CAN0074'
  },
  {
    txt: 'N_CAN0075-埃卡鲁特-Iqaluit',
    code: 'N_CAN0075'
  },
  {
    txt: 'N_CAN0076-Ivy Lea-Ivy Lea',
    code: 'N_CAN0076'
  },
  {
    txt: 'N_CAN0077-Kelsey Bay-Kelsey Bay',
    code: 'N_CAN0077'
  },
  {
    txt: 'N_CAN0078-Kingsville-Kingsville',
    code: 'N_CAN0078'
  },
  {
    txt: 'N_CAN0079-Kingston (Canada)-Kingston (Canada)',
    code: 'N_CAN0079'
  },
  {
    txt: 'N_CAN0080-基提马特-Kitimat',
    code: 'N_CAN0080'
  },
  {
    txt: 'N_CAN0081-Klemtu-Klemtu',
    code: 'N_CAN0081'
  },
  {
    txt: 'N_CAN0082-La Have-La Have',
    code: 'N_CAN0082'
  },
  {
    txt: 'N_CAN0083-Ladysmith-Ladysmith',
    code: 'N_CAN0083'
  },
  {
    txt: "N_CAN0084-Lambert's Cove-Lambert's Cove",
    code: 'N_CAN0084'
  },
  {
    txt: 'N_CAN0085-利明顿-Leamington',
    code: 'N_CAN0085'
  },
  {
    txt: 'N_CAN0086-Les Escoumins-Les Escoumins',
    code: 'N_CAN0086'
  },
  {
    txt: 'N_CAN0087-莱莫尚-Les Mechins',
    code: 'N_CAN0087'
  },
  {
    txt: 'N_CAN0088-Lewisporte-Lewisporte',
    code: 'N_CAN0088'
  },
  {
    txt: 'N_CAN0089-Little Narrows-Little Narrows',
    code: 'N_CAN0089'
  },
  {
    txt: 'N_CAN0090-Little Current-Little Current',
    code: 'N_CAN0090'
  },
  {
    txt: 'N_CAN0091-Liverpool (Canada)-Liverpool (Canada)',
    code: 'N_CAN0091'
  },
  {
    txt: 'N_CAN0092-长庞德-Long Pond',
    code: 'N_CAN0092'
  },
  {
    txt: 'N_CAN0093-隆港-Long Harbour',
    code: 'N_CAN0093'
  },
  {
    txt: "N_CAN0094-Lord's Cove-Lord's Cove",
    code: 'N_CAN0094'
  },
  {
    txt: 'N_CAN0095-路易斯堡-Louisbourg',
    code: 'N_CAN0095'
  },
  {
    txt: 'N_CAN0096-Lower Cove-Lower Cove',
    code: 'N_CAN0096'
  },
  {
    txt: 'N_CAN0097-LOYALIST COVE-LOYALIST COVE',
    code: 'N_CAN0097'
  },
  {
    txt: 'N_CAN0098-Lunenburg-Lunenburg',
    code: 'N_CAN0098'
  },
  {
    txt: 'N_CAN0099-Main Brook-Main Brook',
    code: 'N_CAN0099'
  },
  {
    txt: 'N_CAN00100-Marystown-Marystown',
    code: 'N_CAN00100'
  },
  {
    txt: 'N_CAN00101-马塔讷-Matane',
    code: 'N_CAN00101'
  },
  {
    txt: 'N_CAN00102-米尔杜姆湾-Meldrum Bay',
    code: 'N_CAN00102'
  },
  {
    txt: 'N_CAN00103-Middle Point-Middle Point',
    code: 'N_CAN00103'
  },
  {
    txt: 'N_CAN00104-Midland-Midland',
    code: 'N_CAN00104'
  },
  {
    txt: 'N_CAN00105-Milne Port-Milne Port',
    code: 'N_CAN00105'
  },
  {
    txt: 'N_CAN00106-密西索加-Mississauga',
    code: 'N_CAN00106'
  },
  {
    txt: 'N_CAN00107-Montague-Montague',
    code: 'N_CAN00107'
  },
  {
    txt: 'N_CAN00108-蒙特利尔-Montreal',
    code: 'N_CAN00108'
  },
  {
    txt: 'N_CAN00109-Morrisburg-Morrisburg',
    code: 'N_CAN00109'
  },
  {
    txt: 'N_CAN00110-Mulgrave-Mulgrave',
    code: 'N_CAN00110'
  },
  {
    txt: 'N_CAN00111-纳奈莫-Nanaimo',
    code: 'N_CAN00111'
  },
  {
    txt: 'N_CAN00112-南提考克-Nanticoke',
    code: 'N_CAN00112'
  },
  {
    txt: 'N_CAN00113-Natashquan-Natashquan',
    code: 'N_CAN00113'
  },
  {
    txt: 'N_CAN00114-Newcastle (Canada)-Newcastle (Canada)',
    code: 'N_CAN00114'
  },
  {
    txt: 'N_CAN00115-New Richmond-New Richmond',
    code: 'N_CAN00115'
  },
  {
    txt: 'N_CAN00116-北西德尼-North Sydney',
    code: 'N_CAN00116'
  },
  {
    txt: 'N_CAN00117-Oakville-Oakville',
    code: 'N_CAN00117'
  },
  {
    txt: 'N_CAN00118-Ocean Falls-Ocean Falls',
    code: 'N_CAN00118'
  },
  {
    txt: 'N_CAN00119-奥沙瓦-Oshawa',
    code: 'N_CAN00119'
  },
  {
    txt: 'N_CAN00120-Owen Sound-Owen Sound',
    code: 'N_CAN00120'
  },
  {
    txt: 'N_CAN00121-Parrsboro-Parrsboro',
    code: 'N_CAN00121'
  },
  {
    txt: 'N_CAN00122-Parry Sound-Parry Sound',
    code: 'N_CAN00122'
  },
  {
    txt: 'SEGEN-日内瓦-GENEVA',
    code: 'SEGEN'
  },
  {
    txt: 'CNCF-赤峰-CHIFENG',
    code: 'CNCF'
  },
  {
    txt: 'CNHSTS-虎石台-null',
    code: 'CNHSTS'
  },
  {
    txt: 'CNFJS-福建-FUJIAN',
    code: 'CNFJS'
  },
  {
    txt: 'JZS-晋州-null',
    code: 'JZS'
  },
  {
    txt: 'NQ-内邱-null',
    code: 'NQ'
  },
  {
    txt: 'YD-远达-null',
    code: 'YD'
  },
  {
    txt: 'LS-梁山-null',
    code: 'LS'
  },
  {
    txt: 'CNLJY-龙家营-null',
    code: 'CNLJY'
  },
  {
    txt: 'CNXX-新乡-null',
    code: 'CNXX'
  },
  {
    txt: 'CNXLS-新乐-null',
    code: 'CNXLS'
  },
  {
    txt: 'HDG-黄岛港-null',
    code: 'HDG'
  },
  {
    txt: 'GAOBU-皋埠-null',
    code: 'GAOBU'
  },
  {
    txt: 'VORSINO-沃尔西诺-null',
    code: 'VORSINO'
  },
  {
    txt: 'LODZ-罗兹-null',
    code: 'LODZ'
  },
  {
    txt: 'CNWU-乌海-WUHAI',
    code: 'CNWU'
  },
  {
    txt: 'QINGZHOU-青州-null',
    code: 'QINGZHOU'
  },
  {
    txt: 'N_CAN00123-帕斯佩比亚克-Paspebiac',
    code: 'N_CAN00123'
  },
  {
    txt: 'N_CAN00124-Pelee Island-Pelee Island',
    code: 'N_CAN00124'
  },
  {
    txt: 'N_CAN00125-Penetanguishene-Penetanguishene',
    code: 'N_CAN00125'
  },
  {
    txt: 'N_CAN00126-皮克图-Pictou',
    code: 'N_CAN00126'
  },
  {
    txt: 'N_CAN00127-Picton (Canada)-Picton (Canada)',
    code: 'N_CAN00127'
  },
  {
    txt: 'N_CAN00128-Pointe-au-Pic-Pointe-au-Pic',
    code: 'N_CAN00128'
  },
  {
    txt: 'N_CAN00129-Pond Inlet-Pond Inlet',
    code: 'N_CAN00129'
  },
  {
    txt: 'N_CAN00130-波特诺弗-Portneuf',
    code: 'N_CAN00130'
  },
  {
    txt: 'N_CAN00131-Port Saguenay-Port Saguenay',
    code: 'N_CAN00131'
  },
  {
    txt: 'N_CAN00132-艾伯尼港-Port Alberni',
    code: 'N_CAN00132'
  },
  {
    txt: 'N_CAN00133-艾丽斯港-Port Alice',
    code: 'N_CAN00133'
  },
  {
    txt: 'N_CAN00134-霍克斯伯里港-Port Hawkesbury',
    code: 'N_CAN00134'
  },
  {
    txt: 'N_CAN00135-Port Hardy-Port Hardy',
    code: 'N_CAN00135'
  },
  {
    txt: 'N_CAN00136-Port Hope-Port Hope',
    code: 'N_CAN00136'
  },
  {
    txt: 'N_CAN00137-梅伦港-Port Mellon',
    code: 'N_CAN00137'
  },
  {
    txt: 'N_CAN00138-port daniel est-port daniel est',
    code: 'N_CAN00138'
  },
  {
    txt: 'N_CAN00139-port mcneill-port mcneill',
    code: 'N_CAN00139'
  },
  {
    txt: 'N_CAN00140-Port Stanley (Canada)-Port Stanley (Canada)',
    code: 'N_CAN00140'
  },
  {
    txt: 'N_CAN00141-巴斯克港-Port aux Basques',
    code: 'N_CAN00141'
  },
  {
    txt: 'N_CAN00142-鲍威尔里弗-Powell River',
    code: 'N_CAN00142'
  },
  {
    txt: 'N_CAN00143-Prescott-Prescott',
    code: 'N_CAN00143'
  },
  {
    txt: 'N_CAN00144-鲁珀特太子港-Prince Rupert',
    code: 'N_CAN00144'
  },
  {
    txt: 'N_CAN00145-帕格沃什-Pugwash',
    code: 'N_CAN00145'
  },
  {
    txt: 'N_CAN00146-魁北克-Quebec',
    code: 'N_CAN00146'
  },
  {
    txt: 'N_CAN00147-Richibucto-Richibucto',
    code: 'N_CAN00147'
  },
  {
    txt: 'N_CAN00148-里木斯基-Rimouski',
    code: 'N_CAN00148'
  },
  {
    txt: 'N_CAN00149-Rockport (Canada)-Rockport (Canada)',
    code: 'N_CAN00149'
  },
  {
    txt: 'N_CAN00150-Roddickton-Roddickton',
    code: 'N_CAN00150'
  },
  {
    txt: 'N_CAN00151-Sainte-Anne-des-Monts-Sainte-Anne-des-Monts',
    code: 'N_CAN00151'
  },
  {
    txt: 'N_CAN00152-圣约翰斯-Saint John',
    code: 'N_CAN00152'
  },
  {
    txt: 'N_CAN00153-萨尼亚-Sarnia',
    code: 'N_CAN00153'
  },
  {
    txt: 'N_CAN00154-Sault Ste Marie-Sault Ste Marie',
    code: 'N_CAN00154'
  },
  {
    txt: 'N_CAN00155-Sechelt-Sechelt',
    code: 'N_CAN00155'
  },
  {
    txt: 'N_CAN00156-Sept-Iles-Sept-Iles',
    code: 'N_CAN00156'
  },
  {
    txt: 'N_CAN00157-Shediac-Shediac',
    code: 'N_CAN00157'
  },
  {
    txt: 'N_CAN00158-Shelburne-Shelburne',
    code: 'N_CAN00158'
  },
  {
    txt: 'N_CAN00159-悉尼-Sidney',
    code: 'N_CAN00159'
  },
  {
    txt: 'N_CAN00160-Souris-Souris',
    code: 'N_CAN00160'
  },
  {
    txt: 'N_CAN00161-South Baymouth-South Baymouth',
    code: 'N_CAN00161'
  },
  {
    txt: 'N_CAN00162-Spragge-Spragge',
    code: 'N_CAN00162'
  },
  {
    txt: 'N_CAN00163-斯阔米什-Squamish',
    code: 'N_CAN00163'
  },
  {
    txt: 'N_CAN00164-St Lawrence Seaway-St Lawrence Seaway',
    code: 'N_CAN00164'
  },
  {
    txt: "N_CAN00165-St John's (Canada)-St John's (Canada)",
    code: 'N_CAN00165'
  },
  {
    txt: 'N_CAN00166-St Anthony-St Anthony',
    code: 'N_CAN00166'
  },
  {
    txt: 'N_CAN00167-斯蒂芬维尔-Stephenville',
    code: 'N_CAN00167'
  },
  {
    txt: 'N_CAN00168-斯提瓦特港-Stewart',
    code: 'N_CAN00168'
  },
  {
    txt: 'N_CAN00169-Sturdies Bay-Sturdies Bay',
    code: 'N_CAN00169'
  },
  {
    txt: 'N_CAN00170-萨默赛德-Summerside',
    code: 'N_CAN00170'
  },
  {
    txt: 'N_CAN00171-Sydney (Canada)-Sydney (Canada)',
    code: 'N_CAN00171'
  },
  {
    txt: 'N_CAN00172-Tahsis-Tahsis',
    code: 'N_CAN00172'
  },
  {
    txt: 'N_CAN00173-Texada Island-Texada Island',
    code: 'N_CAN00173'
  },
  {
    txt: 'N_CAN00174-塞萨伦-Thessalon',
    code: 'N_CAN00174'
  },
  {
    txt: 'N_CAN00175-散德贝-Thunder Bay',
    code: 'N_CAN00175'
  },
  {
    txt: 'N_CAN00176-Trois-Rivieres-Trois-Rivieres',
    code: 'N_CAN00176'
  },
  {
    txt: 'N_CAN00177-Tuktoyaktuk-Tuktoyaktuk',
    code: 'N_CAN00177'
  },
  {
    txt: 'N_CAN00178-Twillingate-Twillingate',
    code: 'N_CAN00178'
  },
  {
    txt: 'N_CAN00179-Ulukhaktok-Ulukhaktok',
    code: 'N_CAN00179'
  },
  {
    txt: 'N_CAN00180-瓦莱菲尔德-Valleyfield',
    code: 'N_CAN00180'
  },
  {
    txt: 'N_CAN00181-Vancouver (Canada)-Vancouver (Canada)',
    code: 'N_CAN00181'
  },
  {
    txt: 'N_CAN00182-Victoria (Canada)-Victoria (Canada)',
    code: 'N_CAN00182'
  },
  {
    txt: 'N_CAN00183-Welland Canal-Welland Canal',
    code: 'N_CAN00183'
  },
  {
    txt: 'N_CAN00184-Weymouth (Canada)-Weymouth (Canada)',
    code: 'N_CAN00184'
  },
  {
    txt: 'N_CAN00185-Whitefish River-Whitefish River',
    code: 'N_CAN00185'
  },
  {
    txt: 'N_CAN00186-温莎-Windsor',
    code: 'N_CAN00186'
  },
  {
    txt: 'N_CA001-arinaga-arinaga',
    code: 'N_CA001'
  },
  {
    txt: 'INPIP-皮帕瓦沃-PIPAVAV',
    code: 'INPIP'
  },
  {
    txt: 'CNQIT-其它-其它',
    code: 'CNQIT'
  },
  {
    txt: 'DOHAI-海纳-RIO HAINA',
    code: 'DOHAI'
  },
  {
    txt: 'MEMPHIS-孟菲斯-MEMPHIS',
    code: 'MEMPHIS'
  },
  {
    txt: 'UZCHU-丘库尔赛-CHUKURSAY',
    code: 'UZCHU'
  },
  {
    txt: 'EGSOK-苏哈纳-null',
    code: 'EGSOK'
  },
  {
    txt: 'UZGBK-乌卢格别克-ULUGBEK',
    code: 'UZGBK'
  },
  {
    txt: 'TSB-唐山北站-null',
    code: 'TSB'
  },
  {
    txt: 'BRSUA-苏阿佩港-SUAPE',
    code: 'BRSUA'
  },
  {
    txt: 'CNKYS-开原-null',
    code: 'CNKYS'
  },
  {
    txt: 'FXS-凤县-null',
    code: 'FXS'
  },
  {
    txt: 'NPBIR-比尔根杰-BIRGANJ',
    code: 'NPBIR'
  },
  {
    txt: ' KEMY-巴生北港-null',
    code: ' KEMY'
  },
  {
    txt: 'CNLIJG-丽江-null',
    code: 'CNLIJG'
  },
  {
    txt: 'TRAVY-艾维亚普港-EVYAPPORT',
    code: 'TRAVY'
  },
  {
    txt: 'BGBOJ-布尔加斯- BURGAS',
    code: 'BGBOJ'
  },
  {
    txt: 'N_BR0010-卡贝德洛-Cabedelo',
    code: 'N_BR0010'
  },
  {
    txt: 'N_BR0011-Campos Basin (Pampo Oil Field)-Campos Basin (Pampo Oil Field)',
    code: 'N_BR0011'
  },
  {
    txt: 'N_BR0012-Carmopolis-Carmopolis',
    code: 'N_BR0012'
  },
  {
    txt: 'N_BR0013-Coari-Coari',
    code: 'N_BR0013'
  },
  {
    txt: 'N_BR0014-Corumba-Corumba',
    code: 'N_BR0014'
  },
  {
    txt: 'N_BR0015-Fazendinha-Fazendinha',
    code: 'N_BR0015'
  },
  {
    txt: 'N_BR0016-佛诺-Forno',
    code: 'N_BR0016'
  },
  {
    txt: 'N_BR0017-FPSO Cidade de Caraguatatuba MV27-FPSO Cidade de Caraguatatuba MV27',
    code: 'N_BR0017'
  },
  {
    txt: 'N_BR0018-FPSO Cidade de Itaguai MV26-FPSO Cidade de Itaguai MV26',
    code: 'N_BR0018'
  },
  {
    txt: 'N_BR0019-FPSO Cidade de Mangaratiba MV24-FPSO Cidade de Mangaratiba MV24',
    code: 'N_BR0019'
  },
  {
    txt: 'N_BR0020-FPSO Cidade de Marica-FPSO Cidade de Marica',
    code: 'N_BR0020'
  },
  {
    txt: 'N_BR0021-FPSO Cidade de Saquarema-FPSO Cidade de Saquarema',
    code: 'N_BR0021'
  },
  {
    txt: 'N_BR0022-FPSO Petrobras 66 (Lula Sul field)-FPSO Petrobras 66 (Lula Sul field)',
    code: 'N_BR0022'
  },
  {
    txt: 'N_BR0023-FPSO Pioneiro De Libra-FPSO Pioneiro De Libra',
    code: 'N_BR0023'
  },
  {
    txt: 'N_BR0024-FPSO Dynamic Producer-FPSO Dynamic Producer',
    code: 'N_BR0024'
  },
  {
    txt: 'N_BR0025-FPSO Piranema Spirit-FPSO Piranema Spirit',
    code: 'N_BR0025'
  },
  {
    txt: 'N_BR0026-FPSO Petrojarl I-FPSO Petrojarl I',
    code: 'N_BR0026'
  },
  {
    txt: 'N_BR0027-FPSO Petrobras 58-FPSO Petrobras 58',
    code: 'N_BR0027'
  },
  {
    txt: 'N_BR0028-FPSO P 67-FPSO P 67',
    code: 'N_BR0028'
  },
  {
    txt: 'N_BR0029-FPSO CARIOCA-FPSO CARIOCA',
    code: 'N_BR0029'
  },
  {
    txt: 'N_BR0030-FPSO P69-FPSO P69',
    code: 'N_BR0030'
  },
  {
    txt: 'N_BR0031-FPSO P70-FPSO P70',
    code: 'N_BR0031'
  },
  {
    txt: 'N_BR0032-FPSO P76-FPSO P76',
    code: 'N_BR0032'
  },
  {
    txt: 'N_BR0033-FPSO P75-FPSO P75',
    code: 'N_BR0033'
  },
  {
    txt: 'N_BR0034-FPSO Cidade de Angra dos Reis MV22-FPSO Cidade de Angra dos Reis MV22',
    code: 'N_BR0034'
  },
  {
    txt: 'N_BR0035-FPSO Cidade de Santos MV20-FPSO Cidade de Santos MV20',
    code: 'N_BR0035'
  },
  {
    txt: 'N_BR0036-FPSO Cidade de Niteroi MV18-FPSO Cidade de Niteroi MV18',
    code: 'N_BR0036'
  },
  {
    txt: 'N_BR0037-FPSO Cidade do Rio de Janeiro MV14-FPSO Cidade do Rio de Janeiro MV14',
    code: 'N_BR0037'
  },
  {
    txt: 'N_BR0038-FPSO Petrobras 31 (Albacora Field)-FPSO Petrobras 31 (Albacora Field)',
    code: 'N_BR0038'
  },
  {
    txt: 'N_BR0039-FPSO Petrobras 32 (Marlim-2 Field)-FPSO Petrobras 32 (Marlim-2 Field)',
    code: 'N_BR0039'
  },
  {
    txt: 'N_BR0040-FPSO Petrobras 54 (Roncador Field)-FPSO Petrobras 54 (Roncador Field)',
    code: 'N_BR0040'
  },
  {
    txt: 'N_BR0041-FPSO Petrobras XXXIII (Marlim-3 Field)-FPSO Petrobras XXXIII (Marlim-3 Field)',
    code: 'N_BR0041'
  },
  {
    txt: 'N_BR0042-FPSO Petrobras 35 (Marlim-3 Field)-FPSO Petrobras 35 (Marlim-3 Field)',
    code: 'N_BR0042'
  },
  {
    txt: 'N_BR0043-FPSO Petrobras 37 (Marlim Field)-FPSO Petrobras 37 (Marlim Field)',
    code: 'N_BR0043'
  },
  {
    txt: 'N_BR0044-FPSO Petrobras 47 (Marlim Field)-FPSO Petrobras 47 (Marlim Field)',
    code: 'N_BR0044'
  },
  {
    txt: 'N_BR0045-FPSO Espirito Santo-FPSO Espirito Santo',
    code: 'N_BR0045'
  },
  {
    txt: 'N_BR0046-FPSO Capixaba (Cachalote Oil Field)-FPSO Capixaba (Cachalote Oil Field)',
    code: 'N_BR0046'
  },
  {
    txt: 'N_BR0047-FPSO Petrobras XXXIV (Jubarte Field)-FPSO Petrobras XXXIV (Jubarte Field)',
    code: 'N_BR0047'
  },
  {
    txt: 'N_BR0048-FPSO Petrobras 53 (Marlim Leste Field)-FPSO Petrobras 53 (Marlim Leste Field)',
    code: 'N_BR0048'
  },
  {
    txt: 'N_BR0049-FPSO Cidade de Anchieta-FPSO Cidade de Anchieta',
    code: 'N_BR0049'
  },
  {
    txt: 'N_BR0050-FPSO OSX 1 (Waimea field)-FPSO OSX 1 (Waimea field)',
    code: 'N_BR0050'
  },
  {
    txt: 'N_BR0051-FPSO Petrobras 43 (Barracuda Field)-FPSO Petrobras 43 (Barracuda Field)',
    code: 'N_BR0051'
  },
  {
    txt: 'N_BR0052-FPSO Petrobras 50 (Albacora Leste Field)-FPSO Petrobras 50 (Albacora Leste Field)',
    code: 'N_BR0052'
  },
  {
    txt: 'N_BR0053-FPSO Frade (Frade Field)-FPSO Frade (Frade Field)',
    code: 'N_BR0053'
  },
  {
    txt: 'N_BR0054-FPSO Petrobras 57 (Jubarte Field)-FPSO Petrobras 57 (Jubarte Field)',
    code: 'N_BR0054'
  },
  {
    txt: 'N_BR0055-FPSO Petrojarl Cidade de Rio das Ostras-FPSO Petrojarl Cidade de Rio das Ostras',
    code: 'N_BR0055'
  },
  {
    txt: 'N_BR0056-FPSO Petrobras 48 (Caratinga Field)-FPSO Petrobras 48 (Caratinga Field)',
    code: 'N_BR0056'
  },
  {
    txt: 'N_BR0057-FPSO Polvo (Polvo Field)-FPSO Polvo (Polvo Field)',
    code: 'N_BR0057'
  },
  {
    txt: 'CNTJC-团结村-null',
    code: 'CNTJC'
  },
  {
    txt: 'CNLD-莲东-null',
    code: 'CNLD'
  },
  {
    txt: 'NGONN-奥纳-onne',
    code: 'NGONN'
  },
  {
    txt: 'USLUI-路易维尔-LOUISVILLE',
    code: 'USLUI'
  },
  {
    txt: 'USSTP-圣保罗-ST PAUL',
    code: 'USSTP'
  },
  {
    txt: 'JXYZB-江苏扬州北-null',
    code: 'JXYZB'
  },
  {
    txt: 'ZG-自贡-null',
    code: 'ZG'
  },
  {
    txt: 'JZ-金州-null',
    code: 'JZ'
  },
  {
    txt: 'BLS-比灵斯-BILLINGS',
    code: 'BLS'
  },
  {
    txt: 'CNJBW-金帛湾-null',
    code: 'CNJBW'
  },
  {
    txt: 'HAX-洪安乡-null',
    code: 'HAX'
  },
  {
    txt: 'DHX-敦煌西-null',
    code: 'DHX'
  },
  {
    txt: 'REUTLIN-罗伊特林根-REUTLINGEN',
    code: 'REUTLIN'
  },
  {
    txt: 'Shuwaikh-舒瓦伊赫-Shuwaikh',
    code: 'Shuwaikh'
  },
  {
    txt: 'USST-南本德-SOUTHBEND',
    code: 'USST'
  },
  {
    txt: 'LAVIENTI-万象-VIENTIANE',
    code: 'LAVIENTI'
  },
  {
    txt: '台山-台山-null',
    code: '台山'
  },
  {
    txt: 'INDAD-达得里-ICD DADRI',
    code: 'INDAD'
  },
  {
    txt: 'CNSHIS-石嘴山-CNSHIS',
    code: 'CNSHIS'
  },
  {
    txt: 'N_BR0058-FPSO Peregrino (Peregrino Field)-FPSO Peregrino (Peregrino Field)',
    code: 'N_BR0058'
  },
  {
    txt: 'N_BR0059-FPSO OSX 3 (Tubarao Martelo Field)-FPSO OSX 3 (Tubarao Martelo Field)',
    code: 'N_BR0059'
  },
  {
    txt: 'N_BR0060-FPSO Cidade de Sao Paulo MV23-FPSO Cidade de Sao Paulo MV23',
    code: 'N_BR0060'
  },
  {
    txt: 'N_BR0061-FPSO Cidade de Paraty-FPSO Cidade de Paraty',
    code: 'N_BR0061'
  },
  {
    txt: 'N_BR0062-FPSO Cidade de Ilhabela-FPSO Cidade de Ilhabela',
    code: 'N_BR0062'
  },
  {
    txt: 'N_BR0063-FPSO Petrobras 63 (Papa Terra Field)-FPSO Petrobras 63 (Papa Terra Field)',
    code: 'N_BR0063'
  },
  {
    txt: 'N_BR0064-FPSO Cidade De Itajai-FPSO Cidade De Itajai',
    code: 'N_BR0064'
  },
  {
    txt: 'N_BR0065-FPSO Fluminense (Bijupira, Salema)-FPSO Fluminense (Bijupira, Salema)',
    code: 'N_BR0065'
  },
  {
    txt: 'N_BR0066-FSO Cidade de Macae MV15-FSO Cidade de Macae MV15',
    code: 'N_BR0066'
  },
  {
    txt: 'N_BR0067-FSO P-38 (Marlim Sul Oil Field)-FSO P-38 (Marlim Sul Oil Field)',
    code: 'N_BR0067'
  },
  {
    txt: 'N_BR0068-Gebig-Gebig',
    code: 'N_BR0068'
  },
  {
    txt: 'N_BR0069-Guaiba Island Terminal-Guaiba Island Terminal',
    code: 'N_BR0069'
  },
  {
    txt: 'N_BR0070-Guamare Oil Terminal-Guamare Oil Terminal',
    code: 'N_BR0070'
  },
  {
    txt: 'N_BR0071-Guanabara Bay Terminals-Guanabara Bay Terminals',
    code: 'N_BR0071'
  },
  {
    txt: 'N_BR0072-Guanabara Bay-Guanabara Bay',
    code: 'N_BR0072'
  },
  {
    txt: 'N_BR0073-伊列乌斯-Ilheus',
    code: 'N_BR0073'
  },
  {
    txt: 'N_BR0074-因比土巴-Imbituba',
    code: 'N_BR0074'
  },
  {
    txt: 'N_BR0075-伊塔基-Itaqui',
    code: 'N_BR0075'
  },
  {
    txt: 'N_BR0076-ITACOATIARA-ITACOATIARA',
    code: 'N_BR0076'
  },
  {
    txt: 'N_BR0077-Itacoatiara-Itacoatiara',
    code: 'N_BR0077'
  },
  {
    txt: 'N_BR0078-Juruti-Juruti',
    code: 'N_BR0078'
  },
  {
    txt: 'N_BR0079-Laguna-Laguna',
    code: 'N_BR0079'
  },
  {
    txt: 'N_BR0080-Macae-Macae',
    code: 'N_BR0080'
  },
  {
    txt: 'N_BR0081-Madre de Deus-Madre de Deus',
    code: 'N_BR0081'
  },
  {
    txt: 'N_BR0082-马瑙斯-Manaus',
    code: 'N_BR0082'
  },
  {
    txt: 'N_BR0083-Marlim - 3 Oil Field-Marlim - 3 Oil Field',
    code: 'N_BR0083'
  },
  {
    txt: 'N_BR0084-Marlim Oil Field-Marlim Oil Field',
    code: 'N_BR0084'
  },
  {
    txt: 'N_BR0085-Marlim Sul Oil Field-Marlim Sul Oil Field',
    code: 'N_BR0085'
  },
  {
    txt: 'N_BR0086-Marimba Oil Field-Marimba Oil Field',
    code: 'N_BR0086'
  },
  {
    txt: 'N_BR0087-Munguba-Munguba',
    code: 'N_BR0087'
  },
  {
    txt: 'N_BR0088-纳塔尔-Natal',
    code: 'N_BR0088'
  },
  {
    txt: 'N_BR0089-尼泰罗伊-Niteroi',
    code: 'N_BR0089'
  },
  {
    txt: 'N_BR0090-Obidos-Obidos',
    code: 'N_BR0090'
  },
  {
    txt: 'N_BR0091-巴拉那瓜-Paranagua',
    code: 'N_BR0091'
  },
  {
    txt: 'N_BR0092-Parentins-Parentins',
    code: 'N_BR0092'
  },
  {
    txt: 'N_BR0093-培森?-Pecem',
    code: 'N_BR0093'
  },
  {
    txt: 'N_BR0094-佩洛塔斯-Pelotas',
    code: 'N_BR0094'
  },
  {
    txt: 'N_BR0095-乌布港-Ponta Ubu',
    code: 'N_BR0095'
  },
  {
    txt: 'N_BR0096-阿里格-Porto Alegre',
    code: 'N_BR0096'
  },
  {
    txt: 'N_BR0097-Porto-Ilha-Porto-Ilha',
    code: 'N_BR0097'
  },
  {
    txt: 'N_BR0098-波托塞尔-Portocel',
    code: 'N_BR0098'
  },
  {
    txt: 'N_BR0099-Praia Mole-Praia Mole',
    code: 'N_BR0099'
  },
  {
    txt: 'N_BR00100-累西腓-Recife',
    code: 'N_BR00100'
  },
  {
    txt: 'N_BR00101-Regencia-Regencia',
    code: 'N_BR00101'
  },
  {
    txt: 'N_BR00102-里约热内卢-Rio de Janeiro',
    code: 'N_BR00102'
  },
  {
    txt: 'N_BR00103-Rio Grande (Brazil)-Rio Grande (Brazil)',
    code: 'N_BR00103'
  },
  {
    txt: 'N_BR00104-Salvador-Salvador',
    code: 'N_BR00104'
  },
  {
    txt: 'N_BR00105-圣塔伦-Santarem',
    code: 'N_BR00105'
  },
  {
    txt: 'N_BR00106-Santa Clara (Brazil)-Santa Clara (Brazil)',
    code: 'N_BR00106'
  },
  {
    txt: 'N_BR00107-圣塔纳-Santana',
    code: 'N_BR00107'
  },
  {
    txt: 'N_BR00108-桑托斯-Santos',
    code: 'N_BR00108'
  },
  {
    txt: 'N_BR00109-富兰克斯考-Sao Francisco do Sul',
    code: 'N_BR00109'
  },
  {
    txt: 'N_BR00110-圣塞巴斯蒂昂-Sao Sebastiao',
    code: 'N_BR00110'
  },
  {
    txt: 'N_BR00111-Sao Francisco do Sul Marine Terminal-Sao Francisco do Sul Marine Terminal',
    code: 'N_BR00111'
  },
  {
    txt: 'N_BR00112-Sotave-Sotave',
    code: 'N_BR00112'
  },
  {
    txt: 'N_BR00113-特拉曼达伊-Tramandai',
    code: 'N_BR00113'
  },
  {
    txt: 'N_BR00114-图巴朗-Tubarao',
    code: 'N_BR00114'
  },
  {
    txt: 'N_BR00115-Usiba-Usiba',
    code: 'N_BR00115'
  },
  {
    txt: 'N_BR00116-维拉多康德-Vila do Conde',
    code: 'N_BR00116'
  },
  {
    txt: 'N_BR00117-维多利亚-Vitoria',
    code: 'N_BR00117'
  },
  {
    txt: 'N_BIO001-Diego Garcia-Diego Garcia',
    code: 'N_BIO001'
  },
  {
    txt: 'PHLAO-拉瓦格-LAOAG',
    code: 'PHLAO'
  },
  {
    txt: 'PHLBK-莱巴克-LEBAK',
    code: 'PHLBK'
  },
  {
    txt: 'PHLIN-林加延-LINGAYEN',
    code: 'PHLIN'
  },
  {
    txt: 'PHLUN-拉乌尼翁-LAUNION',
    code: 'PHLUN'
  },
  {
    txt: 'PHMAN-马尼拉-MANILA',
    code: 'PHMAN'
  },
  {
    txt: 'PHMAS-马斯巴特-MASBATE',
    code: 'PHMAS'
  },
  {
    txt: 'PHMAT-马蒂-MATI',
    code: 'PHMAT'
  },
  {
    txt: 'TWHLN-花莲-HUALIAN ',
    code: 'TWHLN'
  },
  {
    txt: 'TWJIL-基隆-KEELUNG',
    code: 'TWJIL'
  },
  {
    txt: 'TWPHU-澎湖-PENGHU ',
    code: 'TWPHU'
  },
  {
    txt: 'TWSUO-苏奥-SUAO ',
    code: 'TWSUO'
  },
  {
    txt: 'TWTIB-台北-TAIPEI',
    code: 'TWTIB'
  },
  {
    txt: 'TWTNA-台南-TAINAN ',
    code: 'TWTNA'
  },
  {
    txt: 'TWTZH-台中-TAIZHONG ',
    code: 'TWTZH'
  },
  {
    txt: 'TZDRS-达累斯萨拉姆-DAR-ES-SALAAM',
    code: 'TZDRS'
  },
  {
    txt: 'TZKKI-基卢瓦基温杰-KILWA KIVINJE',
    code: 'TZKKI'
  },
  {
    txt: 'TZKMA-基卢瓦马索科-KILWA MASOKO',
    code: 'TZKMA'
  },
  {
    txt: 'TZLDI-林迪-LINDI',
    code: 'TZLDI'
  },
  {
    txt: 'TZMIK-米金达尼-MIKINDANI',
    code: 'TZMIK'
  },
  {
    txt: 'TZMTW-姆特瓦拉-MTWARA',
    code: 'TZMTW'
  },
  {
    txt: 'TZPAN-潘加尼-PANGANI',
    code: 'TZPAN'
  },
  {
    txt: 'TZPBI-奔巴岛-PEMBAS LAND',
    code: 'TZPBI'
  },
  {
    txt: 'TZTAN-坦噶-TANGA',
    code: 'TZTAN'
  },
  {
    txt: 'TZZAI-桑给巴尔岛-ZANZIBAR ISLAND',
    code: 'TZZAI'
  },
  {
    txt: 'UABGD-别尔哥罗德德涅斯罗夫-BELGOROD-DNESTROVSKIY',
    code: 'UABGD'
  },
  {
    txt: 'UAERD-别尔迪扬斯克-BERDIANSK',
    code: 'UAERD'
  },
  {
    txt: 'UAILK-伊利乔夫斯克-ILLYCHEVSK',
    code: 'UAILK'
  },
  {
    txt: 'UAIZM-伊兹梅尔-IZMAIL',
    code: 'UAIZM'
  },
  {
    txt: 'UAKER-刻赤-KERTCH',
    code: 'UAKER'
  },
  {
    txt: 'UAKHE-赫尔松-KHERSON',
    code: 'UAKHE'
  },
  {
    txt: 'UAKIA-基里拉-KILIYA',
    code: 'UAKIA'
  },
  {
    txt: 'UAKIV-基辅-KIEV',
    code: 'UAKIV'
  },
  {
    txt: 'UANIK-尼古拉耶夫-NIKOLAYEV',
    code: 'UANIK'
  },
  {
    txt: 'UAODS-敖德萨-ODESSA',
    code: 'UAODS'
  },
  {
    txt: 'UARNI-烈尼-RENI',
    code: 'UARNI'
  },
  {
    txt: 'UASEV-塞瓦斯托波尔-SEVASTOPOL',
    code: 'UASEV'
  },
  {
    txt: 'CNTZU-泰州-TAIZHOU',
    code: 'CNTZU'
  },
  {
    txt: 'CNULT-乌拉斯台-ULASTAI',
    code: 'CNULT'
  },
  {
    txt: 'CNURC-乌鲁木齐-URUMQI',
    code: 'CNURC'
  },
  {
    txt: 'CNWAN-畹町-WANDING',
    code: 'CNWAN'
  },
  {
    txt: 'CNWAS-万山-WANSHAN',
    code: 'CNWAS'
  },
  {
    txt: 'CNWAZ-湾仔-WANZAI',
    code: 'CNWAZ'
  },
  {
    txt: 'CNWEF-潍坊-WEIFANG',
    code: 'CNWEF'
  },
  {
    txt: 'CNWEH-威海-WEIHAI',
    code: 'CNWEH'
  },
  {
    txt: 'CNWHI-芜湖-WUHU',
    code: 'CNWHI'
  },
  {
    txt: 'CNWJD-文锦渡-WENJINDU',
    code: 'CNWJD'
  },
  {
    txt: 'CNWNZ-温州-WENZHOU',
    code: 'CNWNZ'
  },
  {
    txt: 'CNWOR-世界各地-WORLDWIDE',
    code: 'CNWOR'
  },
  {
    txt: 'CNWUH-梧州-WUZHOU',
    code: 'CNWUH'
  },
  {
    txt: 'CNWUS-武汉-WUHAN',
    code: 'CNWUS'
  },
  {
    txt: 'CNWUX-无锡-WUXI',
    code: 'CNWUX'
  },
  {
    txt: 'CNWUZ-吴淞-WUSONG',
    code: 'CNWUZ'
  },
  {
    txt: 'CNXAN-咸宁-null',
    code: 'CNXAN'
  },
  {
    txt: 'CNXCH-宣城-null',
    code: 'CNXCH'
  },
  {
    txt: 'CNXCU-肖厝-XIAOCUO',
    code: 'CNXCU'
  },
  {
    txt: 'CNXFN-襄樊-null',
    code: 'CNXFN'
  },
  {
    txt: 'CNXHX-香河县-null',
    code: 'CNXHX'
  },
  {
    txt: 'CNXIC-西昌-null',
    code: 'CNXIC'
  },
  {
    txt: 'CNXIN-新会-XINHUI',
    code: 'CNXIN'
  },
  {
    txt: 'CNXIS-萧山-XIAOSHAN',
    code: 'CNXIS'
  },
  {
    txt: 'CNXIT-新塘-XINTANG',
    code: 'CNXIT'
  },
  {
    txt: 'CNXME-厦门-XIAMEN',
    code: 'CNXME'
  },
  {
    txt: 'CNXMN-西双版纳-XISHUANGBANNA',
    code: 'CNXMN'
  },
  {
    txt: 'CNXNG-西宁-null',
    code: 'CNXNG'
  },
  {
    txt: 'CNXNN-西安-XIAN',
    code: 'CNXNN'
  },
  {
    txt: 'CNXUK-逊克-XUNKE',
    code: 'CNXUK'
  },
  {
    txt: 'CNXUZ-徐州-XUZHOU',
    code: 'CNXUZ'
  },
  {
    txt: 'CNXYG-秀屿-XIUYU',
    code: 'CNXYG'
  },
  {
    txt: 'CNYCG-永城-YOUGCHEN',
    code: 'CNYCG'
  },
  {
    txt: 'CNYCH-盐城-YANCHEN',
    code: 'CNYCH'
  },
  {
    txt: 'CNYEY-岳阳-YUANYANG',
    code: 'CNYEY'
  },
  {
    txt: 'CNYGJ-盈江-YINGJIANG',
    code: 'CNYGJ'
  },
  {
    txt: 'CNYIC-宜昌-null',
    code: 'CNYIC'
  },
  {
    txt: 'CNYIK-营口-YINGKOU',
    code: 'CNYIK'
  },
  {
    txt: 'CNYIN-伊宁-YINING',
    code: 'CNYIN'
  },
  {
    txt: 'CNYIW-义乌-YIWU',
    code: 'CNYIW'
  },
  {
    txt: 'CNYJI-阳江-YANGJIANG',
    code: 'CNYJI'
  },
  {
    txt: 'CNYNT-烟台-YANTAI',
    code: 'CNYNT'
  },
  {
    txt: 'CNYPG-洋浦-YANGPU',
    code: 'CNYPG'
  },
  {
    txt: 'CNYTN-盐田-YANTIAN',
    code: 'CNYTN'
  },
  {
    txt: 'CNYYG-友谊关-YOUYIGUAN',
    code: 'CNYYG'
  },
  {
    txt: 'CNYZH-扬州-YANGZHOU',
    code: 'CNYZH'
  },
  {
    txt: 'CNZBO-淄博-ZIBO',
    code: 'CNZBO'
  },
  {
    txt: 'CNZHA-湛江-ZHANJIANG',
    code: 'CNZHA'
  },
  {
    txt: 'CNZHC-诸城-ZHUCHENG',
    code: 'CNZHC'
  },
  {
    txt: 'CNZHE-镇江-ZHENJIANG',
    code: 'CNZHE'
  },
  {
    txt: 'CNZHF-章凤-ZHANGFENG',
    code: 'CNZHF'
  },
  {
    txt: 'CNZJG-张家港-ZHANGJIAGANG',
    code: 'CNZJG'
  },
  {
    txt: 'CNZMU-樟木-ZHANGM',
    code: 'CNZMU'
  },
  {
    txt: 'CNZNY-遵义-ZUNYI',
    code: 'CNZNY'
  },
  {
    txt: 'CNZQG-肇庆-ZHAOQING',
    code: 'CNZQG'
  },
  {
    txt: 'CNZSN-中山-ZHONGSHAN',
    code: 'CNZSN'
  },
  {
    txt: 'CNZUH-珠海-ZHUHAI',
    code: 'CNZUH'
  },
  {
    txt: 'COBAQ-巴兰基亚-BARRANQUILLA',
    code: 'COBAQ'
  },
  {
    txt: 'COBOA-波哥大-BOGOTA',
    code: 'COBOA'
  },
  {
    txt: 'COBUA-布韦那文图拉-BUENAVENTURA',
    code: 'COBUA'
  },
  {
    txt: 'CRSAJ-圣约瑟-SAN JOSE',
    code: 'CRSAJ'
  },
  {
    txt: 'ACPRA-普拉亚-PRAIA',
    code: 'ACPRA'
  },
  {
    txt: 'CYLIL-利马索尔-LIMASSOL',
    code: 'CYLIL'
  },
  {
    txt: 'CZPRA-布拉格-PRAHA',
    code: 'CZPRA'
  },
  {
    txt: 'CZVRY-佛翰纳尼-VRANANY',
    code: 'CZVRY'
  },
  {
    txt: 'DEALT-阿尔托纳-ALTONA  ',
    code: 'DEALT'
  },
  {
    txt: 'DEBER-柏林-Berlin',
    code: 'DEBER'
  },
  {
    txt: 'DEBLX-布莱克森-BLEXEN ',
    code: 'DEBLX'
  },
  {
    txt: 'DEBON-波恩-BONN ',
    code: 'DEBON'
  },
  {
    txt: 'DEBRA-布腊克-BRAKE ',
    code: 'DEBRA'
  },
  {
    txt: 'DEBRE-不来梅-BREMEN ',
    code: 'DEBRE'
  },
  {
    txt: 'DEBUS-比涩姆-BUSSUM ',
    code: 'DEBUS'
  },
  {
    txt: 'DEBUT-比茨费莱特-BUTZFLETH ',
    code: 'DEBUT'
  },
  {
    txt: 'DECOB-科布伦茨-COBLENZ ',
    code: 'DECOB'
  },
  {
    txt: 'DECOL-科隆-COLOGNE ',
    code: 'DECOL'
  },
  {
    txt: 'DECUX-库克斯港-CUXHAVEN ',
    code: 'DECUX'
  },
  {
    txt: 'DEDUS-杜塞尔多夫-DUSSELDORF ',
    code: 'DEDUS'
  },
  {
    txt: 'DEECK-埃肯弗尔德-ECKERNFORDE ',
    code: 'DEECK'
  },
  {
    txt: 'DEELS-埃尔斯费莱特-ELSFLETH ',
    code: 'DEELS'
  },
  {
    txt: 'DEEMD-埃姆登-EMDEN ',
    code: 'DEEMD'
  },
  {
    txt: 'DEFRA-法兰克福-FRANKFURT ',
    code: 'DEFRA'
  },
  {
    txt: 'GBPLY-普利茅斯-PLYMOUTH ',
    code: 'GBPLY'
  },
  {
    txt: 'GBPMR-彭迈恩毛尔-PENMAENMAWR ',
    code: 'GBPMR'
  },
  {
    txt: 'GBPOO-普尔-POOLE ',
    code: 'GBPOO'
  },
  {
    txt: 'GBPRE-普雷斯顿-PRESTON ',
    code: 'GBPRE'
  },
  {
    txt: 'GBPRH-波特拉什-PORTRUSH ',
    code: 'GBPRH'
  },
  {
    txt: 'GBPRN -彭林-PENRYN ',
    code: 'GBPRN '
  },
  {
    txt: 'GBPSM-圣马里港-PORT ST. MARY ',
    code: 'GBPSM'
  },
  {
    txt: 'GBPTA-塔尔伯特港-PORT TALBOT ',
    code: 'GBPTA'
  },
  {
    txt: 'GBPTE-波特里-PORTREE',
    code: 'GBPTE'
  },
  {
    txt: 'GBPTH-波次茅斯-PORTSMOUTH ',
    code: 'GBPTH'
  },
  {
    txt: 'GBPTN-帕廷顿-PARTINGTON ',
    code: 'GBPTN'
  },
  {
    txt: 'GBPWI-威廉港-PORT WILLIAM ',
    code: 'GBPWI'
  },
  {
    txt: 'GBPWL-普尔黑利-PWLLHELI ',
    code: 'GBPWL'
  },
  {
    txt: 'GBQBH-昆伯勒-QUEENBOROUGH ',
    code: 'GBQBH'
  },
  {
    txt: 'GBQSY-昆斯费里-QUEENSFERRY ',
    code: 'GBQSY'
  },
  {
    txt: 'GBRAM-拉姆斯盖特-RAMSGATE ',
    code: 'GBRAM'
  },
  {
    txt: 'GBRED-雷德卡-REDCAR ',
    code: 'GBRED'
  },
  {
    txt: 'GBRHM-雷纳姆-RAINHAM',
    code: 'GBRHM'
  },
  {
    txt: 'GBRHY-里尔-RHYL ',
    code: 'GBRHY'
  },
  {
    txt: 'GBRIC-里奇伯勒-RICHBOROUGH ',
    code: 'GBRIC'
  },
  {
    txt: 'GBRID-利德哈姆多克-RIDHAM DOCK ',
    code: 'GBRID'
  },
  {
    txt: 'GBROC-罗切斯特-ROCHESTER ',
    code: 'GBROC'
  },
  {
    txt: 'GBROS-罗赛斯-ROSYTH',
    code: 'GBROS'
  },
  {
    txt: 'GBROT-罗斯西-ROTHESAY ',
    code: 'GBROT'
  },
  {
    txt: 'GBRSY-拉姆西-RAMSEY ',
    code: 'GBRSY'
  },
  {
    txt: 'GBRUN-朗科恩-RUNCORN ',
    code: 'GBRUN'
  },
  {
    txt: 'GBRYE-拉伊-RYE ',
    code: 'GBRYE'
  },
  {
    txt: 'GBSAU-桑德斯富特-SAUNDERSFOOT ',
    code: 'GBSAU'
  },
  {
    txt: 'GBSCF-斯卡帕夫洛-SCAPA FLOW',
    code: 'GBSCF'
  },
  {
    txt: 'GBSCR-斯克拉布斯特-SCRABSTER ',
    code: 'GBSCR'
  },
  {
    txt: 'GBSDY-桑迪-SANDAY ',
    code: 'GBSDY'
  },
  {
    txt: 'GBSED-绍森德-SOUTHEND',
    code: 'GBSED'
  },
  {
    txt: 'GBSFD-斯特兰福德-STRANGFORD',
    code: 'GBSFD'
  },
  {
    txt: 'GBSHA-夏普内斯-SHARPNESE ',
    code: 'GBSHA'
  },
  {
    txt: 'GBSHE-圣赫利尔-ST.HELIER',
    code: 'GBSHE'
  },
  {
    txt: 'GBSHM-锡厄姆-SEAHAM ',
    code: 'GBSHM'
  },
  {
    txt: 'GBSIV-圣艾夫斯-ST.IVES',
    code: 'GBSIV'
  },
  {
    txt: 'GBSLH-谢尔赫文-SHELL HAVEN',
    code: 'GBSLH'
  },
  {
    txt: 'GBSLY-斯卡洛韦-SCALLOWSY ',
    code: 'GBSLY'
  },
  {
    txt: 'GBSMH-圣玛格丽茨贝-ST.MARGARET’S HOPE',
    code: 'GBSMH'
  },
  {
    txt: 'GBSNS-希尔内斯-SHEERNESS ',
    code: 'GBSNS'
  },
  {
    txt: 'GBSPP-圣彼德港-ST.PETER PORT',
    code: 'GBSPP'
  },
  {
    txt: 'GBSRR-斯特兰拉尔-STRANRAER',
    code: 'GBSRR'
  },
  {
    txt: 'GBSTA-斯坦洛-STANLOW',
    code: 'GBSTA'
  },
  {
    txt: 'GBSTO-斯托克顿-STOCKTON',
    code: 'GBSTO'
  },
  {
    txt: 'GBSTR-斯特罗姆内斯-STROMNESS',
    code: 'GBSTR'
  },
  {
    txt: 'GBSUN-森德兰-SUNDERLAND',
    code: 'GBSUN'
  },
  {
    txt: 'GBSUV-萨洛姆湾-SLLOM VOE',
    code: 'GBSUV'
  },
  {
    txt: 'GBSWA-斯旺西-SWANSEA',
    code: 'GBSWA'
  },
  {
    txt: 'GBSWH-桑德威奇-SANDWICH ',
    code: 'GBSWH'
  },
  {
    txt: 'GBSWY-斯托诺韦-STORNOWAY',
    code: 'GBSWY'
  },
  {
    txt: 'GBTAR-塔伯特-TARBERT',
    code: 'GBTAR'
  },
  {
    txt: 'GBTBY-滕比-TENBY',
    code: 'GBTBY'
  },
  {
    txt: 'GBTEE-提兹港-TEESPORT',
    code: 'GBTEE'
  },
  {
    txt: 'GBTEI-廷茅斯-TEIGNMOUTH',
    code: 'GBTEI'
  },
  {
    txt: 'GBTHU-瑟索-THURSO',
    code: 'GBTHU'
  },
  {
    txt: 'GBTIL-蒂尔伯里-TILBURY',
    code: 'GBTIL'
  },
  {
    txt: 'GBTNT-特奈特米纳尔-TENBY TERMINAL',
    code: 'GBTNT'
  },
  {
    txt: 'GBTOB-托伯莫里-TOBERMORY',
    code: 'GBTOB'
  },
  {
    txt: 'GBTOP-托普瑟姆-TOPSHAM',
    code: 'GBTOP'
  },
  {
    txt: 'GBTOR-托基-TORQUAY',
    code: 'GBTOR'
  },
  {
    txt: 'GBTOT-托特尼斯-TOTNES',
    code: 'GBTOT'
  },
  {
    txt: 'GBTRO-特伦-TROON',
    code: 'GBTRO'
  },
  {
    txt: 'GBTRU-特鲁多-TRURO',
    code: 'GBTRU'
  },
  {
    txt: 'GBTYD-太恩港-TYNE DOCK',
    code: 'GBTYD'
  },
  {
    txt: 'GBULL-阿勒浦-ULLAPOOL',
    code: 'GBULL'
  },
  {
    txt: 'GBWAR-沃克沃思-WARKWORTH',
    code: 'GBWAR'
  },
  {
    txt: 'GBWAT-沃切特-WATCHET',
    code: 'GBWAT'
  },
  {
    txt: 'GBWBY-惠特比-WHITBY',
    code: 'GBWBY'
  },
  {
    txt: 'GBWES-韦斯特雷-WESTRAY',
    code: 'GBWES'
  },
  {
    txt: 'GBWEY-韦茅斯-WEYMOUTH',
    code: 'GBWEY'
  },
  {
    txt: 'GBWHI-惠特斯特布尔-WHITSTABLE',
    code: 'GBWHI'
  },
  {
    txt: 'GBWHN-怀特黑文-WHITEHAVEN',
    code: 'GBWHN'
  },
  {
    txt: 'GBWIC-威克-WICK',
    code: 'GBWIC'
  },
  {
    txt: 'GBWIS-威斯贝奇-WISBECH',
    code: 'GBWIS'
  },
  {
    txt: 'GBWLS-韦尔斯-WELLS',
    code: 'GBWLS'
  },
  {
    txt: 'GBWOR-沃金顿-WORKINGTON',
    code: 'GBWOR'
  },
  {
    txt: 'GBWRP-沃伦波因特-WARREN POINT',
    code: 'GBWRP'
  },
  {
    txt: 'GBYAR-雅茅斯-YARMOUTH',
    code: 'GBYAR'
  },
  {
    txt: 'GDSGE-圣乔治-ST.GEORGE',
    code: 'GDSGE'
  },
  {
    txt: 'GEBUS-巴统-BATUMI',
    code: 'GEBUS'
  },
  {
    txt: 'GEPTI-波季-POTI',
    code: 'GEPTI'
  },
  {
    txt: 'GESUI-苏呼米-SUKHUMI',
    code: 'GESUI'
  },
  {
    txt: 'GETBI-第比利斯-TBILISI',
    code: 'GETBI'
  },
  {
    txt: 'GFCAY-卡宴-CAYENNE',
    code: 'GFCAY'
  },
  {
    txt: 'GFDDC-德格拉德卡内斯-DEGRAD DE CANNES',
    code: 'GFDDC'
  },
  {
    txt: 'GHACC-阿拉克-ACCRA',
    code: 'GHACC'
  },
  {
    txt: 'GHADA-阿达-ADDA',
    code: 'GHADA'
  },
  {
    txt: 'GHAXI-阿克西姆-AXIM',
    code: 'GHAXI'
  },
  {
    txt: 'GHCCT-海岸角-CAPE COAST',
    code: 'GHCCT'
  },
  {
    txt: 'GHKET-凯塔-KETA',
    code: 'GHKET'
  },
  {
    txt: 'GHSEK-塞康第-SECONDI',
    code: 'GHSEK'
  },
  {
    txt: 'GHTAK-塔科拉迪-TAKORADI',
    code: 'GHTAK'
  },
  {
    txt: 'GHTEM-特马-TEMA',
    code: 'GHTEM'
  },
  {
    txt: 'GHWIN-温尼巴-WINNEBA',
    code: 'GHWIN'
  },
  {
    txt: 'GLCHR-克里斯蒂安斯霍布-CHRISTIANSHAAB',
    code: 'GLCHR'
  },
  {
    txt: 'GLEGE-埃格瑟斯明讷-EGEDESMINDE',
    code: 'GLEGE'
  },
  {
    txt: 'GLFAE-费灵厄港-FAERINGEHAVN',
    code: 'GLFAE'
  },
  {
    txt: 'GLGHB-戈特霍布-GODTHAAB',
    code: 'GLGHB'
  },
  {
    txt: 'GLGOD-戈德港-GODHAVN',
    code: 'GLGOD'
  },
  {
    txt: 'GLHOL-荷尔斯泰因斯堡-HOLSTEINSBORG',
    code: 'GLHOL'
  },
  {
    txt: 'GLIVI-伊维赫图特-IVIGTUT',
    code: 'GLIVI'
  },
  {
    txt: 'GLJUL-尤利安娜霍布-JULIANEHAAB',
    code: 'GLJUL'
  },
  {
    txt: 'GLKAN-康加米尤特-KANGAMIUT',
    code: 'GLKAN'
  },
  {
    txt: 'GLMAR-马莫里利克-MARMORILIK',
    code: 'GLMAR'
  },
  {
    txt: 'GLSUK-苏克托彭-SUKKERTOPPEN',
    code: 'GLSUK'
  },
  {
    txt: 'GLUMA-乌马纳克-UMANAK',
    code: 'GLUMA'
  },
  {
    txt: 'GLUPE-乌佩尼维克-UOERNIVIK',
    code: 'GLUPE'
  },
  {
    txt: 'GNCNK-科纳克里-CONAKRY',
    code: 'GNCNK'
  },
  {
    txt: 'GRASS-阿斯塔科斯-ASTAKOS',
    code: 'GRASS'
  },
  {
    txt: 'GRATS-雅典-ATHENS',
    code: 'GRATS'
  },
  {
    txt: 'GRPIR-比雷埃夫斯-PIRAEUS',
    code: 'GRPIR'
  },
  {
    txt: 'GRPLI-利瓦德希港-PORT LIVADHI',
    code: 'GRPLI'
  },
  {
    txt: 'GRPRE-普雷韦扎-PREVEZA',
    code: 'GRPRE'
  },
  {
    txt: 'GRPYL-皮洛斯-PYLOS',
    code: 'GRPYL'
  },
  {
    txt: 'GRRDI-罗得-RHODES ISLAND',
    code: 'GRRDI'
  },
  {
    txt: 'GRRET-雷西姆农-RETHIMNON',
    code: 'GRRET'
  },
  {
    txt: 'GRSKO-斯科派洛斯-SKOPELOS',
    code: 'GRSKO'
  },
  {
    txt: 'GRSMS-萨摩斯岛-SAMOS',
    code: 'GRSMS'
  },
  {
    txt: 'GRSTR-斯特拉托尼-STRATONI',
    code: 'GRSTR'
  },
  {
    txt: 'GRVLS-伏洛斯-VOLOS',
    code: 'GRVLS'
  },
  {
    txt: 'GRYER-耶兰基尼-YERAKINI',
    code: 'GRYER'
  },
  {
    txt: 'GRYLI-亚利岛-YALI ISLAND',
    code: 'GRYLI'
  },
  {
    txt: 'GRZAN-赞特-ZANTE',
    code: 'GRZAN'
  },
  {
    txt: 'GTLIV-利文斯顿-LIVINGSTON',
    code: 'GTLIV'
  },
  {
    txt: 'GTPBA-巴里奥斯港-PUERTO BARRIOS',
    code: 'GTPBA'
  },
  {
    txt: 'GTPUQ-夸特扎尔-PUERTO QUETZAL',
    code: 'GTPUQ'
  },
  {
    txt: 'GTSJO-圣何塞-SAN JOSE',
    code: 'GTSJO'
  },
  {
    txt: 'GUAGA-阿加尼-AGANA',
    code: 'GUAGA'
  },
  {
    txt: 'GUAPR-阿普拉-APRA',
    code: 'GUAPR'
  },
  {
    txt: 'GWBIS-比绍-BISSAU',
    code: 'GWBIS'
  },
  {
    txt: 'GWBOL-博拉多-BOLAMA',
    code: 'GWBOL'
  },
  {
    txt: 'PHMIL-米尔布克-MILBUK',
    code: 'PHMIL'
  },
  {
    txt: 'PHMLK-马辛洛克-MASINLOK',
    code: 'PHMLK'
  },
  {
    txt: 'PHMSO-马萨豪-MASAO',
    code: 'PHMSO'
  },
  {
    txt: 'PHNAG-那牙-NAGA',
    code: 'PHNAG'
  },
  {
    txt: 'PHNAS-纳苏格布-NASUGBU',
    code: 'PHNAS'
  },
  {
    txt: 'PHNPT-纳斯皮特-NASIPIT',
    code: 'PHNPT'
  },
  {
    txt: 'PHOLO-奥隆阿坡-OLONGAPO',
    code: 'PHOLO'
  },
  {
    txt: 'PHORM-奥尔莫克-URMOC',
    code: 'PHORM'
  },
  {
    txt: 'CAHCT-哈茨康滕特-HEART’S CONTENT ',
    code: 'CAHCT'
  },
  {
    txt: 'CAHFX-哈利法克斯-HALIFAX ',
    code: 'CAHFX'
  },
  {
    txt: 'CAHGR-格雷斯港-HARBOUR GRACE',
    code: 'CAHGR'
  },
  {
    txt: 'CAHMC-哈麦克-HARMAC',
    code: 'CAHMC'
  },
  {
    txt: 'CAHOL-霍利鲁德-HOLYROOD ',
    code: 'CAHOL'
  },
  {
    txt: 'CAHSP-哈弗圣皮埃尔-HAVRE ST.PIERRE',
    code: 'CAHSP'
  },
  {
    txt: 'CAINA-约纳-IONA ',
    code: 'CAINA'
  },
  {
    txt: 'CAISH-埃萨克斯港-ISAAC’S HARBOUR',
    code: 'CAISH'
  },
  {
    txt: 'CAKIN-金斯顿-KINGSTON ',
    code: 'CAKIN'
  },
  {
    txt: 'CAKIT-基提马特-KITIMAT ',
    code: 'CAKIT'
  },
  {
    txt: 'CALAH -拉阿沃-LA HAVE ',
    code: 'CALAH '
  },
  {
    txt: 'CALIV-利物浦-LIVERPOOL ',
    code: 'CALIV'
  },
  {
    txt: 'CALMD-洛蒙德-LOMOND ',
    code: 'CALMD'
  },
  {
    txt: 'CALOC-洛克波特-LOCKEPORT ',
    code: 'CALOC'
  },
  {
    txt: 'CALOU-路易斯堡-LOUISBURG ',
    code: 'CALOU'
  },
  {
    txt: 'CALSC-利斯科姆-LIS COMB ',
    code: 'CALSC'
  },
  {
    txt: 'CAMAT-马塔纳-MATANE ',
    code: 'CAMAT'
  },
  {
    txt: 'CAMET-梅泰根-METEGHAN ',
    code: 'CAMET'
  },
  {
    txt: 'CAMIC-米奇皮科滕-MICHIPICOTEN (ONT.)',
    code: 'CAMIC'
  },
  {
    txt: 'CAMLS-蒙路易-MONT LOUIS ',
    code: 'CAMLS'
  },
  {
    txt: 'CAMON-蒙塔古-MONTAGUE ',
    code: 'CAMON'
  },
  {
    txt: 'CAMTL-蒙特利尔-MONTREAL ',
    code: 'CAMTL'
  },
  {
    txt: 'CANEW-纽卡斯尔-NEWCASTLE(N.B.)',
    code: 'CANEW'
  },
  {
    txt: 'CANGL-新格拉斯哥-NEW GLASGOW ',
    code: 'CANGL'
  },
  {
    txt: 'CANMO-纳奈莫-NANAIMO ',
    code: 'CANMO'
  },
  {
    txt: 'CANSY-北锡德尼-NORTH SYDNEY ',
    code: 'CANSY'
  },
  {
    txt: 'CANWE-新威斯敏斯特-NEW WESTMINSTER ',
    code: 'CANWE'
  },
  {
    txt: 'CAOAK-奥克维尔-OAKVILE (ONT.)',
    code: 'CAOAK'
  },
  {
    txt: 'CAOFS-福尔斯海-OCEAN FALLS ',
    code: 'CAOFS'
  },
  {
    txt: 'CAOSH-奥沙瓦-OSHAWA (ONT.)',
    code: 'CAOSH'
  },
  {
    txt: 'CAOTO-安大略-ONTARIO',
    code: 'CAOTO'
  },
  {
    txt: 'CAPAB-奥克斯巴凯斯港-PORT AUX BARQUES ',
    code: 'CAPAB'
  },
  {
    txt: 'CAPAE-艾利斯港-PORT ALICE ',
    code: 'CAPAE'
  },
  {
    txt: 'CAPAL-艾伯尼港-PORT ALBERNI ',
    code: 'CAPAL'
  },
  {
    txt: 'CAPAR-帕斯博勒-PARRSBORO ',
    code: 'CAPAR'
  },
  {
    txt: 'CAPCA-卡提尔港-PORT CARTIER',
    code: 'CAPCA'
  },
  {
    txt: 'CAPCO-科尔本港-PORT COLBORNE(ONT.)',
    code: 'CAPCO'
  },
  {
    txt: 'CAPCR-克雷迪特港-PORT CREDIT(ONT.)',
    code: 'CAPCR'
  },
  {
    txt: 'CAPDA-达尔胡西港-PORT DALHOUSIE',
    code: 'CAPDA'
  },
  {
    txt: 'CAPHO-霍普港-PORT HOPE (ONT.)',
    code: 'CAPHO'
  },
  {
    txt: 'CAPHS-霍普辛普森港-PORT HOPE SIMPSON',
    code: 'CAPHS'
  },
  {
    txt: 'CAPIC-皮克图-PICTOU ',
    code: 'CAPIC'
  },
  {
    txt: 'CAPME-梅德韦港-PORT MEDWAY',
    code: 'CAPME'
  },
  {
    txt: 'CAPOR-鲍威尔-POWELL RIVER ',
    code: 'CAPOR'
  },
  {
    txt: 'CAPRE-普雷斯科特-PRESCOTT ',
    code: 'CAPRE'
  },
  {
    txt: 'CAPRR-鲁珀特港-PRINCE RUPERT ',
    code: 'CAPRR'
  },
  {
    txt: 'CAPSD-帕里桑德-PARRY SOUND ',
    code: 'CAPSD'
  },
  {
    txt: 'CAPUG-帕格沃希-PUGWASH ',
    code: 'CAPUG'
  },
  {
    txt: 'CAPWE-威莱尔港-PORT WELLER(ONT.)',
    code: 'CAPWE'
  },
  {
    txt: 'CAQBC-魁北克-QUEBEC ',
    code: 'CAQBC'
  },
  {
    txt: 'CARIC-里奇巴克托-RICHIBUCTO ',
    code: 'CARIC'
  },
  {
    txt: 'CARIM-里穆斯基-RIMOUSKI ',
    code: 'CARIM'
  },
  {
    txt: 'CARNA-里贾纳-REGINA',
    code: 'CARNA'
  },
  {
    txt: 'CAROB-罗伯茨湾-ROBERTS BANK ',
    code: 'CAROB'
  },
  {
    txt: 'CASAR-萨尔尼亚-SARNIA (ONT.)',
    code: 'CASAR'
  },
  {
    txt: 'CASCA-圣凯瑟林斯-ST.CATHARINES',
    code: 'CASCA'
  },
  {
    txt: 'CASDC-谢迪艾克-SHEDIAC ',
    code: 'CASDC'
  },
  {
    txt: 'CASHE-谢尔本-SHELBURNE (N.S.)',
    code: 'CASHE'
  },
  {
    txt: 'CASHI-希皮根-SHIPPEGAN',
    code: 'CASHI'
  },
  {
    txt: 'CASJN-圣约翰-SAINT JOHN ',
    code: 'CASJN'
  },
  {
    txt: 'CASJS-圣约翰斯-ST .JOHN’S(NF.)',
    code: 'CASJS'
  },
  {
    txt: 'CASKE-希布洛克-SHERBROOKE ',
    code: 'CASKE'
  },
  {
    txt: 'CASLA-圣劳伦斯-ST.LAWRENCE(NF.)',
    code: 'CASLA'
  },
  {
    txt: 'CASLD-圣李奥纳多-SAINT LEONARD',
    code: 'CASLD'
  },
  {
    txt: 'CASPH-希普港-SHIP HARBOUR ',
    code: 'CASPH'
  },
  {
    txt: 'CASPR-斯普林代尔-SPRINGDALE',
    code: 'CASPR'
  },
  {
    txt: 'CASQU-斯阔米什-SQUEAMISH ',
    code: 'CASQU'
  },
  {
    txt: 'CASRL-索雷尔-SOREL',
    code: 'CASRL'
  },
  {
    txt: 'CASRS-苏里斯-SOURIS ',
    code: 'CASRS'
  },
  {
    txt: 'CASSN-苏圣马丽-SAULT STE.MARIE(MICH.)',
    code: 'CASSN'
  },
  {
    txt: 'CASTE-斯蒂芬维尔-STEPHENVILLE ',
    code: 'CASTE'
  },
  {
    txt: 'CASTH-希特港-SHEET HARBOUR',
    code: 'CASTH'
  },
  {
    txt: 'CASTT-斯图尔特-STEWART ',
    code: 'CASTT'
  },
  {
    txt: 'CASUM-萨默塞德-SUMMERSIDE ',
    code: 'CASUM'
  },
  {
    txt: 'CASVI-七岛-SEVEN ISLANDS ',
    code: 'CASVI'
  },
  {
    txt: 'CASYD-悉尼-SYDNEY ',
    code: 'CASYD'
  },
  {
    txt: 'CATAD-塔杜萨克-TADOUSSAC ',
    code: 'CATAD'
  },
  {
    txt: 'CATAH-塔西斯-TAHSIS ',
    code: 'CATAH'
  },
  {
    txt: 'CATDB-散德湾-THUNDER BAY (ONT.)',
    code: 'CATDB'
  },
  {
    txt: 'CATHO-索罗尔德-THOROLD(ONT.)',
    code: 'CATHO'
  },
  {
    txt: 'CATHR-三河城-THREE RIVERS',
    code: 'CATHR'
  },
  {
    txt: 'CATOR-多伦多-TORONTO',
    code: 'CATOR'
  },
  {
    txt: 'CATWI-特威林盖特-TWILLINGATE ',
    code: 'CATWI'
  },
  {
    txt: 'CAVAL-瓦利菲尔德-VALLEYFIELD ',
    code: 'CAVAL'
  },
  {
    txt: 'CAVCR-温哥华-VANCOUVER ',
    code: 'CAVCR'
  },
  {
    txt: 'CAVEN-费南-VERNON',
    code: 'CAVEN'
  },
  {
    txt: 'CAVIC-维多利亚-VICTORIA ',
    code: 'CAVIC'
  },
  {
    txt: 'CAWAB-瓦伯兰-WABANA ',
    code: 'CAWAB'
  },
  {
    txt: 'CAWAL-沃尔顿-WALTON ',
    code: 'CAWAL'
  },
  {
    txt: 'CAWAT-瓦特逊岛-WATSON ISLAND ',
    code: 'CAWAT'
  },
  {
    txt: 'CAWEL-威兰-WELLAND (ONT.)',
    code: 'CAWEL'
  },
  {
    txt: 'CAWEY-韦默思-WEYMOUTH ',
    code: 'CAWEY'
  },
  {
    txt: 'CAWIN-温泽尔-WINDSOR (ONT.)',
    code: 'CAWIN'
  },
  {
    txt: 'CAWOO-伍德菲伯-WOODFIBRE ',
    code: 'CAWOO'
  },
  {
    txt: 'CAYAR-雅茅思-YARMOUTH ',
    code: 'CAYAR'
  },
  {
    txt: 'CGMAI-马塔迪-MATADI',
    code: 'CGMAI'
  },
  {
    txt: 'CGPNO-黑角-POINTE-NOIRE',
    code: 'CGPNO'
  },
  {
    txt: 'CHART-阿日哈沙图-ARHAXAT',
    code: 'CHART'
  },
  {
    txt: 'CHBAL-巴塞尔-BASEL',
    code: 'CHBAL'
  },
  {
    txt: 'CHCOU-null-COURTAMAN',
    code: 'CHCOU'
  },
  {
    txt: 'CHWMM-维米斯-WIMMIS',
    code: 'CHWMM'
  },
  {
    txt: 'CHWTG-韦廷根-WETTINGEN',
    code: 'CHWTG'
  },
  {
    txt: 'CLALC-阿里卡-ARICA',
    code: 'CLALC'
  },
  {
    txt: 'CLANA-安多法加斯大-ANTOFAGASTA',
    code: 'CLANA'
  },
  {
    txt: 'CLLIN-立昆-LIRQUEN',
    code: 'CLLIN'
  },
  {
    txt: 'CLSAN-圣地亚哥-SANTIAGO',
    code: 'CLSAN'
  },
  {
    txt: 'CLSAO-圣安东尼奥-SAN ANTONIO',
    code: 'CLSAO'
  },
  {
    txt: 'CLVSO-瓦尔帕莱索-VALPARAISO',
    code: 'CLVSO'
  },
  {
    txt: 'CMDOA-杜阿拉-DOUALA',
    code: 'CMDOA'
  },
  {
    txt: 'CNAHK-阿黑土别克-AHEITUBIEKE',
    code: 'CNAHK'
  },
  {
    txt: 'CNAID-爱店-AIDIAN',
    code: 'CNAID'
  },
  {
    txt: 'CNAKL-阿拉山口-ALASHANKOU',
    code: 'CNAKL'
  },
  {
    txt: 'CNAQG-安庆-ANQING',
    code: 'CNAQG'
  },
  {
    txt: 'CNARS-阿尔山-AERSHAN',
    code: 'CNARS'
  },
  {
    txt: 'CNASN-鞍山-ANSHAN',
    code: 'CNASN'
  },
  {
    txt: 'CNBAT-包头-BAOTOU',
    code: 'CNBAT'
  },
  {
    txt: 'CNBAY-白银-null',
    code: 'CNBAY'
  },
  {
    txt: 'CNBBD-高碑店-null',
    code: 'CNBBD'
  },
  {
    txt: 'CNBHY-北海-BEIHAI',
    code: 'CNBHY'
  },
  {
    txt: 'CNBJI-宝鸡-BAOJI',
    code: 'CNBJI'
  },
  {
    txt: 'CNBKT-巴克图-BAKETU',
    code: 'CNBKT'
  },
  {
    txt: 'CNBLE-博乐-BOLE',
    code: 'CNBLE'
  },
  {
    txt: 'CNBSP-八所-BASUO',
    code: 'CNBSP'
  },
  {
    txt: 'CNBZH-滨州-BINZHOU',
    code: 'CNBZH'
  },
  {
    txt: 'CNCAN-广州-GUANGZHOU',
    code: 'CNCAN'
  },
  {
    txt: 'CNCGB-长白-CHANGBAI',
    code: 'CNCGB'
  },
  {
    txt: 'CNCGQ-长春-CHANGCHUN',
    code: 'CNCGQ'
  },
  {
    txt: 'CNCGS-常熟-CHANGSHU',
    code: 'CNCGS'
  },
  {
    txt: 'CNCHE-城澳-CHENGAO',
    code: 'CNCHE'
  },
  {
    txt: 'CNCHN-郴州-CHENZHOU',
    code: 'CNCHN'
  },
  {
    txt: 'CNCHY-潮阳-CHAOYANG',
    code: 'CNCHY'
  },
  {
    txt: 'CNCKG-重庆-CHONGQING ',
    code: 'CNCKG'
  },
  {
    txt: 'CNCNH-从化-CONGHUA',
    code: 'CNCNH'
  },
  {
    txt: 'CNCOZ-潮州-CHAOZHOU',
    code: 'CNCOZ'
  },
  {
    txt: 'CNCPI-常平-CHANGPING ',
    code: 'CNCPI'
  },
  {
    txt: 'CNCSX-长沙-CHANGSHA',
    code: 'CNCSX'
  },
  {
    txt: 'CNCWN-赤湾-CHIWAN',
    code: 'CNCWN'
  },
  {
    txt: 'CNCZX-常州-CHANGZHOU',
    code: 'CNCZX'
  },
  {
    txt: 'CNDAN-大安-DAAN',
    code: 'CNDAN'
  },
  {
    txt: 'CNDAY-大亚湾-DAYAWAN',
    code: 'CNDAY'
  },
  {
    txt: 'CNDDG-丹东-DANDONG',
    code: 'CNDDG'
  },
  {
    txt: 'CNDEZ-德州-DEZHOU',
    code: 'CNDEZ'
  },
  {
    txt: 'CNDGG-东莞-DONGGUAN',
    code: 'CNDGG'
  },
  {
    txt: 'CNDJT-东角头-DONGJIAOTOU',
    code: 'CNDJT'
  },
  {
    txt: 'CNDLO-打洛-DALUO',
    code: 'CNDLO'
  },
  {
    txt: 'CNDLT-都拉塔-DULATA',
    code: 'CNDLT'
  },
  {
    txt: 'CNDON-东宁-DONGNING',
    code: 'CNDON'
  },
  {
    txt: 'CNDOX-东兴-DONGXING',
    code: 'CNDOX'
  },
  {
    txt: 'CNDQG-大庆-DAQING',
    code: 'CNDQG'
  },
  {
    txt: 'CNDSN-东山-DONGSHAN',
    code: 'CNDSN'
  },
  {
    txt: 'CNDXG-大连新港-DALIANXINGANG',
    code: 'CNDXG'
  },
  {
    txt: 'CNDYG-东营-DONGYING',
    code: 'CNDYG'
  },
  {
    txt: 'CNEDS-鄂尔多斯-null',
    code: 'CNEDS'
  },
  {
    txt: 'CNENP-恩平-ENPING',
    code: 'CNENP'
  },
  {
    txt: 'CNERC-二连浩特-ERENHOT',
    code: 'CNERC'
  },
  {
    txt: 'CNFAN-防城-FANGCHENG',
    code: 'CNFAN'
  },
  {
    txt: 'CNFCG-防城港-null',
    code: 'CNFCG'
  },
  {
    txt: 'CNFOC-福州-FUZHOU',
    code: 'CNFOC'
  },
  {
    txt: 'CNFOS-佛山-FOSHAN',
    code: 'CNFOS'
  },
  {
    txt: 'CNFSN-抚顺-FUSHUN',
    code: 'CNFSN'
  },
  {
    txt: 'CNFUJ-富锦-FUJIN',
    code: 'CNFUJ'
  },
  {
    txt: 'CNFUQ-福清-null',
    code: 'CNFUQ'
  },
  {
    txt: 'CNFUY-抚远-FUYUAN',
    code: 'CNFUY'
  },
  {
    txt: 'CNGAO-高港-GAOGANG',
    code: 'CNGAO'
  },
  {
    txt: 'CNGBP-拱北-GONGBEI',
    code: 'CNGBP'
  },
  {
    txt: 'CNGDO-广东-null',
    code: 'CNGDO'
  },
  {
    txt: 'CNGEJ-个旧-null',
    code: 'CNGEJ'
  },
  {
    txt: 'CNGHI-广海-GUANGHAI',
    code: 'CNGHI'
  },
  {
    txt: 'CNGIR-吉隆-GYIRONG',
    code: 'CNGIR'
  },
  {
    txt: 'CNGOM-高明-GAOMING',
    code: 'CNGOM'
  },
  {
    txt: 'CNGQD-甘其毛德-GANQMOD',
    code: 'CNGQD'
  },
  {
    txt: 'CNGUG-贵港-GUIGANG',
    code: 'CNGUG'
  },
  {
    txt: 'CNHAC-汉川-null',
    code: 'CNHAC'
  },
  {
    txt: 'CNHAK-海口-HAIKOU',
    code: 'CNHAK'
  },
  {
    txt: 'CNHAN-淮安-null',
    code: 'CNHAN'
  },
  {
    txt: 'CNHAZ-汉中-null',
    code: 'CNHAZ'
  },
  {
    txt: 'CNHCN-桦川-HUACHUAN',
    code: 'CNHCN'
  },
  {
    txt: 'CNHEC-河池-null',
    code: 'CNHEC'
  },
  {
    txt: 'CNHEK-黑河-HEIHE',
    code: 'CNHEK'
  },
  {
    txt: 'CNHES-鹤山-HESHAN',
    code: 'CNHES'
  },
  {
    txt: 'CNHET-呼和浩特-HOHHOT',
    code: 'CNHET'
  },
  {
    txt: 'CNHEY-河源-HEYUAN',
    code: 'CNHEY'
  },
  {
    txt: 'CNHEZ-荷泽-null',
    code: 'CNHEZ'
  },
  {
    txt: 'CNHFE-合肥-HEFEI',
    code: 'CNHFE'
  },
  {
    txt: 'CNHGH-杭州-HANGZHOU',
    code: 'CNHGH'
  },
  {
    txt: 'CNHHA-怀化-null',
    code: 'CNHHA'
  },
  {
    txt: 'CNHKM-河口-HEKOU',
    code: 'CNHKM'
  },
  {
    txt: 'CNHLD-海拉尔-HAILAR',
    code: 'CNHLD'
  },
  {
    txt: 'CNHLY-胡列也吐-HULIEYETU',
    code: 'CNHLY'
  },
  {
    txt: 'CNHME-海门-HAIMEN',
    code: 'CNHME'
  },
  {
    txt: 'CNHMN-虎门-HUMEN',
    code: 'CNHMN'
  },
  {
    txt: 'CNHRB-哈尔滨-HARBIN',
    code: 'CNHRB'
  },
  {
    txt: 'CNHRS-霍尔果斯-HUOERGUOSI',
    code: 'CNHRS'
  },
  {
    txt: 'CNHSC-韶关-SHAOGUANG',
    code: 'CNHSC'
  },
  {
    txt: 'CNHSN-舟山-ZHOUSHAN',
    code: 'CNHSN'
  },
  {
    txt: 'CNHST-黑头山-HERTOUSHAN',
    code: 'CNHST'
  },
  {
    txt: 'CNHSZ-红山嘴-HOGNSHANZUI',
    code: 'CNHSZ'
  },
  {
    txt: 'CNHUC-珲春-HUNCHUN',
    code: 'CNHUC'
  },
  {
    txt: 'CNHUG-皇岗-HUANGGANG',
    code: 'CNHUG'
  },
  {
    txt: 'CNHUI-惠州-HUIZHOU',
    code: 'CNHUI'
  },
  {
    txt: 'CNHUM-呼玛-HUMA',
    code: 'CNHUM'
  },
  {
    txt: 'CNHZH-湖州-HUZHOU',
    code: 'CNHZH'
  },
  {
    txt: 'CNJAY-嘉荫-JIAYIN',
    code: 'CNJAY'
  },
  {
    txt: 'CNJDZ-景德镇-JINGTEZHEN',
    code: 'CNJDZ'
  },
  {
    txt: 'CNJEM-吉木乃-JEMINAY',
    code: 'CNJEM'
  },
  {
    txt: 'CNJHA-金华-JINHUA',
    code: 'CNJHA'
  },
  {
    txt: 'CNJHG-景洪-JINGHONG',
    code: 'CNJHG'
  },
  {
    txt: 'CNJIA-江阴-JIANGYIN',
    code: 'CNJIA'
  },
  {
    txt: 'CNJIL-吉林-null',
    code: 'CNJIL'
  },
  {
    txt: 'CNJIM-荆门-null',
    code: 'CNJIM'
  },
  {
    txt: 'CNJIN-济宁-null',
    code: 'CNJIN'
  },
  {
    txt: 'CNJIU-九江-JIUJIANG',
    code: 'CNJIU'
  },
  {
    txt: 'CNJIX-嘉兴-JIAXING',
    code: 'CNJIX'
  },
  {
    txt: 'CNJIY-揭阳-JIEYANG',
    code: 'CNJIY'
  },
  {
    txt: 'CNJMU-佳木斯-JIAMUSI',
    code: 'CNJMU'
  },
  {
    txt: 'CNJNZ-锦州-JINZHOU',
    code: 'CNJNZ'
  },
  {
    txt: 'CNJSH-金水河-JINSHUIHE',
    code: 'CNJSH'
  },
  {
    txt: 'CNJZU-九州-JIUZHOU',
    code: 'CNJZU'
  },
  {
    txt: 'CNKAY-开远-null',
    code: 'CNKAY'
  },
  {
    txt: 'CNKEL-库尔勒-null',
    code: 'CNKEL'
  },
  {
    txt: 'CNKHN-南昌-NANCHANG',
    code: 'CNKHN'
  },
  {
    txt: 'CNKJP-红其拉普-KUNJIRAP',
    code: 'CNKJP'
  },
  {
    txt: 'CNKNC-集安-JIAN',
    code: 'CNKNC'
  },
  {
    txt: 'CNKST-开山屯-KAISHANTUN',
    code: 'CNKST'
  },
  {
    txt: 'CNKUS-昆山-KUNSHAN',
    code: 'CNKUS'
  },
  {
    txt: 'CNKWL-桂林-GUILIN',
    code: 'CNKWL'
  },
  {
    txt: 'CNLAO-聊城-null',
    code: 'CNLAO'
  },
  {
    txt: 'CNLCH-老城-LAOCHENG',
    code: 'CNLCH'
  },
  {
    txt: 'CNLHM-兰州-LANZHOU',
    code: 'CNLHM'
  },
  {
    txt: 'CNLIH-莲花山-LIANHUASHAN',
    code: 'CNLIH'
  },
  {
    txt: 'CNLIN-临江-LINJIANG',
    code: 'CNLIN'
  },
  {
    txt: 'CNLKU-龙口-LONGKOU',
    code: 'CNLKU'
  },
  {
    txt: 'CNLPS-六盘水-null',
    code: 'CNLPS'
  },
  {
    txt: 'CNLSN-岚山-LANSHAN',
    code: 'CNLSN'
  },
  {
    txt: 'CNLUB-萝北-LUOBEI',
    code: 'CNLUB'
  },
  {
    txt: 'CNLUO-罗定-LUODING',
    code: 'CNLUO'
  },
  {
    txt: 'CNLYA-洛阳-LUOYANG',
    code: 'CNLYA'
  },
  {
    txt: 'CNLYG-连云港-LIANYUNGANG',
    code: 'CNLYG'
  },
  {
    txt: 'CNLYI-临沂-LINYI',
    code: 'CNLYI'
  },
  {
    txt: 'CNLZG-莱州-LAIZHOU',
    code: 'CNLZG'
  },
  {
    txt: 'CNLZH-柳州-LIUZHOU',
    code: 'CNLZH'
  },
  {
    txt: 'CNMAA-马鞍山-MAANSHAN',
    code: 'CNMAA'
  },
  {
    txt: 'CNMAW-马尾-MAWEI',
    code: 'CNMAW'
  },
  {
    txt: 'CNMDG-牡丹江-MUDANJIANG',
    code: 'CNMDG'
  },
  {
    txt: 'CNMDL-满都拉-MANDULA',
    code: 'CNMDL'
  },
  {
    txt: 'CNMDN-孟定-MENGDING',
    code: 'CNMDN'
  },
  {
    txt: 'CNMES-眉山-null',
    code: 'CNMES'
  },
  {
    txt: 'CNMHN-磨憨-MOHAN',
    code: 'CNMHN'
  },
  {
    txt: 'CNMIS-密山-MISHAN',
    code: 'CNMIS'
  },
  {
    txt: 'CNMLN-孟连-MENGLIAN',
    code: 'CNMLN'
  },
  {
    txt: 'CNMLX-满洲里-MANZHOULI',
    code: 'CNMLX'
  },
  {
    txt: 'CNMMI-茂名-MAOMING',
    code: 'CNMMI'
  },
  {
    txt: 'CNMOH-漠河-MOHE',
    code: 'CNMOH'
  },
  {
    txt: 'CNMWN-妈湾-MAWAN',
    code: 'CNMWN'
  },
  {
    txt: 'CNMXZ-梅周-MEIZHOU',
    code: 'CNMXZ'
  },
  {
    txt: 'CNMYG-绵阳-MIANYANG',
    code: 'CNMYG'
  },
  {
    txt: 'CNMZS-马鬃山-MAZONGSHAN',
    code: 'CNMZS'
  },
  {
    txt: 'CNMZT-木扎尔特-MUZHAERTE',
    code: 'CNMZT'
  },
  {
    txt: 'CNNAC-南充-null',
    code: 'CNNAC'
  },
  {
    txt: 'CNNAH-南海-NANHAI',
    code: 'CNNAH'
  },
  {
    txt: 'CNNAN-南澳-NANAO',
    code: 'CNNAN'
  },
  {
    txt: 'CNNAP-南坪-NANPING',
    code: 'CNNAP'
  },
  {
    txt: 'CNNDG-齐齐哈尔-QIQIHAR',
    code: 'CNNDG'
  },
  {
    txt: 'CNNEJ-内江-null',
    code: 'CNNEJ'
  },
  {
    txt: 'CNNGB-宁波-NINGBO',
    code: 'CNNGB'
  },
  {
    txt: 'CNNKG-南京-NANJING',
    code: 'CNNKG'
  },
  {
    txt: 'CNNNG-南宁 -NANNING',
    code: 'CNNNG'
  },
  {
    txt: 'CNNNI-南宁-null',
    code: 'CNNNI'
  },
  {
    txt: 'CNNSA-南沙-NANSHA',
    code: 'CNNSA'
  },
  {
    txt: 'CNNSN-南伞-NANSAN',
    code: 'CNNSN'
  },
  {
    txt: 'CNNTG-南通-NANTONG',
    code: 'CNNTG'
  },
  {
    txt: 'CNNTU-南头-NANTOU',
    code: 'CNNTU'
  },
  {
    txt: 'CNPIN-凭祥-PINGXIANG',
    code: 'CNPIN'
  },
  {
    txt: 'CNPLI-蓬莱-PENGLAI',
    code: 'CNPLI'
  },
  {
    txt: 'CNPMA-片马-PIANMA',
    code: 'CNPMA'
  },
  {
    txt: 'CNPNY-番禺-PANYU',
    code: 'CNPNY'
  },
  {
    txt: 'CNPUT-莆田-PUTIAN',
    code: 'CNPUT'
  },
  {
    txt: 'CNPZH-攀枝花-PANZHIHUA',
    code: 'CNPZH'
  },
  {
    txt: 'CNQLN-清澜-QINGLAN',
    code: 'CNQLN'
  },
  {
    txt: 'CNQSA-企沙-QISHA',
    code: 'CNQSA'
  },
  {
    txt: 'CNQUJ-曲靖-null',
    code: 'CNQUJ'
  },
  {
    txt: 'CNQUZ-衢州-null',
    code: 'CNQUZ'
  },
  {
    txt: 'CNQYN-清远-QINGYUAN',
    code: 'CNQYN'
  },
  {
    txt: 'CNQZH-钦州-QINZHOU',
    code: 'CNQZH'
  },
  {
    txt: 'CNRIW-日屋-RIWU',
    code: 'CNRIW'
  },
  {
    txt: 'CNROH-饶河-RAOHE',
    code: 'CNROH'
  },
  {
    txt: 'CNROQ-容奇-RONGQI',
    code: 'CNROQ'
  },
  {
    txt: 'CNRZH-日照-RIZHAO',
    code: 'CNRZH'
  },
  {
    txt: 'CNSAH-三合-SANHE',
    code: 'CNSAH'
  },
  {
    txt: 'CNSAY-绍阳-null',
    code: 'CNSAY'
  },
  {
    txt: 'CNSBU-三埠-SANBU',
    code: 'CNSBU'
  },
  {
    txt: 'CNSDG-水东-SHUIDONG',
    code: 'CNSDG'
  },
  {
    txt: 'CNSFE-绥芬河-SUIFENHEI',
    code: 'CNSFE'
  },
  {
    txt: 'CNSHA-上海-SHANGHAI',
    code: 'CNSHA'
  },
  {
    txt: 'CNSHC-沙埕-SHACHENG',
    code: 'CNSHC'
  },
  {
    txt: 'CNSHE-沈阳-SHENYANG',
    code: 'CNSHE'
  },
  {
    txt: 'CNSHH-石狮-SHISHI',
    code: 'CNSHH'
  },
  {
    txt: 'CNSHK-蛇口-SHEKOU',
    code: 'CNSHK'
  },
  {
    txt: 'CNSHP-秦皇岛-QINGHUAGNDAO ',
    code: 'CNSHP'
  },
  {
    txt: 'CNSHU-沭阳-SHUYANG',
    code: 'CNSHU'
  },
  {
    txt: 'CNSHY-十堰-null',
    code: 'CNSHY'
  },
  {
    txt: 'CNSIA-武夷山-WUYISHAN',
    code: 'CNSIA'
  },
  {
    txt: 'CNSIH-泗洪-null',
    code: 'CNSIH'
  },
  {
    txt: 'CNSIP-四平-null',
    code: 'CNSIP'
  },
  {
    txt: 'CNSJW-石家庄-SHIJIAZHUANG',
    code: 'CNSJW'
  },
  {
    txt: 'CNSKO-水口-SHUIKOU',
    code: 'CNSKO'
  },
  {
    txt: 'CNSON-松下-SONGXIA',
    code: 'CNSON'
  },
  {
    txt: 'CNSOZ-朔州-null',
    code: 'CNSOZ'
  },
  {
    txt: 'CNSTB-石头埠-SHITOUBU',
    code: 'CNSTB'
  },
  {
    txt: 'CNSUD-顺德-SHUNDE',
    code: 'CNSUD'
  },
  {
    txt: 'CNSUW-孙吴-SUNWU',
    code: 'CNSUW'
  },
  {
    txt: 'CNSWA-汕头-SHANTOU',
    code: 'CNSWA'
  },
  {
    txt: 'CNSWI-室韦-SHIWEI',
    code: 'CNSWI'
  },
  {
    txt: 'CNSXG-绍兴-SHAOXING',
    code: 'CNSXG'
  },
  {
    txt: 'CNSYA-邵阳-null',
    code: 'CNSYA'
  },
  {
    txt: 'CNSYX-三亚-SANYA',
    code: 'CNSYX'
  },
  {
    txt: 'CNSZH-苏州-SUZHOU',
    code: 'CNSZH'
  },
  {
    txt: 'CNSZX-深圳-SHENZHEN',
    code: 'CNSZX'
  },
  {
    txt: 'CNTAI-泰安-TAIAN',
    code: 'CNTAI'
  },
  {
    txt: 'CNTAO-青岛-QINGDAO',
    code: 'CNTAO'
  },
  {
    txt: 'CNTAP-太平-TAIPING',
    code: 'CNTAP'
  },
  {
    txt: 'CNTCH-腾冲-TENGCHONG',
    code: 'CNTCH'
  },
  {
    txt: 'CNTGA-塘沽-TANGGU',
    code: 'CNTGA'
  },
  {
    txt: 'CNTGS-唐山-TANGSHAN',
    code: 'CNTGS'
  },
  {
    txt: 'CNTKK-塔克什肯-TAYKEXKIN',
    code: 'CNTKK'
  },
  {
    txt: 'CNTLO-通辽-null',
    code: 'CNTLO'
  },
  {
    txt: 'CNTNA-济南-JINAN',
    code: 'CNTNA'
  },
  {
    txt: 'CNTOJ-同江-TONGJIANG',
    code: 'CNTOJ'
  },
  {
    txt: 'CNTOL-铜陵-TONGLING',
    code: 'CNTOL'
  },
  {
    txt: 'CNTRT-吐尔尕特-TURUGART',
    code: 'CNTRT'
  },
  {
    txt: 'CNTSN-天津-TIANJIN',
    code: 'CNTSN'
  },
  {
    txt: 'CNTXG-天津新港-TIANJINXINGANG',
    code: 'CNTXG'
  },
  {
    txt: 'CNTXH-黄山-HUANGSHAN',
    code: 'CNTXH'
  },
  {
    txt: 'CNTYN-太原-TAIYUAN',
    code: 'CNTYN'
  },
  {
    txt: 'GBIVS-因弗内斯-INVERNESS ',
    code: 'GBIVS'
  },
  {
    txt: 'GBKIL-基林霍尔姆-KILLINGHOIME ',
    code: 'GBKIL'
  },
  {
    txt: 'GBKIR-柯科迪-KIRKCALDY ',
    code: 'GBKIR'
  },
  {
    txt: 'GBKKL-基尔基尔-KILKEEL ',
    code: 'GBKKL'
  },
  {
    txt: 'GBKOL-洛哈尔什教区凯尔-KYLE OF LOCHALSH ',
    code: 'GBKOL'
  },
  {
    txt: 'GBKSL-金斯林-KING’S LYNN ',
    code: 'GBKSL'
  },
  {
    txt: 'GBKWL-柯克沃尔-KIRKWALL ',
    code: 'GBKWL'
  },
  {
    txt: 'GBLAM-朗姆-LAME ',
    code: 'GBLAM'
  },
  {
    txt: 'GBLAN-兰开斯特-LANCASTER ',
    code: 'GBLAN'
  },
  {
    txt: 'GBLBO-洛赫博伊斯代尔-LOCH BOISDALE ',
    code: 'GBLBO'
  },
  {
    txt: 'GBLDY-伦敦德里-LONDONDERRY ',
    code: 'GBLDY'
  },
  {
    txt: 'GBLEI-利斯-LEITH ',
    code: 'GBLEI'
  },
  {
    txt: 'GBLGS-拉格斯-LARGS ',
    code: 'GBLGS'
  },
  {
    txt: 'GBLIT-利特尔汉普顿-LITTLEHAMPTON ',
    code: 'GBLIT'
  },
  {
    txt: 'GBLIV-利物浦-LIVERPOOL',
    code: 'GBLIV'
  },
  {
    txt: 'GBLLI-拉内利-LLANELLI ',
    code: 'GBLLI'
  },
  {
    txt: 'GBLLS-兰杜拉斯-LLANDDULAS ',
    code: 'GBLLS'
  },
  {
    txt: 'GBLMA-洛赫马迪-LOCH MADDY ',
    code: 'GBLMA'
  },
  {
    txt: 'GBLNE-拉恩-LARNE ',
    code: 'GBLNE'
  },
  {
    txt: 'GBLOE-卢港-LOOE ',
    code: 'GBLOE'
  },
  {
    txt: 'GBLON-伦敦-LONDON ',
    code: 'GBLON'
  },
  {
    txt: 'GBLOS-洛西茅斯-LOSSIEMOUTH',
    code: 'GBLOS'
  },
  {
    txt: 'GBLOW-洛斯托夫特-LOWESTOFT ',
    code: 'GBLOW'
  },
  {
    txt: 'GBLWK-勒威克-LERWICK ',
    code: 'GBLWK'
  },
  {
    txt: 'GBLYB-利布斯特-LYBSTER ',
    code: 'GBLYB'
  },
  {
    txt: 'GBMAC-麦克达夫-MACDUFF ',
    code: 'GBMAC'
  },
  {
    txt: 'GBMAL-莫尔登-MALDON ',
    code: 'GBMAL'
  },
  {
    txt: 'GBMAN-曼彻斯特-MANCHESTER ',
    code: 'GBMAN'
  },
  {
    txt: 'GBMEV-梅瓦吉西-MEVAGISSEY ',
    code: 'GBMEV'
  },
  {
    txt: 'GBMID-米德尔斯伯勒-MIDDLESBROUGH ',
    code: 'GBMID'
  },
  {
    txt: 'GBMIS-米斯特利-MISTLEY ',
    code: 'GBMIS'
  },
  {
    txt: 'GBMLG-马莱格-MALLAIG ',
    code: 'GBMLG'
  },
  {
    txt: 'GBMLM-米勒姆-MILLOM ',
    code: 'GBMLM'
  },
  {
    txt: 'GBMON-蒙特罗斯-MONTROSE ',
    code: 'GBMON'
  },
  {
    txt: 'GBMTL-梅西尔-METHIL ',
    code: 'GBMTL'
  },
  {
    txt: 'GBNAI-奈恩-NAIRN ',
    code: 'GBNAI'
  },
  {
    txt: 'GBNBH-纽堡-NEWBURGH ',
    code: 'GBNBH'
  },
  {
    txt: 'GBNCE-纽卡斯尔-NEWCASTLE (CO.DOWN)',
    code: 'GBNCE'
  },
  {
    txt: 'GBNEW-纽波特-NEWPORT(MON.)',
    code: 'GBNEW'
  },
  {
    txt: 'GBNHN-纽黑文-NEWHAVEN ',
    code: 'GBNHN'
  },
  {
    txt: 'GBNLN-纽林-NEWLYN ',
    code: 'GBNLN'
  },
  {
    txt: 'GBNOR-诺里奇-NORWICH ',
    code: 'GBNOR'
  },
  {
    txt: 'GBNRY-纽里-NEWLYN ',
    code: 'GBNRY'
  },
  {
    txt: 'GBNSE-纽卡斯尔-NEWCASTLE(TYNE)',
    code: 'GBNSE'
  },
  {
    txt: 'GBNSU-北森德兰-NORTH SUNDERLAND ',
    code: 'GBNSU'
  },
  {
    txt: 'GBOBN-奥本-OBAN ',
    code: 'GBOBN'
  },
  {
    txt: 'GBPAD-帕德斯托-PADSTOW ',
    code: 'GBPAD'
  },
  {
    txt: 'GBPAL-帕尔纳基-PALNACKIE ',
    code: 'GBPAL'
  },
  {
    txt: 'GBPAQ-帕克斯顿瓜伊-PARKESTON QUAY ',
    code: 'GBPAQ'
  },
  {
    txt: 'GBPAR-帕-PAR ',
    code: 'GBPAR'
  },
  {
    txt: 'GBPAS-波塔斯凯格-PORT ASKAIG',
    code: 'GBPAS'
  },
  {
    txt: 'GBPBY-波尔特布里-PORTBURY',
    code: 'GBPBY'
  },
  {
    txt: 'GBPDG-波特马多克-PORTHMADOG ',
    code: 'GBPDG'
  },
  {
    txt: 'GBPED-彭布罗克-PEMBROKE DOCK',
    code: 'GBPED'
  },
  {
    txt: 'GBPEE-皮尔-PEEL ',
    code: 'GBPEE'
  },
  {
    txt: 'GBPEL-埃伦港-PORT ELLEN ',
    code: 'GBPEL'
  },
  {
    txt: 'GBPEN-彭赞斯-PENZANCE ',
    code: 'GBPEN'
  },
  {
    txt: 'GBPER-珀斯-PERTH',
    code: 'GBPER'
  },
  {
    txt: 'GBPET-彼得黑德-PETERHEAD ',
    code: 'GBPET'
  },
  {
    txt: 'GBPHD-波蒂斯黑德-PORTISHEAD ',
    code: 'GBPHD'
  },
  {
    txt: 'GBPLB-波特兰角-BILL',
    code: 'GBPLB'
  },
  {
    txt: 'GBPLD-波特兰-PORTLAND(DOR.)',
    code: 'GBPLD'
  },
  {
    txt: 'AUHAY-海波因特-HAY POINT',
    code: 'AUHAY'
  },
  {
    txt: 'DEGLU-格吕克施塔特-GLUCKSTADT ',
    code: 'DEGLU'
  },
  {
    txt: 'DEHAM-汉堡-HAMBURG',
    code: 'DEHAM'
  },
  {
    txt: 'DEHOL-霍尔特瑙-HOLTENAU ',
    code: 'DEHOL'
  },
  {
    txt: 'DEHSM-胡苏姆-HUSUM',
    code: 'DEHSM'
  },
  {
    txt: 'DEITZ-伊策霍-ITZEHOE ',
    code: 'DEITZ'
  },
  {
    txt: 'DEKAP-卡珀尔恩-KAPPELN',
    code: 'DEKAP'
  },
  {
    txt: 'DEKIL-基尔-KIEL ',
    code: 'DEKIL'
  },
  {
    txt: 'DEKPE-克雷菲尔德-KREFELD ',
    code: 'DEKPE'
  },
  {
    txt: 'DELAB-拉伯-LABO ',
    code: 'DELAB'
  },
  {
    txt: 'DELER-累尔-LEER ',
    code: 'DELER'
  },
  {
    txt: 'DELUB-卢卑克-LUBECK ',
    code: 'DELUB'
  },
  {
    txt: 'DELUD-路德维希港-LUDWIGSHAFEN ',
    code: 'DELUD'
  },
  {
    txt: 'DEMAI-美因茨-MAINZ ',
    code: 'DEMAI'
  },
  {
    txt: 'DEMHM-曼海姆-MANNHEIM ',
    code: 'DEMHM'
  },
  {
    txt: 'DENHS-诺伊豪斯-NEUHAUS ',
    code: 'DENHS'
  },
  {
    txt: 'DENOR-诺登哈姆-NORDENHAM',
    code: 'DENOR'
  },
  {
    txt: 'DENST-诺伊施塔特-NEUSTADT ',
    code: 'DENST'
  },
  {
    txt: 'DENUS-诺伊斯-NEUSS ',
    code: 'DENUS'
  },
  {
    txt: 'DEORT-奥尔特-ORTH ',
    code: 'DEORT'
  },
  {
    txt: 'DEPAP-帕彭堡-PAPENBURG ',
    code: 'DEPAP'
  },
  {
    txt: 'DEROS-罗斯托克-ROSTOCK ',
    code: 'DEROS'
  },
  {
    txt: 'DERSG-伦茨堡-RENDSBURG ',
    code: 'DERSG'
  },
  {
    txt: 'DESCH-施瓦岑郝廷-SCHWARZENHUTTEN ',
    code: 'DESCH'
  },
  {
    txt: 'DESLU-许劳-SCHULAU ',
    code: 'DESLU'
  },
  {
    txt: 'DESSD-施塔德桑德-STADERSAND ',
    code: 'DESSD'
  },
  {
    txt: 'DESTA-施塔德-STADE ',
    code: 'DESTA'
  },
  {
    txt: 'DESTR-斯特拉尔松-STALSUND ',
    code: 'DESTR'
  },
  {
    txt: 'DESTT-斯图加特-Stuttgart',
    code: 'DESTT'
  },
  {
    txt: 'DETON-滕宁-TONNING ',
    code: 'DETON'
  },
  {
    txt: 'DETRA-特罗弗明德-TRAVEMUNDE ',
    code: 'DETRA'
  },
  {
    txt: 'DEVEG-弗格萨克-VEGESACK ',
    code: 'DEVEG'
  },
  {
    txt: 'DEWAR-瓦尔内明德-WARNEMUNDE ',
    code: 'DEWAR'
  },
  {
    txt: 'DEWIL-威廉港-WILHELMSHAVEN ',
    code: 'DEWIL'
  },
  {
    txt: 'DEWIS-维斯马-WISMAR ',
    code: 'DEWIS'
  },
  {
    txt: 'DEWLG-威廉斯堡-WILHELMSBURG ',
    code: 'DEWLG'
  },
  {
    txt: 'DEWOS-WORMS-沃尔姆斯',
    code: 'DEWOS'
  },
  {
    txt: 'DJJBT-吉布提-DJIBOUTI',
    code: 'DJJBT'
  },
  {
    txt: 'DKAAB-奥本罗-AABENRAA ',
    code: 'DKAAB'
  },
  {
    txt: 'DKABG-奥尔堡-AALBORG ',
    code: 'DKABG'
  },
  {
    txt: 'DKAEP-埃勒斯克平-AEROSKOBING ',
    code: 'DKAEP'
  },
  {
    txt: 'DKAHS-奥尔胡斯-AARHUS ',
    code: 'DKAHS'
  },
  {
    txt: 'DKALL-阿灵厄-ALLINGE ',
    code: 'DKALL'
  },
  {
    txt: 'DKARS-阿尔斯-AARS',
    code: 'DKARS'
  },
  {
    txt: 'DKASS-阿森斯-ASSENS ',
    code: 'DKASS'
  },
  {
    txt: 'DKBHM-班霍尔姆-BANDHOLM ',
    code: 'DKBHM'
  },
  {
    txt: 'DKBOG-博恩瑟-BOGENSE ',
    code: 'DKBOG'
  },
  {
    txt: 'DKCOP-哥本哈根-COPENHAGEN ',
    code: 'DKCOP'
  },
  {
    txt: 'DKEBE-埃伯尔措夫特-EBELTOFT ',
    code: 'DKEBE'
  },
  {
    txt: 'DKESB-埃斯比约-ESBJERG ',
    code: 'DKESB'
  },
  {
    txt: 'DKFAA-福堡-FAABORG ',
    code: 'DKFAA'
  },
  {
    txt: 'DKFLS-法克瑟莱泽普拉斯-FAKSE LADEPLADS ',
    code: 'DKFLS'
  },
  {
    txt: 'DKFSD-腓特烈松-FREDERIKSSUND ',
    code: 'DKFSD'
  },
  {
    txt: 'DKFSK-腓特烈斯韦克-FREDERIKSVARK ',
    code: 'DKFSK'
  },
  {
    txt: 'DKFSN-腓特烈港-FREDERIKSHAVN ',
    code: 'DKFSN'
  },
  {
    txt: 'DKGRA-格罗斯滕-GRASTEN ',
    code: 'DKGRA'
  },
  {
    txt: 'DKGUL-基尔夫港-GULFHAVN ',
    code: 'DKGUL'
  },
  {
    txt: 'DKHAS-海斯勒-HASSLE ',
    code: 'DKHAS'
  },
  {
    txt: 'DKHIN-辛内鲁普-HINNERUP',
    code: 'DKHIN'
  },
  {
    txt: 'DKHOB-霍布罗-HOBRO ',
    code: 'DKHOB'
  },
  {
    txt: 'DKHOL-霍尔拜克-HOLBAEK ',
    code: 'DKHOL'
  },
  {
    txt: 'DKHOR-霍森斯-HORSENS ',
    code: 'DKHOR'
  },
  {
    txt: 'DKHSV-哈泽斯莱乌-HADERSLEV ',
    code: 'DKHSV'
  },
  {
    txt: 'DKKAL-凯隆堡-KALUNDBORG ',
    code: 'DKKAL'
  },
  {
    txt: 'DKKAR-卡勒拜克斯明讷-KARREBAEKSMINDE ',
    code: 'DKKAR'
  },
  {
    txt: 'DKKOG-科厄-KOGE ',
    code: 'DKKOG'
  },
  {
    txt: 'DKKOL-科灵-KOLDING ',
    code: 'DKKOL'
  },
  {
    txt: 'DKKOR-科瑟-KORSOR ',
    code: 'DKKOR'
  },
  {
    txt: 'DKLEM-莱姆维-LEMVIG ',
    code: 'DKLEM'
  },
  {
    txt: 'DKLOS-林斯奥得-LYNGS ODDE ',
    code: 'DKLOS'
  },
  {
    txt: 'DKMAS-马斯讷松-MASNEDSUND ',
    code: 'DKMAS'
  },
  {
    txt: 'DKMID-米泽尔法特-MIDDELFART ',
    code: 'DKMID'
  },
  {
    txt: 'DKMTL-马斯塔尔-MARSTAL ',
    code: 'DKMTL'
  },
  {
    txt: 'DKNBG-尼克宾-NYKOBING(SJA.)',
    code: 'DKNBG'
  },
  {
    txt: 'DKNEK-内克瑟-NEKSO ',
    code: 'DKNEK'
  },
  {
    txt: 'DKNKG-尼克宾-NYKOBING(MORS)',
    code: 'DKNKG'
  },
  {
    txt: 'DKNKV-纳克斯考-NAKSKOV ',
    code: 'DKNKV'
  },
  {
    txt: 'DKNYB-尼堡-NYBORG ',
    code: 'DKNYB'
  },
  {
    txt: 'ESCOR-科伦纳　　　　　　　　-CORUNNA',
    code: 'ESCOR'
  },
  {
    txt: 'ESCRS-卡博尼雷斯-CARBONERAS',
    code: 'ESCRS'
  },
  {
    txt: 'ESCUR-乌迪亚莱斯堡　　　　　-CASTRO URDIALES',
    code: 'ESCUR'
  },
  {
    txt: 'ESESH-埃斯孔布雷阿斯　　　　-ESCOMBRERAS HAR',
    code: 'ESESH'
  },
  {
    txt: 'ESFER-费罗尔　　　　　　　　-FERROL',
    code: 'ESFER'
  },
  {
    txt: 'ESGAN-刚迪亚　　　　　　　　-GANDIA',
    code: 'ESGAN'
  },
  {
    txt: 'ESGAR-加鲁查　　　　　　　　-GARRUCHA',
    code: 'ESGAR'
  },
  {
    txt: 'ESGIJ-希洪　　　　　　　　　-GIJON',
    code: 'ESGIJ'
  },
  {
    txt: 'ESIBI-伊维萨　　　　　　　　-IBIZA',
    code: 'ESIBI'
  },
  {
    txt: 'ESJAV-哈韦阿　　　　　　　　-JAVEA',
    code: 'ESJAV'
  },
  {
    txt: 'ESLCA-拉卡莱拉　　　　　　　-LA CALERA',
    code: 'ESLCA'
  },
  {
    txt: 'ESLPS-拉斯帕尔马斯-LAS PALMAS',
    code: 'ESLPS'
  },
  {
    txt: 'ESLUA-卢阿尔卡　　　　　　　-LUARCA',
    code: 'ESLUA'
  },
  {
    txt: 'GWCAC-卡谢马-CACHEU',
    code: 'GWCAC'
  },
  {
    txt: 'GYBAR-巴提卡-BARTICA',
    code: 'GYBAR'
  },
  {
    txt: 'GYGEO-乔治城-GEORGETOWN',
    code: 'GYGEO'
  },
  {
    txt: 'GYNAM-新阿姆斯特丹-NEW AMSTERDAM',
    code: 'GYNAM'
  },
  {
    txt: 'HKHKG-香港-HONG KONG',
    code: 'HKHKG'
  },
  {
    txt: 'HNAPA-阿马帕拉-AMAPALA',
    code: 'HNAPA'
  },
  {
    txt: 'HNLCE-拉塞瓦-LACEIBA',
    code: 'HNLCE'
  },
  {
    txt: 'HNPCA-卡斯蒂利亚港-PUENO CASTILLA',
    code: 'HNPCA'
  },
  {
    txt: 'HNPCO-科尔特斯港-PUERTO CORTES',
    code: 'HNPCO'
  },
  {
    txt: 'HNR0I-罗阿坦-ROATAN ISLAND',
    code: 'HNR0I'
  },
  {
    txt: 'HNSLO-圣洛伦索-SAN IORENZO',
    code: 'HNSLO'
  },
  {
    txt: 'HNSPS-圣佩德罗苏拉-SAN  PEDRO SULA',
    code: 'HNSPS'
  },
  {
    txt: 'HNTEL-特拉-TELA',
    code: 'HNTEL'
  },
  {
    txt: 'HNTRU-特鲁希略-TRUJILLO',
    code: 'HNTRU'
  },
  {
    txt: 'HRBAK-巴卡尔-BAKAR',
    code: 'HRBAK'
  },
  {
    txt: 'HRDBV-杜布罗夫尼克-DUBROVNIK',
    code: 'HRDBV'
  },
  {
    txt: 'HRDUG-杜吉腊特-DUGI RAT',
    code: 'HRDUG'
  },
  {
    txt: 'HRHVA-赫瓦尔-HVAR',
    code: 'HRHVA'
  },
  {
    txt: 'HRKOR-科尔丘拉-KORCULA',
    code: 'HRKOR'
  },
  {
    txt: 'HRMAS-马斯利尼索-MASLENICA',
    code: 'HRMAS'
  },
  {
    txt: 'HROMI-奥米沙利-OMISALI',
    code: 'HROMI'
  },
  {
    txt: 'HRPLE-卡德尔耶沃-KARDELJEVO',
    code: 'HRPLE'
  },
  {
    txt: 'HRPUL-普拉-PULA',
    code: 'HRPUL'
  },
  {
    txt: 'HRRAS-拉萨-RASA',
    code: 'HRRAS'
  },
  {
    txt: 'HRRIJ-里耶卡-RIIEKA',
    code: 'HRRIJ'
  },
  {
    txt: 'HRROV-罗维尼-ROVINI',
    code: 'HRROV'
  },
  {
    txt: 'HRSEN-塞尼-SENJ',
    code: 'HRSEN'
  },
  {
    txt: 'HRSIB-希贝尼克-SIBENIK',
    code: 'HRSIB'
  },
  {
    txt: 'HRSPL-斯普利特-SPLIT',
    code: 'HRSPL'
  },
  {
    txt: 'HRZAD-扎达尔-ZADAR',
    code: 'HRZAD'
  },
  {
    txt: 'HTACA-奥凯-AUX CAYES',
    code: 'HTACA'
  },
  {
    txt: 'HTCHA-海地角-CAP HAMEN',
    code: 'HTCHA'
  },
  {
    txt: 'HTFLI-利贝泰堡-FORT LIBERTE',
    code: 'HTFLI'
  },
  {
    txt: 'HTGON-戈纳伊夫-GONAIVES',
    code: 'HTGON'
  },
  {
    txt: 'HTJER-热雷米-JEREMIE',
    code: 'HTJER'
  },
  {
    txt: 'HTMIR-米腊关-MIRAGOANE',
    code: 'HTMIR'
  },
  {
    txt: 'HTPAP-太子港-PORTAU PRINCE',
    code: 'HTPAP'
  },
  {
    txt: 'HTSMC-圣马克-ST MARC',
    code: 'HTSMC'
  },
  {
    txt: 'HUBUD-布达佩斯-BUDAPEST',
    code: 'HUBUD'
  },
  {
    txt: 'IDAMI-安汶-AMBON ISLAND',
    code: 'IDAMI'
  },
  {
    txt: 'IDAMP-安佩南-AMPENAN',
    code: 'IDAMP'
  },
  {
    txt: 'IDART-阿米纳油码头-ARDJUNA TERMINAL',
    code: 'IDART'
  },
  {
    txt: 'IDBAN-马辰-BANJARMASIN',
    code: 'IDBAN'
  },
  {
    txt: 'IDBGI-巴纽旺宣-BANYUWANGI',
    code: 'IDBGI'
  },
  {
    txt: 'IDBIT-比通-BITUNG',
    code: 'IDBIT'
  },
  {
    txt: 'IDBLI-勿里洋-B1INYU',
    code: 'IDBLI'
  },
  {
    txt: 'IDBLS-望加丽-BENGKALIS',
    code: 'IDBLS'
  },
  {
    txt: 'IDBLU-朋古鲁-BENGKULU',
    code: 'IDBLU'
  },
  {
    txt: 'IDBMA-比马-BIMA',
    code: 'IDBMA'
  },
  {
    txt: 'IDBSA-巴眼牙比-BAGAN SIAPIAPI',
    code: 'IDBSA'
  },
  {
    txt: 'IDBUL-布莱伦-BULEIENG',
    code: 'IDBUL'
  },
  {
    txt: 'IDCIG-芝格丁-CIGADING',
    code: 'IDCIG'
  },
  {
    txt: 'IDCIL-芝拉扎-CILACAP',
    code: 'IDCIL'
  },
  {
    txt: 'IDCTT-信塔油码头-CINTA TERMINAL',
    code: 'IDCTT'
  },
  {
    txt: 'IDDAB-达博-DABO',
    code: 'IDDAB'
  },
  {
    txt: 'IDDMI-杜迈-DUMAI',
    code: 'IDDMI'
  },
  {
    txt: 'IDFAK-法克法克-FAKFAK',
    code: 'IDFAK'
  },
  {
    txt: 'IDGOR-哥伦打洛-GORONTALO',
    code: 'IDGOR'
  },
  {
    txt: 'DKNYK-尼克宾-NYKOBING(FAL.)',
    code: 'DKNYK'
  },
  {
    txt: 'DKODE-欧登塞-ODENSE ',
    code: 'DKODE'
  },
  {
    txt: 'DKRAN-兰讷斯-RANDERS ',
    code: 'DKRAN'
  },
  {
    txt: 'DKROD-勒兹比港-RODBYHAVN ',
    code: 'DKROD'
  },
  {
    txt: 'DKRON-伦呐-RONNE ',
    code: 'DKRON'
  },
  {
    txt: 'DKRVG-勒兹维-RODVIG',
    code: 'DKRVG'
  },
  {
    txt: 'DKSAK-萨克斯克宾-SAKSKOBING ',
    code: 'DKSAK'
  },
  {
    txt: 'DKSKI-斯基沃-SKIVE ',
    code: 'DKSKI'
  },
  {
    txt: 'DKSKK-斯凯拜克-SKAERBAEK ',
    code: 'DKSKK'
  },
  {
    txt: 'DKSKR-斯凯尔克-SKAELSKOR ',
    code: 'DKSKR'
  },
  {
    txt: 'DKSON-桑讷堡-SONDERBORG ',
    code: 'DKSON'
  },
  {
    txt: 'DKSTP-斯图德斯特鲁普-STUDSTRUP ',
    code: 'DKSTP'
  },
  {
    txt: 'DKSTR-斯楚厄-STRUER ',
    code: 'DKSTR'
  },
  {
    txt: 'DKSTU-斯图伯克宾-STUBBEKOBING ',
    code: 'DKSTU'
  },
  {
    txt: 'DKSVA-斯瓦讷克-SVANEKE ',
    code: 'DKSVA'
  },
  {
    txt: 'DKSVE-斯文堡-SVENDBORG ',
    code: 'DKSVE'
  },
  {
    txt: 'DKTBH-图堡港-TUBORG HAVN',
    code: 'DKTBH'
  },
  {
    txt: 'DKTIS-提斯特德-TISTED ',
    code: 'DKTIS'
  },
  {
    txt: 'DKVEJ-瓦埃勒-VEJLE ',
    code: 'DKVEJ'
  },
  {
    txt: 'DKVOR-沃尔丁堡-VORDINGBORG',
    code: 'DKVOR'
  },
  {
    txt: 'DOCAO-考塞多港-CAUCEDO',
    code: 'DOCAO'
  },
  {
    txt: 'DZALS-阿尔及尔-ALGIERS',
    code: 'DZALS'
  },
  {
    txt: 'ECGUL-瓜亚基尔-GUAYAQUIL',
    code: 'ECGUL'
  },
  {
    txt: 'EETAL-塔林-Tallinn',
    code: 'EETAL'
  },
  {
    txt: 'EGABZ-阿布宰尼迈-ABU ZENIMA ',
    code: 'EGABZ'
  },
  {
    txt: 'EGADA-阿代比耶-ADABIYA ',
    code: 'EGADA'
  },
  {
    txt: 'EGALA-索克哈那-ALAIN ALSOKHNA',
    code: 'EGALA'
  },
  {
    txt: 'EGALE-亚历山德里亚-ALEXANDRIA ',
    code: 'EGALE'
  },
  {
    txt: 'EGASU-艾因苏赫纳-AIN SUKHNA ',
    code: 'EGASU'
  },
  {
    txt: 'EGCAI-开罗-CAIRO',
    code: 'EGCAI'
  },
  {
    txt: 'EGDAM-杜姆亚特-DAMIETTA ',
    code: 'EGDAM'
  },
  {
    txt: 'EGDTA-达米埃塔-DAMIETTA',
    code: 'EGDTA'
  },
  {
    txt: 'EGKOS-库赛尔-KOSSEIR ',
    code: 'EGKOS'
  },
  {
    txt: 'EGMEH-哈姆拉港-MERSA EL HAMRA ',
    code: 'EGMEH'
  },
  {
    txt: 'EGMMA-马特鲁港-MERSA MATRUH ',
    code: 'EGMMA'
  },
  {
    txt: 'EGPIB-易卜拉欣港-PORT IBRAHIM ',
    code: 'EGPIB'
  },
  {
    txt: 'EGPSA-塞得港-PORT SAID ',
    code: 'EGPSA'
  },
  {
    txt: 'EGPTE-陶菲克港-PORT TEWFIK ',
    code: 'EGPTE'
  },
  {
    txt: 'EGRGB-拉斯加里卜-RAS GHARIB ',
    code: 'EGRGB'
  },
  {
    txt: 'EGRSR-喇斯舒海尔-RAS SHUKHEIR ',
    code: 'EGRSR'
  },
  {
    txt: 'EGSAF-萨法贾-SAFAGA ',
    code: 'EGSAF'
  },
  {
    txt: 'EGSKN-索卡纳-SOKHNA',
    code: 'EGSKN'
  },
  {
    txt: 'EGSKR-西迪基里尔-SIDI KERIR ',
    code: 'EGSKR'
  },
  {
    txt: 'EGSLM-塞卢姆-SAFUM ',
    code: 'EGSLM'
  },
  {
    txt: 'EGSUE-苏伊士-SUEZ ',
    code: 'EGSUE'
  },
  {
    txt: 'EGWDF-瓦迪费兰-WADI FEIRAN ',
    code: 'EGWDF'
  },
  {
    txt: 'ACARG-阿尔吉内金-ARGUINEGUIN',
    code: 'ACARG'
  },
  {
    txt: 'ACARR-阿雷西费-ARRECIFE',
    code: 'ACARR'
  },
  {
    txt: 'ACLSA-拉萨利内塔-LA SALINETA',
    code: 'ACLSA'
  },
  {
    txt: 'ACPDR-罗萨里奥港-PUERTO DEL ROSARIO',
    code: 'ACPDR'
  },
  {
    txt: 'ACSCP-圣克鲁斯-SANTA CRUZ(PALMA)',
    code: 'ACSCP'
  },
  {
    txt: 'ACSCT-圣克鲁斯-SANTA CRUZ(TENERIFE)',
    code: 'ACSCT'
  },
  {
    txt: 'ESADR-阿德拉　　　　　　　　-ADRA',
    code: 'ESADR'
  },
  {
    txt: 'ESAGU-阿吉拉斯　　　　　　　-AGUILAS',
    code: 'ESAGU'
  },
  {
    txt: 'ESALC-阿尔库迪亚　　　　　　-ALCUDIA',
    code: 'ESALC'
  },
  {
    txt: 'ESALI-阿利坎特　　　　　　　-ALICANTE',
    code: 'ESALI'
  },
  {
    txt: 'ESALM-阿尔梅里亚　　　　　　-ALMERIA',
    code: 'ESALM'
  },
  {
    txt: 'ESAVI-阿维莱斯　　　　　　　-AVILES',
    code: 'ESAVI'
  },
  {
    txt: 'ESBAE-巴尔瓦特-BARBATE',
    code: 'ESBAE'
  },
  {
    txt: 'ESBAR-巴塞罗那　　　　　　　-BARCELONA',
    code: 'ESBAR'
  },
  {
    txt: 'ESBIL-毕尔巴鄂　　　　　　　-BILBAO',
    code: 'ESBIL'
  },
  {
    txt: 'ESBNS-布拉内斯　　　　　　　-BLANES',
    code: 'ESBNS'
  },
  {
    txt: 'ESBUR-布里亚纳　　　　　　　-BURRIANA',
    code: 'ESBUR'
  },
  {
    txt: 'ESCAD-加的斯　　　　　　　　-CADIZ(ES)',
    code: 'ESCAD'
  },
  {
    txt: 'ESCAR-卡塔赫纳　　　　　　　-CARTAGENA(ES)',
    code: 'ESCAR'
  },
  {
    txt: 'ESCAS-卡斯特利翁　　　　　　-CASTELLON',
    code: 'ESCAS'
  },
  {
    txt: 'ESCEA-休达-CEUTA',
    code: 'ESCEA'
  },
  {
    txt: 'ESCFI-菲力斯特里　　　　　　-CAPE FINISTRERR',
    code: 'ESCFI'
  },
  {
    txt: 'ESCIL-锡列罗　　　　　　　　-CILLERO',
    code: 'ESCIL'
  },
  {
    txt: 'IDGRE-格雷西（锦石）-GRESIK',
    code: 'IDGRE'
  },
  {
    txt: 'IDJAK-雅加达-JAKARTA',
    code: 'IDJAK'
  },
  {
    txt: 'IDJAY-查亚普拉-JAYAPURA',
    code: 'IDJAY'
  },
  {
    txt: 'IDJBI-占碑-JAMBI',
    code: 'IDJBI'
  },
  {
    txt: 'IDKAL-卡利昂厄特-KALIANGET',
    code: 'IDKAL'
  },
  {
    txt: 'IDKAS-卡西姆-KASIM',
    code: 'IDKAS'
  },
  {
    txt: 'IDKET-吉打邦-KETAPANG',
    code: 'IDKET'
  },
  {
    txt: 'IDKKS-瓜拉卡普阿斯-KUALA KAPUAS',
    code: 'IDKKS'
  },
  {
    txt: 'IDKRU-格鲁-KRU',
    code: 'IDKRU'
  },
  {
    txt: 'IDKTG-KUALA TANJUNG-瓜拉丹戎',
    code: 'IDKTG'
  },
  {
    txt: 'IDKUP-古邦-KUPANG',
    code: 'IDKUP'
  },
  {
    txt: 'IDMAC-马卡萨-MACASSAR',
    code: 'IDMAC'
  },
  {
    txt: 'IDMAL-马利利-MALILI',
    code: 'IDMAL'
  },
  {
    txt: 'IDMED-棉兰-MEDAN',
    code: 'IDMED'
  },
  {
    txt: 'IDMEN-万鸦老-MENADO',
    code: 'IDMEN'
  },
  {
    txt: 'IDMER-马老奇-MERAUKE',
    code: 'IDMER'
  },
  {
    txt: 'IDMJU-马穆朱-MAMUJU',
    code: 'IDMJU'
  },
  {
    txt: 'IDMRK-默拉克(孔雀岛)-MERAK',
    code: 'IDMRK'
  },
  {
    txt: 'IDMUN-蒙托克-MUNTOK',
    code: 'IDMUN'
  },
  {
    txt: 'IDPAB-沙璜-SABANG',
    code: 'IDPAB'
  },
  {
    txt: 'IDPAK-北千巴鲁-PAKANBARU',
    code: 'IDPAK'
  },
  {
    txt: 'IDPAL-巨港-PALEMBANG',
    code: 'IDPAL'
  },
  {
    txt: 'IDPAM-帕马努坎-PAMANUKAN',
    code: 'IDPAM'
  },
  {
    txt: 'IDPAR-巴里巴里-PAREPARE',
    code: 'IDPAR'
  },
  {
    txt: 'IDPAS-巴苏鲁安-PASARUAN',
    code: 'IDPAS'
  },
  {
    txt: 'IDPBM-庞卡尔-PANGKAL BALAM',
    code: 'IDPBM'
  },
  {
    txt: 'IDPEK-北加浪岸-PEKALONGAN',
    code: 'IDPEK'
  },
  {
    txt: 'IDPEM-班马吉-PEMANGKAT',
    code: 'IDPEM'
  },
  {
    txt: 'IDPJG-潘姜-PANJANG',
    code: 'IDPJG'
  },
  {
    txt: 'IDPLO-帕洛波-PALOPO',
    code: 'IDPLO'
  },
  {
    txt: 'IDPNK-坤甸-PONTIANAK',
    code: 'IDPNK'
  },
  {
    txt: 'IDPOM-波马拉-POMALAA',
    code: 'IDPOM'
  },
  {
    txt: 'IDPOS-波索-POSO',
    code: 'IDPOS'
  },
  {
    txt: 'IDPPG-槟港-PANGKAL PINANG',
    code: 'IDPPG'
  },
  {
    txt: 'IDPRO-普罗博林戈-PROBOLINGGO',
    code: 'IDPRO'
  },
  {
    txt: 'IDPSA-普劳桑布-PULAU SAMBU',
    code: 'IDPSA'
  },
  {
    txt: 'IDPSU-庞卡兰苏苏-PANGKALAN SUSU',
    code: 'IDPSU'
  },
  {
    txt: 'IDSAL-沙拉瓦蒂-SALAWATI',
    code: 'IDSAL'
  },
  {
    txt: 'IDSAM-三马林达-SAMARINDA',
    code: 'IDSAM'
  },
  {
    txt: 'IDSAN-桑库利朗-SANKULIRANG',
    code: 'IDSAN'
  },
  {
    txt: 'IDSBS-三发-SAMBAS',
    code: 'IDSBS'
  },
  {
    txt: 'IDSEM-三宝垄-SEMARANG',
    code: 'IDSEM'
  },
  {
    txt: 'IDSGE-双溪格龙-SUNGEI GERONG',
    code: 'IDSGE'
  },
  {
    txt: 'IDSIB-实武牙-SIBOLGA',
    code: 'IDSIB'
  },
  {
    txt: 'IDSKO-双溪克拉克-SUNGEI KOLAK',
    code: 'IDSKO'
  },
  {
    txt: 'IDSNT-塞尼帕油码头-SENIPAH TERMINAL',
    code: 'IDSNT'
  },
  {
    txt: 'IDSOR-索龙-SORONG',
    code: 'IDSOR'
  },
  {
    txt: 'IDSPA-巴宁河-SUNGEI PAKNING',
    code: 'IDSPA'
  },
  {
    txt: 'IDSPT-桑皮特-SAMPIT',
    code: 'IDSPT'
  },
  {
    txt: 'IDSTT-圣坦油码头-SANTAN TERMINAL',
    code: 'IDSTT'
  },
  {
    txt: 'IDSUA-泗水-SURABAYA',
    code: 'IDSUA'
  },
  {
    txt: 'IDSUM-苏门答腊岛-SUMARTRA ISLAND',
    code: 'IDSUM'
  },
  {
    txt: 'IDSUR-苏腊巴亚（泗水）-SURABAYA',
    code: 'IDSUR'
  },
  {
    txt: 'IDTAI-打拉根-TARAKAN ISLAND',
    code: 'IDTAI'
  },
  {
    txt: 'IDTBN-图班-TUBAN',
    code: 'IDTBN'
  },
  {
    txt: 'IDTEG-直葛-TEGAL',
    code: 'IDTEG'
  },
  {
    txt: 'IDTEI-德那第-TERNATE ISLAND',
    code: 'IDTEI'
  },
  {
    txt: 'IDTEL-直落勿洞-TELUKBETUNG',
    code: 'IDTEL'
  },
  {
    txt: 'IDTEM-淡美拉汉-TEMBILAHAN',
    code: 'IDTEM'
  },
  {
    txt: 'IDTOB-都保里-TOBOALI',
    code: 'IDTOB'
  },
  {
    txt: 'IDTOL-托利托利-TOLI TOLI',
    code: 'IDTOL'
  },
  {
    txt: 'IDTPA-丹戎潘丹-TANJUNG PANDAN',
    code: 'IDTPA'
  },
  {
    txt: 'IDTPI-丹戎槟榔-TANJUNG PINANG',
    code: 'IDTPI'
  },
  {
    txt: 'IDTPR-丹戎不碌-TANJUNG PRIOK',
    code: 'IDTPR'
  },
  {
    txt: 'IDTRE-丹戎勒德布-TANJUNG REDEB',
    code: 'IDTRE'
  },
  {
    txt: 'IDTSO-丹戎索法-TANJUNG SOFA',
    code: 'IDTSO'
  },
  {
    txt: 'IDTUB-丹戎乌班-TANIUNG UBAN',
    code: 'IDTUB'
  },
  {
    txt: 'IEARK-阿克洛-ARKLOW',
    code: 'IEARK'
  },
  {
    txt: 'IEBAL-巴尔的摩-BALTIMORE',
    code: 'IEBAL'
  },
  {
    txt: 'IEBGN-巴尔布里根-BALBRIGGAN',
    code: 'IEBGN'
  },
  {
    txt: 'IEBNA-巴利纳-BALLINA',
    code: 'IEBNA'
  },
  {
    txt: 'IECAB-卡斯尔顿贝尔-CASTLETOWN BERE',
    code: 'IECAB'
  },
  {
    txt: 'IECAH-克尔西文-CAHLRCIVEEN',
    code: 'IECAH'
  },
  {
    txt: 'IECLA-克莱尔卡斯尔-CLARECASTLE',
    code: 'IECLA'
  },
  {
    txt: 'IECOB-科夫-COBH',
    code: 'IECOB'
  },
  {
    txt: 'IECOK-科克-CORK',
    code: 'IECOK'
  },
  {
    txt: 'IEDCN-邓坎嫩-DUNCANNON',
    code: 'IEDCN'
  },
  {
    txt: 'IEDDK-邓多克-DUNDALK',
    code: 'IEDDK'
  },
  {
    txt: 'IEDRO-德罗赫达-DROGHEDA',
    code: 'IEDRO'
  },
  {
    txt: 'IEDUB-都柏林-DUBLIN',
    code: 'IEDUB'
  },
  {
    txt: 'IEDUL-敦劳费尔-DUNLAOGHAIRE',
    code: 'IEDUL'
  },
  {
    txt: 'IEDVN-邓加文-DUNGARVAN',
    code: 'IEDVN'
  },
  {
    txt: 'IEFNT-费尼特-FENIT',
    code: 'IEFNT'
  },
  {
    txt: 'IEFOY-福因斯-FOYNES',
    code: 'IEFOY'
  },
  {
    txt: 'IEGRE-格里诺尔-GREENORE',
    code: 'IEGRE'
  },
  {
    txt: 'IEHOW-霍思-HOWTH',
    code: 'IEHOW'
  },
  {
    txt: 'IEKIL-基尔罗南-KILRONAN',
    code: 'IEKIL'
  },
  {
    txt: 'IEKIN-金塞尔-KINSALE',
    code: 'IEKIN'
  },
  {
    txt: 'IEKLA-基拉拉-KILLALA',
    code: 'IEKLA'
  },
  {
    txt: 'IELIM-利默里克-LIMERICK',
    code: 'IELIM'
  },
  {
    txt: 'IEMOV-莫维尔-MOVILLE',
    code: 'IEMOV'
  },
  {
    txt: 'IENRS-新罗斯-NEWROSS',
    code: 'IENRS'
  },
  {
    txt: 'IEROS-罗斯莱尔-ROSSLARE',
    code: 'IEROS'
  },
  {
    txt: 'IESCH-斯卡尔-SCHULL',
    code: 'IESCH'
  },
  {
    txt: 'IESLI-斯莱戈-SLIGO',
    code: 'IESLI'
  },
  {
    txt: 'IEVAL-瓦伦西亚-VALENTTA',
    code: 'IEVAL'
  },
  {
    txt: 'IEWAT-沃特福德-WATERFORD',
    code: 'IEWAT'
  },
  {
    txt: 'IEWEX-韦克斯福德-WEXFORD',
    code: 'IEWEX'
  },
  {
    txt: 'IEYOU-约尔-YOUGHAL',
    code: 'IEYOU'
  },
  {
    txt: 'ILACR-阿卡-ACRE',
    code: 'ILACR'
  },
  {
    txt: 'ILASH-阿什克伦-ASHKELON',
    code: 'ILASH'
  },
  {
    txt: 'ILEIL-埃拉特-EILAT',
    code: 'ILEIL'
  },
  {
    txt: 'ILHAD-哈代拉-HADERA',
    code: 'ILHAD'
  },
  {
    txt: 'ILJFA-雅法-JAFFA',
    code: 'ILJFA'
  },
  {
    txt: 'ILTAV-特拉维夫-TEL-AVIV',
    code: 'ILTAV'
  },
  {
    txt: 'INALL-阿勒皮-ALLEPPEY',
    code: 'INALL'
  },
  {
    txt: 'INBED-贝迪-BEDI',
    code: 'INBED'
  },
  {
    txt: 'INBEL-贝莱克里-BELEKERI',
    code: 'INBEL'
  },
  {
    txt: 'INBHA-包纳加尔-BHAVNAGAR',
    code: 'INBHA'
  },
  {
    txt: 'INBHI-比莱-BHILAI',
    code: 'INBHI'
  },
  {
    txt: 'ITARB-阿尔巴塔克斯-ARBATAX',
    code: 'ITARB'
  },
  {
    txt: 'ITAUG-奥古斯塔-AUGUSTA',
    code: 'ITAUG'
  },
  {
    txt: 'ITAVO-阿沃拉-AVOLA',
    code: 'ITAVO'
  },
  {
    txt: 'ITBAG-巴尼奥利-BAGNOLI ',
    code: 'ITBAG'
  },
  {
    txt: 'ITBAR-巴里-BARI',
    code: 'ITBAR'
  },
  {
    txt: 'ITBRI-布林迪西-BRINDISI',
    code: 'ITBRI'
  },
  {
    txt: 'ITBTA-巴列塔-BARLETTA',
    code: 'ITBTA'
  },
  {
    txt: 'ITCAG-卡利亚里-CAGLIAD',
    code: 'ITCAG'
  },
  {
    txt: 'ITCAR-卡洛福泰-CARLOFORTE',
    code: 'ITCAR'
  },
  {
    txt: 'ITCAT-卡塔尼亚-CATANIA',
    code: 'ITCAT'
  },
  {
    txt: 'ITCDS-斯塔比亚海堡-CASTELLAMMAREDI STABIA',
    code: 'ITCDS'
  },
  {
    txt: 'ITCHI-基奥贾-CHIOGGIA',
    code: 'ITCHI'
  },
  {
    txt: 'ITCIV-奇维塔韦基亚-CIVITAVECCHIA',
    code: 'ITCIV'
  },
  {
    txt: 'ITCRE-克罗托内-CROTONE',
    code: 'ITCRE'
  },
  {
    txt: 'ITCRO-卡坦扎罗-CATANZARO',
    code: 'ITCRO'
  },
  {
    txt: 'ITFIU-菲乌米奇诺-FIUMICINO',
    code: 'ITFIU'
  },
  {
    txt: 'ITFOL-福洛尼卡-FOLLONICA',
    code: 'ITFOL'
  },
  {
    txt: 'ITFOR-福尔米亚-FORMIA',
    code: 'ITFOR'
  },
  {
    txt: 'ITGAE-加埃塔-GAETA',
    code: 'ITGAE'
  },
  {
    txt: 'RUNJK-纳雷德卡-NAKHODKA',
    code: 'RUNJK'
  },
  {
    txt: 'RUNNM-纳里扬马尔-NARYAN MAR',
    code: 'RUNNM'
  },
  {
    txt: 'RUNOG-诺格利基-NOGLIKI',
    code: 'RUNOG'
  },
  {
    txt: 'RUNVS-新罗西斯克-NOVOROSSISK',
    code: 'RUNVS'
  },
  {
    txt: 'RUOHA-奥哈-OKHA',
    code: 'RUOHA'
  },
  {
    txt: 'RUOHO-鄂霍次克-OKHOTSK',
    code: 'RUOHO'
  },
  {
    txt: 'RUOKT-十月市-OKTYABRSKIY',
    code: 'RUOKT'
  },
  {
    txt: 'RUONG-奥涅加-ONEGA',
    code: 'RUONG'
  },
  {
    txt: 'RUPRI-普里莫尔斯克-PRIMORSK',
    code: 'RUPRI'
  },
  {
    txt: 'RUPRN-波罗奈斯克-PORONAISK',
    code: 'RUPRN'
  },
  {
    txt: 'RUPTK-彼得罗巴浦洛夫斯克-PETROPAVLOVSK',
    code: 'RUPTK'
  },
  {
    txt: 'RUROV-罗斯托夫-ROSTOV',
    code: 'RUROV'
  },
  {
    txt: 'RUSHA-沙赫乔特斯克-SHAKHTERSK',
    code: 'RUSHA'
  },
  {
    txt: 'RUSOC-索契-SOCHI',
    code: 'RUSOC'
  },
  {
    txt: 'RUSOG-苏维埃港-SOVETSKAVA GAVAN',
    code: 'RUSOG'
  },
  {
    txt: 'RUSVE-斯伟特拉亚河-SVETLAYA RIVER',
    code: 'RUSVE'
  },
  {
    txt: 'RUTAG-塔甘罗格-FAGANROG',
    code: 'RUTAG'
  },
  {
    txt: 'RUTUA-图阿普谢-TUAPSE',
    code: 'RUTUA'
  },
  {
    txt: 'RUUGL-乌格里哥斯克-UGLEGORSK',
    code: 'RUUGL'
  },
  {
    txt: 'RUULU-乌兰乌德-Ulan-Ude',
    code: 'RUULU'
  },
  {
    txt: 'RUUMB-翁巴-UMBA',
    code: 'RUUMB'
  },
  {
    txt: 'RUVNN-瓦尼诺-VANINO',
    code: 'RUVNN'
  },
  {
    txt: 'RUVOS-东方港-VOSTOCHNYY',
    code: 'RUVOS'
  },
  {
    txt: 'RUVUR-武尔纳雷-VURNARY',
    code: 'RUVUR'
  },
  {
    txt: 'RUVVO-符拉迪沃斯托克-VLADIVOSTOK',
    code: 'RUVVO'
  },
  {
    txt: 'RUVYG-维堡-VYBORG',
    code: 'RUVYG'
  },
  {
    txt: 'RUVYS-维索茨克-VYSOTSK',
    code: 'RUVYS'
  },
  {
    txt: 'RUZHD-日丹诺夫-ZHDANOV',
    code: 'RUZHD'
  },
  {
    txt: 'RUZRB-扎鲁比诺-ZARUBINO',
    code: 'RUZRB'
  },
  {
    txt: 'SADAM-达曼-DAMMAM',
    code: 'SADAM'
  },
  {
    txt: 'SAGIZ-季赞-GIZAN',
    code: 'SAGIZ'
  },
  {
    txt: 'SAJED-吉达-JEDDAH',
    code: 'SAJED'
  },
  {
    txt: 'SAJUB-朱拜勒-JUBAIL',
    code: 'SAJUB'
  },
  {
    txt: 'SARAK-拉斯海夫吉-RAS AL KHAFJI',
    code: 'SARAK'
  },
  {
    txt: 'SARAM-米萨卜角-RAS A LMISHAB',
    code: 'SARAM'
  },
  {
    txt: 'SARIH-利雅德-RIYADH',
    code: 'SARIH'
  },
  {
    txt: 'SARTA-拉斯坦努拉-RAS TANURA',
    code: 'SARTA'
  },
  {
    txt: 'SBALH-阿拉迪斯港-ALLARDYCE HARBOUR',
    code: 'SBALH'
  },
  {
    txt: 'SBGIZ-吉佐-GIZO',
    code: 'SBGIZ'
  },
  {
    txt: 'SBHON-霍尼亚拉-HONIARA',
    code: 'SBHON'
  },
  {
    txt: 'SBRGC-林吉湾-RINGI COVE',
    code: 'SBRGC'
  },
  {
    txt: 'SBSLI-肖特兰岛-SHORTLAND ISLAND',
    code: 'SBSLI'
  },
  {
    txt: 'SBTUL-图拉吉-TULAGI',
    code: 'SBTUL'
  },
  {
    txt: 'SCPVI-维多利亚湾-PORT VICTORIA',
    code: 'SCPVI'
  },
  {
    txt: 'SDPSU-苏丹港-PORTSUDAN',
    code: 'SDPSU'
  },
  {
    txt: 'SEAHS-奥胡斯-AHUS',
    code: 'SEAHS'
  },
  {
    txt: 'SEALA-阿拉-ALA',
    code: 'SEALA'
  },
  {
    txt: 'SEARB-阿尔博加-ARBOGA',
    code: 'SEARB'
  },
  {
    txt: 'SEBRO-布罗夫约尔丹-BROFJORDEN',
    code: 'SEBRO'
  },
  {
    txt: 'SEBUR-布雷奥-BUREA',
    code: 'SEBUR'
  },
  {
    txt: 'SEDAL-达拉勒-DALARO',
    code: 'SEDAL'
  },
  {
    txt: 'SEDEG-代格港-DEGERHAMN',
    code: 'SEDEG'
  },
  {
    txt: 'SEENK-恩雪平-ENKOPING',
    code: 'SEENK'
  },
  {
    txt: 'SEFAL-法尔肯贝里-FALKENBERG',
    code: 'SEFAL'
  },
  {
    txt: 'SEGEF-耶夫勒-GEFLE',
    code: 'SEGEF'
  },
  {
    txt: 'SEGOT-哥德堡-GOTHENBURG',
    code: 'SEGOT'
  },
  {
    txt: 'SEHAL-哈尔斯塔维克-HALLSTAVIK',
    code: 'SEHAL'
  },
  {
    txt: 'SEHAN-哈里港-HARGSHAMN',
    code: 'SEHAN'
  },
  {
    txt: 'SEHAR-哈拉霍尔梅-HARAHOLMEN',
    code: 'SEHAR'
  },
  {
    txt: 'SEHEL-赫尔辛堡-HELSINGBORG',
    code: 'SEHEL'
  },
  {
    txt: 'SEHER-赫纳散德-HERNOSAND',
    code: 'SEHER'
  },
  {
    txt: 'SEHOL-霍尔姆松德-HOLMSUND',
    code: 'SEHOL'
  },
  {
    txt: 'SEHOR-霍讷福什-HORNEFORS',
    code: 'SEHOR'
  },
  {
    txt: 'SEHSD-哈尔姆斯塔德-HALMSTAD',
    code: 'SEHSD'
  },
  {
    txt: 'SEHUD-胡迪克斯瓦尔-HUDIKSVALL',
    code: 'SEHUD'
  },
  {
    txt: 'SEIGG-伊格松德-IGGESUND',
    code: 'SEIGG'
  },
  {
    txt: 'SEKAA-卡尔斯克鲁纳-KARLSKRONA',
    code: 'SEKAA'
  },
  {
    txt: 'SEKAD-卡尔斯塔德-KARLSTAD',
    code: 'SEKAD'
  },
  {
    txt: 'SEKAG-卡尔斯堡-KARLSBORG',
    code: 'SEKAG'
  },
  {
    txt: 'SEKAN-卡尔斯港-KARLSHAMN',
    code: 'SEKAN'
  },
  {
    txt: 'SEKAR-卡斯卡-KARSKAR',
    code: 'SEKAR'
  },
  {
    txt: 'SEKLA-克拉格斯港-KLAGSHAMN',
    code: 'SEKLA'
  },
  {
    txt: 'SEKLX-卡利克斯-KALIX',
    code: 'SEKLX'
  },
  {
    txt: 'SEKOP-雪平-KOPING',
    code: 'SEKOP'
  },
  {
    txt: 'SEKRA-克拉姆福什-KRAMFORS',
    code: 'SEKRA'
  },
  {
    txt: 'SEKRI-克里斯蒂娜港-KRISTINEHAMN',
    code: 'SEKRI'
  },
  {
    txt: 'SELAN-兰斯克鲁纳-LANDSKRONA',
    code: 'SELAN'
  },
  {
    txt: 'SELID-利德雪平-LIDKOPING',
    code: 'SELID'
  },
  {
    txt: 'SELIM-利姆港-LIMHAMN',
    code: 'SELIM'
  },
  {
    txt: 'SELJU-于斯讷-LJUSNE',
    code: 'SELJU'
  },
  {
    txt: 'SELOM-卢马-LOMMA',
    code: 'SELOM'
  },
  {
    txt: 'SELUL-吕勒奥-LULEA',
    code: 'SELUL'
  },
  {
    txt: 'SELYS-吕瑟希尔-LYSEKIL',
    code: 'SELYS'
  },
  {
    txt: 'SEMAL-马尔默-MALMO',
    code: 'SEMAL'
  },
  {
    txt: 'SEMAR-马斯特兰德-MARSTRAND',
    code: 'SEMAR'
  },
  {
    txt: 'SEMOT-穆塔拉-MOTALA',
    code: 'SEMOT'
  },
  {
    txt: 'SENOR-诺尔雪平-NORRKOPING',
    code: 'SENOR'
  },
  {
    txt: 'SENRT-诺尔松德-NORRSUNDET',
    code: 'SENRT'
  },
  {
    txt: 'SENYK-尼雪平-NYKOPING',
    code: 'SENYK'
  },
  {
    txt: 'SENYN-尼奈斯港-NYNASHAMN',
    code: 'SENYN'
  },
  {
    txt: 'SEOAX-瓦克森-OAXEN',
    code: 'SEOAX'
  },
  {
    txt: 'SEORN-恩舍尔兹维克-ORNSKOLDSVIK',
    code: 'SEORN'
  },
  {
    txt: 'SEOSK-奥斯卡港-OSKARSHAMN',
    code: 'SEOSK'
  },
  {
    txt: 'SEOTT-乌特拜肯-OTTERBACKEN',
    code: 'SEOTT'
  },
  {
    txt: 'SEOXE-乌克瑟勒松德-OXELOSUND',
    code: 'SEOXE'
  },
  {
    txt: 'SEPAS-波斯卡拉维克-PASKALLAVIK',
    code: 'SEPAS'
  },
  {
    txt: 'SEPAT-帕塔霍尔姆-PATAHOLM',
    code: 'SEPAT'
  },
  {
    txt: 'SEPIT-皮特奥-PITEA',
    code: 'SEPIT'
  },
  {
    txt: 'SERBN-尤讷比港-RONNEBYHAMN',
    code: 'SERBN'
  },
  {
    txt: 'SERON-鲁讷港-RONEHAMN',
    code: 'SERON'
  },
  {
    txt: 'SESAN-桑达讷-SANDARNE',
    code: 'SESAN'
  },
  {
    txt: 'SESIK-锡克奥-SIKEA',
    code: 'SESIK'
  },
  {
    txt: 'SESIM-锡姆里斯港-SIMRISHAMN',
    code: 'SESIM'
  },
  {
    txt: 'SESJE-南泰利耶-SODERTELIE',
    code: 'SESJE'
  },
  {
    txt: 'SESKA-谢莱夫特奥-SKELLEFTEA',
    code: 'SESKA'
  },
  {
    txt: 'SESKE-谢莱夫特港-SKELLEFTEHAMN',
    code: 'SESKE'
  },
  {
    txt: 'SESKO-斯库格哈尔-SKOGHALL',
    code: 'SESKO'
  },
  {
    txt: 'SESKR-斯克雷特维克-SKREDSVIK',
    code: 'SESKR'
  },
  {
    txt: 'SESKU-斯屈特谢尔-SKUTSKAR',
    code: 'SESKU'
  },
  {
    txt: 'SESLI-斯利特-SLITE',
    code: 'SESLI'
  },
  {
    txt: 'SESOD-瑟德港-SODERHAMN',
    code: 'SESOD'
  },
  {
    txt: 'SESOG-南雪平-SODERKOPING',
    code: 'SESOG'
  },
  {
    txt: 'SESOL-瑟尔沃斯堡-SOIVESBORG',
    code: 'SESOL'
  },
  {
    txt: 'SESTD-斯特伦斯塔德-STROMSTAD',
    code: 'SESTD'
  },
  {
    txt: 'SESTE-斯泰农松德-STENUNGSUND',
    code: 'SESTE'
  },
  {
    txt: 'SESTO-斯德哥尔摩-STOCKHOLM',
    code: 'SESTO'
  },
  {
    txt: 'SESTR-斯特兰奈斯-STRANGNAS',
    code: 'SESTR'
  },
  {
    txt: 'SESTU-斯图格松德-STUGSUND',
    code: 'SESTU'
  },
  {
    txt: 'SESVL-松兹瓦尔-SUNDSVALL',
    code: 'SESVL'
  },
  {
    txt: 'SESVN-桑德维肯-SANDVIKEN',
    code: 'SESVN'
  },
  {
    txt: 'SETRE-特雷勒堡-TRELLEBORG',
    code: 'SETRE'
  },
  {
    txt: 'SETRO-特罗尔海坦-TROLLHATTAN',
    code: 'SETRO'
  },
  {
    txt: 'SEUDD-乌德瓦拉-UDDEVALLA',
    code: 'SEUDD'
  },
  {
    txt: 'SEUME-于默奥-UMEA',
    code: 'SEUME'
  },
  {
    txt: 'SEUTA-友丹佐-UTANSJO',
    code: 'SEUTA'
  },
  {
    txt: 'SEVAR-瓦尔贝里-VARBERG',
    code: 'SEVAR'
  },
  {
    txt: 'SEVES-维斯特拉斯-VESTERAS',
    code: 'SEVES'
  },
  {
    txt: 'SEVIF-维弗斯塔瓦夫-VIFSTAVARF',
    code: 'SEVIF'
  },
  {
    txt: 'SEVIS-维斯比-VISBY',
    code: 'SEVIS'
  },
  {
    txt: 'SEWAI-瓦贾-WAIJA',
    code: 'SEWAI'
  },
  {
    txt: 'SEWHN-瓦尔港-WALLHAMN',
    code: 'SEWHN'
  },
  {
    txt: 'SEWVK-喔尔维尔-WALLVIK',
    code: 'SEWVK'
  },
  {
    txt: 'SEYST-于斯塔德-YSTAD',
    code: 'SEYST'
  },
  {
    txt: 'SGJUR-裕廊-JURONG',
    code: 'SGJUR'
  },
  {
    txt: 'SGMNP-马尼拉北港-MANILA NORTH PORT',
    code: 'SGMNP'
  },
  {
    txt: 'SGSEM-森巴旺-SEMBAWANG',
    code: 'SGSEM'
  },
  {
    txt: 'SGSGP-新加坡-SINGAPORE',
    code: 'SGSGP'
  },
  {
    txt: 'SGTPU-丹章彭鲁-TANJONG PENJURU',
    code: 'SGTPU'
  },
  {
    txt: 'SHASI-阿森松岛-ASCENSION ISLAND',
    code: 'SHASI'
  },
  {
    txt: 'SIIZO-伊佐拉-IZOLA',
    code: 'SIIZO'
  },
  {
    txt: 'SIKOP-科佩尔-KOPER',
    code: 'SIKOP'
  },
  {
    txt: 'SIPIR-皮兰-PIRAN',
    code: 'SIPIR'
  },
  {
    txt: 'SLPPL-佩佩尔-PEPEL',
    code: 'SLPPL'
  },
  {
    txt: 'SLSHI-歇尔布罗岛-SHERBRO ISLAND',
    code: 'SLSHI'
  },
  {
    txt: 'SNDAK-达喀尔-DAKAR',
    code: 'SNDAK'
  },
  {
    txt: 'SNKAO-考拉克-KAOLACK',
    code: 'SNKAO'
  },
  {
    txt: 'SOALU-阿鲁拉-ALULA',
    code: 'SOALU'
  },
  {
    txt: 'SOBER-柏培拉-BERBERA',
    code: 'SOBER'
  },
  {
    txt: 'SOBOS-博萨索-BOSASO',
    code: 'SOBOS'
  },
  {
    txt: 'SODAN-丹特-DANTE',
    code: 'SODAN'
  },
  {
    txt: 'SOKIS-基斯马尤-KISMAYU',
    code: 'SOKIS'
  },
  {
    txt: 'SOMER-马尔卡-MERCA',
    code: 'SOMER'
  },
  {
    txt: 'SOOBB-奥比亚-OBBIA',
    code: 'SOOBB'
  },
  {
    txt: 'SRMGO-蒙戈-MOENGO',
    code: 'SRMGO'
  },
  {
    txt: 'SRPAR-帕拉南-PARANAM',
    code: 'SRPAR'
  },
  {
    txt: 'SRPBO-帕拉马里博-PARAMARIBO',
    code: 'SRPBO'
  },
  {
    txt: 'SRWAG-瓦黑宁恩-WAGENINGEN',
    code: 'SRWAG'
  },
  {
    txt: 'STPRI-普林西比岛-PRINCIPE ISLAND',
    code: 'STPRI'
  },
  {
    txt: 'STSTO-圣多美-SAO TOME',
    code: 'STSTO'
  },
  {
    txt: 'SVCUT-库图科-CUTUCO',
    code: 'SVCUT'
  },
  {
    txt: 'SVLLI-拉利贝塔德-LALIBERTAD',
    code: 'SVLLI'
  },
  {
    txt: 'SVLUN-拉乌尼翁-LAUNION',
    code: 'SVLUN'
  },
  {
    txt: 'SYBAN-巴尼亚斯-BANIAS',
    code: 'SYBAN'
  },
  {
    txt: 'SYDAM-大马士革-DAMASCUS',
    code: 'SYDAM'
  },
  {
    txt: 'SYLAT-拉塔基亚-LATTAKIA',
    code: 'SYLAT'
  },
  {
    txt: 'SYTTS-他图斯-TARTOUS',
    code: 'SYTTS'
  },
  {
    txt: 'SZMPA-马萨发-MATSAPHA',
    code: 'SZMPA'
  },
  {
    txt: 'TCGTK-大特克-GRAND TURK',
    code: 'TCGTK'
  },
  {
    txt: 'TGKPE-佩梅-KPEME',
    code: 'TGKPE'
  },
  {
    txt: 'TGLOM-洛美-LOME',
    code: 'TGLOM'
  },
  {
    txt: 'THBKK-曼谷-BANGKOK',
    code: 'THBKK'
  },
  {
    txt: 'THKAN-干当-KANTANG',
    code: 'THKAN'
  },
  {
    txt: 'THKSI-锡昌岛-KO SICHANG',
    code: 'THKSI'
  },
  {
    txt: 'THLCH-林查班-LAEM CHABANG',
    code: 'THLCH'
  },
  {
    txt: 'THNAR-那拉提瓦-NARATHIWAT',
    code: 'THNAR'
  },
  {
    txt: 'THPAK-北榄-PAKNAM',
    code: 'THPAK'
  },
  {
    txt: 'THPAT-北大年-PATTANI',
    code: 'THPAT'
  },
  {
    txt: 'THPHU-普吉-PHUKET',
    code: 'THPHU'
  },
  {
    txt: 'THSAT-梭桃邑-SATTAHIP',
    code: 'THSAT'
  },
  {
    txt: 'THSIR-是拉差-SIRACHA',
    code: 'THSIR'
  },
  {
    txt: 'THSON-宋卡-SONGKHLA',
    code: 'THSON'
  },
  {
    txt: 'TJDSB-杜尚别-DUSHANBE',
    code: 'TJDSB'
  },
  {
    txt: 'TNAST-阿什塔特码头-ASHTART TERMINA',
    code: 'TNAST'
  },
  {
    txt: 'TNBIZ-比塞大-BIZERTA',
    code: 'TNBIZ'
  },
  {
    txt: 'TNDJI-杰尔巴岛-DIERBA LSLAND',
    code: 'TNDJI'
  },
  {
    txt: 'TNGAB-加贝斯-GABES',
    code: 'TNGAB'
  },
  {
    txt: 'TNLGO-拉古莱特-LA GOULETTE',
    code: 'TNLGO'
  },
  {
    txt: 'TNLSK-拉斯基拉-LA SKHIRRA',
    code: 'TNLSK'
  },
  {
    txt: 'TNSFA-斯法克斯-SFAX',
    code: 'TNSFA'
  },
  {
    txt: 'TNSUS-苏萨-SUSA',
    code: 'TNSUS'
  },
  {
    txt: 'TNTNS-突尼斯-TUNIS',
    code: 'TNTNS'
  },
  {
    txt: 'TONEI-内亚富-NEIAFU',
    code: 'TONEI'
  },
  {
    txt: 'TONOI-诺穆卡岛-NOMUKA ISLAND',
    code: 'TONOI'
  },
  {
    txt: 'TONUK-努库阿洛法-NUKUALOFA',
    code: 'TONUK'
  },
  {
    txt: 'TOPAN-庞艾-PANGAI',
    code: 'TOPAN'
  },
  {
    txt: 'TPDIL-帝力-DILI',
    code: 'TPDIL'
  },
  {
    txt: 'TRALA-阿拉尼亚-ALANYA',
    code: 'TRALA'
  },
  {
    txt: 'TRALI-阿利亚加-ALIAGA',
    code: 'TRALI'
  },
  {
    txt: 'TRAMA-阿马斯腊-AMASRA',
    code: 'TRAMA'
  },
  {
    txt: 'TRANT-安塔利亚-ANTALYA',
    code: 'TRANT'
  },
  {
    txt: 'TRAYV-艾瓦勒克-AYVALIK',
    code: 'TRAYV'
  },
  {
    txt: 'TRBAN-班德尔马-BANDIRMA',
    code: 'TRBAN'
  },
  {
    txt: 'TRCAN-恰纳卡莱-CANAKKALE',
    code: 'TRCAN'
  },
  {
    txt: 'TRCID-吉代-CIDE',
    code: 'TRCID'
  },
  {
    txt: 'TRDIK-迪基利-DIKILI',
    code: 'TRDIK'
  },
  {
    txt: 'TRDOR-德尔特约尔-DORTYOL',
    code: 'TRDOR'
  },
  {
    txt: 'TREDI-埃丁吉克-EDINCIK',
    code: 'TREDI'
  },
  {
    txt: 'TRFAT-法特萨-FATSA',
    code: 'TRFAT'
  },
  {
    txt: 'TRFET-费特希耶-FETHIYE',
    code: 'TRFET'
  },
  {
    txt: 'TRFIN-菲尼凯-FINIKE',
    code: 'TRFIN'
  },
  {
    txt: 'TRGEL-盖利博卢-GELIBOLU',
    code: 'TRGEL'
  },
  {
    txt: 'TRGEM-盖姆利克-GEMLIK',
    code: 'TRGEM'
  },
  {
    txt: 'TRGIR-古雷松-GIRESUN',
    code: 'TRGIR'
  },
  {
    txt: 'TRGOL-格尔居克-GOLCUK',
    code: 'TRGOL'
  },
  {
    txt: 'TRGOR-格雷莱-GORELE',
    code: 'TRGOR'
  },
  {
    txt: 'TRHAY-海达尔帕夏-HAYDARPASA',
    code: 'TRHAY'
  },
  {
    txt: 'TRHER-海雷凯-HEREKE',
    code: 'TRHER'
  },
  {
    txt: 'TRHOP-霍帕-HOPA',
    code: 'TRHOP'
  },
  {
    txt: 'TRISK-伊斯肯德伦-ISKENDERUN',
    code: 'TRISK'
  },
  {
    txt: 'TRIST-伊斯坦布尔-ISTANBUL',
    code: 'TRIST'
  },
  {
    txt: 'TRIZM-伊兹密尔-IZMIR',
    code: 'TRIZM'
  },
  {
    txt: 'TRIZT-伊兹米特-IZMIT',
    code: 'TRIZT'
  },
  {
    txt: 'TRMAR-马尔马里斯-MARMARIS',
    code: 'TRMAR'
  },
  {
    txt: 'TRMER-梅尔辛-MERSIN',
    code: 'TRMER'
  },
  {
    txt: 'TRMUD-穆达尼亚-MUDANYA',
    code: 'TRMUD'
  },
  {
    txt: 'TRRIZ-里泽-RIZE',
    code: 'TRRIZ'
  },
  {
    txt: 'TRSAM-萨姆松-SAMSUN',
    code: 'TRSAM'
  },
  {
    txt: 'TRSNP-锡诺普-SINOP',
    code: 'TRSNP'
  },
  {
    txt: 'TRTEK-泰基尔达-TEKIRDAG',
    code: 'TRTEK'
  },
  {
    txt: 'TRTIR-蒂雷博卢-TIREBOLU',
    code: 'TRTIR'
  },
  {
    txt: 'TRTRA-特拉布宗-FRABZON',
    code: 'TRTRA'
  },
  {
    txt: 'TRUNY-云耶-UNYE',
    code: 'TRUNY'
  },
  {
    txt: 'TRUSK-于斯屈达尔-USKUDAR',
    code: 'TRUSK'
  },
  {
    txt: 'TRYAR-亚勒姆贾-YARIMCA',
    code: 'TRYAR'
  },
  {
    txt: 'TRZON-宗古尔达克-ZONGULDAK',
    code: 'TRZON'
  },
  {
    txt: 'TTBRI-布赖顿-BRIGHTON',
    code: 'TTBRI'
  },
  {
    txt: 'TTGAP-加莱奥塔角-GALEOTA POINT',
    code: 'TTGAP'
  },
  {
    txt: 'TTPAP-皮埃尔角城-POINTEA PIERRE',
    code: 'TTPAP'
  },
  {
    txt: 'TTPFN-福廷角-POINT FORTIN',
    code: 'TTPFN'
  },
  {
    txt: 'TTPLS-利萨斯角-POINT LISAS',
    code: 'TTPLS'
  },
  {
    txt: 'TTPOS-西班牙港-PORTOF SPAIN',
    code: 'TTPOS'
  },
  {
    txt: 'TTSCA-斯卡伯勒-SCARBOROUGH',
    code: 'TTSCA'
  },
  {
    txt: 'TTSFE-圣费尔南多-SAN FERNANDO',
    code: 'TTSFE'
  },
  {
    txt: 'TTTEM-滕布拉多腊-TEMBLADORA',
    code: 'TTTEM'
  },
  {
    txt: 'TVFUI-富纳富提-FUNAFUTI ISLAND',
    code: 'TVFUI'
  },
  {
    txt: 'TRINE-伊内博卢-INEBOLU',
    code: 'TRINE'
  },
  {
    txt: 'TRKUS-库沙达瑟-KUSADASI',
    code: 'TRKUS'
  },
  {
    txt: 'TRORD-奥尔杜-ORDU',
    code: 'TRORD'
  },
  {
    txt: 'TRTAS-塔舒朱-TASUCU',
    code: 'TRTAS'
  },
  {
    txt: 'TRTUT-图吞西夫特利克-TUTUNCIFTLIK',
    code: 'TRTUT'
  },
  {
    txt: 'TTCHA-查瓜拉马斯-CHAGUARAMAS',
    code: 'TTCHA'
  },
  {
    txt: 'TTPLY-普利茅斯-PLYMOUTH',
    code: 'TTPLY'
  },
  {
    txt: 'TWGAO-高雄-KAOHSIUNG',
    code: 'TWGAO'
  },
  {
    txt: 'ITVIN-ITVIN-ITVIN',
    code: 'ITVIN'
  },
  {
    txt: 'GN001-GN001-GN001',
    code: 'GN001'
  },
  {
    txt: 'FRTOU-土伦-TOULON ',
    code: 'FRTOU'
  },
  {
    txt: 'FRVER-韦尔东-VERDON ',
    code: 'FRVER'
  },
  {
    txt: 'GBABR-阿伯道尔-ABERDOUR ',
    code: 'GBABR'
  },
  {
    txt: 'GBANG-安纳隆-ANNALONG ',
    code: 'GBANG'
  },
  {
    txt: 'GBCOQ-康纳斯基-CONNAH’S QUAY ',
    code: 'GBCOQ'
  },
  {
    txt: 'GBDBR-邓巴-DUNBAR ',
    code: 'GBDBR'
  },
  {
    txt: 'GBDOV-多佛-DOVER ',
    code: 'GBDOV'
  },
  {
    txt: 'GBEXE-埃克塞特-EXETER ',
    code: 'GBEXE'
  },
  {
    txt: 'GBFEL-弗利克斯托-FELIXSTOWE ',
    code: 'GBFEL'
  },
  {
    txt: 'GBFRA-弗雷泽堡-FRASERBURGH ',
    code: 'GBFRA'
  },
  {
    txt: 'ANORA-奥拉涅斯塔德-ORANJESTAD',
    code: 'ANORA'
  },
  {
    txt: 'ANWIL-威廉斯塔德-WILLEMSTA',
    code: 'ANWIL'
  },
  {
    txt: 'AOLOB-洛比托-LOBITO',
    code: 'AOLOB'
  },
  {
    txt: 'AOPAL-亚历山大港-PORTO ALEXANDRE',
    code: 'AOPAL'
  },
  {
    txt: 'ARCAM-坎帕纳-CAMPANA',
    code: 'ARCAM'
  },
  {
    txt: 'ARIBI-伊比奎-IBICUY',
    code: 'ARIBI'
  },
  {
    txt: 'ARPAC-阿塞维多港-PUERTO ACEVEDO',
    code: 'ARPAC'
  },
  {
    txt: 'ARQUE-克肯-QUEQUEN',
    code: 'ARQUE'
  },
  {
    txt: 'ARRGA-里奥加耶戈斯-RIO GALLEGOS',
    code: 'ARRGA'
  },
  {
    txt: 'ARSFO-圣弗尔南多-SAN FERNANDO',
    code: 'ARSFO'
  },
  {
    txt: 'AUHBT-霍巴特-HOBART',
    code: 'AUHBT'
  },
  {
    txt: 'AUKWI-奎纳纳-KEINANA',
    code: 'AUKWI'
  },
  {
    txt: 'AUMAR-马里伯勒-MARYBOROUGH',
    code: 'AUMAR'
  },
  {
    txt: 'AUPAA-阿尔马港-PORT ALMA',
    code: 'AUPAA'
  },
  {
    txt: 'AUPGI-吉利港-PORT GILES',
    code: 'AUPGI'
  },
  {
    txt: 'AUPKE-肯布拉港-PORT KEMBLA',
    code: 'AUPKE'
  },
  {
    txt: 'AUPPI-皮里港-PORT PIRIE',
    code: 'AUPPI'
  },
  {
    txt: 'AURIS-里司登-RISDON',
    code: 'AURIS'
  },
  {
    txt: 'AUSTB-斯坦豪斯湾-STENHOUSE BAY',
    code: 'AUSTB'
  },
  {
    txt: 'AUTVD-泰弗纳德-THEVENARD ',
    code: 'AUTVD'
  },
  {
    txt: 'AUWDM-温德姆-WYNDHAM',
    code: 'AUWDM'
  },
  {
    txt: 'AUYBA-杨巴-YAMBA ',
    code: 'AUYBA'
  },
  {
    txt: 'BRNIT-尼泰罗伊-NITEROI ',
    code: 'BRNIT'
  },
  {
    txt: 'BRPEL-佩洛塔斯-PELOTAS ',
    code: 'BRPEL'
  },
  {
    txt: 'BRSAL-萨尔瓦多-SALVADOR ',
    code: 'BRSAL'
  },
  {
    txt: 'BRSPA-圣保罗-SAO PAULO',
    code: 'BRSPA'
  },
  {
    txt: 'BRTRA-特拉曼达伊-TRAMANDAI ',
    code: 'BRTRA'
  },
  {
    txt: 'BYMIN-明斯克-MINSKAJA',
    code: 'BYMIN'
  },
  {
    txt: 'CAAMH -阿默斯特堡-AMHERSTBURG ',
    code: 'CAAMH '
  },
  {
    txt: 'CAAST-阿默斯特-AMHERST ',
    code: 'CAAST'
  },
  {
    txt: 'CABDK -巴德克-BADDECK ',
    code: 'CABDK '
  },
  {
    txt: 'CABRI -布里奇沃特-BRIDGEWATER ',
    code: 'CABRI '
  },
  {
    txt: 'CABUR-布林-BURING ',
    code: 'CABUR'
  },
  {
    txt: 'CACAM-坎贝尔顿-CAMPBELLTON ',
    code: 'CACAM'
  },
  {
    txt: 'CACHA-夏洛特敦-CHARLOTTETOWN ',
    code: 'CACHA'
  },
  {
    txt: 'CALEA-利明顿-LEAMINGTON ',
    code: 'CALEA'
  },
  {
    txt: 'CALNA-小窄峡-LITTLE NARROWS ',
    code: 'CALNA'
  },
  {
    txt: 'CALUN-卢嫩堡-LUNENBURG ',
    code: 'CALUN'
  },
  {
    txt: 'CAMID-米德兰-MIDLAND (ONT.)',
    code: 'CAMID'
  },
  {
    txt: 'CANAN-纳尼斯维克-NANISIVK ',
    code: 'CANAN'
  },
  {
    txt: 'CANRI-新里士满-NEW RICHMOND ',
    code: 'CANRI'
  },
  {
    txt: 'CAOSD-欧文桑德-OWEN SOUND ',
    code: 'CAOSD'
  },
  {
    txt: 'CAPAD-艾尔夫雷德港-PORT ALFRED ',
    code: 'CAPAD'
  },
  {
    txt: 'PRSJ-圣胡安-SAN JUAN',
    code: 'PRSJ'
  },
  {
    txt: 'CNRZ-戎庄-RONG ZHUANG',
    code: 'CNRZ'
  },
  {
    txt: 'INLUDH-null-ICD LUDHIANA',
    code: 'INLUDH'
  },
  {
    txt: 'RUBENE-巴尔瑙尔-null',
    code: 'RUBENE'
  },
  {
    txt: 'CIABD-阿比让-ABIDJAN',
    code: 'CIABD'
  },
  {
    txt: 'CUBHB-哈瓦那-HABANA',
    code: 'CUBHB'
  },
  {
    txt: 'CULH-哈瓦那-LA HABANA',
    code: 'CULH'
  },
  {
    txt: 'DEMU-慕尼黑-MUNICH',
    code: 'DEMU'
  },
  {
    txt: 'TDNDJ-恩贾梅纳-NDJAMENA',
    code: 'TDNDJ'
  },
  {
    txt: 'PKISL-伊斯兰堡-ISLAMAB',
    code: 'PKISL'
  },
  {
    txt: 'SARIY-利雅得-RIYADH',
    code: 'SARIY'
  },
  {
    txt: 'CNWCN- 文昌-WENCHANG',
    code: 'CNWCN'
  },
  {
    txt: 'ARYER-埃里温-YEREVAN',
    code: 'ARYER'
  },
  {
    txt: 'BHBAH-巴林-BAHRAIN',
    code: 'BHBAH'
  },
  {
    txt: 'CYLIM-利马索尔- Limassol',
    code: 'CYLIM'
  },
  {
    txt: 'CNYJZ-于家庄-YUJIAZHUANG',
    code: 'CNYJZ'
  },
  {
    txt: 'CNNY-南阳-NANYANG',
    code: 'CNNY'
  },
  {
    txt: 'CNLZ-泸州-LU ZHOU',
    code: 'CNLZ'
  },
  {
    txt: 'CNGY-广元-GUANG YUAN',
    code: 'CNGY'
  },
  {
    txt: 'CNBSE-百色-BAISE',
    code: 'CNBSE'
  },
  {
    txt: 'CNSUZ-宿州-SUZHOU',
    code: 'CNSUZ'
  },
  {
    txt: 'BOLP-拉巴斯-LA PAZ',
    code: 'BOLP'
  },
  {
    txt: 'BSNA-拿骚-Nassau',
    code: 'BSNA'
  },
  {
    txt: 'IQERB-埃尔比勒-Erbil',
    code: 'IQERB'
  },
  {
    txt: 'TMASH-阿什哈巴德-ASHGABAT',
    code: 'TMASH'
  },
  {
    txt: 'AZBAK-巴库-BAKU',
    code: 'AZBAK'
  },
  {
    txt: 'CNMC-满城-MANCHENG',
    code: 'CNMC'
  },
  {
    txt: 'USVAN-温哥华-VANCOUVER',
    code: 'USVAN'
  },
  {
    txt: 'USWMN-威尔明顿-WILMINGTON(DEL．)',
    code: 'USWMN'
  },
  {
    txt: 'UYFBS-弗赖本托斯-FRAY BENTOS',
    code: 'UYFBS'
  },
  {
    txt: 'UYPAY-派桑杜-PAYSANDU',
    code: 'UYPAY'
  },
  {
    txt: 'VCGEO-乔治敦-GEORGETOWN',
    code: 'VCGEO'
  },
  {
    txt: 'VEBJG-巴霍格兰德-BAJO GRANDE',
    code: 'VEBJG'
  },
  {
    txt: 'VECNO-卡鲁帕诺-CARUPANO',
    code: 'VECNO'
  },
  {
    txt: 'VEEGU-埃尔瓜马切-EL GUAMACHE',
    code: 'VEEGU'
  },
  {
    txt: 'VEGUI-圭里亚-GUIRIA',
    code: 'VEGUI'
  },
  {
    txt: 'VEMAR-马拉开波-MARACAIBO',
    code: 'VEMAR'
  },
  {
    txt: 'SAJUT-朱阿马码头-JUAYMAH TERMINAL',
    code: 'SAJUT'
  },
  {
    txt: 'SAYBO-延布-YENBO',
    code: 'SAYBO'
  },
  {
    txt: 'SBNOR-诺鲁-NORO',
    code: 'SBNOR'
  },
  {
    txt: 'SBYAN-扬迪纳-YANDINA',
    code: 'SBYAN'
  },
  {
    txt: 'SDSUA-萨瓦金-SUAKIN',
    code: 'SDSUA'
  },
  {
    txt: 'SEBOR-博里霍尔姆-BORGHOLM',
    code: 'SEBOR'
  },
  {
    txt: 'SEDOM-杜姆舍-DOMSJO',
    code: 'SEDOM'
  },
  {
    txt: 'SEFAR-福勒松德-FAROSUND',
    code: 'SEFAR'
  },
  {
    txt: 'SEHAP-哈帕兰达-HAPARANDA',
    code: 'SEHAP'
  },
  {
    txt: 'SEHOG-赫加奈斯-HOGANAS',
    code: 'SEHOG'
  },
  {
    txt: 'SEHSM-胡苏姆-HUSUM',
    code: 'SEHSM'
  },
  {
    txt: 'SEJON-延雪平-JONKOPING',
    code: 'SEJON'
  },
  {
    txt: 'SEKAL-卡尔马-KALMAR',
    code: 'SEKAL'
  },
  {
    txt: 'SEKLI-克林特港-KLINTEHAMN',
    code: 'SEKLI'
  },
  {
    txt: 'SESTS-斯图龙恩斯-STORUGNS',
    code: 'SESTS'
  },
  {
    txt: 'SETOR-特勒-TORE',
    code: 'SETOR'
  },
  {
    txt: 'SETUN-突亚多尔-TUNADAL',
    code: 'SETUN'
  },
  {
    txt: 'SEVAL-瓦尔德马什维克-VALDEMARSVIK',
    code: 'SEVAL'
  },
  {
    txt: 'SEWES-韦斯特维克-WESTERVIK',
    code: 'SEWES'
  },
  {
    txt: 'SGPBU-普劳布科姆-PULAU BUKOM',
    code: 'SGPBU'
  },
  {
    txt: 'SHJAM-詹姆斯敦-JAMESTOWN',
    code: 'SHJAM'
  },
  {
    txt: 'SLFRE-弗里敦-FREETOWN',
    code: 'SLFRE'
  },
  {
    txt: 'SNZIG-济金绍尔-ZIGHINKOR',
    code: 'SNZIG'
  },
  {
    txt: 'SOBRA-布拉瓦-BRAVA',
    code: 'SOBRA'
  },
  {
    txt: 'SOMOG-摩加迪沙-MOGADISCIO',
    code: 'SOMOG'
  },
  {
    txt: 'SRSMA-斯马卡尔登-SMALKALDEN',
    code: 'SRSMA'
  },
  {
    txt: 'SVACA-阿卡胡特拉-ACAJUTLA',
    code: 'SVACA'
  },
  {
    txt: 'TRDER-代林杰-DERINCE',
    code: 'TRDER'
  },
  {
    txt: 'TRERE-埃雷利-EREGLI',
    code: 'TRERE'
  },
  {
    txt: 'TRGEB-盖布泽-GEBZE',
    code: 'TRGEB'
  },
  {
    txt: 'TRGOC-戈西克-GOCEK',
    code: 'TRGOC'
  },
  {
    txt: 'TRGUL-居吕克-GULLUK',
    code: 'TRGUL'
  },
  {
    txt: 'SFSER-巴尔-BAR',
    code: 'SFSER'
  },
  {
    txt: 'CTPLE-普洛切-PLOCE',
    code: 'CTPLE'
  },
  {
    txt: 'BPGBR-哈博罗内-GABORONE',
    code: 'BPGBR'
  },
  {
    txt: 'USRMG-ROME, GA-ROME, GA',
    code: 'USRMG'
  },
  {
    txt: 'CNXINH-新晃-XINHUANG',
    code: 'CNXINH'
  },
  {
    txt: 'CNLH-绿化-LVHUA',
    code: 'CNLH'
  },
  {
    txt: 'CNZSH-钟山-ZHONGSHAN',
    code: 'CNZSH'
  },
  {
    txt: 'CNDT-大同-DATONG',
    code: 'CNDT'
  },
  {
    txt: 'RUVRN-沃罗涅什-VORONEZH',
    code: 'RUVRN'
  },
  {
    txt: 'CTRJK-里耶卡-RIJEKA',
    code: 'CTRJK'
  },
  {
    txt: 'USNSV-纳什维尔-NASHVILLE',
    code: 'USNSV'
  },
  {
    txt: 'KRDA-瑞山-DAESAN PORT',
    code: 'KRDA'
  },
  {
    txt: 'ERAS-阿斯马拉-ASMARA',
    code: 'ERAS'
  },
  {
    txt: 'BASL-萨拉热窝-SALAJEVO',
    code: 'BASL'
  },
  {
    txt: 'AFKB-喀布尔-KABUL',
    code: 'AFKB'
  },
  {
    txt: 'MNEPD-波德戈里察-PODGORICA',
    code: 'MNEPD'
  },
  {
    txt: 'KIGALI-null-KIGALI',
    code: 'KIGALI'
  },
  {
    txt: 'CDMAT-金沙萨-KINSHASA',
    code: 'CDMAT'
  },
  {
    txt: 'CDLUB-卢本巴希-Lubumbashi',
    code: 'CDLUB'
  },
  {
    txt: 'JOAMM-安曼-Amman',
    code: 'JOAMM'
  },
  {
    txt: 'BIBUJ-布琼布拉-Bujumbura',
    code: 'BIBUJ'
  },
  {
    txt: 'INKOL-加尔各答-KOLKATA',
    code: 'INKOL'
  },
  {
    txt: 'CMYAO-雅温得-yaounde',
    code: 'CMYAO'
  },
  {
    txt: 'FJNAD-楠迪-NADI',
    code: 'FJNAD'
  },
  {
    txt: 'GBBROD-布拉德福德-BRADEORD',
    code: 'GBBROD'
  },
  {
    txt: 'ITBL-博洛尼亚-BOLOGNA',
    code: 'ITBL'
  },
  {
    txt: 'CLCLD-卡尔德拉-CALDERA',
    code: 'CLCLD'
  },
  {
    txt: 'ETAD-亚的斯亚贝巴-ADDIS ABABA',
    code: 'ETAD'
  },
  {
    txt: 'SSDJU-朱巴-JUBA',
    code: 'SSDJU'
  },
  {
    txt: 'IQBAG-巴格达-Baghdad',
    code: 'IQBAG'
  },
  {
    txt: 'CFBAN-班基-Bangui',
    code: 'CFBAN'
  },
  {
    txt: 'KZALM-阿拉梅金-ALMEDIN',
    code: 'KZALM'
  },
  {
    txt: 'ERASM-阿斯玛拉-ASMARA',
    code: 'ERASM'
  },
  {
    txt: 'LSMAS-马塞鲁-MASERU',
    code: 'LSMAS'
  },
  {
    txt: 'BIHBA-巴尼亚卢卡-BANJA LUKA',
    code: 'BIHBA'
  },
  {
    txt: 'AGANU-安提瓜-ANTIGUA',
    code: 'AGANU'
  },
  {
    txt: 'BBBBC-巴巴多斯-BARBADOS',
    code: 'BBBBC'
  },
  {
    txt: 'BNBWN-斯里巴加湾市-BANDAR SERI BEGAWAN',
    code: 'BNBWN'
  },
  {
    txt: 'IQUIQUE-伊基克-IQUIQUE',
    code: 'IQUIQUE'
  },
  {
    txt: 'CUHAV-哈瓦那-HAVANA',
    code: 'CUHAV'
  },
  {
    txt: 'DMDMC-多米尼加-DOMINICA',
    code: 'DMDMC'
  },
  {
    txt: 'DOSDQ-圣多明各-SANTO DOMINGO',
    code: 'DOSDQ'
  },
  {
    txt: 'GBSUL-萨洛姆湾-SULLOM VOE',
    code: 'GBSUL'
  },
  {
    txt: 'GDGDC-格林纳达-GRENADA',
    code: 'GDGDC'
  },
  {
    txt: 'MTMTC-马耳他-MALTA',
    code: 'MTMTC'
  },
  {
    txt: 'MUMUC-毛里求斯-MAURITIUS',
    code: 'MUMUC'
  },
  {
    txt: 'MXGDL-瓜达拉哈拉-GUADALAJARA',
    code: 'MXGDL'
  },
  {
    txt: 'NLCRC-库拉索岛-CURACAO',
    code: 'NLCRC'
  },
  {
    txt: 'REREC-留尼汪岛-REUNION',
    code: 'REREC'
  },
  {
    txt: 'SCSCC-塞舌尔-SEYCHELLES',
    code: 'SCSCC'
  },
  {
    txt: 'USANZ-安提阿-ANTIOCH',
    code: 'USANZ'
  },
  {
    txt: 'ACLPA-拉斯帕尔马斯-LAS PALMAS DE GRAN CANARIA',
    code: 'ACLPA'
  },
  {
    txt: 'DEDRE-德累斯顿-DRESDEN',
    code: 'DEDRE'
  },
  {
    txt: 'DELEI-莱比锡-LEIPZIG',
    code: 'DELEI'
  },
  {
    txt: 'WIN-马尼托巴省-WINNIPEG',
    code: 'WIN'
  },
  {
    txt: 'ASPPG-帕果帕果-PAGO POGO',
    code: 'ASPPG'
  },
  {
    txt: 'GAPOG-让蒂尔港-PORT GENTIL',
    code: 'GAPOG'
  },
  {
    txt: 'KICXI-圣诞岛-CHRISTMAS ISLAND',
    code: 'KICXI'
  },
  {
    txt: 'TOTOC-汤加-TONGA',
    code: 'TOTOC'
  },
  {
    txt: 'VUVLI-维拉港-PORT VILA',
    code: 'VUVLI'
  },
  {
    txt: 'BGSOF-索非亚-SOFIA',
    code: 'BGSOF'
  },
  {
    txt: 'BMHAM-百慕大-BERMUDA',
    code: 'BMHAM'
  },
  {
    txt: 'KYGRA-大开曼-GRAND CAYMAN',
    code: 'KYGRA'
  },
  {
    txt: 'MXMTY-蒙特雷-MONTERREY',
    code: 'MXMTY'
  },
  {
    txt: 'NZRAI-拉罗通加-RAROTONGA',
    code: 'NZRAI'
  },
  {
    txt: 'USPHO-菲尼克斯-PHOENIX',
    code: 'USPHO'
  },
  {
    txt: 'CNWGT-文官屯-WENGUATUN',
    code: 'CNWGT'
  },
  {
    txt: 'CNCL-茶陵-CHALING',
    code: 'CNCL'
  },
  {
    txt: 'CNCH-慈湖-CIHU',
    code: 'CNCH'
  },
  {
    txt: 'CNXH-新化-XINHUA',
    code: 'CNXH'
  },
  {
    txt: 'CNXP-溆浦-XUPU',
    code: 'CNXP'
  },
  {
    txt: 'CNYP-羊坪-YANGPING',
    code: 'CNYP'
  },
  {
    txt: 'VEGAL-古阿兰奥-GUARANAO',
    code: 'VEGAL'
  },
  {
    txt: 'TWTY-桃园-TAOYUAN',
    code: 'TWTY'
  },
  {
    txt: 'MXTL-托卢卡-TOLUCA',
    code: 'MXTL'
  },
  {
    txt: 'USSP-塞班岛-SAIPAN ISLAND',
    code: 'USSP'
  },
  {
    txt: 'CAARG-阿真舍-ARGENTIA',
    code: 'CAARG'
  },
  {
    txt: 'GQMAL-马拉博-MALABO',
    code: 'GQMAL'
  },
  {
    txt: 'INDEL-德里-DELHI',
    code: 'INDEL'
  },
  {
    txt: 'UGENT-恩德培-ENTEBBE',
    code: 'UGENT'
  },
  {
    txt: 'MUMAU-毛里求斯-MAURITI',
    code: 'MUMAU'
  },
  {
    txt: 'MWBLA-布兰太尔-Blantyre',
    code: 'MWBLA'
  },
  {
    txt: 'MKSKO-斯科普里-Skopje',
    code: 'MKSKO'
  },
  {
    txt: 'CNLXAP- 临夏回族自治州-Linxia Hui Autonomous Prefecture',
    code: 'CNLXAP'
  },
  {
    txt: 'GTGUA-危地马拉城-GUATEMALA CITY',
    code: 'GTGUA'
  },
  {
    txt: 'CNJING-金港-jingang',
    code: 'CNJING'
  },
  {
    txt: 'BRNVGS-纳维根特斯-NAVEGANTES',
    code: 'BRNVGS'
  },
  {
    txt: 'CNYB-宜宾-YI BIN',
    code: 'CNYB'
  },
  {
    txt: 'CNYA-雅安-YA AN',
    code: 'CNYA'
  },
  {
    txt: 'CNQX-渠县-QUXIAN',
    code: 'CNQX'
  },
  {
    txt: 'CNXJ-新疆-XINJIANG',
    code: 'CNXJ'
  },
  {
    txt: 'CNCD-常德-CHANGDE',
    code: 'CNCD'
  },
  {
    txt: 'ZWHR-哈拉雷-HARARE',
    code: 'ZWHR'
  },
  {
    txt: 'GQBM-null-BATA & MALAMBO',
    code: 'GQBM'
  },
  {
    txt: 'CDGOM-戈马-GOMA',
    code: 'CDGOM'
  },
  {
    txt: 'SDKHA-喀土穆-KHARTOUM',
    code: 'SDKHA'
  },
  {
    txt: 'INCHE-金奈-CHENNAI',
    code: 'INCHE'
  },
  {
    txt: 'ITROM-罗马-ROME',
    code: 'ITROM'
  },
  {
    txt: 'UABPS-博帕斯纳亚-bopasnaya',
    code: 'UABPS'
  },
  {
    txt: 'CNDZH-达州-DAZHOU',
    code: 'CNDZH'
  },
  {
    txt: 'CNZJJ-张家界-ZHANGJIAJIE',
    code: 'CNZJJ'
  },
  {
    txt: 'CNXS-秀山-XIUSHAN',
    code: 'CNXS'
  },
  {
    txt: 'CNJS-吉首-JISHOU',
    code: 'CNJS'
  },
  {
    txt: 'CNWL-网岭-WANGLING',
    code: 'CNWL'
  },
  {
    txt: 'CNCGY-陈官营-CHENGUANYING',
    code: 'CNCGY'
  },
  {
    txt: 'CNKL-凯里-KAILI',
    code: 'CNKL'
  },
  {
    txt: 'CNLTS-龙潭寺-LONGTANSI',
    code: 'CNLTS'
  },
  {
    txt: 'CNTHZ-天回镇-TIANHUIZHEN',
    code: 'CNTHZ'
  },
  {
    txt: 'CNHJW-何家湾-HEJIAWAN',
    code: 'CNHJW'
  },
  {
    txt: 'CNLY-龙岩-LONGYAN',
    code: 'CNLY'
  },
  {
    txt: 'USPM-帕姆代尔-PALMDALE',
    code: 'USPM'
  },
  {
    txt: 'GQBAT-巴塔-BATA',
    code: 'GQBAT'
  },
  {
    txt: 'SZMAN-曼齐尼-Manzini',
    code: 'SZMAN'
  },
  {
    txt: 'MWLIL-利隆圭-LILONGWE',
    code: 'MWLIL'
  },
  {
    txt: 'MGMAD-马达加斯加-Madagascar',
    code: 'MGMAD'
  },
  {
    txt: 'AFKAB-喀布尔-KABUL',
    code: 'AFKAB'
  },
  {
    txt: 'AESHA-沙迦-SHARJAH',
    code: 'AESHA'
  },
  {
    txt: 'KGBIS-比什凯克-Bishkek',
    code: 'KGBIS'
  },
  {
    txt: 'MNEPO-波德戈里察- Podgorica',
    code: 'MNEPO'
  },
  {
    txt: 'CNATS- 阿图什-ATUSHI',
    code: 'CNATS'
  },
  {
    txt: 'ETMAS-马萨瓦-MASSAWA',
    code: 'ETMAS'
  },
  {
    txt: 'CNXF-香坊-XIANGFANG',
    code: 'CNXF'
  },
  {
    txt: 'ITGAL-加利波利-GALLIPOLI',
    code: 'ITGAL'
  },
  {
    txt: 'ITGEL-杰拉-GELA',
    code: 'ITGEL'
  },
  {
    txt: 'ITGOA-热那亚-GENOA',
    code: 'ITGOA'
  },
  {
    txt: 'ITIMP-因佩里亚-IMPERIA',
    code: 'ITIMP'
  },
  {
    txt: 'ITLAS-拉斯佩齐亚-LA SPEZIA',
    code: 'ITLAS'
  },
  {
    txt: 'ITLEG-里窝那-LEGHORN',
    code: 'ITLEG'
  },
  {
    txt: 'ITLIC-利卡塔-LICATA',
    code: 'ITLIC'
  },
  {
    txt: 'ITLMA-拉马达莱那-LA MADDALENA',
    code: 'ITLMA'
  },
  {
    txt: 'ITMAN-曼夫雷多尼亚-MANFREDONIA',
    code: 'ITMAN'
  },
  {
    txt: 'ITMAR-马尔萨拉-MARSALA',
    code: 'ITMAR'
  },
  {
    txt: 'ITMDC-马里纳迪卡拉拉-MARINADI CARRARA',
    code: 'ITMDC'
  },
  {
    txt: 'ITMDV-马扎拉德尔瓦洛-MAZARADEL VALLO',
    code: 'ITMDV'
  },
  {
    txt: 'ITMEL-梅利利-MELILLI',
    code: 'ITMEL'
  },
  {
    txt: 'ITMES-墨西拿-MESSINA',
    code: 'ITMES'
  },
  {
    txt: 'ITMIL-米拉佐-MILAZZO',
    code: 'ITMIL'
  },
  {
    txt: 'ITMLI-莫诺波利-MONOPOLI',
    code: 'ITMLI'
  },
  {
    txt: 'ITMLN-米兰-MILANO',
    code: 'ITMLN'
  },
  {
    txt: 'ITMOL-莫尔费塔-MOLFETTA',
    code: 'ITMOL'
  },
  {
    txt: 'ITMON-蒙法尔科内-MOFALCONE',
    code: 'ITMON'
  },
  {
    txt: 'ITNAP-那不勒斯-NAPLES',
    code: 'ITNAP'
  },
  {
    txt: 'ITOLB-奥尔比亚-OLBIA',
    code: 'ITOLB'
  },
  {
    txt: 'ITORI-奥里斯塔诺-ORISTANO',
    code: 'ITORI'
  },
  {
    txt: 'ITORT-奥托纳-ORTONA',
    code: 'ITORT'
  },
  {
    txt: 'ITPAL-巴勒莫-PALERMO',
    code: 'ITPAL'
  },
  {
    txt: 'ITPAZ-阿祖罗港-PORTO AZZURRO',
    code: 'ITPAZ'
  },
  {
    txt: 'ITPES-佩斯卡拉-PESCARA',
    code: 'ITPES'
  },
  {
    txt: 'ITPFO-费拉约港-POTTOFERRAIO',
    code: 'ITPFO'
  },
  {
    txt: 'ITPGE-波蒂格里欧内-PORTIGLIONE',
    code: 'ITPGE'
  },
  {
    txt: 'ITPIO-皮翁比诺-PIOMBINO',
    code: 'ITPIO'
  },
  {
    txt: 'ITPMA-马尔盖腊港-PORTO MARGHERA',
    code: 'ITPMA'
  },
  {
    txt: 'ITPRI-普里奥洛-PRIOLO',
    code: 'ITPRI'
  },
  {
    txt: 'ITPRO-佩萨罗-PESARO',
    code: 'ITPRO'
  },
  {
    txt: 'ITPSO-斯库索港-PORTOSCUSO',
    code: 'ITPSO'
  },
  {
    txt: 'ITPTI-波蒂奇-PORTICI',
    code: 'ITPTI'
  },
  {
    txt: 'ITPTO-托雷斯港-PORTO TORRES',
    code: 'ITPTO'
  },
  {
    txt: 'ITPVE-韦斯梅港-POTTO VESME',
    code: 'ITPVE'
  },
  {
    txt: 'ITRAV-腊万纳-RAVENNA',
    code: 'ITRAV'
  },
  {
    txt: 'ITREG-雷焦-REGGIO',
    code: 'ITREG'
  },
  {
    txt: 'ITSAL-萨莱诺-SALERNO',
    code: 'ITSAL'
  },
  {
    txt: 'ITSAR-萨罗奇-SARROCH',
    code: 'ITSAR'
  },
  {
    txt: 'ITSAV-萨沃纳-SAVONA',
    code: 'ITSAV'
  },
  {
    txt: 'ITSIR-锡拉库萨-SIRACUSA',
    code: 'ITSIR'
  },
  {
    txt: 'ITSRE-圣雷莫-SAN REMO',
    code: 'ITSRE'
  },
  {
    txt: 'ITTAA-托雷安农齐亚塔-TORRE ANNUNZIATA',
    code: 'ITTAA'
  },
  {
    txt: 'ITTAL-塔拉莫内-TALAMONE',
    code: 'ITTAL'
  },
  {
    txt: 'ITTDG-托雷德尔格雷科-TORRE DEL GRECO',
    code: 'ITTDG'
  },
  {
    txt: 'ITTRA-特拉帕尼-TRAPANI',
    code: 'ITTRA'
  },
  {
    txt: 'ITTRI-的里雅斯特-TRIESTE',
    code: 'ITTRI'
  },
  {
    txt: 'ITVEN-威尼斯-VENICE',
    code: 'ITVEN'
  },
  {
    txt: 'ITVIA-维亚雷焦-VIAREGGIO',
    code: 'ITVIA'
  },
  {
    txt: 'JMALP-阿利盖德庞德-ALLIGATOR POND',
    code: 'JMALP'
  },
  {
    txt: 'JMBLU-布卢菲尔兹-B1UENELDS',
    code: 'JMBLU'
  },
  {
    txt: 'JMFAL-法尔茅斯-FALMOUTH',
    code: 'JMFAL'
  },
  {
    txt: 'JMKIN-金斯敦-KINGSTON',
    code: 'JMKIN'
  },
  {
    txt: 'JMMOB-蒙特歌湾-MONTEGO BAY',
    code: 'JMMOB'
  },
  {
    txt: 'JMORB-奥乔里奥斯湾-OCHO RIOS BAY',
    code: 'JMORB'
  },
  {
    txt: 'JMPES-埃斯基韦尔港-PORT ESQUIVEL',
    code: 'JMPES'
  },
  {
    txt: 'JMPKA-凯泽港-PORT KAISER',
    code: 'JMPKA'
  },
  {
    txt: 'JMPMO-莫兰特港-PORT MORANT',
    code: 'JMPMO'
  },
  {
    txt: 'JMPRH-罗德港-PORT RHOADES',
    code: 'JMPRH'
  },
  {
    txt: 'JMRBU-里奥布埃诺-RIO BUENO',
    code: 'JMRBU'
  },
  {
    txt: 'JMRCP-罗基波因特-ROCKY POINT',
    code: 'JMRCP'
  },
  {
    txt: 'JMSAR-萨尔特里弗-SALT RIVER',
    code: 'JMSAR'
  },
  {
    txt: 'INBOM-孟买-BOMBAY',
    code: 'INBOM'
  },
  {
    txt: 'INCAL-加尔格答-CALCUTTA',
    code: 'INCAL'
  },
  {
    txt: 'INCAM-格灵格伯德讷姆-CALINGAPATNAM',
    code: 'INCAM'
  },
  {
    txt: 'INCAN-坎纳诺尔-CANNANORE',
    code: 'INCAN'
  },
  {
    txt: 'INCCT-卡利卡特-CALICUT',
    code: 'INCCT'
  },
  {
    txt: 'INCHN-钦奈-CHENNAI',
    code: 'INCHN'
  },
  {
    txt: 'INCOC-科钦-COCHIN',
    code: 'INCOC'
  },
  {
    txt: 'INCOL-科拉歇尔-COLACHEL',
    code: 'INCOL'
  },
  {
    txt: 'INCOO-贡达布尔-COONDAPOOR',
    code: 'INCOO'
  },
  {
    txt: 'INCUD-库达洛尔-CUDDALORE',
    code: 'INCUD'
  },
  {
    txt: 'INDAM-达曼-DAMAN',
    code: 'INDAM'
  },
  {
    txt: 'INDIU-第乌-DIU',
    code: 'INDIU'
  },
  {
    txt: 'INDWA-杜瓦尔卡-DWARKA',
    code: 'INDWA'
  },
  {
    txt: 'INGOP-戈巴尔布尔-GOPALPUR',
    code: 'INGOP'
  },
  {
    txt: 'INHDA-霍尔迪亚-HALDIA',
    code: 'INHDA'
  },
  {
    txt: 'INHYD-海得拉巴-HYDERABAD',
    code: 'INHYD'
  },
  {
    txt: 'INJAF-加法拉巴德-JAFARABAD',
    code: 'INJAF'
  },
  {
    txt: 'INJAK-杰考-JAKHAU',
    code: 'INJAK'
  },
  {
    txt: 'INKAK-卡基纳达-KAKINADA',
    code: 'INKAK'
  },
  {
    txt: 'INKAN-根德拉-KANDLA',
    code: 'INKAN'
  },
  {
    txt: 'INKAR-加尔瓦尔-KARWAR',
    code: 'INKAR'
  },
  {
    txt: 'INKKL-加里加尔-KARIKAL',
    code: 'INKKL'
  },
  {
    txt: 'INMAC-默吉利伯德讷姆-MACHILIPATNAM',
    code: 'INMAC'
  },
  {
    txt: 'INMAD-马德拉斯-MADRAS',
    code: 'INMAD'
  },
  {
    txt: 'INMAH-马埃-MAHE',
    code: 'INMAH'
  },
  {
    txt: 'INMAL-马尔佩-MALPE',
    code: 'INMAL'
  },
  {
    txt: 'INMAN-芒格洛尔-MANGALORE',
    code: 'INMAN'
  },
  {
    txt: 'INMII-米尼科伊岛-MINICOY ISLAND',
    code: 'INMII'
  },
  {
    txt: 'INMOR-莫尔穆冈-MORMUGAO',
    code: 'INMOR'
  },
  {
    txt: 'INMPM-曼达帕姆-MANDAPAM',
    code: 'INMPM'
  },
  {
    txt: 'INMRL-曼格罗尔-MANGROL',
    code: 'INMRL'
  },
  {
    txt: 'INMUN-蒙德拉-MUNDRA',
    code: 'INMUN'
  },
  {
    txt: 'INMVI-曼德维-MANDVI',
    code: 'INMVI'
  },
  {
    txt: 'INNAG-纳加伯蒂讷姆-NAGAPATTINAM',
    code: 'INNAG'
  },
  {
    txt: 'INNAV-瑙勒基-NAVLAKHI',
    code: 'INNAV'
  },
  {
    txt: 'INNDE-新德里-NEW DELHI',
    code: 'INNDE'
  },
  {
    txt: 'INNMA-新芒格洛尔-NEW MANGALORE',
    code: 'INNMA'
  },
  {
    txt: 'INNTU-新土提科林-NEW TUTICORIN',
    code: 'INNTU'
  },
  {
    txt: 'INPAM-班本-PAMBAN',
    code: 'INPAM'
  },
  {
    txt: 'INPAR-巴拉迪布-PARADIP',
    code: 'INPAR'
  },
  {
    txt: 'INPBL-布莱尔港-PORT BLAIR',
    code: 'INPBL'
  },
  {
    txt: 'INPJM-潘吉姆-PANJIM',
    code: 'INPJM'
  },
  {
    txt: 'INPNO-波多诺伏港-PORTO NOVO',
    code: 'INPNO'
  },
  {
    txt: 'INPOK-奥卡港-PORT OKHA',
    code: 'INPOK'
  },
  {
    txt: 'INPON-本地治里-PONDICHERRY',
    code: 'INPON'
  },
  {
    txt: 'INPOR-博尔本碍尔-PORBANDAR',
    code: 'INPOR'
  },
  {
    txt: 'INQUI-奎隆-QUILON',
    code: 'INQUI'
  },
  {
    txt: 'INRAT-勒德纳吉里-RATNAGIRI',
    code: 'INRAT'
  },
  {
    txt: 'INRON-霍纳沃尔-HONAVAR',
    code: 'INRON'
  },
  {
    txt: 'INSAL-瑟拉亚-SALAYA',
    code: 'INSAL'
  },
  {
    txt: 'INSIK-锡卡-SIKKA',
    code: 'INSIK'
  },
  {
    txt: 'INTEL-代利杰里-TELLICNERRY',
    code: 'INTEL'
  },
  {
    txt: 'INTRI-特里凡得琅-TRIVANDRUM',
    code: 'INTRI'
  },
  {
    txt: 'INVEN-文古尔拉-VENGURIA',
    code: 'INVEN'
  },
  {
    txt: 'INVER-韦拉沃尔-VERAVAL',
    code: 'INVER'
  },
  {
    txt: 'INVIS-维沙卡帕特南-VISAKHAPATNAM',
    code: 'INVIS'
  },
  {
    txt: 'IQBAS-巴士拉-BASRAH',
    code: 'IQBAS'
  },
  {
    txt: 'IQKAA-豪尔艾迈耶-KHORAL AMAYA',
    code: 'IQKAA'
  },
  {
    txt: 'IQMAB-阿巴克-MINAAL BAKR',
    code: 'IQMAB'
  },
  {
    txt: 'IRABA-阿巴丹-ABADAN',
    code: 'IRABA'
  },
  {
    txt: 'IRBAB-阿巴斯港-BANDAR ABBAS',
    code: 'IRBAB'
  },
  {
    txt: 'IRBKH-霍梅尼港-BANDAR KHOMEINI',
    code: 'IRBKH'
  },
  {
    txt: 'IRBMA-马夏赫尔港-BANDAR MAHSHAHR',
    code: 'IRBMA'
  },
  {
    txt: 'IRCYT-居鲁士码头-CYRUS TERMINAL',
    code: 'IRCYT'
  },
  {
    txt: 'IRJSK-贾斯克-JASK',
    code: 'IRJSK'
  },
  {
    txt: 'IRKGI-哈尔克岛-KHARG ISLAND',
    code: 'IRKGI'
  },
  {
    txt: 'IRLIN-林格-LINGAH',
    code: 'IRLIN'
  },
  {
    txt: 'IRLVI-拉万岛-LAVAN ISLAND',
    code: 'IRLVI'
  },
  {
    txt: 'IRRBA-巴里根角-RAS BAHREGAN',
    code: 'IRRBA'
  },
  {
    txt: 'IRTEH-德黑兰-TEHRAN',
    code: 'IRTEH'
  },
  {
    txt: 'ISAKR-阿克拉内斯-AKRANES',
    code: 'ISAKR'
  },
  {
    txt: 'ISHAF-哈布纳菲厄泽-HAFNARFJORD',
    code: 'ISHAF'
  },
  {
    txt: 'ISHUS-胡萨维克-HUSAVIK',
    code: 'ISHUS'
  },
  {
    txt: 'ISISA-伊萨菲厄泽-ISAFJORD',
    code: 'ISISA'
  },
  {
    txt: 'ISNES-内斯克伊斯塔泽-NESKAUPSTADUR',
    code: 'ISNES'
  },
  {
    txt: 'ISPAT-帕特雷克峡湾-PATREKSFJORD',
    code: 'ISPAT'
  },
  {
    txt: 'ISREY-雷克雅未克-REYKJAVIK',
    code: 'ISREY'
  },
  {
    txt: 'ISSEY-塞济斯菲厄泽-SEYDISFJORD',
    code: 'ISSEY'
  },
  {
    txt: 'ISSKA-斯卡加斯特伦-SKAGASTROND',
    code: 'ISSKA'
  },
  {
    txt: 'ISSTR-斯特勒伊姆维克-STRAUMSVIK',
    code: 'ISSTR'
  },
  {
    txt: 'ISVSI-韦斯特曼纳岛-VESTMANN ISLANDS',
    code: 'ISVSI'
  },
  {
    txt: 'ITACE-阿瑟瑞尔-ACIREALE',
    code: 'ITACE'
  },
  {
    txt: 'ITANC-安科纳-ANCONA',
    code: 'ITANC'
  },
  {
    txt: 'ITANZ-安齐奥-ANZIO',
    code: 'ITANZ'
  },
  {
    txt: 'GBGLE-格莱纳姆-GLENARM ',
    code: 'GBGLE'
  },
  {
    txt: 'GBGLO-格洛斯特-GLOUCESTER ',
    code: 'GBGLO'
  },
  {
    txt: 'GBGLT-格兰顿-GLANTON',
    code: 'GBGLT'
  },
  {
    txt: 'GBGNK-格里诺克-GREENOCK ',
    code: 'GBGNK'
  },
  {
    txt: 'GBGNW-冈纳斯-GUNNESS ',
    code: 'GBGNW'
  },
  {
    txt: 'GBGOO-古尔-GOOLE ',
    code: 'GBGOO'
  },
  {
    txt: 'GBGRA-格兰奇茅斯-GRANGEMOUTH ',
    code: 'GBGRA'
  },
  {
    txt: 'GBGRI-格里姆斯比-GRIMST',
    code: 'GBGRI'
  },
  {
    txt: 'GBGRK-古罗克-GOUROCK ',
    code: 'GBGRK'
  },
  {
    txt: 'GBGSD-格雷夫森德-GRAVESEND ',
    code: 'GBGSD'
  },
  {
    txt: 'GBGYA-大雅茅斯-GREAT YARMOUTH ',
    code: 'GBGYA'
  },
  {
    txt: 'GBHAR-哈里奇-HARWICH ',
    code: 'GBHAR'
  },
  {
    txt: 'GBHAY-海尔-HAYLE ',
    code: 'GBHAY'
  },
  {
    txt: 'GBHEY-希舍姆-HEYSHAM ',
    code: 'GBHEY'
  },
  {
    txt: 'GBHOL-霍利黑德-HOLYHEAD ',
    code: 'GBHOL'
  },
  {
    txt: 'GBHOP-霍德角-HOUND POINT ',
    code: 'GBHOP'
  },
  {
    txt: 'GBHPL-哈特尔浦-HARTLEPOOL ',
    code: 'GBHPL'
  },
  {
    txt: 'GBHUD-哈德斯菲尔德-HUDDERSFIELD',
    code: 'GBHUD'
  },
  {
    txt: 'GBHUL-赫尔-HULL ',
    code: 'GBHUL'
  },
  {
    txt: 'GBHUN-亨特斯顿-HUNTERSTON ',
    code: 'GBHUN'
  },
  {
    txt: 'GBIMM-伊明赫姆-IMMINGHAM ',
    code: 'GBIMM'
  },
  {
    txt: 'GBINV-因弗戈登-INVERGORDON ',
    code: 'GBINV'
  },
  {
    txt: 'GBIOG-谷岛-ISLE OF GRAIN',
    code: 'GBIOG'
  },
  {
    txt: 'GBIPS-伊普斯威奇-IPSWICH ',
    code: 'GBIPS'
  },
  {
    txt: 'GBIRV-欧文-IRVINE ',
    code: 'GBIRV'
  },
  {
    txt: 'AOLUA-罗安达-LUANDA',
    code: 'AOLUA'
  },
  {
    txt: 'AOMOC-木萨米迪什-MOCARMEDES',
    code: 'AOMOC'
  },
  {
    txt: 'AONRE-新里东杜-NOVO REDONDO',
    code: 'AONRE'
  },
  {
    txt: 'AOPAM-安博因港-PORTO AMBOIM',
    code: 'AOPAM'
  },
  {
    txt: 'AOPSA-萨拉萨尔港-PORTO SALAZAR',
    code: 'AOPSA'
  },
  {
    txt: 'ARBBL-布兰卡港-BAHIA BLANCA',
    code: 'ARBBL'
  },
  {
    txt: 'ARBNA-布宜诺斯艾利斯-BUENOS AIRES',
    code: 'ARBNA'
  },
  {
    txt: 'ARCON-康塞普西翁-CONCEPCION',
    code: 'ARCON'
  },
  {
    txt: 'ARCOR-里瓦达维亚-COMODORO RIVADANIA',
    code: 'ARCOR'
  },
  {
    txt: 'ARDIA-迪亚曼泰-DIAMANTE',
    code: 'ARDIA'
  },
  {
    txt: 'ARLRL-拉普拉塔-LA PLATA',
    code: 'ARLRL'
  },
  {
    txt: 'ARMDP-马德普拉塔-MAR DEL PLATA',
    code: 'ARMDP'
  },
  {
    txt: 'ARNEC-内利切阿-NOCOCHEA',
    code: 'ARNEC'
  },
  {
    txt: 'ARPCO-科罗拉多角-PUNTA COLORADA',
    code: 'ARPCO'
  },
  {
    txt: 'ARPDE-德塞阿多港-PUERTO DESEADO',
    code: 'ARPDE'
  },
  {
    txt: 'ARPMA-马德林港-PUERTO MADRYN',
    code: 'ARPMA'
  },
  {
    txt: 'ARPQU-蓬塔基利亚-PUNTA  QUILLA',
    code: 'ARPQU'
  },
  {
    txt: 'ARRAM-拉马约-RAMALLO',
    code: 'ARRAM'
  },
  {
    txt: 'ARRCA-雷卡拉达-RECALADA',
    code: 'ARRCA'
  },
  {
    txt: 'ARRGR-里奥格兰德-RIO GRANDE',
    code: 'ARRGR'
  },
  {
    txt: 'ARROS-罗萨里奥-ROSARIO',
    code: 'ARROS'
  },
  {
    txt: 'ARSAE-圣安东尼奥-SAN ANTONIO ESTE',
    code: 'ARSAE'
  },
  {
    txt: 'ARSCR-圣克鲁斯-SANTA CRUZ (ARG.) ',
    code: 'ARSCR'
  },
  {
    txt: 'ARSFE-圣菲-SANTA FE',
    code: 'ARSFE'
  },
  {
    txt: 'ARSLO-圣洛伦索-SAN LORENZO',
    code: 'ARSLO'
  },
  {
    txt: 'ARSNS-圣尼古拉斯-SAN NICOLAS',
    code: 'ARSNS'
  },
  {
    txt: 'ARSPE-圣佩德罗-SANPEDRO',
    code: 'ARSPE'
  },
  {
    txt: 'ARSSB-圣塞瓦斯蒂安-SAN SEBASTIAN BAY',
    code: 'ARSSB'
  },
  {
    txt: 'ARUSH-乌斯怀亚-USHUAIA',
    code: 'ARUSH'
  },
  {
    txt: 'ARVCO-孔斯蒂图西翁镇-VILLA CONSTITUCION',
    code: 'ARVCO'
  },
  {
    txt: 'ARZAR-萨拉特-ZARATA',
    code: 'ARZAR'
  },
  {
    txt: 'ASPGO-帕果帕果-PAGO POGO',
    code: 'ASPGO'
  },
  {
    txt: 'ATJOF-萨尔茨堡-JOSEF',
    code: 'ATJOF'
  },
  {
    txt: 'ATVIE-维也纳-Vienna',
    code: 'ATVIE'
  },
  {
    txt: 'AUABB-阿伯特湾-ABBOT BAY ',
    code: 'AUABB'
  },
  {
    txt: 'AUADE-阿德莱德-ADELAIDE',
    code: 'AUADE'
  },
  {
    txt: 'AUALB-奥尔巴尼-ALBANY',
    code: 'AUALB'
  },
  {
    txt: 'AUARD-阿德罗森-ARDROSSAN',
    code: 'AUARD'
  },
  {
    txt: 'AUBAI-巴罗岛-BARROW ISLAND',
    code: 'AUBAI'
  },
  {
    txt: 'AUBDG-班达伯格-BUNDABERG',
    code: 'AUBDG'
  },
  {
    txt: 'AUBHD-巴拉斯特黑德-BALLAST HEAD',
    code: 'AUBHD'
  },
  {
    txt: 'AUBLB-贝尔贝-BELL BAY',
    code: 'AUBLB'
  },
  {
    txt: 'AUBNE-伯尼-BURNIE',
    code: 'AUBNE'
  },
  {
    txt: 'AUBOW-鲍恩-BOWEN ',
    code: 'AUBOW'
  },
  {
    txt: 'AUBRI-布里斯班-BRISBANE',
    code: 'AUBRI'
  },
  {
    txt: 'AUBRO-布鲁姆-BROOME',
    code: 'AUBRO'
  },
  {
    txt: 'AUBTB-植物学湾-BOTANY BAY',
    code: 'AUBTB'
  },
  {
    txt: 'AUBTP-博尤替角-BEAUTY POINT',
    code: 'AUBTP'
  },
  {
    txt: 'AUBUN-班伯里-BUNBURY',
    code: 'AUBUN'
  },
  {
    txt: 'AUBUS-巴瑟尔顿-BUSSELTON',
    code: 'AUBUS'
  },
  {
    txt: 'AUCAB-喀斯喀特湾-CASCADE BAY',
    code: 'AUCAB'
  },
  {
    txt: 'AUCAR-卡那封-CARNARVON',
    code: 'AUCAR'
  },
  {
    txt: 'AUCCU-库维恩角-CAPE CUVIER',
    code: 'AUCCU'
  },
  {
    txt: 'AUCFH-科夫斯湾-COFF’S HARBOUR',
    code: 'AUCFH'
  },
  {
    txt: 'AUCLT-拉姆贝特角-CAPE LAMBERT',
    code: 'AUCLT'
  },
  {
    txt: 'AUCNS-凯恩斯-CAIRNS',
    code: 'AUCNS'
  },
  {
    txt: 'AUCOO-库克敦-COOKTOWN',
    code: 'AUCOO'
  },
  {
    txt: 'AUDBY-德比-DERBY',
    code: 'AUDBY'
  },
  {
    txt: 'AUDEV-德文波特-DEVONPORT',
    code: 'AUDEV'
  },
  {
    txt: 'AUDPR-丹皮尔-DAMPIER ',
    code: 'AUDPR'
  },
  {
    txt: 'AUDWN-达尔文-DARWIN',
    code: 'AUDWN'
  },
  {
    txt: 'AUEDI-伊迪斯堡-EDITHBURGH ',
    code: 'AUEDI'
  },
  {
    txt: 'AUEDN-伊登-EDEN',
    code: 'AUEDN'
  },
  {
    txt: 'AUESP-埃斯佩兰斯-ESPERANCE',
    code: 'AUESP'
  },
  {
    txt: 'AUFRE-弗里曼特尔-FREMANTLE',
    code: 'AUFRE'
  },
  {
    txt: 'AUGEE-吉朗-GEELONG',
    code: 'AUGEE'
  },
  {
    txt: 'AUGER-杰拉尔顿-GERALDTON ',
    code: 'AUGER'
  },
  {
    txt: 'AUGET-格鲁特岛-GROOTE EYLANDT',
    code: 'AUGET'
  },
  {
    txt: 'AUGLA-格拉德斯通-GLADSTONE',
    code: 'AUGLA'
  },
  {
    txt: 'AUGOV-戈弗-GOVE ',
    code: 'AUGOV'
  },
  {
    txt: 'AUGRA-格拉夫顿-GRAFTON',
    code: 'AUGRA'
  },
  {
    txt: 'DKNOR-诺勒松比-NORRESUNDBY ',
    code: 'DKNOR'
  },
  {
    txt: 'DKORE-奥勒霍兹-OREHOVED ',
    code: 'DKORE'
  },
  {
    txt: 'DKRUD-鲁兹克宾-RUDKOBING ',
    code: 'DKRUD'
  },
  {
    txt: 'AUINN-因尼斯费尔-INNISFAIL',
    code: 'AUINN'
  },
  {
    txt: 'AUKIN-金斯敦-KINGSTON',
    code: 'AUKIN'
  },
  {
    txt: 'AUKTE-金斯科特-KINGSCOTE',
    code: 'AUKTE'
  },
  {
    txt: 'AULAU-朗塞斯顿-LAUNCESTON',
    code: 'AULAU'
  },
  {
    txt: 'AULUC-卢辛达-LUCINDA',
    code: 'AULUC'
  },
  {
    txt: 'AUMAC-麦克坦-MACTAN',
    code: 'AUMAC'
  },
  {
    txt: 'AUMEL-墨尔本-MELBOURNE',
    code: 'AUMEL'
  },
  {
    txt: 'AUMKY-麦凯-MACKAY',
    code: 'AUMKY'
  },
  {
    txt: 'AUMOU-莫里扬港-MOURILYAN',
    code: 'AUMOU'
  },
  {
    txt: 'AUNEW-纽卡斯尔-NEWCASTLE(N.S.W)',
    code: 'AUNEW'
  },
  {
    txt: 'AUPAD-阿德莱德港-PORT ADELAIDE',
    code: 'AUPAD'
  },
  {
    txt: 'AUPAL-艾尔弗雷德港-PORT ALFRED',
    code: 'AUPAL'
  },
  {
    txt: 'AUPAU-奥古斯塔港-PORT AUGUSTA',
    code: 'AUPAU'
  },
  {
    txt: 'AUPHE-黑德兰港-PORT HEDLAND',
    code: 'AUPHE'
  },
  {
    txt: 'AUPHU-会翁港-PORT HUON',
    code: 'AUPHU'
  },
  {
    txt: 'AUPJA-杰克逊港-PORT JACKSON',
    code: 'AUPJA'
  },
  {
    txt: 'AUPLA-拉塔港-PORT LATTA',
    code: 'AUPLA'
  },
  {
    txt: 'AUPLD-波特兰-PORTLAND (VIC.)',
    code: 'AUPLD'
  },
  {
    txt: 'AUPLI-林肯港-PORT LINCOLN',
    code: 'AUPLI'
  },
  {
    txt: 'AUPMA-麦夸里港-PORT MACGUARIE',
    code: 'AUPMA'
  },
  {
    txt: 'AUPST-斯坦瓦克港-PORT STANVAC',
    code: 'AUPST'
  },
  {
    txt: 'AUPTH-佩斯-PERCH',
    code: 'AUPTH'
  },
  {
    txt: 'AUPWA-澳尔科特港-PORT WALCOTT',
    code: 'AUPWA'
  },
  {
    txt: 'AUROC-罗克汉普顿-ROCKHAMPTON',
    code: 'AUROC'
  },
  {
    txt: 'AURPB-拉皮德湾-RAPID BAY',
    code: 'AURPB'
  },
  {
    txt: 'AUSTA-斯坦利-STANLEY ',
    code: 'AUSTA'
  },
  {
    txt: 'AUSTR-斯特拉恩-STRAHAN',
    code: 'AUSTR'
  },
  {
    txt: 'AUSYD-悉尼-SYDNEY(AU)',
    code: 'AUSYD'
  },
  {
    txt: 'AUTHU-星期四岛-THURADAY ISLAND',
    code: 'AUTHU'
  },
  {
    txt: 'AUTOW-汤斯维尔-TOWNSVILLE',
    code: 'AUTOW'
  },
  {
    txt: 'AUULP-乌塞勒斯卢普-USELESS LOOP',
    code: 'AUULP'
  },
  {
    txt: 'AUURA-尤兰根-URANGAN',
    code: 'AUURA'
  },
  {
    txt: 'AUVIA-维多利亚-VICTORIA',
    code: 'AUVIA'
  },
  {
    txt: 'AUWAL-沃拉鲁-WALLAROO',
    code: 'AUWAL'
  },
  {
    txt: 'AUWHY-怀阿拉-WHYALLA',
    code: 'AUWHY'
  },
  {
    txt: 'AUWPA-韦帕-WEIPA',
    code: 'AUWPA'
  },
  {
    txt: 'AUWSP-西港-WESTERN PORT',
    code: 'AUWSP'
  },
  {
    txt: 'AUYSD-扬皮桑德-YAMPI SOUND',
    code: 'AUYSD'
  },
  {
    txt: 'BBBTN-布里奇敦-BRIDGETOWN',
    code: 'BBBTN'
  },
  {
    txt: 'BDCNA-查尔纳港-CHALNA ANCHORAGE',
    code: 'BDCNA'
  },
  {
    txt: 'BDCTG-吉大港-CHATTOGRAM',
    code: 'BDCTG'
  },
  {
    txt: 'BDKHU-库尔港-KHULNA ',
    code: 'BDKHU'
  },
  {
    txt: 'BEANT-安特卫普-ANTWERP',
    code: 'BEANT'
  },
  {
    txt: 'BEBRS-布鲁日-BRUGES',
    code: 'BEBRS'
  },
  {
    txt: 'BEBRU-布鲁塞尔-BRUSSELS',
    code: 'BEBRU'
  },
  {
    txt: 'BEGHE-根特-GHENT',
    code: 'BEGHE'
  },
  {
    txt: 'BEHEM -海米克瑟姆-HEMIKSEM ',
    code: 'BEHEM '
  },
  {
    txt: 'BELGE-列日-LIEGE ',
    code: 'BELGE'
  },
  {
    txt: 'BENIE-尼乌波特-NIEUWPOORT ',
    code: 'BENIE'
  },
  {
    txt: 'BEOST-奥斯坦德-OSTEND ',
    code: 'BEOST'
  },
  {
    txt: 'BEZEE-泽布吕赫-ZEEBRUGGE ',
    code: 'BEZEE'
  },
  {
    txt: 'BGBAL-巴尔奇克-BALCHIK ',
    code: 'BGBAL'
  },
  {
    txt: 'BGBGS-布加斯-BOURGAS',
    code: 'BGBGS'
  },
  {
    txt: 'BGKAV-卡瓦尔纳-KAVARNA ',
    code: 'BGKAV'
  },
  {
    txt: 'BGMIC-米丘林-MICHURIN ',
    code: 'BGMIC'
  },
  {
    txt: 'BGNES-纳塞巴尔-NESSEBAR',
    code: 'BGNES'
  },
  {
    txt: 'BGVAR-瓦尔纳-VARNA ',
    code: 'BGVAR'
  },
  {
    txt: 'BHMAA-马纳马-MANAMA',
    code: 'BHMAA'
  },
  {
    txt: 'BJCOT-科托努-COTONOU',
    code: 'BJCOT'
  },
  {
    txt: 'BNMAA-麻拉-MUARA',
    code: 'BNMAA'
  },
  {
    txt: 'BOSAN-圣克鲁斯-SANTA CRUZ',
    code: 'BOSAN'
  },
  {
    txt: 'BRABA-阿里亚布兰卡-AREIA BRANCA ',
    code: 'BRABA'
  },
  {
    txt: 'BRADR-安格拉杜斯雷斯-ANGRA DOS REIS',
    code: 'BRADR'
  },
  {
    txt: 'BRAJU -阿拉卡茹-ARACAJU ',
    code: 'BRAJU '
  },
  {
    txt: 'BRATI-阿拉卡蒂-ARACATI ',
    code: 'BRATI'
  },
  {
    txt: 'BRATU-阿拉图-ARATU ',
    code: 'BRATU'
  },
  {
    txt: 'BRBDT-巴拉奥特菲-BARAO DE TEFFE ',
    code: 'BRBDT'
  },
  {
    txt: 'BRBLM-贝伦-BELEM',
    code: 'BRBLM'
  },
  {
    txt: 'BRCAB-卡贝德卢-CABEDELLO',
    code: 'BRCAB'
  },
  {
    txt: 'BRFLO-弗洛里亚诺波利斯-FLORIANOPOLIS ',
    code: 'BRFLO'
  },
  {
    txt: 'BRFOR-福塔莱萨-FORTALEZA',
    code: 'BRFOR'
  },
  {
    txt: 'BRIJI-伊塔雅伊-ITAJAI ',
    code: 'BRIJI'
  },
  {
    txt: 'BRIMB-因比图巴-IMBITUBA ',
    code: 'BRIMB'
  },
  {
    txt: 'BRIQI-伊塔基-ITAQUI ',
    code: 'BRIQI'
  },
  {
    txt: 'BRITA-伊塔日阿伊-ITAJAI',
    code: 'BRITA'
  },
  {
    txt: 'RUMEZ-美晋-MEZEN',
    code: 'RUMEZ'
  },
  {
    txt: 'BRIUS-伊列乌斯-ILHEUS ',
    code: 'BRIUS'
  },
  {
    txt: 'BRJPA-若昂佩索阿-JOAO PESSOA ',
    code: 'BRJPA'
  },
  {
    txt: 'BRMAC-马塞约-MACEIO',
    code: 'BRMAC'
  },
  {
    txt: 'BRMAN-马瑙斯-MANAUS ',
    code: 'BRMAN'
  },
  {
    txt: 'BRMPA-马卡帕-MACAPA ',
    code: 'BRMPA'
  },
  {
    txt: 'BRNAT-纳塔尔-NATAL ',
    code: 'BRNAT'
  },
  {
    txt: 'BRPAL-阿雷格里港-PORTO ALEGRE ',
    code: 'BRPAL'
  },
  {
    txt: 'BRPAR-巴拉那瓜-PARANAGUA ',
    code: 'BRPAR'
  },
  {
    txt: 'BRPBA-巴纳伊巴-PARNAIBA',
    code: 'BRPBA'
  },
  {
    txt: 'BRPDU-乌布角-PONTA DO UBU ',
    code: 'BRPDU'
  },
  {
    txt: 'BRPOR-波图塞尔-PORTOCEL ',
    code: 'BRPOR'
  },
  {
    txt: 'BRRDJ-里约热内卢-RIO DE JANEIRO ',
    code: 'BRRDJ'
  },
  {
    txt: 'BRREC-累西腓-RECIFE ',
    code: 'BRREC'
  },
  {
    txt: 'BRRGR-里奥格兰德-RIO GRANDE ',
    code: 'BRRGR'
  },
  {
    txt: 'BRSEP-塞佩提巴-SEPETIBA',
    code: 'BRSEP'
  },
  {
    txt: 'BRSFS-南圣弗兰西斯科-SAO FRANCISCO DO SUL ',
    code: 'BRSFS'
  },
  {
    txt: 'BRSLM-圣路易斯-SAO LUIZ DE MARANHAO ',
    code: 'BRSLM'
  },
  {
    txt: 'BRSSE-圣塞巴斯蒂昂-SAO SEBASTIAO ',
    code: 'BRSSE'
  },
  {
    txt: 'BRSTA-圣安娜-SANTANA  ',
    code: 'BRSTA'
  },
  {
    txt: 'BRSTM-圣塔伦-SANTAREM ',
    code: 'BRSTM'
  },
  {
    txt: 'BRSTS-桑托斯-SANTOS ',
    code: 'BRSTS'
  },
  {
    txt: 'BRTRO-特龙贝塔斯-TROMBETAS',
    code: 'BRTRO'
  },
  {
    txt: 'BRTUB-图巴郎-TUBARAO ',
    code: 'BRTUB'
  },
  {
    txt: 'BRVIT-维多利亚-VITORIA ',
    code: 'BRVIT'
  },
  {
    txt: 'BYBBL-博布鲁伊斯克-Babrujsk',
    code: 'BYBBL'
  },
  {
    txt: 'BYZHO-朱第诺-ZHODINO',
    code: 'BYZHO'
  },
  {
    txt: 'BZBEC-伯利兹城-BELIZE C.A',
    code: 'BZBEC'
  },
  {
    txt: 'CAAKL -阿克拉维克-AKLAVIK ',
    code: 'CAAKL '
  },
  {
    txt: 'CAANN-安纳波利斯-ANNAPOLIS ',
    code: 'CAANN'
  },
  {
    txt: 'CAARI-阿里沙特-ARICHAT ',
    code: 'CAARI'
  },
  {
    txt: 'CABAG-巴戈特维尔-BAGOTVILLE ',
    code: 'CABAG'
  },
  {
    txt: 'CABAT-巴瑟斯特-BATHURST ',
    code: 'CABAT'
  },
  {
    txt: 'CABCO-贝科莫-BAIE COMEAU ',
    code: 'CABCO'
  },
  {
    txt: 'CABEL-贝拉顿-BELLE DUNE ',
    code: 'CABEL'
  },
  {
    txt: 'CABGO-伯吉奥-BURGEO ',
    code: 'CABGO'
  },
  {
    txt: 'CABOT-博特伍德-BOTWOOD ',
    code: 'CABOT'
  },
  {
    txt: 'CABRO-布罗克维尔-BROCKVILLE ',
    code: 'CABRO'
  },
  {
    txt: 'CABRS-贝罗伯茨-BAY ROBERTS ',
    code: 'CABRS'
  },
  {
    txt: 'CABSC-巴斯克湾-BASQUE COVE ',
    code: 'CABSC'
  },
  {
    txt: 'CABUC-巴克图什-BUCTOUCHE ',
    code: 'CABUC'
  },
  {
    txt: 'CABVH-比弗港-BEAVER HABOUR ',
    code: 'CABVH'
  },
  {
    txt: 'CACAL-卡尔加里-CALGARY',
    code: 'CACAL'
  },
  {
    txt: 'CACAR-卡尔顿-CARLETON ',
    code: 'CACAR'
  },
  {
    txt: 'CACBC-卡姆拜钱斯-COME-BY-CHANCE ',
    code: 'CACBC'
  },
  {
    txt: 'CACBK-科纳—布鲁特-COMER BROOK ',
    code: 'CACBK'
  },
  {
    txt: 'CACHE-彻梅纳斯-CHEMAINUS ',
    code: 'CACHE'
  },
  {
    txt: 'CACHI-希库提米-CHICOUTIMI ',
    code: 'CACHI'
  },
  {
    txt: 'CACHU-丘吉尔-CHURCHILL ',
    code: 'CACHU'
  },
  {
    txt: 'CACLA-克拉伦维尔-CLARENVILLE ',
    code: 'CACLA'
  },
  {
    txt: 'CACMX-科莫克斯-COMOX',
    code: 'CACMX'
  },
  {
    txt: 'CACOB-科堡-COBOURG ',
    code: 'CACOB'
  },
  {
    txt: 'CACOL-科灵伍德-COLLINGWOOD ',
    code: 'CACOL'
  },
  {
    txt: 'CACON-孔特勒科尔-CONTRECOEUR ',
    code: 'CACON'
  },
  {
    txt: 'CACOR-康沃尔-CORNWALL ',
    code: 'CACOR'
  },
  {
    txt: 'CACQT-卡拉凯特-CARAQUET ',
    code: 'CACQT'
  },
  {
    txt: 'CACRO-克罗夫顿-CROFTON ',
    code: 'CACRO'
  },
  {
    txt: 'CACSH-坎索港-CANSO HARBOUR ',
    code: 'CACSH'
  },
  {
    txt: 'CACTH-康特里港-COUNTRY HARBOUR',
    code: 'CACTH'
  },
  {
    txt: 'CACTM-查塔姆-CHATHAM ',
    code: 'CACTM'
  },
  {
    txt: 'CACTP-谢蒂坎普-CHETICMP ',
    code: 'CACTP'
  },
  {
    txt: 'CACWB-科威恰湾-COWICHAN BAY ',
    code: 'CACWB'
  },
  {
    txt: 'CADAL-达尔豪西-DALHOUSIE ',
    code: 'CADAL'
  },
  {
    txt: 'CADIG-迪格比-DIGBY ',
    code: 'CADIG'
  },
  {
    txt: 'CADOM-多米诺-DOMINO ',
    code: 'CADOM'
  },
  {
    txt: 'CADWL-丁沃尔-DINGWALL ',
    code: 'CADWL'
  },
  {
    txt: 'CAEDM-埃德蒙顿-EDMONTON',
    code: 'CAEDM'
  },
  {
    txt: 'CAESQ-埃斯奎莫尔特-ESQUIMALT ',
    code: 'CAESQ'
  },
  {
    txt: 'CAFOR-福雷斯特维尔-FORESTVILE ',
    code: 'CAFOR'
  },
  {
    txt: 'CAFRB-弗罗比舍湾-FROBISHER BAY ',
    code: 'CAFRB'
  },
  {
    txt: 'CAGAS-如斯佩-GASPE ',
    code: 'CAGAS'
  },
  {
    txt: 'CAGBK-格兰德班克-GRAND BANK ',
    code: 'CAGBK'
  },
  {
    txt: 'CAGEO-乔治敦-GEORGETOWN ',
    code: 'CAGEO'
  },
  {
    txt: 'CAGOD-戈德里奇-GODERICH ',
    code: 'CAGOD'
  },
  {
    txt: 'CAGOR-戈尔德里弗-GOLD RIVER ',
    code: 'CAGOR'
  },
  {
    txt: 'CAGSB-古斯湾-GOOSE BAY ',
    code: 'CAGSB'
  },
  {
    txt: 'CAHAM-哈密尔顿-HAMILTON ',
    code: 'CAHAM'
  },
  {
    txt: 'FRGLS-格拉奥利纳-GRAVELINES ',
    code: 'FRGLS'
  },
  {
    txt: 'FRGON-贡夫勒维尔-GONFREVILE ',
    code: 'FRGON'
  },
  {
    txt: 'ESMAH-马翁　　　　　　　　　-MAHON',
    code: 'ESMAH'
  },
  {
    txt: 'ESMAL-马拉加　　　　　　　　-MALAGA',
    code: 'ESMAL'
  },
  {
    txt: 'ESMAR-马林　　　　　　　　　-MARIN',
    code: 'ESMAR'
  },
  {
    txt: 'ESMAZ-马萨龙　　　　　　　　-MAZARRON',
    code: 'ESMAZ'
  },
  {
    txt: 'ESMOT-莫特里尔　　　　　　　-MOTRIL',
    code: 'ESMOT'
  },
  {
    txt: 'ESMUR-穆罗斯　　　　　　　　-MUROS',
    code: 'ESMUR'
  },
  {
    txt: 'ESPAL-帕尔马　　　　　　　　-PALMA',
    code: 'ESPAL'
  },
  {
    txt: 'ESPAS-帕萨赫斯　　　　　　　-PASAJES',
    code: 'ESPAS'
  },
  {
    txt: 'ESPLS-帕拉莫斯　　　　　　　-PALAMOS',
    code: 'ESPLS'
  },
  {
    txt: 'ESPMI-帕尔马 马略卡-PALMA DE MALLORCA',
    code: 'ESPMI'
  },
  {
    txt: 'ESPOR-波图加莱特　　　　　　-PORTUGALETE',
    code: 'ESPOR'
  },
  {
    txt: 'ESPSO-萨尔塔卡瓦略　　　　　-PUNTA SALTACABA',
    code: 'ESPSO'
  },
  {
    txt: 'ESRDO-里瓦德奥　　　　　　　-RIBADEO',
    code: 'ESRDO'
  },
  {
    txt: 'ESRIB-里瓦德塞利亚　　　　　-RIBADESELLA',
    code: 'ESRIB'
  },
  {
    txt: 'ESROS-罗萨斯　　　　　　　　-ROSAS',
    code: 'ESROS'
  },
  {
    txt: 'ESRTA-罗塔　　　　　　　　　-ROTA',
    code: 'ESRTA'
  },
  {
    txt: 'ESSAG-萨贡托　　　　　　　　-SAGUNTO',
    code: 'ESSAG'
  },
  {
    txt: 'ESSAN-桑坦德　　　　　　　　-SANTANDER',
    code: 'ESSAN'
  },
  {
    txt: 'ESSCA-圣卡洛斯　　　　　　　-SAN CARLOS(ES)',
    code: 'ESSCA'
  },
  {
    txt: 'ESSCI-圣西普里安　　　　　　-SAN CIPRIAN',
    code: 'ESSCI'
  },
  {
    txt: 'ESSEP-圣埃斯特班　　　　　　-SAN ESTEBAN DE',
    code: 'ESSEP'
  },
  {
    txt: 'ESSEV-塞维利亚　　　　　　　-SEVILLE',
    code: 'ESSEV'
  },
  {
    txt: 'ESSFE-圣费尔南多　　　　　　-SAN FERNANDO(ES',
    code: 'ESSFE'
  },
  {
    txt: 'ESSFG-圣费里乌德古绍尔斯　　-SAN FELIU DE GU',
    code: 'ESSFG'
  },
  {
    txt: 'ESSOL-索列尔　　　　　　　　-SOLLER',
    code: 'ESSOL'
  },
  {
    txt: 'ESSSE-圣塞瓦斯蒂安　　　　　-SAN SEBASTIAN',
    code: 'ESSSE'
  },
  {
    txt: 'ESTAR-塔拉戈纳　　　　　　　-TARRAGONA',
    code: 'ESTAR'
  },
  {
    txt: 'ESTOR-托雷维耶哈　　　　　　-TORREVIEJA',
    code: 'ESTOR'
  },
  {
    txt: 'ESVAL-巴伦西亚　　　　　　　-VALENCIA',
    code: 'ESVAL'
  },
  {
    txt: 'ESVIG-维哥　　　　　　　　　-VIGO',
    code: 'ESVIG'
  },
  {
    txt: 'ESVIL-维利亚加西　　　　　　-VILLAGARCIA',
    code: 'ESVIL'
  },
  {
    txt: 'ESVIV-比韦罗　　　　　　　　-VIVERO',
    code: 'ESVIV'
  },
  {
    txt: 'FIBAR-巴罗生特-BAROSUND ',
    code: 'FIBAR'
  },
  {
    txt: 'FIDEG-代格比-DEGERBY ',
    code: 'FIDEG'
  },
  {
    txt: 'FIHAM-哈米纳-HAMINA ',
    code: 'FIHAM'
  },
  {
    txt: 'FIHAN-汉科-HANKO ',
    code: 'FIHAN'
  },
  {
    txt: 'FIHEL-赫尔辛基-HELSINKI',
    code: 'FIHEL'
  },
  {
    txt: 'FIINK-因科-INKOO ',
    code: 'FIINK'
  },
  {
    txt: 'FIISN-伊斯奈斯-ISNAS ',
    code: 'FIISN'
  },
  {
    txt: 'FIKAS-卡斯基宁-KASKINEN ',
    code: 'FIKAS'
  },
  {
    txt: 'FIKEM-凯米-KEMI ',
    code: 'FIKEM'
  },
  {
    txt: 'FIKOK-科科拉-KOKKOLA ',
    code: 'FIKOK'
  },
  {
    txt: 'FIKOT-科特卡-KOTKA ',
    code: 'FIKOT'
  },
  {
    txt: 'FIKOV-科维尔哈-KOVERHAR ',
    code: 'FIKOV'
  },
  {
    txt: 'FILAP-拉柏罗吐-LAPALUOTO ',
    code: 'FILAP'
  },
  {
    txt: 'FILOV-洛维萨-LOVIISA ',
    code: 'FILOV'
  },
  {
    txt: 'FIMAR-玛丽港-MARIEHAMN ',
    code: 'FIMAR'
  },
  {
    txt: 'FIMTO-曼蒂卢奥托-MANTYLUOTO ',
    code: 'FIMTO'
  },
  {
    txt: 'FINAA-楠塔利-NAANTALI ',
    code: 'FINAA'
  },
  {
    txt: 'FINYS-尼斯塔德-NYSTAD ',
    code: 'FINYS'
  },
  {
    txt: 'FIOUL-奥鲁-OULU ',
    code: 'FIOUL'
  },
  {
    txt: 'FIPGS-柏尔加斯-PARGAS ',
    code: 'FIPGS'
  },
  {
    txt: 'FIPIE-皮耶塔尔萨里-PIETARSAARI ',
    code: 'FIPIE'
  },
  {
    txt: 'FIPOR-波卡拉-PORKKALA ',
    code: 'FIPOR'
  },
  {
    txt: 'FIPRI-波里-PORI ',
    code: 'FIPRI'
  },
  {
    txt: 'FIRAA-拉赫-RAAHE ',
    code: 'FIRAA'
  },
  {
    txt: 'FIRAU-劳马-RAUMA ',
    code: 'FIRAU'
  },
  {
    txt: 'FIREP-雷波萨里-REPOSAARI ',
    code: 'FIREP'
  },
  {
    txt: 'FISMC-塞马运河-SAIMAA CANAL ',
    code: 'FISMC'
  },
  {
    txt: 'FITAM-塔米萨里-TAMMISAARI ',
    code: 'FITAM'
  },
  {
    txt: 'FITKS-托基斯-TOIKIS ',
    code: 'FITKS'
  },
  {
    txt: 'FITOR-托尔尼奥-TORNIO ',
    code: 'FITOR'
  },
  {
    txt: 'FITUR-图尔库-TURKU ',
    code: 'FITUR'
  },
  {
    txt: 'FIUUS-新考蓬基-UUSIKAUPUNKI ',
    code: 'FIUUS'
  },
  {
    txt: 'FIVEI-卫特什露土-VEITSILUOTO ',
    code: 'FIVEI'
  },
  {
    txt: 'FIVSA-瓦萨-VAASA',
    code: 'FIVSA'
  },
  {
    txt: 'FIWAL-瓦尔卡姆-WALKOM ',
    code: 'FIWAL'
  },
  {
    txt: 'FIYKS-伊克斯皮拉雅-YKSPIHLAJA ',
    code: 'FIYKS'
  },
  {
    txt: 'ACMAR-马里戈特-MARIGOT',
    code: 'ACMAR'
  },
  {
    txt: 'FRABB-阿布维尔-ABBEVILLE',
    code: 'FRABB'
  },
  {
    txt: 'FRARC-阿尔卡雄-ARCACHON ',
    code: 'FRARC'
  },
  {
    txt: 'FRATR-昂蒂弗-ANTIFER ',
    code: 'FRATR'
  },
  {
    txt: 'FRATS-昂蒂布-ANTIBES',
    code: 'FRATS'
  },
  {
    txt: 'FRBAS-巴森-BASSENS ',
    code: 'FRBAS'
  },
  {
    txt: 'FRBAY-巴约讷-BAYONNE ',
    code: 'FRBAY'
  },
  {
    txt: 'FRBDA-贝克德阿姆比斯-BEC D’AMBES ',
    code: 'FRBDA'
  },
  {
    txt: 'FRBLA-布莱-BLAYE ',
    code: 'FRBLA'
  },
  {
    txt: 'FRBON-博尼法乔-BONIFACIO ',
    code: 'FRBON'
  },
  {
    txt: 'FRBOR-波尔多-BORDEAUX ',
    code: 'FRBOR'
  },
  {
    txt: 'FRBOU-布洛涅-BOULOGNE ',
    code: 'FRBOU'
  },
  {
    txt: 'FRBST-布雷斯特-BREST ',
    code: 'FRBST'
  },
  {
    txt: 'FRBTA-巴斯蒂亚-BASTIA',
    code: 'FRBTA'
  },
  {
    txt: 'FRCAL-加来-CALAIS ',
    code: 'FRCAL'
  },
  {
    txt: 'FRCAM-卡马雷-CAMARET ',
    code: 'FRCAM'
  },
  {
    txt: 'FRCAN-冈昂-CAEN ',
    code: 'FRCAN'
  },
  {
    txt: 'FRCAR-卡隆特-CARINTE ',
    code: 'FRCAR'
  },
  {
    txt: 'FRCBG-瑟堡-CHERBOURG ',
    code: 'FRCBG'
  },
  {
    txt: 'FRCLE-康卡勒-CANCALE ',
    code: 'FRCLE'
  },
  {
    txt: 'FRCNS-戛纳-CANNES ',
    code: 'FRCNS'
  },
  {
    txt: 'FRCON-孔卡尔诺-CONCARNEAU ',
    code: 'FRCON'
  },
  {
    txt: 'FRCVI-卡尔维-CALVI ',
    code: 'FRCVI'
  },
  {
    txt: 'FRDAH-达乌埃-DABOUET ',
    code: 'FRDAH'
  },
  {
    txt: 'FRDEA-多维尔-DEAUVILLE ',
    code: 'FRDEA'
  },
  {
    txt: 'FRDIE-迪耶普-DIEPPE ',
    code: 'FRDIE'
  },
  {
    txt: 'FRDKK-敦刻尔克-DUNKIRK ',
    code: 'FRDKK'
  },
  {
    txt: 'FRDON-栋日-DONGES ',
    code: 'FRDON'
  },
  {
    txt: 'FRDOU-杜阿梅勒兹-DOUAMENEZ ',
    code: 'FRDOU'
  },
  {
    txt: 'FRETA-埃塔普勒-ETAPLES ',
    code: 'FRETA'
  },
  {
    txt: 'FRFEC-费康-FECAMP ',
    code: 'FRFEC'
  },
  {
    txt: 'FRFOS-福斯-FOS ',
    code: 'FRFOS'
  },
  {
    txt: 'FRGRA-格兰维尔-GRANVILLE ',
    code: 'FRGRA'
  },
  {
    txt: 'FRHAV-阿弗尔-HAVRE ',
    code: 'FRHAV'
  },
  {
    txt: 'FRHON-翁弗勒尔-HONFLEUR ',
    code: 'FRHON'
  },
  {
    txt: 'FRIRO-伊尔鲁斯-ILE ROUSSE ',
    code: 'FRIRO'
  },
  {
    txt: 'FRLAN-郎代诺-LANDERNEAU ',
    code: 'FRLAN'
  },
  {
    txt: 'FRLAV-拉瓦拉-LAVERA ',
    code: 'FRLAV'
  },
  {
    txt: 'FRLCH-奥来龙堡-LE CHATEAU ',
    code: 'FRLCH'
  },
  {
    txt: 'FRLCI-拉西约塔-LA CIOTAT ',
    code: 'FRLCI'
  },
  {
    txt: 'FRLEH-勒阿弗尔-LE HAVRE',
    code: 'FRLEH'
  },
  {
    txt: 'FRLGU-勒吉尔多-LE GUILDO ',
    code: 'FRLGU'
  },
  {
    txt: 'FRLIB-利布尔讷-LIBOURNE ',
    code: 'FRLIB'
  },
  {
    txt: 'FRLNO-拉努韦勒-LA NOUVELLA ',
    code: 'FRLNO'
  },
  {
    txt: 'FRLOC-洛克蒂迪-LOCTUDY ',
    code: 'FRLOC'
  },
  {
    txt: 'FRLPA-拉帕利斯-LA PALLICE',
    code: 'FRLPA'
  },
  {
    txt: 'FRLRO-拉罗谢尔-LA ROCHELLE ',
    code: 'FRLRO'
  },
  {
    txt: 'FRMAR-马朗-MARANS ',
    code: 'FRMAR'
  },
  {
    txt: 'FRMOR-莫尔莱-MORLAIX ',
    code: 'FRMOR'
  },
  {
    txt: 'FRMRS-马赛-MARSEILLES ',
    code: 'FRMRS'
  },
  {
    txt: 'FRMTS-马蒂格-MARTIGUES ',
    code: 'FRMTS'
  },
  {
    txt: 'FRNIC-尼斯-NICE',
    code: 'FRNIC'
  },
  {
    txt: 'FROUI-乌伊斯特勒昂-OUISTREHAM',
    code: 'FROUI'
  },
  {
    txt: 'FRPAC-波亚克-PAUILLAC ',
    code: 'FRPAC'
  },
  {
    txt: 'FRPBF-潘伯夫-PAIMBOEUF ',
    code: 'FRPBF'
  },
  {
    txt: 'FRPDB-布克港-PORT DE BOUC',
    code: 'FRPDB'
  },
  {
    txt: 'FRPJE-杰罗姆港-PORT JEROME',
    code: 'FRPJE'
  },
  {
    txt: 'FRPPI-潘波勒-PAIMPOL ',
    code: 'FRPPI'
  },
  {
    txt: 'FRPRO-普罗普里亚诺-PROPRIANO',
    code: 'FRPRO'
  },
  {
    txt: 'FRPRS-帕里斯-PARIS ',
    code: 'FRPRS'
  },
  {
    txt: 'FRPVS-旺德尔港-PORT VENDRES ',
    code: 'FRPVS'
  },
  {
    txt: 'FRQUI-坎佩尔-QUIMPER ',
    code: 'FRQUI'
  },
  {
    txt: 'FRROC-罗什福尔-ROCHEFORT ',
    code: 'FRROC'
  },
  {
    txt: 'FRROS-罗斯科夫-ROSCOFF ',
    code: 'FRROS'
  },
  {
    txt: 'FRSBR-圣布里厄-ST. BRIEUC',
    code: 'FRSBR'
  },
  {
    txt: 'FRSDO-萨布勒多隆-SABLES D’OLONNE ',
    code: 'FRSDO'
  },
  {
    txt: 'FRSET-塞特-SETE ',
    code: 'FRSET'
  },
  {
    txt: 'FRSLR-圣路易罗纳-ST. LOUIS DU RHONE',
    code: 'FRSLR'
  },
  {
    txt: 'FRSMO-圣马洛-ST. MALO',
    code: 'FRSMO'
  },
  {
    txt: 'FRSNA-圣纳泽尔-ST. NAZAIRE ',
    code: 'FRSNA'
  },
  {
    txt: 'FRSSE-圣塞尔旺-ST. SERVAN ',
    code: 'FRSSE'
  },
  {
    txt: 'FRSVC-圣瓦勒利-ST. VALERY EN CAUX',
    code: 'FRSVC'
  },
  {
    txt: 'FRSVS-圣瓦莱利昂科-ST.VALERY SUR SOMME',
    code: 'FRSVS'
  },
  {
    txt: 'FRTGR-特雷吉耶-TREGUIER ',
    code: 'FRTGR'
  },
  {
    txt: 'FRTNC-托内沙朗特-TONNAY CHARENTE ',
    code: 'FRTNC'
  },
  {
    txt: 'FRTPT-特雷波特-TREPORT ',
    code: 'FRTPT'
  },
  {
    txt: 'FRTRO-特鲁维尔-TROUVILLE',
    code: 'FRTRO'
  },
  {
    txt: 'FRVAN-瓦讷-VANES ',
    code: 'FRVAN'
  },
  {
    txt: 'GALIE-利伯维尔-LIBREVILLE',
    code: 'GALIE'
  },
  {
    txt: 'GBABE-阿伯里斯特威斯-ABERYSTWYTH ',
    code: 'GBABE'
  },
  {
    txt: 'GBABN-阿伯丁-ABERDEEN ',
    code: 'GBABN'
  },
  {
    txt: 'GBABY-阿伯多维-ABERDOVEY ',
    code: 'GBABY'
  },
  {
    txt: 'GBAGS-阿德格拉斯-ARDGLASS ',
    code: 'GBAGS'
  },
  {
    txt: 'GBALD-奥尔德尼-ALDERNEY ',
    code: 'GBALD'
  },
  {
    txt: 'GBAML-阿姆卢赫-AMLWCH ',
    code: 'GBAML'
  },
  {
    txt: 'GBANN-安嫩-ANNAN ',
    code: 'GBANN'
  },
  {
    txt: 'GBANS-安斯特拉瑟-ANSTRUTHER ',
    code: 'GBANS'
  },
  {
    txt: 'GBAPP-阿普尔多尔-APPLEDORE ',
    code: 'GBAPP'
  },
  {
    txt: 'GBARB-阿布罗斯-ARBROATH ',
    code: 'GBARB'
  },
  {
    txt: 'GBARG-阿德里希格-ARDRISHAIG ',
    code: 'GBARG'
  },
  {
    txt: 'GBARN-阿德罗森-ARDROSSAN ',
    code: 'GBARN'
  },
  {
    txt: 'GBAVO-埃文茅斯-AVONMOUTH ',
    code: 'GBAVO'
  },
  {
    txt: 'GBAYR-艾尔-AYR ',
    code: 'GBAYR'
  },
  {
    txt: 'GBBAE-巴恩斯特珀尔-BARNSTAPLE ',
    code: 'GBBAE'
  },
  {
    txt: 'GBBAN-班戈-BANGOR (CO.DOWN)',
    code: 'GBBAN'
  },
  {
    txt: 'GBBAR-巴顿-BARTON (W.C.)',
    code: 'GBBAR'
  },
  {
    txt: 'GBBEL-贝尔法斯特-BELFAST ',
    code: 'GBBEL'
  },
  {
    txt: 'GBBER-伯威克-BERWICK ',
    code: 'GBBER'
  },
  {
    txt: 'GBBGR-班戈-BANGOR (CAER.)',
    code: 'GBBGR'
  },
  {
    txt: 'GBBHD-伯格黑德-BURGHEAD ',
    code: 'GBBHD'
  },
  {
    txt: 'GBBID-比迪福德-BIDEFORD ',
    code: 'GBBID'
  },
  {
    txt: 'GBBIR-伯肯黑德-BIRKENHEAD ',
    code: 'GBBIR'
  },
  {
    txt: 'GBBLN-布里德灵顿-BRIDLINGTON ',
    code: 'GBBLN'
  },
  {
    txt: 'GBBLY-布莱斯-BLYTH ',
    code: 'GBBLY'
  },
  {
    txt: 'GBBMS-博马里斯-BEAUMARIS ',
    code: 'GBBMS'
  },
  {
    txt: 'GBBOS-波士顿-BOSTON ',
    code: 'GBBOS'
  },
  {
    txt: 'GBBRD-布朗巴勒-BROMBOROUGH DOCK',
    code: 'GBBRD'
  },
  {
    txt: 'GBBRI-布里德波特-BRIDPORT ',
    code: 'GBBRI'
  },
  {
    txt: 'GBBRN-布赖特灵西-BRIGHTLINGSEA ',
    code: 'GBBRN'
  },
  {
    txt: 'GBBRW-巴罗-BARROW ',
    code: 'GBBRW'
  },
  {
    txt: 'GBBRY-巴里-BARRY ',
    code: 'GBBRY'
  },
  {
    txt: 'GBBTH-巴茅思-BARMOUTH ',
    code: 'GBBTH'
  },
  {
    txt: 'GBBTL-布里斯托尔-BRISTOL ',
    code: 'GBBTL'
  },
  {
    txt: 'GBBTN-巴顿-BARTON (E.C.)',
    code: 'GBBTN'
  },
  {
    txt: 'GBBUC-巴基-BUCKIE ',
    code: 'GBBUC'
  },
  {
    txt: 'GBBUR-本泰兰-BURNTISLAND ',
    code: 'GBBUR'
  },
  {
    txt: 'GBBWR-布里奇沃特-BRIDGWATER ',
    code: 'GBBWR'
  },
  {
    txt: 'GBBXM-布里克瑟姆-BRIXHAM ',
    code: 'GBBXM'
  },
  {
    txt: 'GBCAE-卡那封-CAERNARFON ',
    code: 'GBCAE'
  },
  {
    txt: 'GBCAM-坎贝尔敦-CAMPBELTOWN',
    code: 'GBCAM'
  },
  {
    txt: 'GBCAR-卡里克弗格斯-CARRICKFERGUS ',
    code: 'GBCAR'
  },
  {
    txt: 'GBCAS-卡斯尔敦-CASTLETOWN ',
    code: 'GBCAS'
  },
  {
    txt: 'GBCDF-加的夫-CARDIFF ',
    code: 'GBCDF'
  },
  {
    txt: 'GBCHA-查尔斯敦-CHARLESTOWN (CORN.)',
    code: 'GBCHA'
  },
  {
    txt: 'GBCHN-查尔斯敦-CHARLESTOWN (FIFE)',
    code: 'GBCHN'
  },
  {
    txt: 'GBCOE-科尔雷恩-COLERAINE ',
    code: 'GBCOE'
  },
  {
    txt: 'GBCOL-科尔切斯特-COLCHESTER ',
    code: 'GBCOL'
  },
  {
    txt: 'GBCOR-科铂赫-COROACH ',
    code: 'GBCOR'
  },
  {
    txt: 'GBCOS-考斯-COWES ',
    code: 'GBCOS'
  },
  {
    txt: 'GBCRO-克罗默蒂-CROMARTY',
    code: 'GBCRO'
  },
  {
    txt: 'GBCST-赛伦塞斯特-CIRENCESTER',
    code: 'GBCST'
  },
  {
    txt: 'GBDAR-达特茅斯-DARTMOUTH ',
    code: 'GBDAR'
  },
  {
    txt: 'GBDDE-邓迪-DUNDEE ',
    code: 'GBDDE'
  },
  {
    txt: 'GBDHO-肖勒姆-SHOREHAM ',
    code: 'GBDHO'
  },
  {
    txt: 'GBDIN-丁沃尔-DIGWALL ',
    code: 'GBDIN'
  },
  {
    txt: 'GBDOU-道格拉斯-DOUGLAS ',
    code: 'GBDOU'
  },
  {
    txt: 'GBDRM-邓德拉姆-DUNDRUM ',
    code: 'GBDRM'
  },
  {
    txt: 'GBDUM-邓弗里斯-DUMFRIES ',
    code: 'GBDUM'
  },
  {
    txt: 'GBEDB-爱丁堡-EDINBURGH',
    code: 'GBEDB'
  },
  {
    txt: 'GBELP-埃尔斯米尔港-ELLESMERE PORT ',
    code: 'GBELP'
  },
  {
    txt: 'GBEXM-埃克斯茅斯-EXMOUTH ',
    code: 'GBEXM'
  },
  {
    txt: 'GBEYE-艾茅斯-EYEMOUTH ',
    code: 'GBEYE'
  },
  {
    txt: 'GBFAL-法尔茅斯-FALMOUTH ',
    code: 'GBFAL'
  },
  {
    txt: 'GBFAW-福利-FAWLEY ',
    code: 'GBFAW'
  },
  {
    txt: 'GBFIN-芬纳特-FINNART ',
    code: 'GBFIN'
  },
  {
    txt: 'GBFIS-菲什加德-FISHGUARD ',
    code: 'GBFIS'
  },
  {
    txt: 'GBFLE-弗利特伍德-FLEETWOOD ',
    code: 'GBFLE'
  },
  {
    txt: 'GBFOL -福克斯通-FOLKESTONE ',
    code: 'GBFOL '
  },
  {
    txt: 'GBFOW-福伊-FOWEY ',
    code: 'GBFOW'
  },
  {
    txt: 'GBGAI-盖恩斯伯勒-GAINSBOROUGH ',
    code: 'GBGAI'
  },
  {
    txt: 'GBGAN-加利斯敦-GARLIESTON ',
    code: 'GBGAN'
  },
  {
    txt: 'GBGAR-加斯顿-GARSTON ',
    code: 'GBGAR'
  },
  {
    txt: 'GBGIR-格文-GIRVAN ',
    code: 'GBGIR'
  },
  {
    txt: 'GBGLA-格拉斯哥-GLASGOW',
    code: 'GBGLA'
  },
  {
    txt: 'DZZKYE-贾扎伊尔-EL DJAZAIR',
    code: 'DZZKYE'
  },
  {
    txt: 'KSWPLSDN-普里什蒂纳-PRISTINA',
    code: 'KSWPLSDN'
  },
  {
    txt: 'JPCS-冲绳-OKINAWA',
    code: 'JPCS'
  },
  {
    txt: 'SKSL-萨拉-SALA',
    code: 'SKSL'
  },
  {
    txt: 'UAMLWPE-马里乌波尔-MARIUPOL',
    code: 'UAMLWPE'
  },
  {
    txt: 'CSBEGLD-贝尔格莱德-BELGRADE',
    code: 'CSBEGLD'
  },
  {
    txt: 'SOMJDX-摩加迪休-MOGADISHU',
    code: 'SOMJDX'
  },
  {
    txt: 'INNS-那瓦西瓦-NHAVA SHEVA',
    code: 'INNS'
  },
  {
    txt: 'INAMD-艾哈迈达巴德-AHMEDABAD',
    code: 'INAMD'
  },
  {
    txt: 'CNGL-关林-GUANLIN',
    code: 'CNGL'
  },
  {
    txt: 'BDDK-达卡-DHAKA',
    code: 'BDDK'
  },
  {
    txt: 'USSTLS-圣路易市-ST.LOUSIE',
    code: 'USSTLS'
  },
  {
    txt: 'KHSHNVL-斯哈诺克维尔-SIHANOUKVILLE',
    code: 'KHSHNVL'
  },
  {
    txt: 'USADS-阿迪森-ADDISON',
    code: 'USADS'
  },
  {
    txt: 'USLS-林赛-LINDSAY',
    code: 'USLS'
  },
  {
    txt: 'THLKB-拉加班-LAT KRABANG',
    code: 'THLKB'
  },
  {
    txt: 'GBBMH-伯明翰-BIRMINGHAM',
    code: 'GBBMH'
  },
  {
    txt: 'AEAAB-阿布埃尔布霍希-ABU AL BUKHOOSH',
    code: 'AEAAB'
  },
  {
    txt: 'AEABD-阿布扎比-ABU DHABI',
    code: 'AEABD'
  },
  {
    txt: 'AEAJM-阿治曼-AJMAN',
    code: 'AEAJM'
  },
  {
    txt: 'AEDAS-达斯岛-DAS ISLAND',
    code: 'AEDAS'
  },
  {
    txt: 'AEDUB-迪拜-DUBAI',
    code: 'AEDUB'
  },
  {
    txt: 'AEFAT-法特油码头-FATEH TERMINAL',
    code: 'AEFAT'
  },
  {
    txt: 'AEFUJ-富查伊拉-FUJAIRAH',
    code: 'AEFUJ'
  },
  {
    txt: 'AEJAL-阿里山-JEBEL ALI',
    code: 'AEJAL'
  },
  {
    txt: 'AEJDA-杰贝尔丹那-JEBEL DHANNA',
    code: 'AEJDA'
  },
  {
    txt: 'AEKFA-豪尔费坎-KHOR FAKKAN',
    code: 'AEKFA'
  },
  {
    txt: 'AEMSA-沙奎港-MINA SAQR',
    code: 'AEMSA'
  },
  {
    txt: 'AEMUI-穆巴腊岛-MUBARRAS ISLAND',
    code: 'AEMUI'
  },
  {
    txt: 'AEMZA-扎伊德港-MINA ZAYED',
    code: 'AEMZA'
  },
  {
    txt: 'AERAK-哈伊马角-RAS AL KHAIMAB',
    code: 'AERAK'
  },
  {
    txt: 'AESAJ-沙加-SHARJAH',
    code: 'AESAJ'
  },
  {
    txt: 'AESJH-舍尔杰-SHARJAH',
    code: 'AESJH'
  },
  {
    txt: 'AEUAQ-乌姆盖万-UMM AL QUWAIN',
    code: 'AEUAQ'
  },
  {
    txt: 'AGSJS-圣约翰斯-ST.JOHNS',
    code: 'AGSJS'
  },
  {
    txt: 'ALDRS-都拉斯-DURRES',
    code: 'ALDRS'
  },
  {
    txt: 'ALSAR-萨兰达-SARANDE',
    code: 'ALSAR'
  },
  {
    txt: 'ALSGN-圣吉尼-SHENGJIN',
    code: 'ALSGN'
  },
  {
    txt: 'ALTIA-地拉那-TIRANA',
    code: 'ALTIA'
  },
  {
    txt: 'ALVLO-发罗拉-VLONE',
    code: 'ALVLO'
  },
  {
    txt: 'ANBAR-巴尔卡德拉-BARCADERA',
    code: 'ANBAR'
  },
  {
    txt: 'ANBLB-布伦湾-BULLEN BAY',
    code: 'ANBLB'
  },
  {
    txt: 'ANCSB-加拉加斯湾-CARACAS BAY',
    code: 'ANCSB'
  },
  {
    txt: 'ANFKB-福克湾-FUIK BAY',
    code: 'ANFKB'
  },
  {
    txt: 'ANKRA-克拉伦代克-KARLENDIJK',
    code: 'ANKRA'
  },
  {
    txt: 'ANPHB-菲利普斯堡-PHILIPS BOURG',
    code: 'ANPHB'
  },
  {
    txt: 'ANSAB-萨巴-SABA',
    code: 'ANSAB'
  },
  {
    txt: 'ANSMB-圣米歇尔斯湾-ST.MICHIEL’S BAY',
    code: 'ANSMB'
  },
  {
    txt: 'ANSNB-圣尼古拉司湾-SAN NICOLAS BAY',
    code: 'ANSNB'
  },
  {
    txt: 'AOABZ-安布里什-AMBRIZ',
    code: 'AOABZ'
  },
  {
    txt: 'AOAMB-安布里泽特-AMBRIZETE',
    code: 'AOAMB'
  },
  {
    txt: 'AOBEN-本格拉-BENGUELA',
    code: 'AOBEN'
  },
  {
    txt: 'AOCAB-卡宾达-CABINDA',
    code: 'AOCAB'
  },
  {
    txt: 'CAPAS-帕斯佩比亚克-PASPEBIAC ',
    code: 'CAPAS'
  },
  {
    txt: 'CAPHA-霍克斯伯里港-PORT HAWKESBURY ',
    code: 'CAPHA'
  },
  {
    txt: 'CAPMU-穆尔格拉维港-PORT MULGRAVE',
    code: 'CAPMU'
  },
  {
    txt: 'CAPTU-图佩尔角-POINT TUPPER ',
    code: 'CAPTU'
  },
  {
    txt: 'CARDL-里维耶尔-迪卢-RIVIERE DU LOUP',
    code: 'CARDL'
  },
  {
    txt: 'CASAN-圣安德鲁斯-ST .ANDREWS ',
    code: 'CASAN'
  },
  {
    txt: 'CHZRH-苏黎世-ZURICH',
    code: 'CHZRH'
  },
  {
    txt: 'CLMEJ-梅希约内斯-MEJILLONES',
    code: 'CLMEJ'
  },
  {
    txt: 'CNABA-阿坝州-null',
    code: 'CNABA'
  },
  {
    txt: 'CNALL-全国各地-null',
    code: 'CNALL'
  },
  {
    txt: 'CNBAO-宝山-BAOSHAN',
    code: 'CNBAO'
  },
  {
    txt: 'CNBBU-蚌埠-BENGBU',
    code: 'CNBBU'
  },
  {
    txt: 'CNBJS-北京-BEIJING ',
    code: 'CNBJS'
  },
  {
    txt: 'CNBUR-普兰-BURANG',
    code: 'CNBUR'
  },
  {
    txt: 'CNCGO-郑州-ZHENGZHOU',
    code: 'CNCGO'
  },
  {
    txt: 'CNCHI-池洲-CHIZHOU',
    code: 'CNCHI'
  },
  {
    txt: 'CNCLJ-城陵矶-CHENGLINGJI',
    code: 'CNCLJ'
  },
  {
    txt: 'CNCTU-成都-CHENGDU',
    code: 'CNCTU'
  },
  {
    txt: 'CNDAI-大理-DALI',
    code: 'CNDAI'
  },
  {
    txt: 'CNDEY-德阳-DEYANG',
    code: 'CNDEY'
  },
  {
    txt: 'CNDLC-大连-DALIAN',
    code: 'CNDLC'
  },
  {
    txt: 'CNDOU-斗门-DOUMEN',
    code: 'CNDOU'
  },
  {
    txt: 'CNHNY-衡阳-HENGYANG',
    code: 'CNHNY'
  },
  {
    txt: 'CNHSI-黄石-HUANGSHI',
    code: 'CNHSI'
  },
  {
    txt: 'CNHUA-黄埔-HUANGPU',
    code: 'CNHUA'
  },
  {
    txt: 'CNHUL-虎林-HULIN',
    code: 'CNHUL'
  },
  {
    txt: 'CNINC-银川-YINCHUAN',
    code: 'CNINC'
  },
  {
    txt: 'CNJGZ-荆州-JINZHOU',
    code: 'CNJGZ'
  },
  {
    txt: 'CNJIJ-晋江-null',
    code: 'CNJIJ'
  },
  {
    txt: 'CNJIS-江山-JIANGSHAN',
    code: 'CNJIS'
  },
  {
    txt: 'CNJMN-江门-JIANGMEN',
    code: 'CNJMN'
  },
  {
    txt: 'CNJYG-嘉峪关-null',
    code: 'CNJYG'
  },
  {
    txt: 'CNKHG-喀什-KASHI',
    code: 'CNKHG'
  },
  {
    txt: 'CNKMG-昆明-KUNMING',
    code: 'CNKMG'
  },
  {
    txt: 'CNKWE-贵阳-GUIYANG',
    code: 'CNKWE'
  },
  {
    txt: 'CNLES-乐山-null',
    code: 'CNLES'
  },
  {
    txt: 'CNLIS-丽水-null',
    code: 'CNLIS'
  },
  {
    txt: 'CNLSH-旅顺-LUSHUN',
    code: 'CNLSH'
  },
  {
    txt: 'CNLXA-拉萨-LHASA',
    code: 'CNLXA'
  },
  {
    txt: 'CNLYM-老爷庙-LAOYEMIAO',
    code: 'CNLYM'
  },
  {
    txt: 'CNQZJ-泉州-QUANZHOU',
    code: 'CNQZJ'
  },
  {
    txt: 'CNRUI-瑞丽-RUILI',
    code: 'CNRUI'
  },
  {
    txt: 'CNSBN-绥滨-SUIBIN',
    code: 'CNSBN'
  },
  {
    txt: 'CNSEZ-嵊州-null',
    code: 'CNSEZ'
  },
  {
    txt: 'CNSHD-石岛-SHIDAO',
    code: 'CNSHD'
  },
  {
    txt: 'CNSHL-硕龙-SHUOLONG',
    code: 'CNSHL'
  },
  {
    txt: 'CNSHZ-石河子-null',
    code: 'CNSHZ'
  },
  {
    txt: 'CNSJQ-三水-SANSHUI',
    code: 'CNSJQ'
  },
  {
    txt: 'CNSOG-寿光-null',
    code: 'CNSOG'
  },
  {
    txt: 'CNSTJ-沙头角-SHATOUJIAO',
    code: 'CNSTJ'
  },
  {
    txt: 'CNSWE-汕尾-SHANWEI',
    code: 'CNSWE'
  },
  {
    txt: 'CNSYM-思茅-SIMAO',
    code: 'CNSYM'
  },
  {
    txt: 'CNTAC-太仓-TAICANG',
    code: 'CNTAC'
  },
  {
    txt: 'CNTBO-天保-TIANBAO',
    code: 'CNTBO'
  },
  {
    txt: 'CNTIZ-台州-null',
    code: 'CNTIZ'
  },
  {
    txt: 'CNTME-图们-TUMEN',
    code: 'CNTME'
  },
  {
    txt: 'CNTPW-太平湾-TAIPINGWAN',
    code: 'CNTPW'
  },
  {
    txt: 'CNYNJ-延吉-YANJI',
    code: 'CNYNJ'
  },
  {
    txt: 'CNYUF-云浮-YUNFU',
    code: 'CNYUF'
  },
  {
    txt: 'CNZEQ-珠恩噶达布其-ZHUENGEDABUQI',
    code: 'CNZEQ'
  },
  {
    txt: 'CNZHK-周口市-ZHOUKOU',
    code: 'CNZHK'
  },
  {
    txt: 'CNZPU-乍浦-ZHAPU',
    code: 'CNZPU'
  },
  {
    txt: 'CNZZU-漳州-ZHANGZHOU',
    code: 'CNZZU'
  },
  {
    txt: 'CRPCA-卡尔德拉港-PUERTO CALDERA',
    code: 'CRPCA'
  },
  {
    txt: 'CZBRA-伯拉第斯拉瓦-BRATISLAVA',
    code: 'CZBRA'
  },
  {
    txt: 'DEBHN-不来梅港-BREMERHAVEN',
    code: 'DEBHN'
  },
  {
    txt: 'DEBRU-布龙斯比特尔-BRUNSBUTTEL ',
    code: 'DEBRU'
  },
  {
    txt: 'DEDUI-杜伊斯堡-DUISBURG ',
    code: 'DEDUI'
  },
  {
    txt: 'DEFLE-弗伦斯堡-FLENSBURG ',
    code: 'DEFLE'
  },
  {
    txt: 'DEHEI-海利根港-HEILIGENHAFEN ',
    code: 'DEHEI'
  },
  {
    txt: 'DKELS-埃尔西诺-ELSINORE ',
    code: 'DKELS'
  },
  {
    txt: 'DKFRE-腓特烈西亚-FREDERICIA ',
    code: 'DKFRE'
  },
  {
    txt: 'DKGRE-格雷诺-GRENAA ',
    code: 'DKGRE'
  },
  {
    txt: 'DKHIR-希茨海尔斯-HIRTSHALS ',
    code: 'DKHIR'
  },
  {
    txt: 'DKHSD-海松-HADSUND ',
    code: 'DKHSD'
  },
  {
    txt: 'DKKER-凯特明讷-KERTEMINDE ',
    code: 'DKKER'
  },
  {
    txt: 'DKKYN-金比-KYNDBY ',
    code: 'DKKYN'
  },
  {
    txt: 'DKMAR-玛丽艾厄-MARIAGER ',
    code: 'DKMAR'
  },
  {
    txt: 'DKNAE-奈斯特韦兹-MAESTVED ',
    code: 'DKNAE'
  },
  {
    txt: 'ZAMOB-莫塞尔贝-MOSSEL BAY',
    code: 'ZAMOB'
  },
  {
    txt: 'DKSKN-斯卡恩-SKAGEN ',
    code: 'DKSKN'
  },
  {
    txt: 'DKSTE-斯泰厄-STEGE ',
    code: 'DKSTE'
  },
  {
    txt: 'ESALG-阿尔赫西拉斯　　　　　-ALGECIRAS',
    code: 'ESALG'
  },
  {
    txt: 'ESBLA-布雷拉　　　　　　　　-BURELA',
    code: 'ESBLA'
  },
  {
    txt: 'ESCBN-科尔库维翁　　　　　　-CORCUBION',
    code: 'ESCBN'
  },
  {
    txt: 'ESDEN-德尼亚　　　　　　　　-DENIA',
    code: 'ESDEN'
  },
  {
    txt: 'ESHUE-韦尔瓦　　　　　　　　-HUELVA',
    code: 'ESHUE'
  },
  {
    txt: 'ESMAD-马德里-MADRID',
    code: 'ESMAD'
  },
  {
    txt: 'IDBAL-巴厘巴板-BALIKPAPAN',
    code: 'IDBAL'
  },
  {
    txt: 'IDBLN-勿拉湾-BELAWAN',
    code: 'IDBLN'
  },
  {
    txt: 'IDBNA-伯诺阿-BENOA',
    code: 'IDBNA'
  },
  {
    txt: 'IDCIR-井里汶-CIREBON',
    code: 'IDCIR'
  },
  {
    txt: 'IDDON-栋加-DONGGALA',
    code: 'IDDON'
  },
  {
    txt: 'IDGSI-古农西托利-GUNUNG SITOLI',
    code: 'IDGSI'
  },
  {
    txt: 'IDKEN-肯达里-KENDARI',
    code: 'IDKEN'
  },
  {
    txt: 'IDKTB-哥打巴鲁-KOTA BARU',
    code: 'IDKTB'
  },
  {
    txt: 'IDMAN-马诺夸里-MANOKWARI',
    code: 'IDMAN'
  },
  {
    txt: 'IDMEU-米拉务-MEULABOH',
    code: 'IDMEU'
  },
  {
    txt: 'IDPAD-巴东-PADANG',
    code: 'IDPAD'
  },
  {
    txt: 'IDPAN-巴那鲁干-PANARUKAN',
    code: 'IDPAN'
  },
  {
    txt: 'IEBAN-班特里-BANTRY',
    code: 'IEBAN'
  },
  {
    txt: 'IEBUP-伯顿波特-BURTON PORT',
    code: 'IEBUP'
  },
  {
    txt: 'IECLO-克洛纳基尔蒂-CLONAKILTY',
    code: 'IECLO'
  },
  {
    txt: 'IEDON-多尼戈尔-DONEGAL',
    code: 'IEDON'
  },
  {
    txt: 'IEDUN-邓莫尔-DUNMORE',
    code: 'IEDUN'
  },
  {
    txt: 'IEGAL-戈尔韦-GALWAY',
    code: 'IEGAL'
  },
  {
    txt: 'IEKGS-基利贝格斯-KILLYBEGS',
    code: 'IEKGS'
  },
  {
    txt: 'IEKSH-基尔拉什-KILRUSH',
    code: 'IEKSH'
  },
  {
    txt: 'IERAT-拉斯马伦-RATHMULLEN',
    code: 'IERAT'
  },
  {
    txt: 'IETRA-特拉利-TRALEE',
    code: 'IETRA'
  },
  {
    txt: 'IEWIC-威克洛-WICKLOW',
    code: 'IEWIC'
  },
  {
    txt: 'ILASD-阿什杜德-ASHDOD',
    code: 'ILASD'
  },
  {
    txt: 'ILHFA-海法-HAIFA',
    code: 'ILHFA'
  },
  {
    txt: 'INBAE-班加罗尔-BANGALORE',
    code: 'INBAE'
  },
  {
    txt: 'INBHE-比穆尼帕特南-BHEEMUNIPATNAM',
    code: 'INBHE'
  },
  {
    txt: 'ITPEM-恩佩多克莱港-PORTO EMPEDOCLE',
    code: 'ITPEM'
  },
  {
    txt: 'ITPOZ-波佐利-POZZUOLI',
    code: 'ITPOZ'
  },
  {
    txt: 'ITPSS-圣托斯特凡诺港-PORTO SANTO STEFANO',
    code: 'ITPSS'
  },
  {
    txt: 'ITRMA-里奥马里纳-RIO MARINA',
    code: 'ITRMA'
  },
  {
    txt: 'ITSPE-斯佩齐亚-SPEZIA',
    code: 'ITSPE'
  },
  {
    txt: 'ITTAR-塔兰托-TARANTO',
    code: 'ITTAR'
  },
  {
    txt: 'ITVAS-瓦斯托-VASTO',
    code: 'ITVAS'
  },
  {
    txt: 'JMBLR-布莱克河-B1ACK RIVER',
    code: 'JMBLR'
  },
  {
    txt: 'JMLUC-卢西-LUCEA',
    code: 'JMLUC'
  },
  {
    txt: 'JMPAN-安东尼奥港-PORT ANTONIO',
    code: 'JMPAN'
  },
  {
    txt: 'JMPRO-罗亚尔港-PORT ROYAL',
    code: 'JMPRO'
  },
  {
    txt: 'JMSLM-滨海萨凡纳-SAVANNALA MAR',
    code: 'JMSLM'
  },
  {
    txt: 'INPUR-普里-PURI',
    code: 'INPUR'
  },
  {
    txt: 'INRED-雷迪-REDI',
    code: 'INRED'
  },
  {
    txt: 'INSUR-苏拉特-SURAT',
    code: 'INSUR'
  },
  {
    txt: 'INTUT-杜蒂戈林-TUTICORIN',
    code: 'INTUT'
  },
  {
    txt: 'IQFAO-法奥-FAO',
    code: 'IQFAO'
  },
  {
    txt: 'IQUMQ-乌姆盖斯尔-UMM QASR',
    code: 'IQUMQ'
  },
  {
    txt: 'IRBUS-布什尔-BUSHIRE',
    code: 'IRBUS'
  },
  {
    txt: 'IRKHO-霍拉姆沙赫尔-KHORRAMSHAHR',
    code: 'IRKHO'
  },
  {
    txt: 'ISAKU-阿克雷里-AKUREYRI',
    code: 'ISAKU'
  },
  {
    txt: 'ISKEF-凯夫拉维克-KEFLAVLK',
    code: 'ISKEF'
  },
  {
    txt: 'ISSIG-锡格吕菲厄泽-SIGLUFJORD',
    code: 'ISSIG'
  },
  {
    txt: 'ITALG-阿尔盖罗-A1GHERO',
    code: 'ITALG'
  },
  {
    txt: 'GBLOP-洛哈林-LOCHALINE PIER ',
    code: 'GBLOP'
  },
  {
    txt: 'GBLYD-利德尼-LYDNEY ',
    code: 'GBLYD'
  },
  {
    txt: 'GBMIN-迈恩黑德-MINEHEAD ',
    code: 'GBMIN'
  },
  {
    txt: 'GBMOS-莫斯廷-MOSTYN ',
    code: 'GBMOS'
  },
  {
    txt: 'GBNEA-尼思-NEATH ',
    code: 'GBNEA'
  },
  {
    txt: 'GBNPT-纽波特-NEWPORT (I.O.W.)',
    code: 'GBNPT'
  },
  {
    txt: 'GBSCA-斯卡伯勒-SCARBOROUGH ',
    code: 'GBSCA'
  },
  {
    txt: 'GBSEL-塞尔比-SELBY ',
    code: 'GBSEL'
  },
  {
    txt: 'GBSHN-斯通黑文-STONEHAVEN',
    code: 'GBSHN'
  },
  {
    txt: 'GBSOU-南安普顿-SOUTHAMPTON',
    code: 'GBSOU'
  },
  {
    txt: 'GBSUB-萨顿布里奇-SUTTON BRIDGE',
    code: 'GBSUB'
  },
  {
    txt: 'GIGIB-直布罗陀-GIBRALTAR',
    code: 'GIGIB'
  },
  {
    txt: 'GLFRE-腓特烈斯霍布-FREDERIKSHAAB',
    code: 'GLFRE'
  },
  {
    txt: 'GLJAK-雅格布港-JAKOBSHAVN',
    code: 'GLJAK'
  },
  {
    txt: 'GLNAR-纳萨尔苏瓦克-NARSSARSSUAQ',
    code: 'GLNAR'
  },
  {
    txt: 'MYPDI-波德申-PORT DICKSON',
    code: 'MYPDI'
  },
  {
    txt: 'MYPRI-布莱-PRAI',
    code: 'MYPRI'
  },
  {
    txt: 'MYSAT-萨哈巴特-SAHABAT',
    code: 'MYSAT'
  },
  {
    txt: 'MYSUM-双溪麻坡-SUNGEI MUAR',
    code: 'MYSUM'
  },
  {
    txt: 'MYTRA-特洛拉穆尼亚-TELOK RAMUNIA',
    code: 'MYTRA'
  },
  {
    txt: 'MZBRA-贝拉-BEIRA',
    code: 'MZBRA'
  },
  {
    txt: 'MZMAP-马普托-MAPUTO',
    code: 'MZMAP'
  },
  {
    txt: 'NLEUR-欧罗波特-EUROPOORT',
    code: 'NLEUR'
  },
  {
    txt: 'NLHEL-海尔蒙德-HELMOND',
    code: 'NLHEL'
  },
  {
    txt: 'NLMAS-马斯莱斯-MAASSLUIS',
    code: 'NLMAS'
  },
  {
    txt: 'NLSCH-斯海弗宁恩-SCHEVENINGEN',
    code: 'NLSCH'
  },
  {
    txt: 'NLTER-泰尔讷曾-TERNEUZEN',
    code: 'NLTER'
  },
  {
    txt: 'NLYMU-伊穆伊登-YMUIDEN',
    code: 'NLYMU'
  },
  {
    txt: 'NLZWO-兹沃勒-ZWOLLE',
    code: 'NLZWO'
  },
  {
    txt: 'NOARE-阿伦达尔-ARENDAL',
    code: 'NOARE'
  },
  {
    txt: 'NOBRO-布伦讷于松-BRONNOYSUND',
    code: 'NOBRO'
  },
  {
    txt: 'NOFAR-法尔松德-FARSUND',
    code: 'NOFAR'
  },
  {
    txt: 'NOGLO-格洛姆菲尤尔-GLOMFJORD',
    code: 'NOGLO'
  },
  {
    txt: 'NOHAU-海尔格松-HAUGESUND',
    code: 'NOHAU'
  },
  {
    txt: 'NOHOY-赫扬厄尔-HOYANGER',
    code: 'NOHOY'
  },
  {
    txt: 'NOTOF-托夫特-TOFTE',
    code: 'NOTOF'
  },
  {
    txt: 'NOTVE-特维德斯特兰德-TVEDESTRAND',
    code: 'NOTVE'
  },
  {
    txt: 'NPKAU-加德满都-KATHMANDU',
    code: 'NPKAU'
  },
  {
    txt: 'NZBIF-布拉夫-BLUFF',
    code: 'NZBIF'
  },
  {
    txt: 'NZGIS-吉斯珀恩-GISBORNE',
    code: 'NZGIS'
  },
  {
    txt: 'NZMMI-芒特芒阿努伊-MOUNT MAUNGANUI',
    code: 'NZMMI'
  },
  {
    txt: 'NZONE-奥尼洪加-ONEHUNGA',
    code: 'NZONE'
  },
  {
    txt: 'NZPRU-罗塞尔港-PON RUSSELL',
    code: 'NZPRU'
  },
  {
    txt: 'NZWAI-怀劳-WAIRAU',
    code: 'NZWAI'
  },
  {
    txt: 'NZWES-韦斯特皮特-WESTPORT',
    code: 'NZWES'
  },
  {
    txt: 'OMMUT-马特拉-MUTTRAH',
    code: 'OMMUT'
  },
  {
    txt: 'PAALM-阿尔米兰特-A1MIRANTE',
    code: 'PAALM'
  },
  {
    txt: 'PACRI-克里斯托瓦尔-CRISTOBAL',
    code: 'PACRI'
  },
  {
    txt: 'PAVAC-巴卡蒙特-VACAMONTE',
    code: 'PAVAC'
  },
  {
    txt: 'PECAZ-塞罗阿苏尔-CERRO AZUL',
    code: 'PECAZ'
  },
  {
    txt: 'PGKBE-金贝-KIMBE',
    code: 'PGKBE'
  },
  {
    txt: 'PGMAD-马当-MADANG',
    code: 'PGMAD'
  },
  {
    txt: 'PGRAB-腊包尔-RABAUL',
    code: 'PGRAB'
  },
  {
    txt: 'PHABU-阿布约-ABUYOG',
    code: 'PHABU'
  },
  {
    txt: 'PHAPA-阿帕里-APARRI',
    code: 'PHAPA'
  },
  {
    txt: 'PHBIS-比斯利格-BISLIG',
    code: 'PHBIS'
  },
  {
    txt: 'PHCAL-甲描育-CALBAYOG',
    code: 'PHCAL'
  },
  {
    txt: 'PHCDZ-加的斯-CADIZ(PH)',
    code: 'PHCDZ'
  },
  {
    txt: 'PHDGT-迪纳加特-DINAGAT',
    code: 'PHDGT'
  },
  {
    txt: 'PHHIN-希尼加兰-HINIGARAN',
    code: 'PHHIN'
  },
  {
    txt: 'PHJOL-霍洛-JOLO',
    code: 'PHJOL'
  },
  {
    txt: 'PHLEG-黎牙实比-LEGASPI',
    code: 'PHLEG'
  },
  {
    txt: 'PHMAR-马里韦莱斯-MARIVELES',
    code: 'PHMAR'
  },
  {
    txt: 'USALE-亚历山德里亚-A1EXANDRIA',
    code: 'USALE'
  },
  {
    txt: 'USASD-阿什兰-ASHLAND',
    code: 'USASD'
  },
  {
    txt: 'USATR-阿特雷科-ATRECO',
    code: 'USATR'
  },
  {
    txt: 'USBCG-波卡洛兰德-BOCA GRANDE',
    code: 'USBCG'
  },
  {
    txt: 'USBER-伯克利-BERKELEY',
    code: 'USBER'
  },
  {
    txt: 'USBRO-布朗斯维尔-BROWNSVILLE',
    code: 'USBRO'
  },
  {
    txt: 'USBUH-伯恩斯港-BURNS HARBOUR',
    code: 'USBUH'
  },
  {
    txt: 'USCAR-卡拉贝尔-CARRABELLE',
    code: 'USCAR'
  },
  {
    txt: 'USCHI-芝加哥-CHICAGO',
    code: 'USCHI'
  },
  {
    txt: 'USCLD-克里夫兰-CLEVELAND',
    code: 'USCLD'
  },
  {
    txt: 'USCOR-科尔多瓦-CORDOVA',
    code: 'USCOR'
  },
  {
    txt: 'USDON-唐纳森维尔-DONALDSONVILLE',
    code: 'USDON'
  },
  {
    txt: 'USLOR-洛雷恩-LORAIN',
    code: 'USLOR'
  },
  {
    txt: 'USMIA-迈阿密-MIAMI',
    code: 'USMIA'
  },
  {
    txt: 'USMOB-莫比尔-MOBILE',
    code: 'USMOB'
  },
  {
    txt: 'USMOV-维农山-MOUNT VERNON',
    code: 'USMOV'
  },
  {
    txt: 'USNDN-新伦敦-NEW LONDON(CONN．)',
    code: 'USNDN'
  },
  {
    txt: 'USNHA-纽黑文-NEW HAVEN',
    code: 'USNHA'
  },
  {
    txt: 'USNOM-诺姆-NOME',
    code: 'USNOM'
  },
  {
    txt: 'USNYK-纽约-NEW YORK',
    code: 'USNYK'
  },
  {
    txt: 'USOST-奥斯特里卡-OSTRICA',
    code: 'USOST'
  },
  {
    txt: 'USPAN-安吉利斯港-PORT ANGELES',
    code: 'USPAN'
  },
  {
    txt: 'USPEN-彭萨科拉-PENSACOLA',
    code: 'USPEN'
  },
  {
    txt: 'USPHU-怀尼米港-PORT HUENEME',
    code: 'USPHU'
  },
  {
    txt: 'YESAA-萨那-SANAA',
    code: 'YESAA'
  },
  {
    txt: 'YUBAR-巴尔-BAR',
    code: 'YUBAR'
  },
  {
    txt: 'ZACPT-开普敦-CAPE TOWN',
    code: 'ZACPT'
  },
  {
    txt: 'RUMAG-马加丹-MAGADAN',
    code: 'RUMAG'
  },
  {
    txt: 'RUMAK-马卡洛夫-MAKAROV',
    code: 'RUMAK'
  },
  {
    txt: 'ZASIM-西蒙斯敦-SIMONSTOWN',
    code: 'ZASIM'
  },
  {
    txt: 'USTPK-托皮卡-TOPEKA',
    code: 'USTPK'
  },
  {
    txt: 'GMBAN-班珠尔-BANJUL',
    code: 'GMBAN'
  },
  {
    txt: 'GRPAT-佩特雷-PATRAS',
    code: 'GRPAT'
  },
  {
    txt: 'GRPVA-瓦锡港-PORT VATHY',
    code: 'GRPVA'
  },
  {
    txt: 'GRSDB-苏达湾-SUDABAY',
    code: 'GRSDB'
  },
  {
    txt: 'GRTHE-塞萨洛尼基-THESSALONIKI',
    code: 'GRTHE'
  },
  {
    txt: 'GTCHA-钱佩里科-CHAMPERICO',
    code: 'GTCHA'
  },
  {
    txt: 'GTSTC-圣托马斯-SANTO TOMAS DE CASTILLA',
    code: 'GTSTC'
  },
  {
    txt: 'GWBUB-布巴克-BUBAQUE',
    code: 'GWBUB'
  },
  {
    txt: 'PLWRO-弗罗茨瓦夫-WROCLAW',
    code: 'PLWRO'
  },
  {
    txt: 'PRARE-阿雷西沃-ARECIBO',
    code: 'PRARE'
  },
  {
    txt: 'PRGUA-瓜亚尼亚-GUAYANILLA',
    code: 'PRGUA'
  },
  {
    txt: 'PRSIU-圣胡安-SAN UAN',
    code: 'PRSIU'
  },
  {
    txt: 'ACHOR-奥尔塔-HORTA',
    code: 'ACHOR'
  },
  {
    txt: 'PTAVE-阿威罗-AVEIRO',
    code: 'PTAVE'
  },
  {
    txt: 'PTFIG-菲盖拉-FIGUEIRA',
    code: 'PTFIG'
  },
  {
    txt: 'PTOPO-波尔图-OPORTO',
    code: 'PTOPO'
  },
  {
    txt: 'PTSNS-锡尼什-SINES',
    code: 'PTSNS'
  },
  {
    txt: 'PYCDE-埃斯特城-CIUDAD DEL ESTE',
    code: 'PYCDE'
  },
  {
    txt: 'RESDE-圣但尼-ST．DENIS',
    code: 'RESDE'
  },
  {
    txt: 'ROBUT-布加勒斯特-BUCHAREST',
    code: 'ROBUT'
  },
  {
    txt: 'ROTUL-图耳恰-TULCEA',
    code: 'ROTUL'
  },
  {
    txt: 'RUDKA-德卡斯特莱-DEKASTRI',
    code: 'RUDKA'
  },
  {
    txt: 'JPIOM-伊予三岛-IYOMISHIMA',
    code: 'JPIOM'
  },
  {
    txt: 'JPISI-石垣-ISHIGAKI',
    code: 'JPISI'
  },
  {
    txt: 'JPIZA-严原-IZUHARA',
    code: 'JPIZA'
  },
  {
    txt: 'JPKAK-加古川-KAKOGAWA',
    code: 'JPKAK'
  },
  {
    txt: 'JPKAR-唐津-KARATSU',
    code: 'JPKAR'
  },
  {
    txt: 'JPKIK-菊间-KIKUMA',
    code: 'JPKIK'
  },
  {
    txt: 'JPKKA-笠冈-KASAOKA',
    code: 'JPKKA'
  },
  {
    txt: 'JPKOB-神户-KOBE',
    code: 'JPKOB'
  },
  {
    txt: 'JPKOK-小仓-KOKURA',
    code: 'JPKOK'
  },
  {
    txt: 'JPKUD-下松-KUDAMATSU',
    code: 'JPKUD'
  },
  {
    txt: 'JPKWA-鹿川-KANOKAWA',
    code: 'JPKWA'
  },
  {
    txt: 'JPMAK-牧山-MAKIYAMA',
    code: 'JPMAK'
  },
  {
    txt: 'JPMEG-妻鹿-MEGA',
    code: 'JPMEG'
  },
  {
    txt: 'JPMIK-美川-MIKAWA',
    code: 'JPMIK'
  },
  {
    txt: 'JPMIY-宫崎-MIYAZAKI',
    code: 'JPMIY'
  },
  {
    txt: 'JPMKO-宫古-MIYAKO',
    code: 'JPMKO'
  },
  {
    txt: 'JPMON-门别-MONBETSU',
    code: 'JPMON'
  },
  {
    txt: 'JPMUK-向岛-MUKAISHIMA',
    code: 'JPMUK'
  },
  {
    txt: 'JPNAG-名古屋-NAGOYA',
    code: 'JPNAG'
  },
  {
    txt: 'JPNAK-中城-NAKAGUSUKU',
    code: 'JPNAK'
  },
  {
    txt: 'JPNEG-根岸-NEGISHI',
    code: 'JPNEG'
  },
  {
    txt: 'JPNHI-新渴东-NIIGATA HIGASHI',
    code: 'JPNHI'
  },
  {
    txt: 'JPNOS-能代-NOSHIRO',
    code: 'JPNOS'
  },
  {
    txt: 'JPOGI-扇岛-OGISHIMA',
    code: 'JPOGI'
  },
  {
    txt: 'JPOMI-大三岛-OMISHIMA',
    code: 'JPOMI'
  },
  {
    txt: 'JPOSK-大阪-OSAKA',
    code: 'JPOSK'
  },
  {
    txt: 'JPOWA-尾鹫-OWASE',
    code: 'JPOWA'
  },
  {
    txt: 'JPTSI-鹤崎-TSURUSAKI',
    code: 'JPTSI'
  },
  {
    txt: 'JPUCH-内浦-UCHIURA',
    code: 'JPUCH'
  },
  {
    txt: 'JPWKA-和歌山-WAKAYAMA',
    code: 'JPWKA'
  },
  {
    txt: 'JPYAW-八幡-YAWATA',
    code: 'JPYAW'
  },
  {
    txt: 'JPYOK-横滨-YOKOHAMA',
    code: 'JPYOK'
  },
  {
    txt: 'JPYUR-由良-YUFA',
    code: 'JPYUR'
  },
  {
    txt: 'KEMOM-蒙巴萨-MOMBASA',
    code: 'KEMOM'
  },
  {
    txt: 'KHPPH-金边-PHNOM-PENH',
    code: 'KHPPH'
  },
  {
    txt: 'KITAI-塔拉瓦岛-TARAWA ISLAND',
    code: 'KITAI'
  },
  {
    txt: 'KNBAS-巴斯特尔-BASSETERRE',
    code: 'KNBAS'
  },
  {
    txt: 'KPHUN-兴南-HUNGNAM',
    code: 'KPHUN'
  },
  {
    txt: 'KPWON-元山-WONSAN',
    code: 'KPWON'
  },
  {
    txt: 'KRDAE-台普-DAEPORI',
    code: 'KRDAE'
  },
  {
    txt: 'KRKWY-光阳-KWANG YANG',
    code: 'KRKWY'
  },
  {
    txt: 'KRPOH-浦项-POHANG',
    code: 'KRPOH'
  },
  {
    txt: 'KRSEO-首尔-SEOUL',
    code: 'KRSEO'
  },
  {
    txt: 'KWKAM-霍尔姆法塔-KHOR AL MUFATTA',
    code: 'KWKAM'
  },
  {
    txt: 'KWMSD-米纳索特-MENA SAUD',
    code: 'KWMSD'
  },
  {
    txt: 'KZALY-阿马阿塔-ALMATY',
    code: 'KZALY'
  },
  {
    txt: 'MAAGA-阿加迪尔-AGADIR',
    code: 'MAAGA'
  },
  {
    txt: 'MAJOL-朱尔夫莱斯费尔-JORF LASFAR',
    code: 'MAJOL'
  },
  {
    txt: 'MANAD-纳祖尔-NADOR',
    code: 'MANAD'
  },
  {
    txt: 'MATAR-塔尔法亚-TARFAYA',
    code: 'MATAR'
  },
  {
    txt: 'MGANT-塔那那利佛-ANTANANARIVO',
    code: 'MGANT'
  },
  {
    txt: 'MGMBE-穆龙贝-MOROMBE',
    code: 'MGMBE'
  },
  {
    txt: 'MGSAM-桑巴瓦-SAMBAVA',
    code: 'MGSAM'
  },
  {
    txt: 'MGTRO-托拉纳罗-TOLAGNATO',
    code: 'MGTRO'
  },
  {
    txt: 'MMMOU-毛淡棉-MOULMEIN',
    code: 'MMMOU'
  },
  {
    txt: 'MMTEN-德林达伊-TENASSERIM',
    code: 'MMTEN'
  },
  {
    txt: 'MYBRM-巴拉姆-BARAM',
    code: 'MYBRM'
  },
  {
    txt: 'MYDUN-龙运-DUNGUN',
    code: 'MYDUN'
  },
  {
    txt: 'MYIDU-隆杜-LUNDU',
    code: 'MYIDU'
  },
  {
    txt: 'MYKOB-哥打巴鲁-KOTA BHARU',
    code: 'MYKOB'
  },
  {
    txt: 'MYKSE-瓜拉雪兰莪-KUALA SELANGOR',
    code: 'MYKSE'
  },
  {
    txt: 'MYKUC-古晋-KUCHING',
    code: 'MYKUC'
  },
  {
    txt: 'MYLAB-拉布安-LABUAN',
    code: 'MYLAB'
  },
  {
    txt: 'MYMEI-马六甲-MELAKA',
    code: 'MYMEI'
  },
  {
    txt: 'RUMGO-马戈-MAGO',
    code: 'RUMGO'
  },
  {
    txt: 'RUMMK-摩尔曼斯克-MURMANSK',
    code: 'RUMMK'
  },
  {
    txt: 'RUMSC-莫斯科-MOSCOW, RUSSIA',
    code: 'RUMSC'
  },
  {
    txt: 'RUNEV-涅韦尔斯克-NEVELSK',
    code: 'RUNEV'
  },
  {
    txt: 'VEPPS-南帕尔马斯角-PUNTA PALMAS',
    code: 'VEPPS'
  },
  {
    txt: 'VEPSU-苏克里港-PUERTO SUCRE',
    code: 'VEPSU'
  },
  {
    txt: 'VEPTR-潘帕塔尔-PAMPATAR',
    code: 'VEPTR'
  },
  {
    txt: 'VESFX-圣费利克斯-SAN FELIX',
    code: 'VESFX'
  },
  {
    txt: 'VESLO-圣洛伦索-SAN LORENZO',
    code: 'VESLO'
  },
  {
    txt: 'VETUC-图卡卡斯-TUCACAS',
    code: 'VETUC'
  },
  {
    txt: 'VETUR-图里亚莫-TURIAMO',
    code: 'VETUR'
  },
  {
    txt: 'VGTOR-托尔托拉-TORTOLA',
    code: 'VGTOR'
  },
  {
    txt: 'VICHR-克里斯琴斯特德-CHRISTIANSTED',
    code: 'VICHR'
  },
  {
    txt: 'VIFRE-弗雷德里克斯特德-FREDERIKSTED',
    code: 'VIFRE'
  },
  {
    txt: 'VISTS-圣托马斯-ST．THOMAS',
    code: 'VISTS'
  },
  {
    txt: 'VNBTI-边水-BEN THUI',
    code: 'VNBTI'
  },
  {
    txt: 'VNDAN-岘港-DA-NANG',
    code: 'VNDAN'
  },
  {
    txt: 'VNHAN-河内-HANOI',
    code: 'VNHAN'
  },
  {
    txt: 'VNHCM-胡志明市-HO CHIMINH CITY',
    code: 'VNHCM'
  },
  {
    txt: 'VNHGY-鸿基-HONGAY',
    code: 'VNHGY'
  },
  {
    txt: 'VNHPG-海防-HAIPHONG',
    code: 'VNHPG'
  },
  {
    txt: 'VNHUE-顺化-HUE',
    code: 'VNHUE'
  },
  {
    txt: 'VNKYN-广义-KWANG YEN',
    code: 'VNKYN'
  },
  {
    txt: 'VNMYT-美富-MY THO',
    code: 'VNMYT'
  },
  {
    txt: 'VNNTG-芽庄-NHA TRANG',
    code: 'VNNTG'
  },
  {
    txt: 'VNPCA-锦普港-PORT CAMPHA',
    code: 'VNPCA'
  },
  {
    txt: 'VNPRE-雷东港-PORT REDON',
    code: 'VNPRE'
  },
  {
    txt: 'VNQUN-归仁-QUI NHON',
    code: 'VNQUN'
  },
  {
    txt: 'VNVIN-荣市-VINH',
    code: 'VNVIN'
  },
  {
    txt: 'VNVUT-头顿-VUNG TAU',
    code: 'VNVUT'
  },
  {
    txt: 'VULUB-卢甘维尔港-LUGANVILLEBAY',
    code: 'VULUB'
  },
  {
    txt: 'VUPVI-维拉港-PORTVILA',
    code: 'VUPVI'
  },
  {
    txt: 'WSAPI-阿皮亚-APIA',
    code: 'WSAPI'
  },
  {
    txt: 'YEADN-亚丁-ADEN',
    code: 'YEADN'
  },
  {
    txt: 'YEHOD-荷台达-HODEIDAH',
    code: 'YEHOD'
  },
  {
    txt: 'YEMOK-穆哈-MOKHA',
    code: 'YEMOK'
  },
  {
    txt: 'YEMUK-穆卡拉-MUKALLA',
    code: 'YEMUK'
  },
  {
    txt: 'YERAK-卡塞卜角-RASALKATHEEB',
    code: 'YERAK'
  },
  {
    txt: 'YESLF-萨利夫-SALEEF',
    code: 'YESLF'
  },
  {
    txt: 'YESOI-索科特拉岛-SOCOTRAISLAND',
    code: 'YESOI'
  },
  {
    txt: 'YUKOT-科托尔-KOTOR',
    code: 'YUKOT'
  },
  {
    txt: 'YUZAG-萨格勒布-ZAGREB',
    code: 'YUZAG'
  },
  {
    txt: 'YUZEI-泽莱尼卡-ZELENIKA',
    code: 'YUZEI'
  },
  {
    txt: 'ZADUR-德班-DURBAN',
    code: 'ZADUR'
  },
  {
    txt: 'ZAELN-东伦敦-EAST LONDON',
    code: 'ZAELN'
  },
  {
    txt: 'ZAJPG-约翰内斯堡-JOHANNESBURG',
    code: 'ZAJPG'
  },
  {
    txt: 'ZAPEI-伊丽莎白港-PORT ELIZABETH',
    code: 'ZAPEI'
  },
  {
    txt: 'ZARIB-里查德湾-RICHARDS BAY',
    code: 'ZARIB'
  },
  {
    txt: 'ZASAB-萨尔达尼亚湾-SALDANHA BAY',
    code: 'ZASAB'
  },
  {
    txt: 'ZMLUS-卢萨卡-LUSAKA',
    code: 'ZMLUS'
  },
  {
    txt: 'FIKRI-克里斯蒂纳-KRISTIINA ',
    code: 'FIKRI'
  },
  {
    txt: 'FIMER-梅里卡尔维亚-MERIKARVIA',
    code: 'FIMER'
  },
  {
    txt: 'FIPAT-帕特尼安密-PATENIEMI ',
    code: 'FIPAT'
  },
  {
    txt: 'FIPVO-波尔沃-PORVOO ',
    code: 'FIPVO'
  },
  {
    txt: 'FISKU-斯库卢-SKURU ',
    code: 'FISKU'
  },
  {
    txt: 'FITOP-托比拉-TOPPILA ',
    code: 'FITOP'
  },
  {
    txt: 'FIVAS-瓦斯克尔良托-VASKILUOTO ',
    code: 'FIVAS'
  },
  {
    txt: 'FJSUA-苏瓦-SUVA',
    code: 'FJSUA'
  },
  {
    txt: 'FRAJA-阿维克修-AJACCIO',
    code: 'FRAJA'
  },
  {
    txt: 'FRLOR-洛里昂-LORIENT ',
    code: 'FRLOR'
  },
  {
    txt: 'FRMON-蒙托伊尔-MONTOIR ',
    code: 'FRMON'
  },
  {
    txt: 'FRNTS-南特-NANTES ',
    code: 'FRNTS'
  },
  {
    txt: 'FRPAR-巴黎-PARIS',
    code: 'FRPAR'
  },
  {
    txt: 'FRPLA-彭拉贝-PONT L’ ABBE ',
    code: 'FRPLA'
  },
  {
    txt: 'FRPVO-韦基奥港-PORTO VECCHIO',
    code: 'FRPVO'
  },
  {
    txt: 'FRROU-鲁昂-ROUEN ',
    code: 'FRROU'
  },
  {
    txt: 'FRSJL-圣让德吕兹-ST. JEAN DE LUZ',
    code: 'FRSJL'
  },
  {
    txt: 'FRSTR-斯特拉斯堡-STRASBOURG ',
    code: 'FRSTR'
  },
  {
    txt: 'USPLD-波特兰-PORT AND(ME．)',
    code: 'USPLD'
  },
  {
    txt: 'USPLY-普列茅斯-P1YMOUTH',
    code: 'USPLY'
  },
  {
    txt: 'USPMT-普利茅斯-PLYMOUTH',
    code: 'USPMT'
  },
  {
    txt: 'USPNA-帕萨迪纳-PASADENA',
    code: 'USPNA'
  },
  {
    txt: 'USPNE-内奇斯港-PORT NECHES',
    code: 'USPNE'
  },
  {
    txt: 'USPNP-皮内角-PINEY POINT',
    code: 'USPNP'
  },
  {
    txt: 'USPRE-普罗维登斯-PROVIDENCE',
    code: 'USPRE'
  },
  {
    txt: 'USPRO-罗亚尔港-PORT ROYAL',
    code: 'USPRO'
  },
  {
    txt: 'USPSH-波次茅斯-PORTSMOUTH',
    code: 'USPSH'
  },
  {
    txt: 'USPSI-圣路易斯港-PORT SANLUIS',
    code: 'USPSI'
  },
  {
    txt: 'USPSJ-圣乔港-PORT ST．JOE',
    code: 'USPSJ'
  },
  {
    txt: 'USPSU-萨尔弗港-PORT SULPHUR',
    code: 'USPSU'
  },
  {
    txt: 'USPTD-波特兰-PORTLAND(OREG．)',
    code: 'USPTD'
  },
  {
    txt: 'USPTO-汤森港-PORT TOWNSEND',
    code: 'USPTO'
  },
  {
    txt: 'USQCY-昆西-QUINCY',
    code: 'USQCY'
  },
  {
    txt: 'USRAC-拉辛-RACINE',
    code: 'USRAC'
  },
  {
    txt: 'USRCD-里士满-RICHMOND(VA．)',
    code: 'USRCD'
  },
  {
    txt: 'USRIC-里士满-RICHMOND(CAL．)',
    code: 'USRIC'
  },
  {
    txt: 'USRIL-米湖-RICE LACK',
    code: 'USRIL'
  },
  {
    txt: 'USRIV-里弗黑德-RIVERHEAD',
    code: 'USRIV'
  },
  {
    txt: 'USROC-罗切斯特-ROCHESTER',
    code: 'USROC'
  },
  {
    txt: 'USROS-罗斯威尔-ROSWELL',
    code: 'USROS'
  },
  {
    txt: 'USSAB-萨宾-SABINE',
    code: 'USSAB'
  },
  {
    txt: 'USSAC-萨克拉门托-SACRAMENTO',
    code: 'USSAC'
  },
  {
    txt: 'USSAV-萨凡纳-SAVANNAH(GA．)',
    code: 'USSAV'
  },
  {
    txt: 'USSBA-圣巴巴拉-SANTA BARBARA',
    code: 'USSBA'
  },
  {
    txt: 'USSCO-斯科特斯戴尔-SCOTTSDALE',
    code: 'USSCO'
  },
  {
    txt: 'USSCR-圣克鲁斯-SANTA CRUZ(CALIF．)',
    code: 'USSCR'
  },
  {
    txt: 'USSDI-圣迭戈-SAN DIEGO',
    code: 'USSDI'
  },
  {
    txt: 'USSEA-西雅图-SEATTLE',
    code: 'USSEA'
  },
  {
    txt: 'USSEW-苏厄德-SEWARD',
    code: 'USSEW'
  },
  {
    txt: 'USSFO-圣弗朗西斯科-SAN FRANCISCO',
    code: 'USSFO'
  },
  {
    txt: 'USSIT-锡特卡-SITKA',
    code: 'USSIT'
  },
  {
    txt: 'USSKA-斯卡圭-SKAGWAY',
    code: 'USSKA'
  },
  {
    txt: 'USSLC-盐湖城-Salt Lake City',
    code: 'USSLC'
  },
  {
    txt: 'USSMB-斯密斯布卢夫-SMITH’SBLUFF',
    code: 'USSMB'
  },
  {
    txt: 'USSPE-圣佩德罗-SAN PEDRO',
    code: 'USSPE'
  },
  {
    txt: 'USSPG-圣彼得斯堡-ST．PETERSBURG',
    code: 'USSPG'
  },
  {
    txt: 'USSPT-锡斯波特-SEARSPORT',
    code: 'USSPT'
  },
  {
    txt: 'USSTO-斯托克顿-STOCKTON',
    code: 'USSTO'
  },
  {
    txt: 'USTAC-塔科马-TACOMA',
    code: 'USTAC'
  },
  {
    txt: 'USTAH-塔科尼特港-TACONITE HARBOUR',
    code: 'USTAH'
  },
  {
    txt: 'USTAM-坦帕-TAMPA',
    code: 'USTAM'
  },
  {
    txt: 'USTOL-托莱多-TOLEDO',
    code: 'USTOL'
  },
  {
    txt: 'USTRE-特伦顿-TRENTON',
    code: 'USTRE'
  },
  {
    txt: 'USTXC-得克萨斯城-TEXAS CITY',
    code: 'USTXC'
  },
  {
    txt: 'USVAL-瓦尔迪斯-VALDEZ',
    code: 'USVAL'
  },
  {
    txt: 'USWAS-华盛顿-WASHINGTON',
    code: 'USWAS'
  },
  {
    txt: 'USWEE-威霍肯-WEEHAWKEN',
    code: 'USWEE'
  },
  {
    txt: 'USWIL-威尔明顿-WILMINGTON(CAL．)',
    code: 'USWIL'
  },
  {
    txt: 'USWPA-威拉帕-WILLAPA',
    code: 'USWPA'
  },
  {
    txt: 'USWRA-兰格尔-WRANGELL',
    code: 'USWRA'
  },
  {
    txt: 'USWTN-威尔明顿-WILMINGTON(N．C．)',
    code: 'USWTN'
  },
  {
    txt: 'UYCOL-科洛尼亚-COLONIA',
    code: 'UYCOL'
  },
  {
    txt: 'UYJIO-何塞伊格纳西奥-JOSE IGNACIO',
    code: 'UYJIO'
  },
  {
    txt: 'UYMAL-马尔多纳多-MALDONADO',
    code: 'UYMAL'
  },
  {
    txt: 'UYMON-蒙得维的亚-MONTEVIDEO',
    code: 'UYMON'
  },
  {
    txt: 'UYNPA-新帕尔米拉-NUEVA PALMIRA',
    code: 'UYNPA'
  },
  {
    txt: 'UYPDE-埃斯特角-PUNTA DEL ESTE',
    code: 'UYPDE'
  },
  {
    txt: 'UZKAS-喀山-KASAN',
    code: 'UZKAS'
  },
  {
    txt: 'UZTAT-塔什干-TASHKENT',
    code: 'UZTAT'
  },
  {
    txt: 'VCKIN-金斯敦-KINGSTOWN',
    code: 'VCKIN'
  },
  {
    txt: 'VEAMB-阿穆艾湾-AMUAY BAY',
    code: 'VEAMB'
  },
  {
    txt: 'VEARA-阿拉亚-ARAYA',
    code: 'VEARA'
  },
  {
    txt: 'VEBAC-巴查克罗-BACHAQUERO',
    code: 'VEBAC'
  },
  {
    txt: 'VECAB-卡维马斯-CABIMAS',
    code: 'VECAB'
  },
  {
    txt: 'VECAS-加拉加斯-CARACAS',
    code: 'VECAS'
  },
  {
    txt: 'VECHI-奇奇里维切-CHICHIRIVICHI',
    code: 'VECHI'
  },
  {
    txt: 'VECIB-玻利瓦尔城-CIUDAD BOLIVAR',
    code: 'VECIB'
  },
  {
    txt: 'VECOL-科隆查-COLONCHA',
    code: 'VECOL'
  },
  {
    txt: 'VECTO-卡里皮托-CARIPITO',
    code: 'VECTO'
  },
  {
    txt: 'VECUM-库马纳-CUMANA',
    code: 'VECUM'
  },
  {
    txt: 'VEECH-埃尔乔雷-EL CHAURE',
    code: 'VEECH'
  },
  {
    txt: 'VEEPA-埃尔巴利托-EL PALITO',
    code: 'VEEPA'
  },
  {
    txt: 'VEETA-埃尔塔布拉齐奥-EL TABLAZO',
    code: 'VEETA'
  },
  {
    txt: 'VEGTA-关塔-GUANTA',
    code: 'VEGTA'
  },
  {
    txt: 'VEGUA-古阿兰奥港-GUARAGUAO',
    code: 'VEGUA'
  },
  {
    txt: 'VELES-拉斯塔加达-LA ESTACADA',
    code: 'VELES'
  },
  {
    txt: 'VELGU-拉瓜伊拉-LA GUAIRA',
    code: 'VELGU'
  },
  {
    txt: 'VELSA-拉萨利纳-LA SALINA',
    code: 'VELSA'
  },
  {
    txt: 'VEMAT-马坦萨斯-MATANZAS',
    code: 'VEMAT'
  },
  {
    txt: 'RUKHO-霍尔姆斯克-KHOLMSK',
    code: 'RUKHO'
  },
  {
    txt: 'RUKOR-科尔萨科夫-KORSAKOV',
    code: 'RUKOR'
  },
  {
    txt: 'RULAZ-拉扎烈夫-LAZAREV',
    code: 'RULAZ'
  },
  {
    txt: 'RULED-圣彼得堡-ST．PETERSBURG',
    code: 'RULED'
  },
  {
    txt: 'RULES-列索克-LESOK',
    code: 'RULES'
  },
  {
    txt: 'CAHAN-汉茨波特-HANTSPORT',
    code: 'CAHAN'
  },
  {
    txt: 'PHPAM-潘普洛纳-PAMPLONA',
    code: 'PHPAM'
  },
  {
    txt: 'PHPAR-帕兰-PARANG',
    code: 'PHPAR'
  },
  {
    txt: 'PHPHO-荷兰港-PORT HOLLAND',
    code: 'PHPHO'
  },
  {
    txt: 'PHPOL-波略克-POLLOC',
    code: 'PHPOL'
  },
  {
    txt: 'PHPOR-波罗-PORO(PORO I.)',
    code: 'PHPOR'
  },
  {
    txt: 'PHPPR-普林塞萨港-PUERTO PRINCESA',
    code: 'PHPPR'
  },
  {
    txt: 'PHPRO-波罗-PORO(LUZON)',
    code: 'PHPRO'
  },
  {
    txt: 'PHPSM-圣玛丽亚港-PORT SANTA MARIA',
    code: 'PHPSM'
  },
  {
    txt: 'PHPUL-普卢潘丹-PULUPANDAN',
    code: 'PHPUL'
  },
  {
    txt: 'PHSCA-圣卡洛斯-SAN CARLOS',
    code: 'PHSCA'
  },
  {
    txt: 'PHSCR-圣克鲁斯-SANTA CRUZ(LUZON)',
    code: 'PHSCR'
  },
  {
    txt: 'PHSFE-圣弗尔南多-SAN FERNANDO',
    code: 'PHSFE'
  },
  {
    txt: 'PHSGI-桑义-SANGI',
    code: 'PHSGI'
  },
  {
    txt: 'PHSOR-索索贡-SORSOGON',
    code: 'PHSOR'
  },
  {
    txt: 'PHSUR-苏里高-SURIGAO',
    code: 'PHSUR'
  },
  {
    txt: 'PHTAB-塔瓦科-TABACO',
    code: 'PHTAB'
  },
  {
    txt: 'PHTAC-塔克洛班-TACLOBAN',
    code: 'PHTAC'
  },
  {
    txt: 'PHTAG-塔比拉兰-TAGBILARAN',
    code: 'PHTAG'
  },
  {
    txt: 'PHTDC-坦多哥-TANDOC',
    code: 'PHTDC'
  },
  {
    txt: 'PHTOL-托莱多-TOLEDO',
    code: 'PHTOL'
  },
  {
    txt: 'PHVIL-维拉努埃瓦-VILLANUEVA',
    code: 'PHVIL'
  },
  {
    txt: 'PHZAM-三宝颜-ZAMBOANGA',
    code: 'PHZAM'
  },
  {
    txt: 'PKGEA-瓜德尔-GWADAR',
    code: 'PKGEA'
  },
  {
    txt: 'PKKAR-卡拉奇-KARACHI',
    code: 'PKKAR'
  },
  {
    txt: 'PKORM-奥尔马拉-ORMARA',
    code: 'PKORM'
  },
  {
    txt: 'PKPAS-伯斯尼-PASNI',
    code: 'PKPAS'
  },
  {
    txt: 'PLDAR-达尔沃尔-DARLOWO',
    code: 'PLDAR'
  },
  {
    txt: 'PLGDA-格但斯克-GDANSK',
    code: 'PLGDA'
  },
  {
    txt: 'PLGDY-格丁尼-GDYNIA',
    code: 'PLGDY'
  },
  {
    txt: 'PLHEL-海尔-HEL',
    code: 'PLHEL'
  },
  {
    txt: 'PLKOL-科沃布热格-KOLOBRZEG',
    code: 'PLKOL'
  },
  {
    txt: 'PLSWI-希维诺乌伊希切-SWINOUJSCIE',
    code: 'PLSWI'
  },
  {
    txt: 'PLSZC-什切青-SZCZECIN',
    code: 'PLSZC'
  },
  {
    txt: 'PLUST-乌斯特卡-USTKA',
    code: 'PLUST'
  },
  {
    txt: 'PLWLA-弗瓦迪斯瓦沃沃-WLADYSLAWOWO',
    code: 'PLWLA'
  },
  {
    txt: 'PLWRS-华沙-WARSAW',
    code: 'PLWRS'
  },
  {
    txt: 'PMSPI-圣皮埃尔-ST．PIERRE',
    code: 'PMSPI'
  },
  {
    txt: 'PNADA-亚当斯敦-ADAMSTOWN',
    code: 'PNADA'
  },
  {
    txt: 'PRAGU-阿瓜迪亚-AGUADILLA',
    code: 'PRAGU'
  },
  {
    txt: 'PRFAJ-法哈多-HJARDO',
    code: 'PRFAJ'
  },
  {
    txt: 'PRGCA-瓜尼卡-GUANICA',
    code: 'PRGCA'
  },
  {
    txt: 'PRGMA-瓜亚马-GUAYAMA',
    code: 'PRGMA'
  },
  {
    txt: 'PRJOB-乔布斯-JOBOS',
    code: 'PRJOB'
  },
  {
    txt: 'PRMAY-马亚圭斯-MAYAGUEZ',
    code: 'PRMAY'
  },
  {
    txt: 'PRPON-蓬塞-PONCE',
    code: 'PRPON'
  },
  {
    txt: 'PRYAB-亚武科阿-YABUCOA',
    code: 'PRYAB'
  },
  {
    txt: 'ACADH-英雄港-ANGRA DO HEROISMO',
    code: 'ACADH'
  },
  {
    txt: 'ACFUN-丰沙尔-FUNCHAL',
    code: 'ACFUN'
  },
  {
    txt: 'ACPDE-蓬塔德尔加达港-PONTA DELGADA',
    code: 'ACPDE'
  },
  {
    txt: 'ACSCF-圣克鲁斯-SANTA CRUZ(FLORES)',
    code: 'ACSCF'
  },
  {
    txt: 'ACSCG-圣克鲁斯-SANTA CRUZ(GRACIOSA)',
    code: 'ACSCG'
  },
  {
    txt: 'PTBAR-巴雷鲁-BARREIRO',
    code: 'PTBAR'
  },
  {
    txt: 'PTBEL-贝伦-BELEM',
    code: 'PTBEL'
  },
  {
    txt: 'PTFAR-法鲁-FARO',
    code: 'PTFAR'
  },
  {
    txt: 'PTLIS-里斯本-LISBON',
    code: 'PTLIS'
  },
  {
    txt: 'PTLXS-雷克索斯-LEIXOES',
    code: 'PTLXS'
  },
  {
    txt: 'PTOLH-奥良-OLHAO',
    code: 'PTOLH'
  },
  {
    txt: 'PTPOM-波马朗-POMARAO',
    code: 'PTPOM'
  },
  {
    txt: 'PTPOR-波尔蒂芒-PORTIMAO',
    code: 'PTPOR'
  },
  {
    txt: 'PTSBL-塞图巴尔-SETUBAL',
    code: 'PTSBL'
  },
  {
    txt: 'PTVDC-维亚纳堡-VIANA DO CASTELO',
    code: 'PTVDC'
  },
  {
    txt: 'PTVIR-雷阿尔城-VILA REAL',
    code: 'PTVIR'
  },
  {
    txt: 'PYASU-亚松森-ASUNCION',
    code: 'PYASU'
  },
  {
    txt: 'QADOH-多哈-DOHA',
    code: 'QADOH'
  },
  {
    txt: 'QAHAI-哈卢勒岛-HALUL ISLAND',
    code: 'QAHAI'
  },
  {
    txt: 'QAUMS-乌姆赛义德-UMM SAID',
    code: 'QAUMS'
  },
  {
    txt: 'REPDG-加勒茨角-POINTE DES GALETS',
    code: 'REPDG'
  },
  {
    txt: 'RESLO-圣路易-ST．LOUIS',
    code: 'RESLO'
  },
  {
    txt: 'ROBLA-布勒伊拉-BRAILA',
    code: 'ROBLA'
  },
  {
    txt: 'ROCON-康斯坦萨-CONSTANTZA',
    code: 'ROCON'
  },
  {
    txt: 'ROGLZ-加拉茨-GALATZ',
    code: 'ROGLZ'
  },
  {
    txt: 'ROMAN-曼加利-MANGALIA',
    code: 'ROMAN'
  },
  {
    txt: 'ROSUL-苏利纳-SULINA',
    code: 'ROSUL'
  },
  {
    txt: 'RUALE-历山大罗夫斯克-ALEXANDROVSK',
    code: 'RUALE'
  },
  {
    txt: 'RUARH-阿尔汉格尔-ARCHANGEL',
    code: 'RUARH'
  },
  {
    txt: 'RUKAN-喀山-KAZAN',
    code: 'RUKAN'
  },
  {
    txt: 'RUKER-克烈季-KERET',
    code: 'RUKER'
  },
  {
    txt: 'RUKGD-加里宁格勒-KALININGRAD',
    code: 'RUKGD'
  },
  {
    txt: 'JOAQA-亚喀巴-AQABA',
    code: 'JOAQA'
  },
  {
    txt: 'JPABA-网走-ABASHIRI',
    code: 'JPABA'
  },
  {
    txt: 'JPABO-网干-ABOSHI',
    code: 'JPABO'
  },
  {
    txt: 'JPAIN-相浦-AINOURA',
    code: 'JPAIN'
  },
  {
    txt: 'JPAIO-相生-AIOI',
    code: 'JPAIO'
  },
  {
    txt: 'JPAKI-秋田-AKITA',
    code: 'JPAKI'
  },
  {
    txt: 'JPAKW-赤穗湾-AKO WAN',
    code: 'JPAKW'
  },
  {
    txt: 'JPAMA-尼崎-AMAGASAKI',
    code: 'JPAMA'
  },
  {
    txt: 'JPANA-阿南-ANAN',
    code: 'JPANA'
  },
  {
    txt: 'JPAOM-青森-AOMORI',
    code: 'JPAOM'
  },
  {
    txt: 'JPATS-渥美-ATSUMI',
    code: 'JPATS'
  },
  {
    txt: 'JPBEP-别府-BEPPU',
    code: 'JPBEP'
  },
  {
    txt: 'JPCBA-千叶-CHIBA',
    code: 'JPCBA'
  },
  {
    txt: 'JPCHI-知多-CHITA',
    code: 'JPCHI'
  },
  {
    txt: 'JPENA-江名-ENA',
    code: 'JPENA'
  },
  {
    txt: 'JPETA-江田岛-ETAJIMA',
    code: 'JPETA'
  },
  {
    txt: 'JPFKA-福冈-FUKUOKA',
    code: 'JPFKA'
  },
  {
    txt: 'JPFNA-船川-FUNAKAWA',
    code: 'JPFNA'
  },
  {
    txt: 'JPFUK-福山-FUKUYAMA',
    code: 'JPFUK'
  },
  {
    txt: 'JPFUN-船桥-FUNABASHI',
    code: 'JPFUN'
  },
  {
    txt: 'JPFUS-伏木-FUSHIKI',
    code: 'JPFUS'
  },
  {
    txt: 'JPGAM-蒲郡-GAMAGORI',
    code: 'JPGAM'
  },
  {
    txt: 'JPHAC-八户-HACHINOHE',
    code: 'JPHAC'
  },
  {
    txt: 'JPHAG-荻-HAGI',
    code: 'JPHAG'
  },
  {
    txt: 'JPHAK-函馆-HAKODATE',
    code: 'JPHAK'
  },
  {
    txt: 'JPHAM-滨田-HAMADA',
    code: 'JPHAM'
  },
  {
    txt: 'JPHAN-阪南-HANNAN',
    code: 'JPHAN'
  },
  {
    txt: 'JPHDA-半田-HANDA',
    code: 'JPHDA'
  },
  {
    txt: 'JPHIA-日明-HIAGARI',
    code: 'JPHIA'
  },
  {
    txt: 'JPHIB-响滩湾-HIBIKINADA',
    code: 'JPHIB'
  },
  {
    txt: 'JPHIK-光市-HIKARI',
    code: 'JPHIK'
  },
  {
    txt: 'JPHIM-姬路-HIMEJI',
    code: 'JPHIM'
  },
  {
    txt: 'JPHIR-平生-HIRAO',
    code: 'JPHIR'
  },
  {
    txt: 'JPHIS-久之滨-HISANOHAMA',
    code: 'JPHIS'
  },
  {
    txt: 'JPHIT-日立-HITACHI',
    code: 'JPHIT'
  },
  {
    txt: 'JPHKA-博多-HAKATA',
    code: 'JPHKA'
  },
  {
    txt: 'JPHMA-广岛-HIROSHIMA',
    code: 'JPHMA'
  },
  {
    txt: 'JPHOS-细岛-HOSOSHIMA',
    code: 'JPHOS'
  },
  {
    txt: 'JPHSU-滨松-HAMAMATSU',
    code: 'JPHSU'
  },
  {
    txt: 'JPHTA-广田-HIROHATA',
    code: 'JPHTA'
  },
  {
    txt: 'JPIHO-辑保-IHO',
    code: 'JPIHO'
  },
  {
    txt: 'JPIMA-今治-IMABARI',
    code: 'JPIMA'
  },
  {
    txt: 'JPINN-因岛-INNOSHIMA',
    code: 'JPINN'
  },
  {
    txt: 'JPIRA-伊良湖-IRAKO',
    code: 'JPIRA'
  },
  {
    txt: 'JPIRI-伊万里-IMARI',
    code: 'JPIRI'
  },
  {
    txt: 'JPISH-石卷-ISHINOMAKI',
    code: 'JPISH'
  },
  {
    txt: 'JPITO-系崎-ITOZAKI',
    code: 'JPITO'
  },
  {
    txt: 'JPIWA-岩国-IWAKUNI',
    code: 'JPIWA'
  },
  {
    txt: 'JPIZU-泉佐野-IZUMISANO',
    code: 'JPIZU'
  },
  {
    txt: 'JPKAG-鹿儿岛-KAGOSHIMA',
    code: 'JPKAG'
  },
  {
    txt: 'JPKAI-海南-KAINAN',
    code: 'JPKAI'
  },
  {
    txt: 'JPKAM-釜石-KAMAISHI',
    code: 'JPKAM'
  },
  {
    txt: 'JPKAN-关门-KANMON',
    code: 'JPKAN'
  },
  {
    txt: 'JPKAW-川崎-KAWASAKI',
    code: 'JPKAW'
  },
  {
    txt: 'JPKII-喜入-KIIRE',
    code: 'JPKII'
  },
  {
    txt: 'JPKIN-衣浦-KINUURA',
    code: 'JPKIN'
  },
  {
    txt: 'JPKIS-木更津-KISARAZU',
    code: 'JPKIS'
  },
  {
    txt: 'JPKIT-北九州-KITAKYUSHU',
    code: 'JPKIT'
  },
  {
    txt: 'JPKMA-鹿岛-KASHIMA',
    code: 'JPKMA'
  },
  {
    txt: 'JPKNA-金泽-KANAZAWA',
    code: 'JPKNA'
  },
  {
    txt: 'JPKOC-高知-KOCHI',
    code: 'JPKOC'
  },
  {
    txt: 'JPKOE-川之江-KAWANOE',
    code: 'JPKOE'
  },
  {
    txt: 'JPKOM-小松岛-KOMATSUSHIMA',
    code: 'JPKOM'
  },
  {
    txt: 'JPKON-神岛-KONOSHIMA',
    code: 'JPKON'
  },
  {
    txt: 'JPKUI-黑崎-KUROSAKI',
    code: 'JPKUI'
  },
  {
    txt: 'JPKUR-吴-KURE',
    code: 'JPKUR'
  },
  {
    txt: 'JPKUS-钏路-KUSHIRO',
    code: 'JPKUS'
  },
  {
    txt: 'JPKWN-金湾-KIN WAN',
    code: 'JPKWN'
  },
  {
    txt: 'JPMAI-舞鹤-MAIZURU',
    code: 'JPMAI'
  },
  {
    txt: 'JPMAR-丸龟-MARUGAME',
    code: 'JPMAR'
  },
  {
    txt: 'JPMAT-松坂-MATUZAKA',
    code: 'JPMAT'
  },
  {
    txt: 'JPMGA-松永-MATSUNAGA',
    code: 'JPMGA'
  },
  {
    txt: 'JPMIH-三原-MIHARA',
    code: 'JPMIH'
  },
  {
    txt: 'JPMIN-水俣-MINAMATA',
    code: 'JPMIN'
  },
  {
    txt: 'JPMIS-三角-MISUMI',
    code: 'JPMIS'
  },
  {
    txt: 'JPMIT-三子岛-MITSUKOSHIMA',
    code: 'JPMIT'
  },
  {
    txt: 'JPMIZ-水岛-MIZUSHIMA',
    code: 'JPMIZ'
  },
  {
    txt: 'JPMKE-三池-MIIKE',
    code: 'JPMKE'
  },
  {
    txt: 'JPMMA-松山-MATSUYAMA',
    code: 'JPMMA'
  },
  {
    txt: 'JPMOJ-门司-MOJI',
    code: 'JPMOJ'
  },
  {
    txt: 'JPMSM-三岛-MISIMA',
    code: 'JPMSM'
  },
  {
    txt: 'JPMTA-马刀泻-MATEGATA',
    code: 'JPMTA'
  },
  {
    txt: 'JPMUR-室兰-MURORAN',
    code: 'JPMUR'
  },
  {
    txt: 'JPMUT-六连-MUTSURE',
    code: 'JPMUT'
  },
  {
    txt: 'JPMZU-宫津-MIYAZU',
    code: 'JPMZU'
  },
  {
    txt: 'JPNAH-那霸-NAHA',
    code: 'JPNAH'
  },
  {
    txt: 'JPNAI-直岛-NAOSHIMA ISLAND',
    code: 'JPNAI'
  },
  {
    txt: 'JPNAN-七尾-NANAO',
    code: 'JPNAN'
  },
  {
    txt: 'JPNAO-直江津-NAOETSU',
    code: 'JPNAO'
  },
  {
    txt: 'JPNAR-奈良-NARA',
    code: 'JPNAR'
  },
  {
    txt: 'JPNEM-根室-NEMURO',
    code: 'JPNEM'
  },
  {
    txt: 'JPNGA-长浦-NAGAURA',
    code: 'JPNGA'
  },
  {
    txt: 'JPNIS-西宫-NISHINOMIYA',
    code: 'JPNIS'
  },
  {
    txt: 'JPNKI-长崎-NAGASAKI',
    code: 'JPNKI'
  },
  {
    txt: 'JPNMA-新居滨-NIIHAMA',
    code: 'JPNMA'
  },
  {
    txt: 'JPNTA-新渴-NIIGATA',
    code: 'JPNTA'
  },
  {
    txt: 'JPOFU-大船渡-OFUNATO',
    code: 'JPOFU'
  },
  {
    txt: 'USHMR-荷马-HOMER',
    code: 'USHMR'
  },
  {
    txt: 'USHOM-霍姆-HOME',
    code: 'USHOM'
  },
  {
    txt: 'USHOP-霍普韦尔-HOPEWELL',
    code: 'USHOP'
  },
  {
    txt: 'USHOU-休斯敦-HOUSTON',
    code: 'USHOU'
  },
  {
    txt: 'USHTR-汉普顿港群-HAMPTON ROADS',
    code: 'USHTR'
  },
  {
    txt: 'USHUR-休伦-HURON',
    code: 'USHUR'
  },
  {
    txt: 'USINS-印第安纳波利斯-INDIANAPOLIS',
    code: 'USINS'
  },
  {
    txt: 'USJAC-杰克逊维尔-JACKSONVILLE',
    code: 'USJAC'
  },
  {
    txt: 'USJEC-泽西城-JERSEY CITY',
    code: 'USJEC'
  },
  {
    txt: 'USJPL-乔普林-JOPLIN',
    code: 'USJPL'
  },
  {
    txt: 'USJUN-朱诺-JUNEAU',
    code: 'USJUN'
  },
  {
    txt: 'USKAL-卡拉马-KALAMA',
    code: 'USKAL'
  },
  {
    txt: 'USKDY-肯尼迪-KENNEDY',
    code: 'USKDY'
  },
  {
    txt: 'USKEN-基诺沙-KENOSHA',
    code: 'USKEN'
  },
  {
    txt: 'USKET-凯奇坎-KETCHIKAN',
    code: 'USKET'
  },
  {
    txt: 'USKNI-克奈-KENAI',
    code: 'USKNI'
  },
  {
    txt: 'USKOD-科迪亚克-KODIAK',
    code: 'USKOD'
  },
  {
    txt: 'USKSB-金斯湾-KINGS BAY',
    code: 'USKSB'
  },
  {
    txt: 'USKSC-堪萨斯城-KANSAS CITY',
    code: 'USKSC'
  },
  {
    txt: 'USKWT-基韦斯特-KEY WEST',
    code: 'USKWT'
  },
  {
    txt: 'USLCS-莱克查尔斯-LAKE CHARLES',
    code: 'USLCS'
  },
  {
    txt: 'USLGB-长滩-LONG BEACH',
    code: 'USLGB'
  },
  {
    txt: 'USLON-隆维尤-LONGVIEW',
    code: 'USLON'
  },
  {
    txt: 'USLSA-洛杉矶-LOS ANGELES',
    code: 'USLSA'
  },
  {
    txt: 'USMAN-马尼托沃克-MANITOWOC',
    code: 'USMAN'
  },
  {
    txt: 'USMET-梅特拉卡特拉-METLAKATLA',
    code: 'USMET'
  },
  {
    txt: 'USMII-蜜尔沃基-MILWAUKEE',
    code: 'USMII'
  },
  {
    txt: 'USMIK-明内通卡-MINNETONKA',
    code: 'USMIK'
  },
  {
    txt: 'USMIN-明尼阿波利斯-MINNEAPOLIS',
    code: 'USMIN'
  },
  {
    txt: 'USMOC-莫尔黑德城-MOREHEAD CITY',
    code: 'USMOC'
  },
  {
    txt: 'USMON-门罗-MONROE',
    code: 'USMON'
  },
  {
    txt: 'USMOR-英文-MORRISVILLE',
    code: 'USMOR'
  },
  {
    txt: 'USMUS-马斯基根-MUSKEGON',
    code: 'USMUS'
  },
  {
    txt: 'USMYG-默特尔克里克-MYRILE GROVE',
    code: 'USMYG'
  },
  {
    txt: 'USNAK-瑙加特克-NAUGATUCK',
    code: 'USNAK'
  },
  {
    txt: 'USNBE-新贝德福德-NEW BEDFORD',
    code: 'USNBE'
  },
  {
    txt: 'USNET-纽波特-NEWPON(R．I．)',
    code: 'USNET'
  },
  {
    txt: 'USNEW-纽卡斯尔-NEW CASTLE(DEL．)',
    code: 'USNEW'
  },
  {
    txt: 'USNFK-诺福克-NORFOLK',
    code: 'USNFK'
  },
  {
    txt: 'USNIK-尼基斯基-NIKISKI',
    code: 'USNIK'
  },
  {
    txt: 'USNOB-诺思贝-NORTHBEN',
    code: 'USNOB'
  },
  {
    txt: 'USNOL-新奥尔良-NEW ORLEANS',
    code: 'USNOL'
  },
  {
    txt: 'USNPN-纽波特纽斯-NEWPON NEWS(VA．)',
    code: 'USNPN'
  },
  {
    txt: 'USNPT-纽波特-NEWPON(OREG．)',
    code: 'USNPT'
  },
  {
    txt: 'USNWK-纽瓦克-NEWARK',
    code: 'USNWK'
  },
  {
    txt: 'USOAK-奥克兰-OAKLAND',
    code: 'USOAK'
  },
  {
    txt: 'USOIY-奥林匹亚-OLYMPIA',
    code: 'USOIY'
  },
  {
    txt: 'USORA-奥兰治-ORANGE',
    code: 'USORA'
  },
  {
    txt: 'USOSW-奥斯威戈-OSWEGO',
    code: 'USOSW'
  },
  {
    txt: 'USPAB-奥巴斯克斯港-PORTAUX BASQUES',
    code: 'USPAB'
  },
  {
    txt: 'USPAD-亚当斯港-PORT ADAMS',
    code: 'USPAD'
  },
  {
    txt: 'USPAR-阿瑟港-PORT ARTHUR(TEX．)',
    code: 'USPAR'
  },
  {
    txt: 'USPAS-帕斯卡古拉-PASCAGOULA',
    code: 'USPAS'
  },
  {
    txt: 'USPAU-保罗斯伯罗-PAULSBORO',
    code: 'USPAU'
  },
  {
    txt: 'USPCA-卡纳维拉尔港-PORT CANAVERAL',
    code: 'USPCA'
  },
  {
    txt: 'USPET-彼得斯堡-PETERSBURG',
    code: 'USPET'
  },
  {
    txt: 'USPEV-埃弗格雷斯港-PORT EVERGLADES',
    code: 'USPEV'
  },
  {
    txt: 'USPHI-费城-PHILADELPHIA',
    code: 'USPHI'
  },
  {
    txt: 'USPHN-休伦港-PORT HURON',
    code: 'USPHN'
  },
  {
    txt: 'USPIA-拉瓦卡港-PORT LAVACA',
    code: 'USPIA'
  },
  {
    txt: 'USPIS-伊萨贝尔港-PORT ISABEL',
    code: 'USPIS'
  },
  {
    txt: 'USPIT-匹兹堡-PITTSBURGH',
    code: 'USPIT'
  },
  {
    txt: 'USPLB-棕榈滩-PALM BEACH',
    code: 'USPLB'
  },
  {
    txt: 'VEMOR-莫龙-MORON',
    code: 'VEMOR'
  },
  {
    txt: 'VEPAL-帕卢亚-PALUA',
    code: 'VEPAL'
  },
  {
    txt: 'VEPAM-帕马塔克亚尔-PAMATACUAL',
    code: 'VEPAM'
  },
  {
    txt: 'VEPCA-卡贝略港-PUERTO CABELLO',
    code: 'VEPCA'
  },
  {
    txt: 'VEPCN-篷塔卡尔东-PUNTA CARDON',
    code: 'VEPCN'
  },
  {
    txt: 'VEPCO-库希略角-PUNTA CUCHILLO',
    code: 'VEPCO'
  },
  {
    txt: 'VEPDH-耶罗港-PUERTODE HIERRO',
    code: 'VEPDH'
  },
  {
    txt: 'VEPER-佩蒂格来特-PERTIGALETE',
    code: 'VEPER'
  },
  {
    txt: 'VEPLC-拉克鲁斯港-PUERTOLA CRUZ',
    code: 'VEPLC'
  },
  {
    txt: 'VEPMI-米兰达港-PUERTO MIRANDA',
    code: 'VEPMI'
  },
  {
    txt: 'VEPMR-波拉马尔-PORLAMAR',
    code: 'VEPMR'
  },
  {
    txt: 'VEPOR-奥尔达斯港-PUERTO ORDAZ',
    code: 'VEPOR'
  },
  {
    txt: 'UASKA-斯卡多夫斯克-SKADOVSK',
    code: 'UASKA'
  },
  {
    txt: 'UATHE-费奥多西亚-THEODOSIA',
    code: 'UATHE'
  },
  {
    txt: 'UAUDK-乌斯列戈尔斯克-UST-DUNAYSK',
    code: 'UAUDK'
  },
  {
    txt: 'UAYAI-雅尔塔-YALTA',
    code: 'UAYAI'
  },
  {
    txt: 'UAYUZ-尤日内-YUZHNYY',
    code: 'UAYUZ'
  },
  {
    txt: 'UGKAA-坎帕拉-KAMPALA',
    code: 'UGKAA'
  },
  {
    txt: 'UMHIL-希洛-HILO',
    code: 'UMHIL'
  },
  {
    txt: 'UMHON-火奴鲁鲁-HONOLULU',
    code: 'UMHON'
  },
  {
    txt: 'UMKAH-卡胡卢伊-KAHULUI',
    code: 'UMKAH'
  },
  {
    txt: 'UMNAW-纳威利威利-NAWILIWILI',
    code: 'UMNAW'
  },
  {
    txt: 'USABE-阿伯丁-ABERDEEN',
    code: 'USABE'
  },
  {
    txt: 'USADR-亚历山大-ALEXANDR',
    code: 'USADR'
  },
  {
    txt: 'USALA-阿拉姆达-ALAMEDA',
    code: 'USALA'
  },
  {
    txt: 'USALB-奥尔巴尼-ALBANY',
    code: 'USALB'
  },
  {
    txt: 'USALP-阿尔皮纳-A1PENA',
    code: 'USALP'
  },
  {
    txt: 'USANA-阿纳科特斯-ANACORTES',
    code: 'USANA'
  },
  {
    txt: 'USANC-安科雷奇-ANCHORAGE',
    code: 'USANC'
  },
  {
    txt: 'USAPA-阿巴拉契科拉-APALACHICOLA',
    code: 'USAPA'
  },
  {
    txt: 'USASH-阿什塔比拉-ASHTABULA',
    code: 'USASH'
  },
  {
    txt: 'USAST-阿斯托里亚-ASTORIA',
    code: 'USAST'
  },
  {
    txt: 'USATA-亚特兰大-ATLANTA',
    code: 'USATA'
  },
  {
    txt: 'USBAL-巴尔的摩-BALTIMORE',
    code: 'USBAL'
  },
  {
    txt: 'USBAN-班戈-BANGOR(ME．)',
    code: 'USBAN'
  },
  {
    txt: 'USBAT-巴斯-BATH',
    code: 'USBAT'
  },
  {
    txt: 'USBAY-贝敦-BAYTOWN',
    code: 'USBAY'
  },
  {
    txt: 'USBCN-布坎南-BUCHANAN',
    code: 'USBCN'
  },
  {
    txt: 'USBEA-博蒙特-BEAUMONT',
    code: 'USBEA'
  },
  {
    txt: 'USBEE-贝尔维迪尔-BELVIDERE',
    code: 'USBEE'
  },
  {
    txt: 'USBEL-贝灵哈姆-BELLINGHAM',
    code: 'USBEL'
  },
  {
    txt: 'USBOS-波士顿-BOSTON',
    code: 'USBOS'
  },
  {
    txt: 'USBRE-布雷默顿-BREMERTON',
    code: 'USBRE'
  },
  {
    txt: 'USBRI-布里奇波特-BRIDGEPORT',
    code: 'USBRI'
  },
  {
    txt: 'USBRU-不伦瑞克-BRUNSWICK',
    code: 'USBRU'
  },
  {
    txt: 'USBTR-巴吞鲁日-BATON ROUGE',
    code: 'USBTR'
  },
  {
    txt: 'USBUC-巴克斯波特-BUCKSPORT',
    code: 'USBUC'
  },
  {
    txt: 'USBUF-布法罗-BUFFALO',
    code: 'USBUF'
  },
  {
    txt: 'USBUR-伯恩赛德-BURNSIDE',
    code: 'USBUR'
  },
  {
    txt: 'USCAE-夏洛特阿马利亚-CHARLOTTE',
    code: 'USCAE'
  },
  {
    txt: 'USCAM-卡姆登-CAMDEN',
    code: 'USCAM'
  },
  {
    txt: 'USCGE-剑桥-CAMBRIDGE',
    code: 'USCGE'
  },
  {
    txt: 'USCHA-查尔斯顿-CHARLESTON(S．C．)',
    code: 'USCHA'
  },
  {
    txt: 'USCHC-切萨皮克城-CHESAPEAKE CITY',
    code: 'USCHC'
  },
  {
    txt: 'USCHE-希博伊甘-CHEBOYGAN',
    code: 'USCHE'
  },
  {
    txt: 'USCHR-切斯特-CHESTER',
    code: 'USCHR'
  },
  {
    txt: 'USCIN-辛辛那提-CINCINNATI',
    code: 'USCIN'
  },
  {
    txt: 'USCLB-哥伦布-COLUMBUS',
    code: 'USCLB'
  },
  {
    txt: 'USCLE-克利夫兰-CLEVELAND',
    code: 'USCLE'
  },
  {
    txt: 'USCOC-科珀斯克里斯蒂-CORPUS CHRISTI',
    code: 'USCOC'
  },
  {
    txt: 'USCOH-康尼奥特港-CONNEAUT HARBOUR',
    code: 'USCOH'
  },
  {
    txt: 'USDAL-达拉斯-DALLAS',
    code: 'USDAL'
  },
  {
    txt: 'USDEB-德拉华湾-DELAWARE BAY',
    code: 'USDEB'
  },
  {
    txt: 'USDES-特斯特汉-DESTREHAN',
    code: 'USDES'
  },
  {
    txt: 'USDET-底特律-DETROIT',
    code: 'USDET'
  },
  {
    txt: 'USDUH-荷兰港-DUTCH HARBOUR',
    code: 'USDUH'
  },
  {
    txt: 'USDUL-德卢斯-DULUTH',
    code: 'USDUL'
  },
  {
    txt: 'USDVE-丹佛-DENVER',
    code: 'USDVE'
  },
  {
    txt: 'USEPO-厄尔巴索-EL PASO',
    code: 'USEPO'
  },
  {
    txt: 'USERI-伊利-ERIE',
    code: 'USERI'
  },
  {
    txt: 'USESO-埃尔塞贡多-EL SEGUNDO',
    code: 'USESO'
  },
  {
    txt: 'USESP-伊丽莎白港-ELIZABETH PORT',
    code: 'USESP'
  },
  {
    txt: 'USEUR-尤里卡-EUREKA',
    code: 'USEUR'
  },
  {
    txt: 'USEVE-埃弗里特-EVERETT(WASH．)',
    code: 'USEVE'
  },
  {
    txt: 'USFER-芬代尔-FERNDALE',
    code: 'USFER'
  },
  {
    txt: 'USFLR-福尔里弗-FALLRIVER',
    code: 'USFLR'
  },
  {
    txt: 'USFNA-费南迪纳-FERNANDINA',
    code: 'USFNA'
  },
  {
    txt: 'USFRE-弗里波特-FREEPORT',
    code: 'USFRE'
  },
  {
    txt: 'USGAL-加尔维斯顿-GALVESTON',
    code: 'USGAL'
  },
  {
    txt: 'USGDH-好望角-GOOD HOPE',
    code: 'USGDH'
  },
  {
    txt: 'USGEI-盖斯马-GEISMAR',
    code: 'USGEI'
  },
  {
    txt: 'USGEO-乔治敦-GEORGETOWN',
    code: 'USGEO'
  },
  {
    txt: 'USGHN-格兰德黑文-GRAND HAVEN',
    code: 'USGHN'
  },
  {
    txt: 'USGLO-格洛斯特-GLOUCESTER(MASS．)',
    code: 'USGLO'
  },
  {
    txt: 'USGRA-格拉梅西-GRAMERCY',
    code: 'USGRA'
  },
  {
    txt: 'USGRB-格林贝-GREEN BAY',
    code: 'USGRB'
  },
  {
    txt: 'USGRH-格雷斯港-GRAYS HARBOUR',
    code: 'USGRH'
  },
  {
    txt: 'USGUL-格尔夫波特-GULFPORT',
    code: 'USGUL'
  },
  {
    txt: 'JPOIT-大分-OITA',
    code: 'JPOIT'
  },
  {
    txt: 'JPOKA-冈山-OKAYAMA',
    code: 'JPOKA'
  },
  {
    txt: 'JPOMA-御前崎-OMAEZAKI',
    code: 'JPOMA'
  },
  {
    txt: 'JPONA-小名滨-ONAHAMA',
    code: 'JPONA'
  },
  {
    txt: 'JPONO-尾道-ONOMICHI',
    code: 'JPONO'
  },
  {
    txt: 'JPOTA-小樽-OTARU',
    code: 'JPOTA'
  },
  {
    txt: 'JPOTO-大凑-OMINATO',
    code: 'JPOTO'
  },
  {
    txt: 'JPRUM-留萌-RUMOI',
    code: 'JPRUM'
  },
  {
    txt: 'JPSAG-佐贺关-SAGANOSEKI',
    code: 'JPSAG'
  },
  {
    txt: 'JPSAI-佐伯-SAIKI',
    code: 'JPSAI'
  },
  {
    txt: 'JPSAK-境港-SAKAIMINATO',
    code: 'JPSAK'
  },
  {
    txt: 'JPSBO-佐世-SASEBO',
    code: 'JPSBO'
  },
  {
    txt: 'JPSDA-下田-SHIMODA',
    code: 'JPSDA'
  },
  {
    txt: 'JPSDE-坂出-SAKAIDE',
    code: 'JPSDE'
  },
  {
    txt: 'JPSEN-仙台-SENDAI',
    code: 'JPSEN'
  },
  {
    txt: 'JPSGA-盐斧-SHIOGAMA',
    code: 'JPSGA'
  },
  {
    txt: 'JPSHI-下关-SHIMONOSEKI',
    code: 'JPSHI'
  },
  {
    txt: 'JPSIN-新凑-SINMINATO',
    code: 'JPSIN'
  },
  {
    txt: 'JPSMA-饰磨-SHIKAMA',
    code: 'JPSMA'
  },
  {
    txt: 'JPSRA-芝浦-SHIBAURA',
    code: 'JPSRA'
  },
  {
    txt: 'JPSSI-师崎-SHINSAKI',
    code: 'JPSSI'
  },
  {
    txt: 'JPSSU-下津-SHIMOTSU',
    code: 'JPSSU'
  },
  {
    txt: 'JPSTA-酒田-SAKATA',
    code: 'JPSTA'
  },
  {
    txt: 'JPSTO-崎户-SAKITO',
    code: 'JPSTO'
  },
  {
    txt: 'JPSUZ-须崎-SUZAKI',
    code: 'JPSUZ'
  },
  {
    txt: 'JPSZU-清水-SHIMIZU',
    code: 'JPSZU'
  },
  {
    txt: 'JPTAC-桔-TACHIBANA',
    code: 'JPTAC'
  },
  {
    txt: 'JPTAD-多度津-TADOTU',
    code: 'JPTAD'
  },
  {
    txt: 'JPTAG-田子浦-TAGONOURA',
    code: 'JPTAG'
  },
  {
    txt: 'JPTAM-玉野-TAMANO',
    code: 'JPTAM'
  },
  {
    txt: 'JPTAN-田边-TANABE',
    code: 'JPTAN'
  },
  {
    txt: 'JPTAT-馆山-TATEYAMA',
    code: 'JPTAT'
  },
  {
    txt: 'JPTAU-谷山-TANIYAMA',
    code: 'JPTAU'
  },
  {
    txt: 'JPTCI-十胜-TOKACHI',
    code: 'JPTCI'
  },
  {
    txt: 'JPTGA-敦贺-TSURUGA',
    code: 'JPTGA'
  },
  {
    txt: 'JPTKI-东海-TOKAI',
    code: 'JPTKI'
  },
  {
    txt: 'JPTKU-高松-TAKAMATSU',
    code: 'JPTKU'
  },
  {
    txt: 'JPTMA-诧间-TAKUMA',
    code: 'JPTMA'
  },
  {
    txt: 'JPTMI-津久见-TSUKUMI',
    code: 'JPTMI'
  },
  {
    txt: 'JPTMS-玉岛-TAMA SIMA',
    code: 'JPTMS'
  },
  {
    txt: 'JPTOA-德山-TOKUYAMA',
    code: 'JPTOA'
  },
  {
    txt: 'JPTOK-东京-TOKYO',
    code: 'JPTOK'
  },
  {
    txt: 'JPTOM-苫小牧-TOMAKOMAI',
    code: 'JPTOM'
  },
  {
    txt: 'JPTON-富田-TONDA',
    code: 'JPTON'
  },
  {
    txt: 'JPTOS-富山新港-TOYAMA SINKO',
    code: 'JPTOS'
  },
  {
    txt: 'JPTOT-鸟取-TOTTORI',
    code: 'JPTOT'
  },
  {
    txt: 'JPTOY-丰桥-TOYOHASHI',
    code: 'JPTOY'
  },
  {
    txt: 'JPTRI-鹤见-TSURUMI',
    code: 'JPTRI'
  },
  {
    txt: 'JPTSA-津居山-TSUIYAMA',
    code: 'JPTSA'
  },
  {
    txt: 'JPTSU-津-TSU',
    code: 'JPTSU'
  },
  {
    txt: 'JPTYA-富山-TOYAMA',
    code: 'JPTYA'
  },
  {
    txt: 'JPUBE-宇部-UBE',
    code: 'JPUBE'
  },
  {
    txt: 'JPUNO-宇野-UNO',
    code: 'JPUNO'
  },
  {
    txt: 'JPWAK-稚内-WAKKANAI',
    code: 'JPWAK'
  },
  {
    txt: 'JPWKU-若松-WAKAMATSU',
    code: 'JPWKU'
  },
  {
    txt: 'JPYAA-八恬滨-YAWATAHAMA',
    code: 'JPYAA'
  },
  {
    txt: 'JPYAT-八代-YATSUSHIRO',
    code: 'JPYAT'
  },
  {
    txt: 'JPYCI-四日市-YOKKAICHI',
    code: 'JPYCI'
  },
  {
    txt: 'JPYKA-横须贺-YOKO SUKA',
    code: 'JPYKA'
  },
  {
    txt: 'JPYOR-寄岛-YORISHIMA',
    code: 'JPYOR'
  },
  {
    txt: 'JPYOT-四仓-YOTUKURA',
    code: 'JPYOT'
  },
  {
    txt: 'KELAM-拉穆-LAMU',
    code: 'KELAM'
  },
  {
    txt: 'KEMAL-马林迪-MALINDI',
    code: 'KEMAL'
  },
  {
    txt: 'KENAI-内罗毕-NAIROBI',
    code: 'KENAI'
  },
  {
    txt: 'KEVGA-万加-VANGA',
    code: 'KEVGA'
  },
  {
    txt: 'KHKPS-磅逊-KOMPONG SOM',
    code: 'KHKPS'
  },
  {
    txt: 'KIBAN-巴纳巴岛-BANABA',
    code: 'KIBAN'
  },
  {
    txt: 'KICHI-圣诞岛-CHDSTMAS ISLAND',
    code: 'KICHI'
  },
  {
    txt: 'KIFNI-范宁岛-FANNING ISLAND',
    code: 'KIFNI'
  },
  {
    txt: 'KMDZA-藻德济-DZAOUDZI',
    code: 'KMDZA'
  },
  {
    txt: 'KMFOM-丰博尼-FOMBONI',
    code: 'KMFOM'
  },
  {
    txt: 'KMMOR-莫罗尼-MORONI',
    code: 'KMMOR'
  },
  {
    txt: 'KMMUT-穆察穆杜-MUTSAMUDU',
    code: 'KMMUT'
  },
  {
    txt: 'KNCHA-查尔斯敦-CHARLESTOWN(NEVIS)',
    code: 'KNCHA'
  },
  {
    txt: 'KPCHI-镇南浦-CHINNAMPO',
    code: 'KPCHI'
  },
  {
    txt: 'KPCJN-清津-CHUNGJIN',
    code: 'KPCJN'
  },
  {
    txt: 'KPHAE-海州-HAEJU',
    code: 'KPHAE'
  },
  {
    txt: 'KPPYG-平壤-PYONGYANG',
    code: 'KPPYG'
  },
  {
    txt: 'KPRAJ-罗津-RAJIN',
    code: 'KPRAJ'
  },
  {
    txt: 'KPSON-松林-SONGRIM',
    code: 'KPSON'
  },
  {
    txt: 'KRBUS-釜山-BUSAN',
    code: 'KRBUS'
  },
  {
    txt: 'KRCHG-长项-CHANGHANG',
    code: 'KRCHG'
  },
  {
    txt: 'KRCHI-镇海-CHINHAE',
    code: 'KRCHI'
  },
  {
    txt: 'KRCJU-济州-CHEJU',
    code: 'KRCJU'
  },
  {
    txt: 'KRINC-仁川-INCHON',
    code: 'KRINC'
  },
  {
    txt: 'KRKUN-群山-KUNSAN',
    code: 'KRKUN'
  },
  {
    txt: 'KRMAS-马山-MASAN',
    code: 'KRMAS'
  },
  {
    txt: 'KRMHO-墨湖-MUKHO',
    code: 'KRMHO'
  },
  {
    txt: 'KRMOK-木浦-MOKPO',
    code: 'KRMOK'
  },
  {
    txt: 'KRPYO-平泽-PYEONGTAEK',
    code: 'KRPYO'
  },
  {
    txt: 'KRSAM-三陟-SAMCHOK',
    code: 'KRSAM'
  },
  {
    txt: 'MGFAR-法拉凡加纳-FARAFANGANA',
    code: 'MGFAR'
  },
  {
    txt: 'MGFDA-多凡堡-FORT DAUPHIN',
    code: 'MGFDA'
  },
  {
    txt: 'MGMAH-马任加-MAHAJANGA',
    code: 'MGMAH'
  },
  {
    txt: 'MGMAR-马鲁安采特拉-MAROANTSETRA',
    code: 'MGMAR'
  },
  {
    txt: 'MGMRA-马纳卡拉-MANAKARA',
    code: 'MGMRA'
  },
  {
    txt: 'MGMRY-马南扎里河-MANANJARY',
    code: 'MGMRY'
  },
  {
    txt: 'MGMVA-穆龙达瓦-MORONDAVA',
    code: 'MGMVA'
  },
  {
    txt: 'MGNBE-贝岛-NOSY BE',
    code: 'MGNBE'
  },
  {
    txt: 'MGTAE-塔马塔夫-TAMATAVE',
    code: 'MGTAE'
  },
  {
    txt: 'MGTOA-图阿马西纳-TOAMASINA',
    code: 'MGTOA'
  },
  {
    txt: 'MGTOL-图莱亚尔-TOLEARY',
    code: 'MGTOL'
  },
  {
    txt: 'MMAKY-实兑-AKYAB',
    code: 'MMAKY'
  },
  {
    txt: 'MMBSN-勃生-BASSEIN',
    code: 'MMBSN'
  },
  {
    txt: 'MMKYA-皎漂-KYAUKPYU',
    code: 'MMKYA'
  },
  {
    txt: 'MMMGI-墨吉-MERGUI',
    code: 'MMMGI'
  },
  {
    txt: 'MMRAN-仰光-RANGOON',
    code: 'MMRAN'
  },
  {
    txt: 'MMSAN-山多威-SANDOWAY',
    code: 'MMSAN'
  },
  {
    txt: 'MMTAV-土瓦-TAVOY',
    code: 'MMTAV'
  },
  {
    txt: 'MMVIP-维多利亚角-VICTORIA POINT',
    code: 'MMVIP'
  },
  {
    txt: 'MNERD-额尔登特-ERDENET, MONGOLIA',
    code: 'MNERD'
  },
  {
    txt: 'MNUBR-乌兰巴托-ULAN BATOR, MONGOLIA',
    code: 'MNUBR'
  },
  {
    txt: 'MOMAC-澳门-MACAU',
    code: 'MOMAC'
  },
  {
    txt: 'MQFDF-法兰西堡-FORT DE FRANCE',
    code: 'MQFDF'
  },
  {
    txt: 'MQTRI-特里尼泰-TRINITE',
    code: 'MQTRI'
  },
  {
    txt: 'MRNBU-努瓦迪布-NOUADHIBOU',
    code: 'MRNBU'
  },
  {
    txt: 'MRNOU-努瓦克肖特-NOUAKCHOTT',
    code: 'MRNOU'
  },
  {
    txt: 'MSPLY-普利茅斯-P1YMOUTH',
    code: 'MSPLY'
  },
  {
    txt: 'MTVAL-瓦莱塔-VALLETTA',
    code: 'MTVAL'
  },
  {
    txt: 'MUPLO-路易港-PORT LOUIS',
    code: 'MUPLO'
  },
  {
    txt: 'MVADA-阿杜环礁-ADDUATOLL',
    code: 'MVADA'
  },
  {
    txt: 'MVMLI-马累岛-MALEISLAND',
    code: 'MVMLI'
  },
  {
    txt: 'MXACA-阿卡普尔科-ACAPULCODEJUAREZ',
    code: 'MXACA'
  },
  {
    txt: 'MXALV-阿尔瓦拉多-ALVARADO',
    code: 'MXALV'
  },
  {
    txt: 'MXC0I-科苏梅尔岛-COZUMEL ISLAND',
    code: 'MXC0I'
  },
  {
    txt: 'MXCAM-坎佩切-CAMPECHE',
    code: 'MXCAM'
  },
  {
    txt: 'MXCAT-阿卡斯群岛码头-CAYOSARCAS TERMINAL',
    code: 'MXCAT'
  },
  {
    txt: 'MXCDC-卡门城-CIUDADDEL CARMEN',
    code: 'MXCDC'
  },
  {
    txt: 'MXCOA-夸察夸尔科斯-COATZACOALCOS',
    code: 'MXCOA'
  },
  {
    txt: 'MXDBS-多斯博卡斯-DOSBOCAS',
    code: 'MXDBS'
  },
  {
    txt: 'MXENS-恩塞纳达-ENSENADA',
    code: 'MXENS'
  },
  {
    txt: 'MXFRO-弗龙特拉-FRONTERA',
    code: 'MXFRO'
  },
  {
    txt: 'MXGMS-瓜伊马斯-GUAYMAS',
    code: 'MXGMS'
  },
  {
    txt: 'MXLCS-拉萨罗卡德纳斯-LAZARO CARDENAS',
    code: 'MXLCS'
  },
  {
    txt: 'MXLPZ-拉巴斯-LA PAZ',
    code: 'MXLPZ'
  },
  {
    txt: 'MXMAN-曼萨尼略-MANZANILLO',
    code: 'MXMAN'
  },
  {
    txt: 'MXMAZ-马萨特兰-MAZATLAN',
    code: 'MXMAZ'
  },
  {
    txt: 'MXMEX-墨西哥城-MEXICO CITY',
    code: 'MXMEX'
  },
  {
    txt: 'MXMIN-米纳蒂特兰-MINATITLAN',
    code: 'MXMIN'
  },
  {
    txt: 'MXMRE-莫罗雷东杜-MORRO REDONDO',
    code: 'MXMRE'
  },
  {
    txt: 'MXNAN-南吉塔-NANCHITAL',
    code: 'MXNAN'
  },
  {
    txt: 'MXNAU-瑙特拉-NAUTLA',
    code: 'MXNAU'
  },
  {
    txt: 'MXPMA-马德罗港-PUERTO MADERO',
    code: 'MXPMA'
  },
  {
    txt: 'MXPRO-普罗格雷素-PROGRESO',
    code: 'MXPRO'
  },
  {
    txt: 'MXROT-罗萨里托码头-ROSARITO TERMINAL',
    code: 'MXROT'
  },
  {
    txt: 'MXSAC-萨利纳克鲁斯-SALINA CRUZ',
    code: 'MXSAC'
  },
  {
    txt: 'MXSRO-圣罗萨利亚-SANTA ROSALIA',
    code: 'MXSRO'
  },
  {
    txt: 'MXTAM-坦皮科-TAMPICO',
    code: 'MXTAM'
  },
  {
    txt: 'MXTOP-托波洛班波-TOPOLOBAMPO',
    code: 'MXTOP'
  },
  {
    txt: 'MXTUX-图斯潘-TUXPAN',
    code: 'MXTUX'
  },
  {
    txt: 'MXVER-韦拉克鲁斯-VERACRUZ',
    code: 'MXVER'
  },
  {
    txt: 'MXTJA-蒂华纳港-TIJUANA',
    code: 'MXTJA'
  },
  {
    txt: 'MYBGD-巴眼拿督-BAGAN DATOH',
    code: 'MYBGD'
  },
  {
    txt: 'MYBIN-民那丹-BINATANG',
    code: 'MYBIN'
  },
  {
    txt: 'MYBTP-巴株巴辖-BATU PAHAT',
    code: 'MYBTP'
  },
  {
    txt: 'MYBTU-民都鲁-BINTULU',
    code: 'MYBTU'
  },
  {
    txt: 'MYBUT-巴特沃思-BUTTERWORTH',
    code: 'MYBUT'
  },
  {
    txt: 'MYEND-恩达乌-ENDAU',
    code: 'MYEND'
  },
  {
    txt: 'MYGEO-乔治敦-GEORGETOWN',
    code: 'MYGEO'
  },
  {
    txt: 'MYJOB-柔佛巴鲁-JOHORE BAHRU',
    code: 'MYJOB'
  },
  {
    txt: 'MYKEM-甘马挽-KEMAMAN',
    code: 'MYKEM'
  },
  {
    txt: 'MYKER-居茶-KERTEH',
    code: 'MYKER'
  },
  {
    txt: 'MYKOK-亚庇-KOTA KINABALU',
    code: 'MYKOK'
  },
  {
    txt: 'MYKPA-瓜拉彭亨-KUALA PAHANG',
    code: 'MYKPA'
  },
  {
    txt: 'MYKRO-瓜拉弄宾-KUALAROMPIN',
    code: 'MYKRO'
  },
  {
    txt: 'MYKSU-瓜拉苏埃-KUALA SUAI',
    code: 'MYKSU'
  },
  {
    txt: 'MYKTR-瓜拉丁加奴-KUALA TRENGGANU',
    code: 'MYKTR'
  },
  {
    txt: 'KRULS-蔚山-ULSAN',
    code: 'KRULS'
  },
  {
    txt: 'KRYOS-丽水-YOSU',
    code: 'KRYOS'
  },
  {
    txt: 'KWKUT-科威特港-KUWAIT',
    code: 'KWKUT'
  },
  {
    txt: 'KWKUW-科威特-KUWAIT',
    code: 'KWKUW'
  },
  {
    txt: 'KWMAA-米纳艾哈迈迪-MENA A LAHMADI',
    code: 'KWMAA'
  },
  {
    txt: 'KWMAB-米纳阿卜杜拉-MENA ABDULLA',
    code: 'KWMAB'
  },
  {
    txt: 'KWSHU-舒艾拜-SHUAIBA',
    code: 'KWSHU'
  },
  {
    txt: 'KWSWH-舒韦赫-SHUWAIKH',
    code: 'KWSWH'
  },
  {
    txt: 'KZALA-阿拉木图-ALMA-ATA',
    code: 'KZALA'
  },
  {
    txt: 'KZASA-阿斯塔纳-ASTANA',
    code: 'KZASA'
  },
  {
    txt: 'KZDOK-多斯特克-DOSTYK',
    code: 'KZDOK'
  },
  {
    txt: 'LAKHN-甘蒙-KHAMMOUAN',
    code: 'LAKHN'
  },
  {
    txt: 'LBBRT-贝鲁特-BEIRUT',
    code: 'LBBRT'
  },
  {
    txt: 'LBCHE-舍卡-CHEKKA',
    code: 'LBCHE'
  },
  {
    txt: 'LBJOU-朱尼耶-JOUNIEH',
    code: 'LBJOU'
  },
  {
    txt: 'LBRSE-腊斯塞拉塔-RAS SELATA',
    code: 'LBRSE'
  },
  {
    txt: 'LBSID-西顿-SIDON',
    code: 'LBSID'
  },
  {
    txt: 'LBSOU-苏尔-SOUR',
    code: 'LBSOU'
  },
  {
    txt: 'LBTRI-的黎波里-TRIPOLI',
    code: 'LBTRI'
  },
  {
    txt: 'LBZAH-宰赫拉尼-ZAHRANI',
    code: 'LBZAH'
  },
  {
    txt: 'LCPCS-卡斯特里港-PORT CASTRIES',
    code: 'LCPCS'
  },
  {
    txt: 'LCSOU-苏弗里耶尔-SOUFRIERE',
    code: 'LCSOU'
  },
  {
    txt: 'LCVXF-维约堡-VIEUX FORT',
    code: 'LCVXF'
  },
  {
    txt: 'LKBAT-拜蒂克洛-BATTICALOA',
    code: 'LKBAT'
  },
  {
    txt: 'LKCOL-科伦坡-COLOMBO',
    code: 'LKCOL'
  },
  {
    txt: 'LKGAL-加勒-GALLE',
    code: 'LKGAL'
  },
  {
    txt: 'LKJAF-贾夫纳-JAFFNA',
    code: 'LKJAF'
  },
  {
    txt: 'LKKAL-卡卢特勒-KALUTARA',
    code: 'LKKAL'
  },
  {
    txt: 'LKKAN-坎凯桑图赖-KANKESANTURAI',
    code: 'LKKAN'
  },
  {
    txt: 'LKKAY-凯茨-KAYTS',
    code: 'LKKAY'
  },
  {
    txt: 'LKMAT-马特勒-MATARA',
    code: 'LKMAT'
  },
  {
    txt: 'LKNEG-尼甘布-NEGOMBO',
    code: 'LKNEG'
  },
  {
    txt: 'LKTRI-亭可马里-TRINCOMALEE',
    code: 'LKTRI'
  },
  {
    txt: 'LRBUC-布坎南-BUCHANAN',
    code: 'LRBUC'
  },
  {
    txt: 'LRCPA-帕尔马斯角-CAPE PALMAS',
    code: 'LRCPA'
  },
  {
    txt: 'LRGBA-大巴萨-GRAND BASSA',
    code: 'LRGBA'
  },
  {
    txt: 'LRGRE-格林维尔-GREENVILLE',
    code: 'LRGRE'
  },
  {
    txt: 'LRMAR-马歇尔-MARSHALL',
    code: 'LRMAR'
  },
  {
    txt: 'LRMON-蒙罗维亚-MONROVIA',
    code: 'LRMON'
  },
  {
    txt: 'LRRCS-里弗塞斯-RIVER CESS',
    code: 'LRRCS'
  },
  {
    txt: 'LTKLA-克莱佩达-KLAIPEDA',
    code: 'LTKLA'
  },
  {
    txt: 'LTVIS-维尔纽斯-VILNIUS',
    code: 'LTVIS'
  },
  {
    txt: 'LVLPX-利耶帕亚-LIEPAJA',
    code: 'LVLPX'
  },
  {
    txt: 'LVRIX-里加-RIGA',
    code: 'LVRIX'
  },
  {
    txt: 'LVVNT-文茨皮尔斯-VENTSPILS',
    code: 'LVVNT'
  },
  {
    txt: 'LYAZA-阿济伟亚-AZ ZAWIYAH',
    code: 'LYAZA'
  },
  {
    txt: 'LYBDA-巴迪亚-BARDIA',
    code: 'LYBDA'
  },
  {
    txt: 'LYBEN-班加西-BENGHAZI',
    code: 'LYBEN'
  },
  {
    txt: 'LYDER-德尔纳-DERNA',
    code: 'LYDER'
  },
  {
    txt: 'LYESR-锡德尔-ES SIDER',
    code: 'LYESR'
  },
  {
    txt: 'LYMEB-马萨勃利加-MARSA EL BREGA',
    code: 'LYMEB'
  },
  {
    txt: 'LYMIS-米苏拉塔区-MISURATA',
    code: 'LYMIS'
  },
  {
    txt: 'LYRLA-拉斯拉努夫-RASLANUF',
    code: 'LYRLA'
  },
  {
    txt: 'LYTOB-图卜鲁格-TOBRUK',
    code: 'LYTOB'
  },
  {
    txt: 'LYTRI-的黎波里-TRIPOLI',
    code: 'LYTRI'
  },
  {
    txt: 'LYZLE-兹利坦-ZLEITEN',
    code: 'LYZLE'
  },
  {
    txt: 'LYZUA-兹瓦拉-ZUARA',
    code: 'LYZUA'
  },
  {
    txt: 'LYZUE-祖埃提纳-ZUETINA',
    code: 'LYZUE'
  },
  {
    txt: 'MACAS-卡萨布兰卡-CASABLANCA',
    code: 'MACAS'
  },
  {
    txt: 'MAEJA-贾迪卡-EL JADIDA',
    code: 'MAEJA'
  },
  {
    txt: 'MAESS-索维拉-ESSAOUIRA',
    code: 'MAESS'
  },
  {
    txt: 'MAKEN-盖尼特拉-KENITRA',
    code: 'MAKEN'
  },
  {
    txt: 'MALAR-拉腊什-LARACHE',
    code: 'MALAR'
  },
  {
    txt: 'MAMOH-穆罕默迪耶-MOHAMMEDIA',
    code: 'MAMOH'
  },
  {
    txt: 'MAMTC-蒙特卡洛-MONTECARLO',
    code: 'MAMTC'
  },
  {
    txt: 'MARBT-拉巴特-RABAT',
    code: 'MARBT'
  },
  {
    txt: 'MASAF-萨菲-SAFI',
    code: 'MASAF'
  },
  {
    txt: 'MATAN-丹吉尔-TANGIER',
    code: 'MATAN'
  },
  {
    txt: 'MDCHU-基希讷乌-CHISINAU',
    code: 'MDCHU'
  },
  {
    txt: 'MGAHA-安塔拉哈-ANTALAHA',
    code: 'MGAHA'
  },
  {
    txt: 'MGANA-阿纳拉拉瓦-ANALALAVA',
    code: 'MGANA'
  },
  {
    txt: 'MYKUA-关丹-KUANTAN',
    code: 'MYKUA'
  },
  {
    txt: 'MYKUD-古达-KUDAT',
    code: 'MYKUD'
  },
  {
    txt: 'MYKUL-吉隆坡-KUALA LUMPUR',
    code: 'MYKUL'
  },
  {
    txt: 'MYKUN-库纳克-KUNAK',
    code: 'MYKUN'
  },
  {
    txt: 'MYLAD-拉哈达图-LAHAD DATU',
    code: 'MYLAD'
  },
  {
    txt: 'MYLGA-林加-LINGGA',
    code: 'MYLGA'
  },
  {
    txt: 'MYLUM-卢穆特-LUMUT',
    code: 'MYLUM'
  },
  {
    txt: 'MYMLR-米里-MIRI',
    code: 'MYMLR'
  },
  {
    txt: 'MYNIA-尼亚-NIAH',
    code: 'MYNIA'
  },
  {
    txt: 'MYPEN-槟城-PENANG',
    code: 'MYPEN'
  },
  {
    txt: 'MYPKE-巴生港-KELANG',
    code: 'MYPKE'
  },
  {
    txt: 'MYPRG-巴西古丹-PASIR GUDANG',
    code: 'MYPRG'
  },
  {
    txt: 'MYPWE-文德港-PORT WELD',
    code: 'MYPWE'
  },
  {
    txt: 'MYSAN-山打根-SANDAKAN',
    code: 'MYSAN'
  },
  {
    txt: 'MYSAR-泗里奎-SADKD',
    code: 'MYSAR'
  },
  {
    txt: 'MYSEJ-塞京卡-SEIINKAT',
    code: 'MYSEJ'
  },
  {
    txt: 'MYSEM-仙本那-SEMPOMA',
    code: 'MYSEM'
  },
  {
    txt: 'MYSIB-泗务-SIBU',
    code: 'MYSIB'
  },
  {
    txt: 'MYTAN-安顺-TELOK ANSON',
    code: 'MYTAN'
  },
  {
    txt: 'MYTAW-斗湖-TAWAU',
    code: 'MYTAW'
  },
  {
    txt: 'MYTJM-丹章马尼-TANJONG MANI',
    code: 'MYTJM'
  },
  {
    txt: 'MYTUM-通北-TUMPAT',
    code: 'MYTUM'
  },
  {
    txt: 'MYWAB-华莱士-WALLACE BAY',
    code: 'MYWAB'
  },
  {
    txt: 'MYWES-韦斯顿-WESTON',
    code: 'MYWES'
  },
  {
    txt: 'MZANE-安托尼奥埃尼什-ANTONIOENES',
    code: 'MZANE'
  },
  {
    txt: 'MZCDE-欣代-CHINDE',
    code: 'MZCDE'
  },
  {
    txt: 'MZIBO-伊博-IBO',
    code: 'MZIBO'
  },
  {
    txt: 'MZINH-伊尼扬巴内-INHAMBANE',
    code: 'MZINH'
  },
  {
    txt: 'MZMOC-莫辛布瓦-MOCIMBOA',
    code: 'MZMOC'
  },
  {
    txt: 'MZMOM-莫马-MOMA',
    code: 'MZMOM'
  },
  {
    txt: 'MZMOZ-莫桑比克-MOZAMBIQUE',
    code: 'MZMOZ'
  },
  {
    txt: 'MZNAC-纳卡拉-NACALA',
    code: 'MZNAC'
  },
  {
    txt: 'MZPEM-彭巴-PEMBA',
    code: 'MZPEM'
  },
  {
    txt: 'MZQUE-克利马内-QUELIMANE',
    code: 'MZQUE'
  },
  {
    txt: 'NALUD-卢德立次-LUDERITZ',
    code: 'NALUD'
  },
  {
    txt: 'NAWVB-鲸湾港-WALVIS BAY',
    code: 'NAWVB'
  },
  {
    txt: 'NCBAB-巴布勒特-BABOUILLAT',
    code: 'NCBAB'
  },
  {
    txt: 'NCNOA-努美阿-NOUMEA',
    code: 'NCNOA'
  },
  {
    txt: 'NFKIN-金斯敦-KINGSTON',
    code: 'NFKIN'
  },
  {
    txt: 'NGAKA-阿卡萨-AKASSA',
    code: 'NGAKA'
  },
  {
    txt: 'NGAPA-阿帕帕-APAPA',
    code: 'NGAPA'
  },
  {
    txt: 'NGBAD-巴达格里-BADAGRI',
    code: 'NGBAD'
  },
  {
    txt: 'NGBON-邦尼-BONNY',
    code: 'NGBON'
  },
  {
    txt: 'NGBRS-布拉斯-BRASS',
    code: 'NGBRS'
  },
  {
    txt: 'NGBUR-布鲁图-BURUTU',
    code: 'NGBUR'
  },
  {
    txt: 'NGCAL-卡拉巴尔-CALABAR',
    code: 'NGCAL'
  },
  {
    txt: 'NGDEG-代盖马-DEGEMA',
    code: 'NGDEG'
  },
  {
    txt: 'NGESC-拉沃斯河-ESCRAVOS',
    code: 'NGESC'
  },
  {
    txt: 'NGFOR-福卡多斯-FORCADOS',
    code: 'NGFOR'
  },
  {
    txt: 'NGKOK-科科-KOKO',
    code: 'NGKOK'
  },
  {
    txt: 'NGLAG-拉各斯-LAGOS',
    code: 'NGLAG'
  },
  {
    txt: 'NGOKR-奥克里卡-OKRIKA',
    code: 'NGOKR'
  },
  {
    txt: 'NGOPO-奥波博-OPOBO',
    code: 'NGOPO'
  },
  {
    txt: 'NGPHA-哈科特港-PORT HARCOURT',
    code: 'NGPHA'
  },
  {
    txt: 'NGPNT-彭宁顿码头-PENNINGTON TERMINAL',
    code: 'NGPNT'
  },
  {
    txt: 'NGQIB-夸伊博-QUA IBOE',
    code: 'NGQIB'
  },
  {
    txt: 'NGSAP-萨佩莱-SAPELE',
    code: 'NGSAP'
  },
  {
    txt: 'NGWAR-瓦里-WARRI',
    code: 'NGWAR'
  },
  {
    txt: 'NIBLU-布鲁菲尔兹-B1UEFIELDS',
    code: 'NIBLU'
  },
  {
    txt: 'NICOR-科林托-CORINTO',
    code: 'NICOR'
  },
  {
    txt: 'NIKAM-坎彭-KAMPEN',
    code: 'NIKAM'
  },
  {
    txt: 'NIMAA-马那瓜-MANAGUA',
    code: 'NIMAA'
  },
  {
    txt: 'NIMEP-梅珀尔-MEPPEL',
    code: 'NIMEP'
  },
  {
    txt: 'NIPCA-卡贝萨斯港-PUERTO CABEZAS',
    code: 'NIPCA'
  },
  {
    txt: 'NIPSA-圣蒂诺港-PUERTO SANDINO',
    code: 'NIPSA'
  },
  {
    txt: 'NISJS-南圣胡安-SAN JUAN DEL SUR',
    code: 'NISJS'
  },
  {
    txt: 'ACPHI-菲利普斯堡-PHILIPSBURG',
    code: 'ACPHI'
  },
  {
    txt: 'NLAII-阿尔克马-AIKMAAR',
    code: 'NLAII'
  },
  {
    txt: 'NLAMS-阿姆斯特丹-AMSTERDAM',
    code: 'NLAMS'
  },
  {
    txt: 'NLARN-阿纳姆-ARNHEM',
    code: 'NLARN'
  },
  {
    txt: 'NLBRO-布劳沃斯港-BROUWERSHAVEN',
    code: 'NLBRO'
  },
  {
    txt: 'NLBUS-比瑟姆-BUSSUM',
    code: 'NLBUS'
  },
  {
    txt: 'NLDEL-德尔夫寄尔-DELFZYL',
    code: 'NLDEL'
  },
  {
    txt: 'NLDOR-多德雷赫特-DORDRECHT',
    code: 'NLDOR'
  },
  {
    txt: 'NLEEM-埃姆斯哈文-EEMSHAVEN',
    code: 'NLEEM'
  },
  {
    txt: 'NLFIU-符拉辛-FIUSHING',
    code: 'NLFIU'
  },
  {
    txt: 'NLGRO-格罗宁根-GRONINGEN',
    code: 'NLGRO'
  },
  {
    txt: 'NLHAN-汉斯韦尔特-HANSWEERT',
    code: 'NLHAN'
  },
  {
    txt: 'NLHAR-哈灵根-HARLINGEN',
    code: 'NLHAR'
  },
  {
    txt: 'NLHOH-荷兰角-HOOK OF HOLLAND',
    code: 'NLHOH'
  },
  {
    txt: 'NLLDN-莱顿-LEIDEN',
    code: 'NLLDN'
  },
  {
    txt: 'NLLEE-吕伐登-LEEUWARDEN',
    code: 'NLLEE'
  },
  {
    txt: 'NLMID-米德尔堡-MIDDELBURG',
    code: 'NLMID'
  },
  {
    txt: 'NLROT-鹿特丹-ROTTERDAM',
    code: 'NLROT'
  },
  {
    txt: 'NLROZ-罗曾堡-ROZENBURG',
    code: 'NLROZ'
  },
  {
    txt: 'NLSCM-斯希丹-SCHIEDAM',
    code: 'NLSCM'
  },
  {
    txt: 'NLSLU-斯勒伊斯基尔-SLUISKIL',
    code: 'NLSLU'
  },
  {
    txt: 'NLSVG-萨斯范亨特-SAS VAN GHENT',
    code: 'NLSVG'
  },
  {
    txt: 'NLTIL-蒂尔堡-TILBURG',
    code: 'NLTIL'
  },
  {
    txt: 'NLVLA-弗拉尔丁恩-VLAARDINGEN',
    code: 'NLVLA'
  },
  {
    txt: 'NLVLO-文洛-VENLO',
    code: 'NLVLO'
  },
  {
    txt: 'NLWIL-威廉斯塔德-WILLEMSTAD',
    code: 'NLWIL'
  },
  {
    txt: 'NLZAA-赞丹-ZAANDAM',
    code: 'NLZAA'
  },
  {
    txt: 'NLZIE-济里克泽-ZIERIKZEE',
    code: 'NLZIE'
  },
  {
    txt: 'NLZUT-聚特芬-ZUTPHEN',
    code: 'NLZUT'
  },
  {
    txt: 'NOAAL-奥勒松-AALESUND',
    code: 'NOAAL'
  },
  {
    txt: 'NOAAR-阿尔达尔斯坦根-AARDALSTANGEN',
    code: 'NOAAR'
  },
  {
    txt: 'NOBGN-卑尔根-BERGEN',
    code: 'NOBGN'
  },
  {
    txt: 'NOBOD-博多-BODO',
    code: 'NOBOD'
  },
  {
    txt: 'NOBRE-布雷维克-BREVIK',
    code: 'NOBRE'
  },
  {
    txt: 'NODRA-德拉门-DRAMMEN',
    code: 'NODRA'
  },
  {
    txt: 'NOEGE-艾格松-EGERSUND',
    code: 'NOEGE'
  },
  {
    txt: 'NOEIT-艾特尔海姆-EITRHEIM',
    code: 'NOEIT'
  },
  {
    txt: 'NOFAG-法格斯特兰德-FAGERSTRAND',
    code: 'NOFAG'
  },
  {
    txt: 'NOFLE-弗莱克菲尤尔-FLEKKEFJORD',
    code: 'NOFLE'
  },
  {
    txt: 'NOFLO-弗卢勒-FLORO',
    code: 'NOFLO'
  },
  {
    txt: 'NOFRE-腓特烈斯塔-FREDRIKSTAD',
    code: 'NOFRE'
  },
  {
    txt: 'NOGRI-格里姆斯塔-GRIMSTAD',
    code: 'NOGRI'
  },
  {
    txt: 'NOHAL-哈尔登-HALDEN',
    code: 'NOHAL'
  },
  {
    txt: 'NOHAM-哈默菲斯特-HAMMERFEST',
    code: 'NOHAM'
  },
  {
    txt: 'NOHAR-哈尔斯塔-HARSTAD',
    code: 'NOHAR'
  },
  {
    txt: 'NOHER-哈略-HEROYA',
    code: 'NOHER'
  },
  {
    txt: 'NOHOL-霍尔默斯特兰-HOLMESTRAND',
    code: 'NOHOL'
  },
  {
    txt: 'NOHOR-霍腾-HORTEN',
    code: 'NOHOR'
  },
  {
    txt: 'NOHUS-许斯内斯-HUSNES',
    code: 'NOHUS'
  },
  {
    txt: 'NOHVK-哈维克-HAVIK',
    code: 'NOHVK'
  },
  {
    txt: 'NOKIR-希尔克内斯-KIRKENES',
    code: 'NOKIR'
  },
  {
    txt: 'NOKLE-克来文-KLEVEN',
    code: 'NOKLE'
  },
  {
    txt: 'NOKOP-科珀维克-KOPERVIK',
    code: 'NOKOP'
  },
  {
    txt: 'NOKRA-克拉格勒-KRAGERO',
    code: 'NOKRA'
  },
  {
    txt: 'NOKRN-克里斯蒂安松(北)-KRISTIANSUND N.',
    code: 'NOKRN'
  },
  {
    txt: 'NOKRS-克里斯蒂安桑(南)-KRISTIANSAND S．',
    code: 'NOKRS'
  },
  {
    txt: 'NOKVI-克维内斯达尔-KVINESDAL',
    code: 'NOKVI'
  },
  {
    txt: 'NOLAN-朗厄松-LANGESUND',
    code: 'NOLAN'
  },
  {
    txt: 'NOLIL-利勒桑-LILLESAND',
    code: 'NOLIL'
  },
  {
    txt: 'NOLOD-勒丁恩-LODINGEN',
    code: 'NOLOD'
  },
  {
    txt: 'NOLVK-拉尔维克-LARVIK',
    code: 'NOLVK'
  },
  {
    txt: 'NOLYN -林厄尔-LYNGOR',
    code: 'NOLYN '
  },
  {
    txt: 'NOMAA-马洛于-MAALOY',
    code: 'NOMAA'
  },
  {
    txt: 'NOMDL-曼达尔-MANDAL',
    code: 'NOMDL'
  },
  {
    txt: 'NOMEN-曼斯塔德-MENSTAD',
    code: 'NOMEN'
  },
  {
    txt: 'NOMJN-莫舍恩-MOSJOEN',
    code: 'NOMJN'
  },
  {
    txt: 'NOMLM-马尔姆-MALM',
    code: 'NOMLM'
  },
  {
    txt: 'NOMOL-莫尔德-MOLDE',
    code: 'NOMOL'
  },
  {
    txt: 'NOMON-蒙斯塔德-MONGSTAD',
    code: 'NOMON'
  },
  {
    txt: 'NOMOR-摩城-MOIRANA',
    code: 'NOMOR'
  },
  {
    txt: 'NOMOS-莫斯-MOSS',
    code: 'NOMOS'
  },
  {
    txt: 'NONAE-奈斯内斯-NAERSNES',
    code: 'NONAE'
  },
  {
    txt: 'NONAM-纳姆索斯-NAMSOS',
    code: 'NONAM'
  },
  {
    txt: 'NONAR-纳尔维克-NARVIK',
    code: 'NONAR'
  },
  {
    txt: 'NOODD-奥达-ODDA',
    code: 'NOODD'
  },
  {
    txt: 'NOOPL-奥普洛-OPLO',
    code: 'NOOPL'
  },
  {
    txt: 'NOOSL-奥斯陆-OSLO',
    code: 'NOOSL'
  },
  {
    txt: 'NOPOR-波斯格伦-PORSGRUNN',
    code: 'NOPOR'
  },
  {
    txt: 'NORIS-里瑟尔-RISOR',
    code: 'NORIS'
  },
  {
    txt: 'NOSAN-桑讷菲尤尔-SANDEFJORD',
    code: 'NOSAN'
  },
  {
    txt: 'NOSAR-萨尔普斯堡-SARPSBORG',
    code: 'NOSAR'
  },
  {
    txt: 'NOSAU-赛于达-SAUDA',
    code: 'NOSAU'
  },
  {
    txt: 'NOSGN-斯韦尔根-SVELGEN',
    code: 'NOSGN'
  },
  {
    txt: 'NOSKI-希恩-SKIEN',
    code: 'NOSKI'
  },
  {
    txt: 'NOSKU-斯屈德内斯港-SKUDENESHAVN',
    code: 'NOSKU'
  },
  {
    txt: 'NOSLA-斯拉根-SLAGEN',
    code: 'NOSLA'
  },
  {
    txt: 'NOSNS-桑内斯-SANDNES',
    code: 'NOSNS'
  },
  {
    txt: 'NOSTA-斯塔万格-STAVANGER',
    code: 'NOSTA'
  },
  {
    txt: 'NOSTE-斯泰恩谢尔-STEINKJER',
    code: 'NOSTE'
  },
  {
    txt: 'NOSUN-孙达尔瑟拉-SUNNDALSORA',
    code: 'NOSUN'
  },
  {
    txt: 'NOSVK-斯瓦尔维克-SVELVIK',
    code: 'NOSVK'
  },
  {
    txt: 'NOSVO-斯沃尔韦尔-SVO1VAER',
    code: 'NOSVO'
  },
  {
    txt: 'NOTHA-塔姆港-THAMSHAMN',
    code: 'NOTHA'
  },
  {
    txt: 'NOTHM-特隆赫姆-TRONDHEIM',
    code: 'NOTHM'
  },
  {
    txt: 'NOTON-滕斯贝格-TONSBERG',
    code: 'NOTON'
  },
  {
    txt: 'NOTOU-塔乌-TOU',
    code: 'NOTOU'
  },
  {
    txt: 'NOTRO-特罗姆瑟-TROMSO',
    code: 'NOTRO'
  },
  {
    txt: 'NOVAD-瓦德瑟-VADSO',
    code: 'NOVAD'
  },
  {
    txt: 'NOVAK-瓦克斯达尔-VAKSDAI',
    code: 'NOVAK'
  },
  {
    txt: 'NOVAR-沃尔德-VARDO',
    code: 'NOVAR'
  },
  {
    txt: 'NOVOL-沃尔达-VOLDA',
    code: 'NOVOL'
  },
  {
    txt: 'NRNRI-瑙鲁岛-NAURU ISLAND',
    code: 'NRNRI'
  },
  {
    txt: 'NRYAR-亚伦-YAREN',
    code: 'NRYAR'
  },
  {
    txt: 'NUALO-阿洛菲-ALOFI',
    code: 'NUALO'
  },
  {
    txt: 'NZAUC-奥克兰-AUCKLAND',
    code: 'NZAUC'
  },
  {
    txt: 'NZCHI-克赖斯特切奇-CHRISTCHURCH',
    code: 'NZCHI'
  },
  {
    txt: 'NZDUN-达尼丁-DUNEDIN',
    code: 'NZDUN'
  },
  {
    txt: 'NZGRE-格雷茅斯-GREVMOUTH',
    code: 'NZGRE'
  },
  {
    txt: 'NZINV-因弗卡吉-HVERCARGILL',
    code: 'NZINV'
  },
  {
    txt: 'NZLYT-利特尔顿-LYTTELTON',
    code: 'NZLYT'
  },
  {
    txt: 'NZNAP-内皮尔-NAPIER',
    code: 'NZNAP'
  },
  {
    txt: 'NZNEL-纳尔逊-NELSON',
    code: 'NZNEL'
  },
  {
    txt: 'NZNPL-新普利茅斯-NEW PLYMOUTH',
    code: 'NZNPL'
  },
  {
    txt: 'NZOAM-奥马鲁-OAMARU',
    code: 'NZOAM'
  },
  {
    txt: 'NZOPU-奥普阿-OPUA',
    code: 'NZOPU'
  },
  {
    txt: 'NZPCH-查马斯港-PON CHAIMERS',
    code: 'NZPCH'
  },
  {
    txt: 'NZPIC-皮克顿-PICTON',
    code: 'NZPIC'
  },
  {
    txt: 'NZTAU-陶朗阿-TAURANGA',
    code: 'NZTAU'
  },
  {
    txt: 'NZTIM-蒂马鲁-TIMARU',
    code: 'NZTIM'
  },
  {
    txt: 'NZWAH-韦弗利港-WAVERLEY HARBOUR',
    code: 'NZWAH'
  },
  {
    txt: 'NZWAN-旺加努伊-WANGANUI',
    code: 'NZWAN'
  },
  {
    txt: 'NZWEL-惠灵顿-WELLINGTON',
    code: 'NZWEL'
  },
  {
    txt: 'NZWHA-璜加雷-WHAHGAREI',
    code: 'NZWHA'
  },
  {
    txt: 'OMMAF-费赫勒港-MINAAL FAHAL',
    code: 'OMMAF'
  },
  {
    txt: 'OMMQS-米纳卡布斯-MINA QABOOS',
    code: 'OMMQS'
  },
  {
    txt: 'OMMUA-马斯喀特-MUSCAT',
    code: 'OMMUA'
  },
  {
    txt: 'OMSAL-塞拉莱-SALALAH',
    code: 'OMSAL'
  },
  {
    txt: 'PAAGD-阿瓜杜尔塞-AGUA DULCE',
    code: 'PAAGD'
  },
  {
    txt: 'PAARM-安蒙勒斯-ARMUELLES',
    code: 'PAARM'
  },
  {
    txt: 'PABAI-巴尔博亚-BALBOA',
    code: 'PABAI'
  },
  {
    txt: 'PACGE-大奇里基-CMDQUI GRANDE',
    code: 'PACGE'
  },
  {
    txt: 'PACOI-科隆-COLON',
    code: 'PACOI'
  },
  {
    txt: 'PALPA-拉帕尔马-LA PALMA',
    code: 'PALPA'
  },
  {
    txt: 'PAPAC-巴拿马城-PANAMA CITY',
    code: 'PAPAC'
  },
  {
    txt: 'PAPAN-帕纳马-PANAMA',
    code: 'PAPAN'
  },
  {
    txt: 'PAPED-佩德雷加尔-PEDREGAL',
    code: 'PAPED'
  },
  {
    txt: 'PEANC-安孔-ANCON',
    code: 'PEANC'
  },
  {
    txt: 'PEATI-阿蒂科-ATICO',
    code: 'PEATI'
  },
  {
    txt: 'PEC01-科伊希科-COISCO',
    code: 'PEC01'
  },
  {
    txt: 'PECAL-卡亚俄-CALLAO',
    code: 'PECAL'
  },
  {
    txt: 'PECBL-卡沃布兰科-CABO BLANCO',
    code: 'PECBL'
  },
  {
    txt: 'PECCB-康昌-CONCHAN BEACH',
    code: 'PECCB'
  },
  {
    txt: 'PECHA-钱凯-CHANCAY',
    code: 'PECHA'
  },
  {
    txt: 'PECHI-钦博塔-CHIMBOTE',
    code: 'PECHI'
  },
  {
    txt: 'PECMA-奇卡马-CHICAMA',
    code: 'PECMA'
  },
  {
    txt: 'PEETN-埃腾-ETEN',
    code: 'PEETN'
  },
  {
    txt: 'PEGSM-圣马丁将军镇-GENERAL SAN MARTIN',
    code: 'PEGSM'
  },
  {
    txt: 'PEHMY-瓦尔梅-HUARMEY',
    code: 'PEHMY'
  },
  {
    txt: 'PEHUA-瓦乔-HUACHO',
    code: 'PEHUA'
  },
  {
    txt: 'PEILO-伊洛-ILO',
    code: 'PEILO'
  },
  {
    txt: 'PEIQU-伊基托斯-IQUITOS',
    code: 'PEIQU'
  },
  {
    txt: 'PELDT-洛布斯岛-LOBOSDE TIERRA',
    code: 'PELDT'
  },
  {
    txt: 'PELIM-利马-LIMA',
    code: 'PELIM'
  },
  {
    txt: 'PELOB-洛维托斯-LOBITOS',
    code: 'PELOB'
  },
  {
    txt: 'PELOM-洛马斯-LOMAS',
    code: 'PELOM'
  },
  {
    txt: 'PELPA-拉帕姆皮拉-LAPAMPILLA',
    code: 'PELPA'
  },
  {
    txt: 'PEMAT-马塔拉尼-MATARANI',
    code: 'PEMAT'
  },
  {
    txt: 'PEMOL-莫延多-MOLLENDO',
    code: 'PEMOL'
  },
  {
    txt: 'PEPAC-帕卞斯马约-PACASMAYO',
    code: 'PEPAC'
  },
  {
    txt: 'PEPAR-帕拉蒙加-PARAMONGA',
    code: 'PEPAR'
  },
  {
    txt: 'PEPBA-巴约瓦尔港-PUERTO BAYOVAR',
    code: 'PEPBA'
  },
  {
    txt: 'PEPCO-皮斯科-PISCO',
    code: 'PEPCO'
  },
  {
    txt: 'PEPIM-皮门特尔-PIMENTEL',
    code: 'PEPIM'
  },
  {
    txt: 'PEPIS-皮萨瓜-PISAGUA',
    code: 'PEPIS'
  },
  {
    txt: 'PEPTA-派塔-PAITA',
    code: 'PEPTA'
  },
  {
    txt: 'PESAL-萨拉韦里-SALAVERRY',
    code: 'PESAL'
  },
  {
    txt: 'PESJU-圣胡安-SAN JUAN',
    code: 'PESJU'
  },
  {
    txt: 'PESNI-圣尼古拉斯-SAN NICOLAS',
    code: 'PESNI'
  },
  {
    txt: 'PESUP-苏佩-SUPE',
    code: 'PESUP'
  },
  {
    txt: 'PETAL-塔拉拉-TALARA',
    code: 'PETAL'
  },
  {
    txt: 'PETDM-坦博—德莫拉-TAMBO DE MORA',
    code: 'PETDM'
  },
  {
    txt: 'PFHAO-豪岛-HAOISLAND',
    code: 'PFHAO'
  },
  {
    txt: 'PFMAK-麦卡梯-MAKATEA',
    code: 'PFMAK'
  },
  {
    txt: 'PFMUR-穆鲁路-MURUROA',
    code: 'PFMUR'
  },
  {
    txt: 'PFPAP-帕皮提-PAPEETE',
    code: 'PFPAP'
  },
  {
    txt: 'PFVAI-韦他佩-VAITAPE',
    code: 'PFVAI'
  },
  {
    txt: 'PGAIT-艾塔佩-AITAPE',
    code: 'PGAIT'
  },
  {
    txt: 'PGALO-阿洛陶-ALOTAU',
    code: 'PGALO'
  },
  {
    txt: 'PGANB-安诺瓦湾-ANEWA BAY',
    code: 'PGANB'
  },
  {
    txt: 'PGBUK-布卡-BUKA',
    code: 'PGBUK'
  },
  {
    txt: 'PGBUN-布纳-BUNA',
    code: 'PGBUN'
  },
  {
    txt: 'PGCHO-霍斯金斯角-CAPE HOSKINS',
    code: 'PGCHO'
  },
  {
    txt: 'PGDAR-达鲁-DARU',
    code: 'PGDAR'
  },
  {
    txt: 'PGFIN-芬什哈芬-FINSCHAVEN',
    code: 'PGFIN'
  },
  {
    txt: 'PGGAI-加斯马塔岛-GASMATA ISLAND',
    code: 'PGGAI'
  },
  {
    txt: 'PGKAV-卡维恩-KAVIENG',
    code: 'PGKAV'
  },
  {
    txt: 'PGKTA-基埃塔-KIETA',
    code: 'PGKTA'
  },
  {
    txt: 'PGLAE-莱城-LAE',
    code: 'PGLAE'
  },
  {
    txt: 'PGLOR-洛伦高-LORENGAU',
    code: 'PGLOR'
  },
  {
    txt: 'PGMOR-莫罗贝-MOROBE',
    code: 'PGMOR'
  },
  {
    txt: 'PGORO-奥鲁湾-ORO BAY',
    code: 'PGORO'
  },
  {
    txt: 'PGPMO-莫尔兹比港-PORT MORESBY',
    code: 'PGPMO'
  },
  {
    txt: 'PGSAL-萨拉毛-SALAMAUA',
    code: 'PGSAL'
  },
  {
    txt: 'PGSAM-萨马赖-SAMARAI',
    code: 'PGSAM'
  },
  {
    txt: 'PGWEK-威瓦克-WEWAK',
    code: 'PGWEK'
  },
  {
    txt: 'PGWOI-伍德拉克岛-WOODIARK ISLAND',
    code: 'PGWOI'
  },
  {
    txt: 'PHAMU-阿姆尼坦-AMUNITAN',
    code: 'PHAMU'
  },
  {
    txt: 'PHANA-阿纳根-ANAKAN',
    code: 'PHANA'
  },
  {
    txt: 'PHANT-安蒂莫纳-ANTIMONAN',
    code: 'PHANT'
  },
  {
    txt: 'PHBAC-巴科洛德-BACOLOD',
    code: 'PHBAC'
  },
  {
    txt: 'PHBAS-拜斯-BAIS',
    code: 'PHBAS'
  },
  {
    txt: 'PHBAT-八打雁-BATANGAS',
    code: 'PHBAT'
  },
  {
    txt: 'PHBTN-巴丹-BATAAN',
    code: 'PHBTN'
  },
  {
    txt: 'PHBUG-博哥-BUGO',
    code: 'PHBUG'
  },
  {
    txt: 'PHBUT-武端-BUTUAN',
    code: 'PHBUT'
  },
  {
    txt: 'PHCAP-卡皮斯-CAPIZ',
    code: 'PHCAP'
  },
  {
    txt: 'PHCAV-甲米地-CAVITE',
    code: 'PHCAV'
  },
  {
    txt: 'PHCDO-卡加延德奥罗-CAGAYANDEORO',
    code: 'PHCDO'
  },
  {
    txt: 'PHCEB-宿务-CEBU',
    code: 'PHCEB'
  },
  {
    txt: 'PHDAV-达沃-DAVAO',
    code: 'PHDAV'
  },
  {
    txt: 'PHDGB-丁阿兰湾-DINGALAN BAY',
    code: 'PHDGB'
  },
  {
    txt: 'PHDIP-第波罗-DIPOLOG',
    code: 'PHDIP'
  },
  {
    txt: 'PHDUM-杜马格特-DUREAGUETE',
    code: 'PHDUM'
  },
  {
    txt: 'PHGSA-桑托斯-GENERAL SANTOS',
    code: 'PHGSA'
  },
  {
    txt: 'PHGUI-吉马拉斯岛-GUIMARAS ISLAND',
    code: 'PHGUI'
  },
  {
    txt: 'PHIBL-伊萨贝尔-ISABEL',
    code: 'PHIBL'
  },
  {
    txt: 'PHILA-伊萨贝拉-ISABELA',
    code: 'PHILA'
  },
  {
    txt: 'PHILI-尹利甘-ILIGAN',
    code: 'PHILI'
  },
  {
    txt: 'PHJPA-荷塞庞阿尼班村-JOSE PANGANIBAN',
    code: 'PHJPA'
  },
  {
    txt: 'SMXS-三门峡西-null',
    code: 'SMXS'
  },
  {
    txt: 'CNDQD-大庆东-null',
    code: 'CNDQD'
  },
  {
    txt: 'ZRH-朱日和-null',
    code: 'ZRH'
  },
  {
    txt: 'INKAT-印诺尔港-KATTUPALLI',
    code: 'INKAT'
  },
  {
    txt: 'USGWO-格林伍德-GREENWOOD',
    code: 'USGWO'
  },
  {
    txt: 'SYD-沈阳东-null',
    code: 'SYD'
  },
  {
    txt: 'CNHD-邯郸-null',
    code: 'CNHD'
  },
  {
    txt: 'CNCZ-沧州-null',
    code: 'CNCZ'
  },
  {
    txt: 'CNQMT-前磨头-null',
    code: 'CNQMT'
  },
  {
    txt: 'CNSZ-深州-null',
    code: 'CNSZ'
  },
  {
    txt: 'CNLHS1-临河-null',
    code: 'CNLHS1'
  },
  {
    txt: 'CNWYS1-五原-null',
    code: 'CNWYS1'
  },
  {
    txt: 'CNXIW-襄阳-XIANGYANG',
    code: 'CNXIW'
  },
  {
    txt: 'ZJYYX-浙江余姚西-null',
    code: 'ZJYYX'
  },
  {
    txt: 'LNTSF-辽宁天师符-null',
    code: 'LNTSF'
  },
  {
    txt: 'GXNNN-广西南宁南-null',
    code: 'GXNNN'
  },
  {
    txt: 'XS-元氏-null',
    code: 'XS'
  },
  {
    txt: 'XCZ-新城子-null',
    code: 'XCZ'
  },
  {
    txt: 'XY-新沂-null',
    code: 'XY'
  },
  {
    txt: 'LZN-柳州南-null',
    code: 'LZN'
  },
  {
    txt: 'CNXNY-新余-XINYU',
    code: 'CNXNY'
  },
  {
    txt: 'HNSAP-圣彼得苏拉-SAN PEDRO SULA',
    code: 'HNSAP'
  },
  {
    txt: 'CNQIONGH-琼海-null',
    code: 'CNQIONGH'
  },
  {
    txt: 'CNMJ-棉竹-null',
    code: 'CNMJ'
  },
  {
    txt: 'XT-邢台-null',
    code: 'XT'
  },
  {
    txt: 'Ambarli-阿姆巴利港-Ambarli',
    code: 'Ambarli'
  },
  {
    txt: 'CNZZ-株洲-Zhuzhou',
    code: 'CNZZ'
  },
  {
    txt: 'CNEZHOU-鄂州-EZHOU',
    code: 'CNEZHOU'
  },
  {
    txt: 'CNTIELI-铁岭-null',
    code: 'CNTIELI'
  },
  {
    txt: 'CNPUTAN-圃田-null',
    code: 'CNPUTAN'
  },
  {
    txt: 'EGAIS- 苏科纳- SOKHNA',
    code: 'EGAIS'
  },
  {
    txt: 'HAILJQI-七台河-null',
    code: 'HAILJQI'
  },
  {
    txt: 'N_RA0015-Rio Cullen Marine Terminal-Rio Cullen Marine Terminal',
    code: 'N_RA0015'
  },
  {
    txt: 'N_RA0016-River Plate Lightering Areas-River Plate Lightering Areas',
    code: 'N_RA0016'
  },
  {
    txt: 'N_RA0017-圣胡利安-San Julian',
    code: 'N_RA0017'
  },
  {
    txt: 'N_RA0018-San Lorenzo - San Martin-San Lorenzo - San Martin',
    code: 'N_RA0018'
  },
  {
    txt: 'N_RA0019-San Nicolas (Argentina)-San Nicolas (Argentina)',
    code: 'N_RA0019'
  },
  {
    txt: 'N_RA0020-San Pedro (Argentina)-San Pedro (Argentina)',
    code: 'N_RA0020'
  },
  {
    txt: 'N_RA0021-萨拉特-Zarate',
    code: 'N_RA0021'
  },
  {
    txt: 'N_RA0022-Zona Comun-Zona Comun',
    code: 'N_RA0022'
  },
  {
    txt: 'N_ARU001-Oranjestad (Aruba)-Oranjestad (Aruba)',
    code: 'N_ARU001'
  },
  {
    txt: 'N_ARU002-San Nicolas (Aruba)-San Nicolas (Aruba)',
    code: 'N_ARU002'
  },
  {
    txt: 'N_AUS001-阿博特波特-Abbot Point',
    code: 'N_AUS001'
  },
  {
    txt: 'N_AUS002-Albany (Australia)-Albany (Australia)',
    code: 'N_AUS002'
  },
  {
    txt: 'N_AUS003-Ardrossan (Australia)-Ardrossan (Australia)',
    code: 'N_AUS003'
  },
  {
    txt: 'N_AUS004-Barry Beach-Barry Beach',
    code: 'N_AUS004'
  },
  {
    txt: 'N_AUS005-Bayu Undan Terminal-Bayu Undan Terminal',
    code: 'N_AUS005'
  },
  {
    txt: 'N_AUS006-博罗卢拉-Bing Bong',
    code: 'N_AUS006'
  },
  {
    txt: 'N_AUS007-福莱特瑞-Cape Flattery',
    code: 'N_AUS007'
  },
  {
    txt: 'N_AUS008-Cape Jervis-Cape Jervis',
    code: 'N_AUS008'
  },
  {
    txt: 'N_AUS009-普林斯顿-Cape Preston',
    code: 'N_AUS009'
  },
  {
    txt: 'N_AUS0010-Challis Venture Terminal-Challis Venture Terminal',
    code: 'N_AUS0010'
  },
  {
    txt: 'N_AUS0011-丹皮尔-Dampier',
    code: 'N_AUS0011'
  },
  {
    txt: 'N_AUS0012-Devonport (Australia)-Devonport (Australia)',
    code: 'N_AUS0012'
  },
  {
    txt: 'N_AUS0013-Exmouth (Australia)-Exmouth (Australia)',
    code: 'N_AUS0013'
  },
  {
    txt: 'N_AUS0014-Flinders Island-Flinders Island',
    code: 'N_AUS0014'
  },
  {
    txt: 'N_AUS0015-FPSO Okha-FPSO Okha',
    code: 'N_AUS0015'
  },
  {
    txt: 'N_AUS0016-FPSO Ningaloo Vision-FPSO Ningaloo Vision',
    code: 'N_AUS0016'
  },
  {
    txt: 'N_AUS0017-FPSO Armada Claire-FPSO Armada Claire',
    code: 'N_AUS0017'
  },
  {
    txt: 'N_AUS0018-FPSO Montara Venture-FPSO Montara Venture',
    code: 'N_AUS0018'
  },
  {
    txt: 'N_AUS0019-FPSO Maersk Ngujima Yin-FPSO Maersk Ngujima Yin',
    code: 'N_AUS0019'
  },
  {
    txt: 'N_AUS0020-杰拉尔顿-Geraldton',
    code: 'N_AUS0020'
  },
  {
    txt: 'N_AUS0021-戈夫-Gove',
    code: 'N_AUS0021'
  },
  {
    txt: 'N_AUS0022-哈斯廷斯-Hastings',
    code: 'N_AUS0022'
  },
  {
    txt: 'N_AUS0023-卡伦巴-Karumba',
    code: 'N_AUS0023'
  },
  {
    txt: 'N_AUS0024-King Island-King Island',
    code: 'N_AUS0024'
  },
  {
    txt: 'N_AUS0025-棱角-Klein Point',
    code: 'N_AUS0025'
  },
  {
    txt: 'N_AUS0026-Laminaria-Corallina Terminal-Laminaria-Corallina Terminal',
    code: 'N_AUS0026'
  },
  {
    txt: 'N_AUS0027-米尔纳湾-Milner Bay',
    code: 'N_AUS0027'
  },
  {
    txt: 'N_AUS0028-Mutineer-Exeter Development-Mutineer-Exeter Development',
    code: 'N_AUS0028'
  },
  {
    txt: 'N_AUS0029-Newcastle (Australia)-Newcastle (Australia)',
    code: 'N_AUS0029'
  },
  {
    txt: 'N_AUS0030-Nganhurra Terminal-Nganhurra Terminal',
    code: 'N_AUS0030'
  },
  {
    txt: 'N_AUS0031-Onslow-Onslow',
    code: 'N_AUS0031'
  },
  {
    txt: 'N_AUS0032-Penneshaw-Penneshaw',
    code: 'N_AUS0032'
  },
  {
    txt: 'N_AUS0033-Port Botany-Port Botany',
    code: 'N_AUS0033'
  },
  {
    txt: 'N_AUS0034-Port Phillip Bay-Port Phillip Bay',
    code: 'N_AUS0034'
  },
  {
    txt: 'N_AUS0035-Portland (Australia)-Portland (Australia)',
    code: 'N_AUS0035'
  },
  {
    txt: 'N_AUS0036-Port Bundaberg-Port Bundaberg',
    code: 'N_AUS0036'
  },
  {
    txt: 'N_AUS0037-波尼森港-Port Bonython',
    code: 'N_AUS0037'
  },
  {
    txt: 'LDYN-卢迪亚纳-LUDHIANA',
    code: 'LDYN'
  },
  {
    txt: 'CNCXS-城厢-null',
    code: 'CNCXS'
  },
  {
    txt: 'CNYMM-衙门庙-null',
    code: 'CNYMM'
  },
  {
    txt: 'CMKRI-克里比-KRIBI',
    code: 'CMKRI'
  },
  {
    txt: 'CNHAI-海安-HAIAN',
    code: 'CNHAI'
  },
  {
    txt: 'CNJJ-井陉-null',
    code: 'CNJJ'
  },
  {
    txt: 'XJWB-新疆乌北-null',
    code: 'XJWB'
  },
  {
    txt: 'HBXY-湖北襄阳-null',
    code: 'HBXY'
  },
  {
    txt: 'XM-新民-null',
    code: 'XM'
  },
  {
    txt: 'WQ-吴桥-null',
    code: 'WQ'
  },
  {
    txt: 'CT-昌图-null',
    code: 'CT'
  },
  {
    txt: 'lrx-隆尧县-null',
    code: 'lrx'
  },
  {
    txt: 'KZAY-阿腾科里-ALTYNKOL',
    code: 'KZAY'
  },
  {
    txt: 'GANZHOU-赣州-null',
    code: 'GANZHOU'
  },
  {
    txt: 'US LOSAN- 洛杉机-null',
    code: 'US LOSAN'
  },
  {
    txt: 'Onne-奥纳港-Onne',
    code: 'Onne'
  },
  {
    txt: 'LIAONG-朝阳-null',
    code: 'LIAONG'
  },
  {
    txt: 'EGDEK-狄克拉-DEKHEILA',
    code: 'EGDEK'
  },
  {
    txt: 'N_AUS0038-Port of Amrun-Port of Amrun',
    code: 'N_AUS0038'
  },
  {
    txt: 'N_AUS0039-Port of Ashburton-Port of Ashburton',
    code: 'N_AUS0039'
  },
  {
    txt: 'N_AUS0040-Port Melville-Port Melville',
    code: 'N_AUS0040'
  },
  {
    txt: 'N_AUS0041-Pyrenees Marine Terminal-Pyrenees Marine Terminal',
    code: 'N_AUS0041'
  },
  {
    txt: 'N_AUS0042-Saladin Marine Terminal-Saladin Marine Terminal',
    code: 'N_AUS0042'
  },
  {
    txt: 'N_AUS0043-Skardon River-Skardon River',
    code: 'N_AUS0043'
  },
  {
    txt: 'N_AUS0044-Spring Bay-Spring Bay',
    code: 'N_AUS0044'
  },
  {
    txt: 'N_AUS0045-Stanley (Australia)-Stanley (Australia)',
    code: 'N_AUS0045'
  },
  {
    txt: 'N_AUS0046-Stag Marine Terminal-Stag Marine Terminal',
    code: 'N_AUS0046'
  },
  {
    txt: 'N_AUS0047-Sydney (Australia)-Sydney (Australia)',
    code: 'N_AUS0047'
  },
  {
    txt: 'N_AUS0048-塞夫纳德-Thevenard',
    code: 'N_AUS0048'
  },
  {
    txt: 'N_AUS0049-星期四岛港-Thursday Island',
    code: 'N_AUS0049'
  },
  {
    txt: 'N_AUS0050-Torres Strait & Great Barrier Reef-Torres Strait & Great Barrier Reef',
    code: 'N_AUS0050'
  },
  {
    txt: 'N_AUS0051-瓦拉奴斯港?-Varanus Island Terminal',
    code: 'N_AUS0051'
  },
  {
    txt: 'N_AUS0052-Wandoo Marine Terminal-Wandoo Marine Terminal',
    code: 'N_AUS0052'
  },
  {
    txt: 'N_AUS0053-亚姆巴-Yamba',
    code: 'N_AUS0053'
  },
  {
    txt: 'N_AZ001-Cais do Pico-Cais do Pico',
    code: 'N_AZ001'
  },
  {
    txt: 'N_AZ002-Praia da Vitoria-Praia da Vitoria',
    code: 'N_AZ002'
  },
  {
    txt: 'N_AZ003-Praia da Graciosa-Praia da Graciosa',
    code: 'N_AZ003'
  },
  {
    txt: 'N_AZ004-Velas-Velas',
    code: 'N_AZ004'
  },
  {
    txt: 'N_AZ005-Vila do Porto-Vila do Porto',
    code: 'N_AZ005'
  },
  {
    txt: 'N_BS001-Bimini-Bimini',
    code: 'N_BS001'
  },
  {
    txt: 'N_BS002-Freeport (Bahamas)-Freeport (Bahamas)',
    code: 'N_BS002'
  },
  {
    txt: 'N_BS003-Gorda Cay-Gorda Cay',
    code: 'N_BS003'
  },
  {
    txt: 'N_BS004-Half Moon Cay-Half Moon Cay',
    code: 'N_BS004'
  },
  {
    txt: 'N_BS005-Inagua Islands-Inagua Islands',
    code: 'N_BS005'
  },
  {
    txt: 'N_BS006-Marsh Harbor-Marsh Harbor',
    code: 'N_BS006'
  },
  {
    txt: 'N_BS007-Ocean Cay-Ocean Cay',
    code: 'N_BS007'
  },
  {
    txt: 'N_BS008-Slaughter Harbour-Slaughter Harbour',
    code: 'N_BS008'
  },
  {
    txt: 'N_BS009-南里丁角-South Riding Point',
    code: 'N_BS009'
  },
  {
    txt: 'N_BRN001-Khalifa Bin Salman-Khalifa Bin Salman',
    code: 'N_BRN001'
  },
  {
    txt: 'N_BRN002-Mina Salman-Mina Salman',
    code: 'N_BRN002'
  },
  {
    txt: 'N_BRN003-锡特拉-Sitra',
    code: 'N_BRN003'
  },
  {
    txt: 'N_BNG001-吉大港-Chittagong',
    code: 'N_BNG001'
  },
  {
    txt: 'N_BNG002-蒙拉-Mongla',
    code: 'N_BNG002'
  },
  {
    txt: 'N_B001-Genk-Genk',
    code: 'N_B001'
  },
  {
    txt: 'N_B002-Izegem-Izegem',
    code: 'N_B002'
  },
  {
    txt: 'N_B003-雷格-Liege',
    code: 'N_B003'
  },
  {
    txt: 'N_B004-Meerhout-Meerhout',
    code: 'N_B004'
  },
  {
    txt: 'N_B005-Nieuwpoort-Nieuwpoort',
    code: 'N_B005'
  },
  {
    txt: 'N_B006-奥斯坦德-Ostend',
    code: 'N_B006'
  },
  {
    txt: 'N_B007-Ruisbroek-Ruisbroek',
    code: 'N_B007'
  },
  {
    txt: 'N_B008-Willebroek-Willebroek',
    code: 'N_B008'
  },
  {
    txt: 'N_B009-泽布腊赫-Zeebrugge',
    code: 'N_B009'
  },
  {
    txt: 'N_BL001-Belize City-Belize City',
    code: 'N_BL001'
  },
  {
    txt: 'N_BL002-比格克里克-Big Creek',
    code: 'N_BL002'
  },
  {
    txt: 'N_BL003-斯坦克里克-Commerce Bight',
    code: 'N_BL003'
  },
  {
    txt: 'N_BE001-Freeport (Bermuda)-Freeport (Bermuda)',
    code: 'N_BE001'
  },
  {
    txt: 'N_BE002-Hamilton (Bermuda)-Hamilton (Bermuda)',
    code: 'N_BE002'
  },
  {
    txt: "N_BE003-St George's (Bermuda)-St George's (Bermuda)",
    code: 'N_BE003'
  },
  {
    txt: 'N_BON001-Bonoil Terminal-Bonoil Terminal',
    code: 'N_BON001'
  },
  {
    txt: 'N_BON002-Bopec Terminal-Bopec Terminal',
    code: 'N_BON002'
  },
  {
    txt: 'N_BON003-克腊伦代克-Kralendijk',
    code: 'N_BON003'
  },
  {
    txt: 'N_BON004-萨利纳-Salina',
    code: 'N_BON004'
  },
  {
    txt: 'N_BR001-Acu Superport-Acu Superport',
    code: 'N_BR001'
  },
  {
    txt: 'N_BR002-安东尼纳-Antonina',
    code: 'N_BR002'
  },
  {
    txt: 'N_BR003-阿拉图-Aratu',
    code: 'N_BR003'
  },
  {
    txt: 'N_BR004-阿腊卡茹-Aracaju',
    code: 'N_BR004'
  },
  {
    txt: 'N_BR005-Barra dos Coqueiros-Barra dos Coqueiros',
    code: 'N_BR005'
  },
  {
    txt: 'N_BR006-Barcarena-Barcarena',
    code: 'N_BR006'
  },
  {
    txt: 'N_BR007-BR ADR FPSO P74-BR ADR FPSO P74',
    code: 'N_BR007'
  },
  {
    txt: 'N_BR008-Breves-Breves',
    code: 'N_BR008'
  },
  {
    txt: 'N_BR009-Buzios-Buzios',
    code: 'N_BR009'
  },
  {
    txt: 'INKRI-克里什纳帕特南-KRISHNAPATNAM',
    code: 'INKRI'
  },
  {
    txt: 'PHMNS-马尼拉南港-MANILA SOUTH HARBOUR',
    code: 'PHMNS'
  },
  {
    txt: 'CNMEI-梅州-MEIZHOU',
    code: 'CNMEI'
  },
  {
    txt: 'CNSQI-宿迁-SUQIAN',
    code: 'CNSQI'
  },
  {
    txt: 'CNLFB-廊坊北-null',
    code: 'CNLFB'
  },
  {
    txt: 'CNZB-淄博-null',
    code: 'CNZB'
  },
  {
    txt: 'CNXYX-夏邑县-null',
    code: 'CNXYX'
  },
  {
    txt: 'CLCOR-科罗内尔-CORONEL',
    code: 'CLCOR'
  },
  {
    txt: 'QAHMD-哈马德-HAMAD',
    code: 'QAHMD'
  },
  {
    txt: 'wn-渭南-null',
    code: 'wn'
  },
  {
    txt: 'PLMLW-马拉舍维奇-MALASZEWICZE',
    code: 'PLMLW'
  },
  {
    txt: 'JPSBS-志不志-SHIBUSHI',
    code: 'JPSBS'
  },
  {
    txt: '3080-梅杰乌-MEDEU',
    code: '3080'
  },
  {
    txt: 'CNCHU-滁州-CHUZHOU',
    code: 'CNCHU'
  },
  {
    txt: 'YANAN-延安-null',
    code: 'YANAN'
  },
  {
    txt: 'JIANGSU-如皋市-null',
    code: 'JIANGSU'
  },
  {
    txt: 'NGTIN-廷坎岛港-TINCAN',
    code: 'NGTIN'
  },
  {
    txt: 'FUZHOU-抚州-null',
    code: 'FUZHOU'
  },
  {
    txt: 'CYLARNA-拉纳卡-LARNACA',
    code: 'CYLARNA'
  },
  {
    txt: 'IGNATIUS-圣伊格内修斯-null',
    code: 'IGNATIUS'
  },
  {
    txt: 'HENAN-河南-null',
    code: 'HENAN'
  },
  {
    txt: 'SHANGRAO-上饶-null',
    code: 'SHANGRAO'
  },
  {
    txt: 'XICBAZH-巴中-BAZHONG',
    code: 'XICBAZH'
  },
  {
    txt: 'N_AL001-Himara-Himara',
    code: 'N_AL001'
  },
  {
    txt: 'N_AL002-Romano-Romano',
    code: 'N_AL002'
  },
  {
    txt: 'N_AL003-洛尔-Vlore',
    code: 'N_AL003'
  },
  {
    txt: 'N_AG001-安纳巴-Annaba',
    code: 'N_AG001'
  },
  {
    txt: 'N_AG002-阿尔泽-Arzew',
    code: 'N_AG002'
  },
  {
    txt: 'N_AG003-Arzew El-Djedid-Arzew El-Djedid',
    code: 'N_AG003'
  },
  {
    txt: 'N_AG004-贝贾亚-Bejaia',
    code: 'N_AG004'
  },
  {
    txt: 'N_AG005-Beni Saf-Beni Saf',
    code: 'N_AG005'
  },
  {
    txt: 'N_AG006-Cherchell-Cherchell',
    code: 'N_AG006'
  },
  {
    txt: 'N_AG007-Collo-Collo',
    code: 'N_AG007'
  },
  {
    txt: 'N_AG008-德利斯-Dellys',
    code: 'N_AG008'
  },
  {
    txt: 'N_AG009-DP World Djen-Djen-DP World Djen-Djen',
    code: 'N_AG009'
  },
  {
    txt: 'N_AG0010-内穆尔-Ghazaouet',
    code: 'N_AG0010'
  },
  {
    txt: 'N_AG0011-Jijel-Jijel',
    code: 'N_AG0011'
  },
  {
    txt: 'N_AG0012-默斯塔加内姆-Mostaganem',
    code: 'N_AG0012'
  },
  {
    txt: 'N_AG0013-奥兰-Oran',
    code: 'N_AG0013'
  },
  {
    txt: 'N_AG0014-Port Methanier-Port Methanier',
    code: 'N_AG0014'
  },
  {
    txt: 'N_AG0015-斯克基达-Skikda',
    code: 'N_AG0015'
  },
  {
    txt: 'N_AG0016-特内斯-Tenes',
    code: 'N_AG0016'
  },
  {
    txt: 'N_SAM001-Pago-Pago-Pago-Pago',
    code: 'N_SAM001'
  },
  {
    txt: 'N_AN001-Cabinda Oil Fields-Cabinda Oil Fields',
    code: 'N_AN001'
  },
  {
    txt: 'N_AN002-Dalia Terminal-Dalia Terminal',
    code: 'N_AN002'
  },
  {
    txt: 'N_AN003-FPSO Kaombo TMN Kaombo Norte-FPSO Kaombo TMN Kaombo Norte',
    code: 'N_AN003'
  },
  {
    txt: 'N_AN004-FPSO Kaombo TMN Kaombo Sul-FPSO Kaombo TMN Kaombo Sul',
    code: 'N_AN004'
  },
  {
    txt: 'N_AN005-FPSO Pazflor-FPSO Pazflor',
    code: 'N_AN005'
  },
  {
    txt: 'N_AN006-FPSO Gimboa-FPSO Gimboa',
    code: 'N_AN006'
  },
  {
    txt: 'N_AN007-FPSO PSVM-FPSO PSVM',
    code: 'N_AN007'
  },
  {
    txt: 'N_AN008-FPSO CLOV-FPSO CLOV',
    code: 'N_AN008'
  },
  {
    txt: 'N_AN009-FPSO Armada Olombendo-FPSO Armada Olombendo',
    code: 'N_AN009'
  },
  {
    txt: "N_AN0010-FPSO 'N'Goma FPSO'-FPSO 'N'Goma FPSO'",
    code: 'N_AN0010'
  },
  {
    txt: 'N_AN0011-FPSO Sanha LPG-FPSO Sanha LPG',
    code: 'N_AN0011'
  },
  {
    txt: 'N_AN0012-Futila Terminal-Futila Terminal',
    code: 'N_AN0012'
  },
  {
    txt: 'N_AN0013-Girassol Terminal-Girassol Terminal',
    code: 'N_AN0013'
  },
  {
    txt: 'N_AN0014-Greater Plutonio Terminal-Greater Plutonio Terminal',
    code: 'N_AN0014'
  },
  {
    txt: 'N_AN0015-Kizomba A Terminal-Kizomba A Terminal',
    code: 'N_AN0015'
  },
  {
    txt: 'N_AN0016-Kizomba B Terminal-Kizomba B Terminal',
    code: 'N_AN0016'
  },
  {
    txt: 'N_AN0017-Kizomba C Terminal-Kizomba C Terminal',
    code: 'N_AN0017'
  },
  {
    txt: 'N_AN0018-马隆格港-Malongo Terminal',
    code: 'N_AN0018'
  },
  {
    txt: 'N_AN0019-内米贝-Namibe',
    code: 'N_AN0019'
  },
  {
    txt: 'N_AN0020-Palanca Terminal-Palanca Terminal',
    code: 'N_AN0020'
  },
  {
    txt: 'N_AN0021-索尤-Soyo',
    code: 'N_AN0021'
  },
  {
    txt: 'N_AN0022-Takula Terminal-Takula Terminal',
    code: 'N_AN0022'
  },
  {
    txt: 'N_AT001-north sound-north sound',
    code: 'N_AT001'
  },
  {
    txt: "N_AT002-St John's (Antigua)-St John's (Antigua)",
    code: 'N_AT002'
  },
  {
    txt: 'N_RA001-Arroyo Seco-Arroyo Seco',
    code: 'N_RA001'
  },
  {
    txt: 'N_RA002-阿图查-Atucha',
    code: 'N_RA002'
  },
  {
    txt: 'N_RA003-Barranqueras-Barranqueras',
    code: 'N_RA003'
  },
  {
    txt: 'N_RA004-卡莱塔保拉-Caleta Paula',
    code: 'N_RA004'
  },
  {
    txt: 'N_RA005-里瓦达维亚海军准将城-Comodoro Rivadavia',
    code: 'N_RA005'
  },
  {
    txt: 'N_RA006-孔塞普雄-Concepcion del Uruguay',
    code: 'N_RA006'
  },
  {
    txt: 'N_RA007-Del Guazu-Del Guazu',
    code: 'N_RA007'
  },
  {
    txt: 'N_RA008-Deseado-Deseado',
    code: 'N_RA008'
  },
  {
    txt: 'N_RA009-Escobar LNG-Escobar LNG',
    code: 'N_RA009'
  },
  {
    txt: 'N_RA0010-Puerto Martins-Puerto Martins',
    code: 'N_RA0010'
  },
  {
    txt: 'N_RA0011-Puerto Parana-Puerto Parana',
    code: 'N_RA0011'
  },
  {
    txt: 'N_RA0012-库拉-Punta Quilla',
    code: 'N_RA0012'
  },
  {
    txt: 'N_RA0013-洛尤拉-Punta Loyola',
    code: 'N_RA0013'
  },
  {
    txt: 'N_RA0014-Rio Grande (Argentina)-Rio Grande (Argentina)',
    code: 'N_RA0014'
  },
  {
    txt: 'N_CHI0034-海安-Haian (Guangdong Province)',
    code: 'N_CHI0034'
  },
  {
    txt: 'N_CHI0035-Haian (Jiangsu Province)-Haian (Jiangsu Province)',
    code: 'N_CHI0035'
  },
  {
    txt: 'N_CHI0036-Hechuan-Hechuan',
    code: 'N_CHI0036'
  },
  {
    txt: 'N_CHI0037-淮阴-Huaiyin',
    code: 'N_CHI0037'
  },
  {
    txt: 'N_CHI0038-黄骅港-Huanghua',
    code: 'N_CHI0038'
  },
  {
    txt: 'N_CHI0039-淮安-Huaian',
    code: 'N_CHI0039'
  },
  {
    txt: 'N_CHI0040-Huainan-Huainan',
    code: 'N_CHI0040'
  },
  {
    txt: 'N_CHI0041-Huilai Power Station-Huilai Power Station',
    code: 'N_CHI0041'
  },
  {
    txt: 'N_CHI0042-Huizhou Terminal-Huizhou Terminal',
    code: 'N_CHI0042'
  },
  {
    txt: 'N_CHI0043-湖口-Hukou',
    code: 'N_CHI0043'
  },
  {
    txt: 'N_CHI0044-葫芦岛-Huludao',
    code: 'N_CHI0044'
  },
  {
    txt: "N_CHI0045-Ji'an-Ji'an",
    code: 'N_CHI0045'
  },
  {
    txt: 'N_CHI0046-Jiantiao-Jiantiao',
    code: 'N_CHI0046'
  },
  {
    txt: 'N_CHI0047-Jianhu-Jianhu',
    code: 'N_CHI0047'
  },
  {
    txt: 'N_CHI0048-Jiading-Jiading',
    code: 'N_CHI0048'
  },
  {
    txt: 'N_CHI0049-金山-Jinshan',
    code: 'N_CHI0049'
  },
  {
    txt: 'N_CHI0050-荆州-Jingzhou',
    code: 'N_CHI0050'
  },
  {
    txt: 'N_CHI0051-Jingdezhen-Jingdezhen',
    code: 'N_CHI0051'
  },
  {
    txt: 'N_CHI0052-济宁-Jining',
    code: 'N_CHI0052'
  },
  {
    txt: 'N_CHI0053-Jinshi-Jinshi',
    code: 'N_CHI0053'
  },
  {
    txt: 'N_CHI0054-靖江-Jingjiang',
    code: 'N_CHI0054'
  },
  {
    txt: 'N_CHI0055-Kemen-Kemen',
    code: 'N_CHI0055'
  },
  {
    txt: 'N_CHI0056-兰溪-Lanxi',
    code: 'N_CHI0056'
  },
  {
    txt: 'N_CHI0057-Leping-Leping',
    code: 'N_CHI0057'
  },
  {
    txt: 'N_CHI0058-Lihekou-Lihekou',
    code: 'N_CHI0058'
  },
  {
    txt: 'N_CHI0059-Lijiaxiang-Lijiaxiang',
    code: 'N_CHI0059'
  },
  {
    txt: 'N_CHI0060-Linhai-Linhai',
    code: 'N_CHI0060'
  },
  {
    txt: 'N_CHI0061-longhai-longhai',
    code: 'N_CHI0061'
  },
  {
    txt: 'N_CHI0062-陆丰-Lufeng Terminal',
    code: 'N_CHI0062'
  },
  {
    txt: 'N_CHI0063-吕四-Lusi',
    code: 'N_CHI0063'
  },
  {
    txt: 'N_CHI0064-泸州-Luzhou',
    code: 'N_CHI0064'
  },
  {
    txt: 'N_CHI0065-海口-Macun',
    code: 'N_CHI0065'
  },
  {
    txt: 'N_CHI0066-Maocaojie-Maocaojie',
    code: 'N_CHI0066'
  },
  {
    txt: 'N_CHI0067-南港（上海）-nangang(shanghai)',
    code: 'N_CHI0067'
  },
  {
    txt: 'N_CHI0068-南汇-Nanhui',
    code: 'N_CHI0068'
  },
  {
    txt: 'N_CHI0069-宁德-Ningde',
    code: 'N_CHI0069'
  },
  {
    txt: 'N_CHI0070-Panyu Oil Terminal-Panyu Oil Terminal',
    code: 'N_CHI0070'
  },
  {
    txt: 'N_CHI0071-彭泽-Pengze',
    code: 'N_CHI0071'
  },
  {
    txt: 'N_CHI0072-Pingnan-Pingnan',
    code: 'N_CHI0072'
  },
  {
    txt: 'N_CHI0073-Pinghu-Pinghu',
    code: 'N_CHI0073'
  },
  {
    txt: 'N_CHI0074-邳州-Pizhou',
    code: 'N_CHI0074'
  },
  {
    txt: 'N_CHI0075-Poyang-Poyang',
    code: 'N_CHI0075'
  },
  {
    txt: 'N_CHI0076-启东-Qidong (Jiangsu)',
    code: 'N_CHI0076'
  },
  {
    txt: 'N_CHI0077-秦皇岛-Qinhuangdao',
    code: 'N_CHI0077'
  },
  {
    txt: 'N_CHI0078-Qingpu-Qingpu',
    code: 'N_CHI0078'
  },
  {
    txt: 'N_CHI0079-Qinglanshan Oil Terminal-Qinglanshan Oil Terminal',
    code: 'N_CHI0079'
  },
  {
    txt: 'N_CHI0080-Ranlishan-Ranlishan',
    code: 'N_CHI0080'
  },
  {
    txt: 'N_CHI0081-瑞昌-Ruichang',
    code: 'N_CHI0081'
  },
  {
    txt: 'N_CHI0082-SAN MEN-SAN MEN',
    code: 'N_CHI0082'
  },
  {
    txt: 'N_CHI0083-Sanbing-Sanbing',
    code: 'N_CHI0083'
  },
  {
    txt: 'N_CHI0084-Shahezi-Shahezi',
    code: 'N_CHI0084'
  },
  {
    txt: 'N_CHI0085-Shi Wei-Shi Wei',
    code: 'N_CHI0085'
  },
  {
    txt: 'N_CHI0086-石浦-Shipu',
    code: 'N_CHI0086'
  },
  {
    txt: 'N_CHI0087-Shilong-Shilong',
    code: 'N_CHI0087'
  },
  {
    txt: 'N_CHI0088-水东-Shui Dong',
    code: 'N_CHI0088'
  },
  {
    txt: 'N_CHI0089-Shuifu-Shuifu',
    code: 'N_CHI0089'
  },
  {
    txt: 'N_CHI0090-Sian-Sian',
    code: 'N_CHI0090'
  },
  {
    txt: 'N_CHI0091-Songjiang-Songjiang',
    code: 'N_CHI0091'
  },
  {
    txt: 'N_CHI0092-泰兴-Taixing',
    code: 'N_CHI0092'
  },
  {
    txt: 'N_CHI0093-田镇-Tianzhen',
    code: 'N_CHI0093'
  },
  {
    txt: 'N_CHI0094-Tongshan-Tongshan',
    code: 'N_CHI0094'
  },
  {
    txt: 'N_CHI0095-万州-Wanzhou',
    code: 'N_CHI0095'
  },
  {
    txt: 'N_CHI0096-威海-Wei Hai',
    code: 'N_CHI0096'
  },
  {
    txt: 'N_CHI0097-Weitang-Weitang',
    code: 'N_CHI0097'
  },
  {
    txt: 'N_CHI0098-Wucheng-Wucheng',
    code: 'N_CHI0098'
  },
  {
    txt: 'N_CHI0099-Wujiang-Wujiang',
    code: 'N_CHI0099'
  },
  {
    txt: 'N_CHI00100-Wutong-Wutong',
    code: 'N_CHI00100'
  },
  {
    txt: 'N_CHI00101-武穴-Wuxue',
    code: 'N_CHI00101'
  },
  {
    txt: 'N_CHI00102-象山-Xiangshan Harbour',
    code: 'N_CHI00102'
  },
  {
    txt: 'N_CHI00103-Xiaopu-Xiaopu',
    code: 'N_CHI00103'
  },
  {
    txt: 'N_CHI00104-Xiangfan-Xiangfan',
    code: 'N_CHI00104'
  },
  {
    txt: 'N_CHI00105-Xiashi-Xiashi',
    code: 'N_CHI00105'
  },
  {
    txt: 'N_CHI00106-Xiangtan-Xiangtan',
    code: 'N_CHI00106'
  },
  {
    txt: 'N_CHI00107-Xijiang Marine Terminal-Xijiang Marine Terminal',
    code: 'N_CHI00107'
  },
  {
    txt: 'N_CHI00108-Xingzi-Xingzi',
    code: 'N_CHI00108'
  },
  {
    txt: 'N_CHI00109-Xinghua-Xinghua',
    code: 'N_CHI00109'
  },
  {
    txt: 'N_CHI00110-洋口-Yangkou',
    code: 'N_CHI00110'
  },
  {
    txt: 'N_CHI00111-洋山-Yangshan',
    code: 'N_CHI00111'
  },
  {
    txt: 'N_CHI00112-盐城-Yancheng',
    code: 'N_CHI00112'
  },
  {
    txt: 'N_CHI00113-宜宾-Yibin',
    code: 'N_CHI00113'
  },
  {
    txt: 'N_CHI00114-宜昌-Yichang',
    code: 'N_CHI00114'
  },
  {
    txt: 'N_CHI00115-Yingtan-Yingtan',
    code: 'N_CHI00115'
  },
  {
    txt: 'N_CHI00116-Yixing-Yixing',
    code: 'N_CHI00116'
  },
  {
    txt: 'N_CHI00117-Yiyang-Yiyang',
    code: 'N_CHI00117'
  },
  {
    txt: 'N_CHI00118-沅江-Yuanjiang',
    code: 'N_CHI00118'
  },
  {
    txt: 'N_CHI00119-Yuedong LNG Terminal-Yuedong LNG Terminal',
    code: 'N_CHI00119'
  },
  {
    txt: 'N_CHI00120-岳阳-Yueyang',
    code: 'N_CHI00120'
  },
  {
    txt: 'N_CHI00121-Yutang-Yutang',
    code: 'N_CHI00121'
  },
  {
    txt: 'N_CHI00122-Zhangshu-Zhangshu',
    code: 'N_CHI00122'
  },
  {
    txt: 'N_CHI00123-张家埠-Zhangjiabu',
    code: 'N_CHI00123'
  },
  {
    txt: 'N_CHI00124-枝城-Zhicheng (Hubei Province)',
    code: 'N_CHI00124'
  },
  {
    txt: 'N_CHI00125-Zhicheng (Zhejiang Province)-Zhicheng (Zhejiang Province)',
    code: 'N_CHI00125'
  },
  {
    txt: 'N_CHI00126-珠江口-Zhujiangkou',
    code: 'N_CHI00126'
  },
  {
    txt: 'N_CY004-博阿兹-Kalecik',
    code: 'N_CY004'
  },
  {
    txt: 'N_CY005-凯里尼亚-Kyrenia',
    code: 'N_CY005'
  },
  {
    txt: 'N_CY006-Latchi-Latchi',
    code: 'N_CY006'
  },
  {
    txt: 'N_CY007-Moni-Moni',
    code: 'N_CY007'
  },
  {
    txt: 'N_CY008-STS Limassol OPL-STS Limassol OPL',
    code: 'N_CY008'
  },
  {
    txt: 'N_CY009-Teknecik-Teknecik',
    code: 'N_CY009'
  },
  {
    txt: 'N_CY0010-Vassiliko-Vassiliko',
    code: 'N_CY0010'
  },
  {
    txt: 'N_CY0011-Vassiliko Power Station-Vassiliko Power Station',
    code: 'N_CY0011'
  },
  {
    txt: 'N_DK001-沃本罗-Aabenraa',
    code: 'N_DK001'
  },
  {
    txt: 'N_DK002-奥尔堡-Aalborg',
    code: 'N_DK002'
  },
  {
    txt: 'N_DK003-奥尔胡斯-Aarhus',
    code: 'N_DK003'
  },
  {
    txt: 'N_DK004-Aeroskobing-Aeroskobing',
    code: 'N_DK004'
  },
  {
    txt: 'N_DK005-阿格松-Aggersund',
    code: 'N_DK005'
  },
  {
    txt: 'N_DK006-Akzo Nobel Salt-Akzo Nobel Salt',
    code: 'N_DK006'
  },
  {
    txt: 'N_DK007-Allinge-Allinge',
    code: 'N_DK007'
  },
  {
    txt: 'N_DK008-Asnaesvaerkets Havn-Asnaesvaerkets Havn',
    code: 'N_DK008'
  },
  {
    txt: 'N_DK009-阿森斯-Assens',
    code: 'N_DK009'
  },
  {
    txt: 'N_DK0010-Avedoere-Avedoere',
    code: 'N_DK0010'
  },
  {
    txt: 'N_DK0011-Ballen-Ballen',
    code: 'N_DK0011'
  },
  {
    txt: 'N_DK0012-班霍尔姆-Bandholm',
    code: 'N_DK0012'
  },
  {
    txt: 'N_DK0013-哥本哈根-Copenhagen',
    code: 'N_DK0013'
  },
  {
    txt: 'N_DK0014-Dania-Dania',
    code: 'N_DK0014'
  },
  {
    txt: 'N_DK0015-Ebeltoft-Ebeltoft',
    code: 'N_DK0015'
  },
  {
    txt: 'N_DK0016-Elsinore-Elsinore',
    code: 'N_DK0016'
  },
  {
    txt: 'N_DK0017-沃本罗-Ensted',
    code: 'N_DK0017'
  },
  {
    txt: 'N_DK0018-埃斯比约-Esbjerg',
    code: 'N_DK0018'
  },
  {
    txt: 'N_DK0019-Faaborg-Faaborg',
    code: 'N_DK0019'
  },
  {
    txt: 'N_DK0020-Fakse Ladeplads-Fakse Ladeplads',
    code: 'N_DK0020'
  },
  {
    txt: 'N_DK0021-腓特烈港-Frederikshavn',
    code: 'N_DK0021'
  },
  {
    txt: 'N_DK0022-Frederiksvaerk-Frederiksvaerk',
    code: 'N_DK0022'
  },
  {
    txt: 'N_DK0023-腓特烈西亚-Fredericia',
    code: 'N_DK0023'
  },
  {
    txt: 'N_DK0024-Fynshav-Fynshav',
    code: 'N_DK0024'
  },
  {
    txt: 'N_DK0025-格塞-Gedser',
    code: 'N_DK0025'
  },
  {
    txt: 'N_DK0026-Gilleleje-Gilleleje',
    code: 'N_DK0026'
  },
  {
    txt: 'N_DK0027-Glatved Lasteanlaeg-Glatved Lasteanlaeg',
    code: 'N_DK0027'
  },
  {
    txt: 'N_DK0028-Graasten-Graasten',
    code: 'N_DK0028'
  },
  {
    txt: 'N_DK0029-格林纳-Grenaa',
    code: 'N_DK0029'
  },
  {
    txt: 'N_DK0030-古尔夫哈文-Gulfhavn',
    code: 'N_DK0030'
  },
  {
    txt: 'N_DK0031-海松-Hadsund',
    code: 'N_DK0031'
  },
  {
    txt: 'N_DK0032-Hals-Hals',
    code: 'N_DK0032'
  },
  {
    txt: 'N_DK0033-Hammerhavn-Hammerhavn',
    code: 'N_DK0033'
  },
  {
    txt: 'N_DK0034-汉斯特霍尔姆-Hanstholm',
    code: 'N_DK0034'
  },
  {
    txt: 'N_DK0035-Havnso-Havnso',
    code: 'N_DK0035'
  },
  {
    txt: 'N_DK0036-希茨海尔斯-Hirtshals',
    code: 'N_DK0036'
  },
  {
    txt: 'N_DK0037-Hobro-Hobro',
    code: 'N_DK0037'
  },
  {
    txt: 'N_DK0038-Holstebro-Struer-Holstebro-Struer',
    code: 'N_DK0038'
  },
  {
    txt: 'N_DK0039-霍尔森斯-Horsens',
    code: 'N_DK0039'
  },
  {
    txt: 'N_DK0040-Hundested Havn-Hundested Havn',
    code: 'N_DK0040'
  },
  {
    txt: 'N_DK0041-Hvide Sande-Hvide Sande',
    code: 'N_DK0041'
  },
  {
    txt: 'N_DK0042-凯隆堡-Kalundborg',
    code: 'N_DK0042'
  },
  {
    txt: 'N_DK0043-Kerteminde-Kerteminde',
    code: 'N_DK0043'
  },
  {
    txt: 'N_DK0044-凯耶-Koge',
    code: 'N_DK0044'
  },
  {
    txt: 'N_DK0045-科耳林-Kolding',
    code: 'N_DK0045'
  },
  {
    txt: 'N_DK0046-Kolby Kaas-Kolby Kaas',
    code: 'N_DK0046'
  },
  {
    txt: 'N_DK0047-Korsoer-Korsoer',
    code: 'N_DK0047'
  },
  {
    txt: 'N_DK0048-Kragenaes-Kragenaes',
    code: 'N_DK0048'
  },
  {
    txt: 'N_DK0049-Kyndby-Kyndby',
    code: 'N_DK0049'
  },
  {
    txt: 'N_DK0050-Langor-Langor',
    code: 'N_DK0050'
  },
  {
    txt: 'N_DK0051-累姆维-Lemvig',
    code: 'N_DK0051'
  },
  {
    txt: 'N_DK0052-Loegstoer-Loegstoer',
    code: 'N_DK0052'
  },
  {
    txt: 'N_DK0053-马斯特尔-Marstal',
    code: 'N_DK0053'
  },
  {
    txt: 'N_DK0054-Marup-Marup',
    code: 'N_DK0054'
  },
  {
    txt: 'N_DK0055-米泽尔法特-Middelfart',
    code: 'N_DK0055'
  },
  {
    txt: 'N_DK0056-讷斯特佛德-Naestved',
    code: 'N_DK0056'
  },
  {
    txt: 'N_DK0057-纳克斯科夫-Nakskov',
    code: 'N_DK0057'
  },
  {
    txt: 'N_DK0058-讷克塞-Nekso',
    code: 'N_DK0058'
  },
  {
    txt: 'N_DK0059-尼堡-Nyborg',
    code: 'N_DK0059'
  },
  {
    txt: 'N_DK0060-Nykobing (Falster)-Nykobing (Falster)',
    code: 'N_DK0060'
  },
  {
    txt: 'N_DK0061-Nykobing (Mors)-Nykobing (Mors)',
    code: 'N_DK0061'
  },
  {
    txt: 'N_DK0062-Nykobing (Sjaelland)-Nykobing (Sjaelland)',
    code: 'N_DK0062'
  },
  {
    txt: 'N_DK0063-欧登塞-Odense',
    code: 'N_DK0063'
  },
  {
    txt: 'N_DK0064-奥霍夫德-Orehoved',
    code: 'N_DK0064'
  },
  {
    txt: 'N_DK0065-Osterby Havn-Osterby Havn',
    code: 'N_DK0065'
  },
  {
    txt: 'N_DK0066-兰内斯-Randers',
    code: 'N_DK0066'
  },
  {
    txt: 'N_DK0067-Ringkobing-Ringkobing',
    code: 'N_DK0067'
  },
  {
    txt: 'N_DK0068-Rodbyhavn-Rodbyhavn',
    code: 'N_DK0068'
  },
  {
    txt: 'N_DK0069-Romo-Romo',
    code: 'N_DK0069'
  },
  {
    txt: 'N_DK0070-勒讷-Ronne',
    code: 'N_DK0070'
  },
  {
    txt: 'N_DK0071-鲁兹克宾-Rudkobing',
    code: 'N_DK0071'
  },
  {
    txt: 'N_DK0072-Saelvig-Saelvig',
    code: 'N_DK0072'
  },
  {
    txt: 'N_DK0073-Sakskoebing-Sakskoebing',
    code: 'N_DK0073'
  },
  {
    txt: 'N_DK0074-Samso Island-Samso Island',
    code: 'N_DK0074'
  },
  {
    txt: 'N_DK0075-Sejero-Sejero',
    code: 'N_DK0075'
  },
  {
    txt: 'N_DK0076-Skaelskor-Skaelskor',
    code: 'N_DK0076'
  },
  {
    txt: 'N_DK0077-斯卡曼港-Skagen',
    code: 'N_DK0077'
  },
  {
    txt: 'N_DK0078-Skaerbaekvaerket Havn-Skaerbaekvaerket Havn',
    code: 'N_DK0078'
  },
  {
    txt: 'N_DK0079-斯基佛-Skive',
    code: 'N_DK0079'
  },
  {
    txt: 'N_DK0080-Soby Havn-Soby Havn',
    code: 'N_DK0080'
  },
  {
    txt: 'N_DK0081-斯泰厄-Stege',
    code: 'N_DK0081'
  },
  {
    txt: 'N_DK0082-Stigsnaesvaerkets Havn-Stigsnaesvaerkets Havn',
    code: 'N_DK0082'
  },
  {
    txt: 'N_DK0083-Stubbekobing-Stubbekobing',
    code: 'N_DK0083'
  },
  {
    txt: 'N_DK0084-斯图德斯特茹普-Studstrup',
    code: 'N_DK0084'
  },
  {
    txt: 'N_DK0085-Svendborg-Svendborg',
    code: 'N_DK0085'
  },
  {
    txt: 'N_DK0086-提斯特德-Thisted',
    code: 'N_DK0086'
  },
  {
    txt: 'N_DK0087-Thyboron-Thyboron',
    code: 'N_DK0087'
  },
  {
    txt: 'N_DK0088-瓦埃勒-Vejle',
    code: 'N_DK0088'
  },
  {
    txt: 'N_DK0089-Vestero Havn-Vestero Havn',
    code: 'N_DK0089'
  },
  {
    txt: 'N_DMC001-Portsmouth (Dominica)-Portsmouth (Dominica)',
    code: 'N_DMC001'
  },
  {
    txt: 'N_DMC002-罗索-Roseau',
    code: 'N_DMC002'
  },
  {
    txt: 'N_DOM001-安德烈斯液化天然气码头-AES Andres LNG Terminal',
    code: 'N_DOM001'
  },
  {
    txt: 'N_DOM002-Amber Cove-Amber Cove',
    code: 'N_DOM002'
  },
  {
    txt: 'N_DOM003-巴拉奥纳-Barahona',
    code: 'N_DOM003'
  },
  {
    txt: 'N_DOM004-博卡奇卡-Boca Chica',
    code: 'N_DOM004'
  },
  {
    txt: 'N_DOM005-红角-Cabo Rojo',
    code: 'N_DOM005'
  },
  {
    txt: 'N_DOM006-罗马纳-La Romana',
    code: 'N_DOM006'
  },
  {
    txt: 'N_DOM007-Manzanillo (Dominican Rep)-Manzanillo (Dominican Rep)',
    code: 'N_DOM007'
  },
  {
    txt: 'N_DOM008-Palenque-Palenque',
    code: 'N_DOM008'
  },
  {
    txt: 'N_DOM009-普拉塔港-Puerto Plata',
    code: 'N_DOM009'
  },
  {
    txt: 'N_DOM0010-阿苏瓦-Puerto Viejo de Azua',
    code: 'N_DOM0010'
  },
  {
    txt: 'N_DOM0011-杜阿特港-Samana',
    code: 'N_DOM0011'
  },
  {
    txt: 'N_DOM0012-圣佩德罗德马科里斯-San Pedro de Macoris',
    code: 'N_DOM0012'
  },
  {
    txt: 'N_EQ001-Bahia de Caraquez-Bahia de Caraquez',
    code: 'N_EQ001'
  },
  {
    txt: 'N_EQ002-埃斯梅拉达斯-Esmeraldas',
    code: 'N_EQ002'
  },
  {
    txt: 'N_EQ003-La Libertad (Ecuador)-La Libertad (Ecuador)',
    code: 'N_EQ003'
  },
  {
    txt: 'N_EQ004-曼塔-Manta',
    code: 'N_EQ004'
  },
  {
    txt: 'N_EQ005-蒙特韦尔德-Monteverde',
    code: 'N_EQ005'
  },
  {
    txt: 'N_EQ006-OCP Marine Terminal-OCP Marine Terminal',
    code: 'N_EQ006'
  },
  {
    txt: 'N_EQ007-Posorja-Posorja',
    code: 'N_EQ007'
  },
  {
    txt: 'N_EQ008-Puerto Bolivar (Ecuador)-Puerto Bolivar (Ecuador)',
    code: 'N_EQ008'
  },
  {
    txt: 'N_EQ009-Salinas-Salinas',
    code: 'N_EQ009'
  },
  {
    txt: 'N_EQ0010-San Lorenzo (Ecuador)-San Lorenzo (Ecuador)',
    code: 'N_EQ0010'
  },
  {
    txt: 'N_ET001-阿布基尔-Abu Qir',
    code: 'N_ET001'
  },
  {
    txt: 'N_ET002-Abu Zenima-Abu Zenima',
    code: 'N_ET002'
  },
  {
    txt: 'N_ET003-Abu Ghosoun-Abu Ghosoun',
    code: 'N_ET003'
  },
  {
    txt: 'N_ET004-阿达比亚-Adabiya',
    code: 'N_ET004'
  },
  {
    txt: 'N_ET005-Ain Sukhna Terminal-Ain Sukhna Terminal',
    code: 'N_ET005'
  },
  {
    txt: 'N_ET006-库塞尔-Al Qusayr',
    code: 'N_ET006'
  },
  {
    txt: 'N_ET007-Al-Tour-Al-Tour',
    code: 'N_ET007'
  },
  {
    txt: 'N_ET008-Alexandria (Egypt)-Alexandria (Egypt)',
    code: 'N_ET008'
  },
  {
    txt: 'N_ET009-Bernees-Bernees',
    code: 'N_ET009'
  },
  {
    txt: 'N_ET0010-Edco-Edco',
    code: 'N_ET0010'
  },
  {
    txt: 'N_ET0011-亚历山大-El Dekheila',
    code: 'N_ET0011'
  },
  {
    txt: 'N_ET0012-El Arish-El Arish',
    code: 'N_ET0012'
  },
  {
    txt: 'N_ET0013-El Maadiya-El Maadiya',
    code: 'N_ET0013'
  },
  {
    txt: 'N_ET0014-FPSO Al Zaafarana-FPSO Al Zaafarana',
    code: 'N_ET0014'
  },
  {
    txt: 'N_ET0015-汉拉维恩-Hamrawein',
    code: 'N_ET0015'
  },
  {
    txt: 'N_ET0016-Hurghada-Hurghada',
    code: 'N_ET0016'
  },
  {
    txt: 'N_ET0017-Mersa Matruh-Mersa Matruh',
    code: 'N_ET0017'
  },
  {
    txt: 'N_ET0018-阿拉曼-Mersa el Hamra',
    code: 'N_ET0018'
  },
  {
    txt: 'N_ET0019-Nuweibah-Nuweibah',
    code: 'N_ET0019'
  },
  {
    txt: 'N_ET0020-塞得港-Port Said',
    code: 'N_ET0020'
  },
  {
    txt: 'N_ET0021-Ras Budran-Ras Budran',
    code: 'N_ET0021'
  },
  {
    txt: 'N_ET0022-Ras Sudr-Ras Sudr',
    code: 'N_ET0022'
  },
  {
    txt: 'N_ET0023-腊斯舒海尔-Ras Shukheir',
    code: 'N_ET0023'
  },
  {
    txt: 'N_ET0024-腊斯加里卜-Ras Gharib',
    code: 'N_ET0024'
  },
  {
    txt: 'N_ET0025-Ras Malaab-Ras Malaab',
    code: 'N_ET0025'
  },
  {
    txt: 'N_ET0026-Rasif El Sharkiyoun-Rasif El Sharkiyoun',
    code: 'N_ET0026'
  },
  {
    txt: 'N_ET0027-Sadat-Sadat',
    code: 'N_ET0027'
  },
  {
    txt: 'N_ET0028-塞法杰-Safaga',
    code: 'N_ET0028'
  },
  {
    txt: 'N_ET0029-Sharm El-Sheikh-Sharm El-Sheikh',
    code: 'N_ET0029'
  },
  {
    txt: 'N_ET0030-Sidi Kerir Terminal-Sidi Kerir Terminal',
    code: 'N_ET0030'
  },
  {
    txt: 'N_ET0031-Suez-Suez',
    code: 'N_ET0031'
  },
  {
    txt: 'N_ET0032-Suez Canal-Suez Canal',
    code: 'N_ET0032'
  },
  {
    txt: 'N_ET0033-Wadi Feiran Terminal-Wadi Feiran Terminal',
    code: 'N_ET0033'
  },
  {
    txt: 'N_ET0034-Zeit Bay Terminal-Zeit Bay Terminal',
    code: 'N_ET0034'
  },
  {
    txt: 'N_ES001-Acajutla Offshore Terminal-Acajutla Offshore Terminal',
    code: 'N_ES001'
  },
  {
    txt: 'N_EGU001-Benito-Benito',
    code: 'N_EGU001'
  },
  {
    txt: 'N_EGU002-Ceiba Marine Terminal-Ceiba Marine Terminal',
    code: 'N_EGU002'
  },
  {
    txt: 'N_EGU003-Cogo-Cogo',
    code: 'N_EGU003'
  },
  {
    txt: 'N_EGU004-FPSO Aseng-FPSO Aseng',
    code: 'N_EGU004'
  },
  {
    txt: 'N_EGU005-卢巴-Luba',
    code: 'N_EGU005'
  },
  {
    txt: 'N_EGU006-Punta Europa Terminal-Punta Europa Terminal',
    code: 'N_EGU006'
  },
  {
    txt: 'N_EGU007-Zafiro Marine Terminal-Zafiro Marine Terminal',
    code: 'N_EGU007'
  },
  {
    txt: 'N_ERI001-伊萨布-Assab',
    code: 'N_ERI001'
  },
  {
    txt: 'N_EST001-海尔泰尔马-Heltermaa',
    code: 'N_EST001'
  },
  {
    txt: 'N_EST002-库伊瓦斯图-Kuivastu',
    code: 'N_EST002'
  },
  {
    txt: 'N_EST003-昆达-Kunda',
    code: 'N_EST003'
  },
  {
    txt: 'N_EST004-Lehtma-Lehtma',
    code: 'N_EST004'
  },
  {
    txt: 'N_EST005-Loksa-Loksa',
    code: 'N_EST005'
  },
  {
    txt: 'N_EST006-Miiduranna-Miiduranna',
    code: 'N_EST006'
  },
  {
    txt: 'N_EST007-Muuga-Port of Tallinn-Muuga-Port of Tallinn',
    code: 'N_EST007'
  },
  {
    txt: 'N_EST008-Paljassaare-Port of Tallinn-Paljassaare-Port of Tallinn',
    code: 'N_EST008'
  },
  {
    txt: 'N_EST009-Paldiski-Port of Tallinn-Paldiski-Port of Tallinn',
    code: 'N_EST009'
  },
  {
    txt: 'N_EST0010-Parnu-Parnu',
    code: 'N_EST0010'
  },
  {
    txt: 'N_EST0011-Ringsu-Ringsu',
    code: 'N_EST0011'
  },
  {
    txt: 'N_EST0012-Rohukula-Rohukula',
    code: 'N_EST0012'
  },
  {
    txt: 'N_EST0013-Roomassaare-Roomassaare',
    code: 'N_EST0013'
  },
  {
    txt: 'N_EST0014-Saaremaa-Port of Tallinn-Saaremaa-Port of Tallinn',
    code: 'N_EST0014'
  },
  {
    txt: 'N_EST0015-锡拉迈埃-Sillamae',
    code: 'N_EST0015'
  },
  {
    txt: 'N_EST0016-Sviby-Sviby',
    code: 'N_EST0016'
  },
  {
    txt: 'N_EST0017-Virtsu-Virtsu',
    code: 'N_EST0017'
  },
  {
    txt: 'N_FA001-Fuglafjordur-Fuglafjordur',
    code: 'N_FA001'
  },
  {
    txt: 'N_FA002-Klaksvik-Klaksvik',
    code: 'N_FA002'
  },
  {
    txt: 'N_FA003-Kollafjordur-Kollafjordur',
    code: 'N_FA003'
  },
  {
    txt: 'N_FA004-Midvaag-Midvaag',
    code: 'N_FA004'
  },
  {
    txt: 'N_FA005-Runavik-Runavik',
    code: 'N_FA005'
  },
  {
    txt: 'N_FA006-Skaala-Skaala',
    code: 'N_FA006'
  },
  {
    txt: 'N_FA007-Soervaag-Soervaag',
    code: 'N_FA007'
  },
  {
    txt: 'N_FA008-soldarfjordur-soldarfjordur',
    code: 'N_FA008'
  },
  {
    txt: 'N_FA009-Strendur-Strendur',
    code: 'N_FA009'
  },
  {
    txt: 'N_FA0010-Toftir-Toftir',
    code: 'N_FA0010'
  },
  {
    txt: 'N_FA0011-Torshavn-Torshavn',
    code: 'N_FA0011'
  },
  {
    txt: 'N_FA0012-Trangisvaag-Trangisvaag',
    code: 'N_FA0012'
  },
  {
    txt: 'N_FA0013-Vagur-Vagur',
    code: 'N_FA0013'
  },
  {
    txt: 'N_FA0014-Vestmanna-Vestmanna',
    code: 'N_FA0014'
  },
  {
    txt: 'N_FI001-Stanley (Falkland Islands)-Stanley (Falkland Islands)',
    code: 'N_FI001'
  },
  {
    txt: 'N_FJ001-劳托卡-Lautoka',
    code: 'N_FJ001'
  },
  {
    txt: 'N_FJ002-雷伍卡-Levuka',
    code: 'N_FJ002'
  },
  {
    txt: 'N_FJ003-Malau-Malau',
    code: 'N_FJ003'
  },
  {
    txt: 'N_FJ004-Savu Savu-Savu Savu',
    code: 'N_FJ004'
  },
  {
    txt: 'N_SF001-Berghamn Eckero-Berghamn Eckero',
    code: 'N_SF001'
  },
  {
    txt: 'N_SF002-dalsbruk-dalsbruk',
    code: 'N_SF002'
  },
  {
    txt: 'N_SF003-塔米萨里-Ekenas',
    code: 'N_SF003'
  },
  {
    txt: 'N_SF004-马里安哈米纳-Farjsund',
    code: 'N_SF004'
  },
  {
    txt: 'N_SF005-Forby-Forby',
    code: 'N_SF005'
  },
  {
    txt: 'N_SF006-哈米纳-Hamina',
    code: 'N_SF006'
  },
  {
    txt: 'N_SF007-汉科-Hanko',
    code: 'N_SF007'
  },
  {
    txt: 'N_SF008-因科-Inkoo',
    code: 'N_SF008'
  },
  {
    txt: 'N_SF009-Isnas-Isnas',
    code: 'N_SF009'
  },
  {
    txt: 'N_SF0010-皮埃塔尔萨里-Jakobstad',
    code: 'N_SF0010'
  },
  {
    txt: 'N_SF0011-庄恩苏-Joensuu',
    code: 'N_SF0011'
  },
  {
    txt: 'N_SF0012-朱特西诺-Joutseno',
    code: 'N_SF0012'
  },
  {
    txt: 'N_SF0013-Kalkkiranta-Kalkkiranta',
    code: 'N_SF0013'
  },
  {
    txt: 'N_SF0014-卡斯基宁-Kaskinen',
    code: 'N_SF0014'
  },
  {
    txt: 'N_SF0015-Kaukas-Kaukas',
    code: 'N_SF0015'
  },
  {
    txt: 'N_SF0016-凯米-Kemi',
    code: 'N_SF0016'
  },
  {
    txt: 'N_SF0017-Kemio-Kemio',
    code: 'N_SF0017'
  },
  {
    txt: 'N_SF0018-科科拉-Kokkola',
    code: 'N_SF0018'
  },
  {
    txt: 'N_SF0019-科特卡-Kotka',
    code: 'N_SF0019'
  },
  {
    txt: 'N_SF0020-科佛哈尔-Koverhar',
    code: 'N_SF0020'
  },
  {
    txt: 'N_SF0021-克里斯提南考蓬基-Kristinestad',
    code: 'N_SF0021'
  },
  {
    txt: 'N_SF0022-Kumpusalmi-Kumpusalmi',
    code: 'N_SF0022'
  },
  {
    txt: 'N_SF0023-langnas-langnas',
    code: 'N_SF0023'
  },
  {
    txt: 'N_SF0024-Lappohja-Lappohja',
    code: 'N_SF0024'
  },
  {
    txt: 'N_SF0025-Loviisa-Loviisa',
    code: 'N_SF0025'
  },
  {
    txt: 'N_SF0026-马里安哈米纳-Mariehamn',
    code: 'N_SF0026'
  },
  {
    txt: 'N_SF0027-Mustola-Mustola',
    code: 'N_SF0027'
  },
  {
    txt: 'N_SF0028-楠塔利-Naantali',
    code: 'N_SF0028'
  },
  {
    txt: 'N_SF0029-Olkiluoto-Olkiluoto',
    code: 'N_SF0029'
  },
  {
    txt: 'N_SF0030-奥卢-Oulu',
    code: 'N_SF0030'
  },
  {
    txt: 'N_SF0031-帕瑞恩-Pargas',
    code: 'N_SF0031'
  },
  {
    txt: 'N_SF0032-波里-Pori',
    code: 'N_SF0032'
  },
  {
    txt: 'N_SF0033-Porvoo-Porvoo',
    code: 'N_SF0033'
  },
  {
    txt: 'N_SF0034-波卡拉-Porkkala',
    code: 'N_SF0034'
  },
  {
    txt: 'N_SF0035-腊黑-Raahe',
    code: 'N_SF0035'
  },
  {
    txt: 'N_SF0036-Rahja-Rahja',
    code: 'N_SF0036'
  },
  {
    txt: 'N_SF0037-劳马-Rauma',
    code: 'N_SF0037'
  },
  {
    txt: 'N_SF0038-里斯提纳-Ristiina',
    code: 'N_SF0038'
  },
  {
    txt: 'N_SF0039-Saimaa Canal-Saimaa Canal',
    code: 'N_SF0039'
  },
  {
    txt: 'N_SF0040-Salo Uskela-Salo Uskela',
    code: 'N_SF0040'
  },
  {
    txt: 'N_SF0041-萨温利纳-Savonlinna',
    code: 'N_SF0041'
  },
  {
    txt: 'N_SF0042-Taalintehdas-Taalintehdas',
    code: 'N_SF0042'
  },
  {
    txt: 'N_SF0043-Tolkkinen-Tolkis-Tolkkinen-Tolkis',
    code: 'N_SF0043'
  },
  {
    txt: 'N_SF0044-托尔尼欧-Tornio',
    code: 'N_SF0044'
  },
  {
    txt: 'N_SF0045-土尔库-Turku',
    code: 'N_SF0045'
  },
  {
    txt: 'N_SF0046-Uto-Uto',
    code: 'N_SF0046'
  },
  {
    txt: 'N_SF0047-乌西考蓬基-Uusikaupunki',
    code: 'N_SF0047'
  },
  {
    txt: 'N_SF0048-Varkaus Harbours-Varkaus Harbours',
    code: 'N_SF0048'
  },
  {
    txt: 'N_SF0049-Vuoksen Terminal-Vuoksen Terminal',
    code: 'N_SF0049'
  },
  {
    txt: 'N_F001-Bayonne (France)-Bayonne (France)',
    code: 'N_F001'
  },
  {
    txt: 'N_F002-博尼法乔-Bonifacio',
    code: 'N_F002'
  },
  {
    txt: 'N_F003-波尔多-Bordeaux',
    code: 'N_F003'
  },
  {
    txt: 'N_F004-布洛涅（滨海布洛涅）-Boulogne-sur-Mer',
    code: 'N_F004'
  },
  {
    txt: 'N_F005-布雷斯特-Brest',
    code: 'N_F005'
  },
  {
    txt: 'N_F006-卡昂-Caen',
    code: 'N_F006'
  },
  {
    txt: 'N_F007-卡耳维-Calvi',
    code: 'N_F007'
  },
  {
    txt: 'N_F008-加来-Calais',
    code: 'N_F008'
  },
  {
    txt: 'N_F009-Camaret-Camaret',
    code: 'N_F009'
  },
  {
    txt: 'N_F0010-戛纳-Cannes',
    code: 'N_F0010'
  },
  {
    txt: 'N_F0011-瑟堡-Cherbourg',
    code: 'N_F0011'
  },
  {
    txt: 'N_F0012-Deauville-Deauville',
    code: 'N_F0012'
  },
  {
    txt: 'N_F0013-迪耶普-Dieppe',
    code: 'N_F0013'
  },
  {
    txt: 'N_F0014-Douarnenez-Douarnenez',
    code: 'N_F0014'
  },
  {
    txt: 'N_F0015-敦刻尔克-Dunkirk',
    code: 'N_F0015'
  },
  {
    txt: 'N_F0016-费康-Fecamp',
    code: 'N_F0016'
  },
  {
    txt: 'N_F0017-格朗维尔-Granville',
    code: 'N_F0017'
  },
  {
    txt: 'N_F0018-翁弗勒-Honfleur',
    code: 'N_F0018'
  },
  {
    txt: 'N_F0019-上科西嘉-Ile Rousse',
    code: 'N_F0019'
  },
  {
    txt: 'N_F0020-La Rochelle-Pallice-La Rochelle-Pallice',
    code: 'N_F0020'
  },
  {
    txt: 'N_F0021-拉努韦勒-La Nouvelle',
    code: 'N_F0021'
  },
  {
    txt: 'N_F0022-Le Treport-Le Treport',
    code: 'N_F0022'
  },
  {
    txt: "N_F0023-Le Chateau d'Oleron-Le Chateau d'Oleron",
    code: 'N_F0023'
  },
  {
    txt: 'N_F0024-Le Legue/Saint Brieuc-Le Legue/Saint Brieuc',
    code: 'N_F0024'
  },
  {
    txt: "N_F0025-萨布利斯-Les Sables d'Olonne",
    code: 'N_F0025'
  },
  {
    txt: 'N_F0026-Loctudy-Loctudy',
    code: 'N_F0026'
  },
  {
    txt: 'N_F0027-洛里昂-Lorient',
    code: 'N_F0027'
  },
  {
    txt: 'N_F0028-Lyon-Lyon',
    code: 'N_F0028'
  },
  {
    txt: 'N_F0029-Marseille-Marseille',
    code: 'N_F0029'
  },
  {
    txt: 'N_F0030-Morlaix-Morlaix',
    code: 'N_F0030'
  },
  {
    txt: 'N_F0031-Nantes-St Nazaire-Nantes-St Nazaire',
    code: 'N_F0031'
  },
  {
    txt: 'N_F0032-旺德尔港-Port Vendres',
    code: 'N_F0032'
  },
  {
    txt: 'N_F0033-Quimper-Corniguel-Quimper-Corniguel',
    code: 'N_F0033'
  },
  {
    txt: 'N_F0034-Rochefort & Tonnay-Charente-Rochefort & Tonnay-Charente',
    code: 'N_F0034'
  },
  {
    txt: 'N_F0035-Roscoff-Bloscon-Roscoff-Bloscon',
    code: 'N_F0035'
  },
  {
    txt: 'N_F0036-鲁昂-Rouen',
    code: 'N_F0036'
  },
  {
    txt: 'N_F0037-塞特-Sete',
    code: 'N_F0037'
  },
  {
    txt: 'N_F0038-St Valery-en-Caux-St Valery-en-Caux',
    code: 'N_F0038'
  },
  {
    txt: 'N_F0039-St Malo-St Malo',
    code: 'N_F0039'
  },
  {
    txt: 'N_F0040-斯特拉斯堡-Strasbourg',
    code: 'N_F0040'
  },
  {
    txt: 'N_F0041-土伦-Toulon',
    code: 'N_F0041'
  },
  {
    txt: 'N_F0042-特雷吉耶-Treguier',
    code: 'N_F0042'
  },
  {
    txt: 'N_F0043-Vannes-Vannes',
    code: 'N_F0043'
  },
  {
    txt: 'N_F0044-Villefranche-Villefranche',
    code: 'N_F0044'
  },
  {
    txt: 'N_FGU001-Degrad des Cannes-Degrad des Cannes',
    code: 'N_FGU001'
  },
  {
    txt: 'N_FGU002-Pariacabo-Pariacabo',
    code: 'N_FGU002'
  },
  {
    txt: 'N_FGU003-St Laurent du Maroni-St Laurent du Maroni',
    code: 'N_FGU003'
  },
  {
    txt: 'N_GAB001-Cap Lopez-Cap Lopez',
    code: 'N_GAB001'
  },
  {
    txt: 'N_GAB002-FPSO BW Adolo-FPSO BW Adolo',
    code: 'N_GAB002'
  },
  {
    txt: 'N_GAB003-FPSO Knock Allan (Olowi Field)-FPSO Knock Allan (Olowi Field)',
    code: 'N_GAB003'
  },
  {
    txt: 'N_GAB004-FPSO Petroleo Nautipa (Etame)-FPSO Petroleo Nautipa (Etame)',
    code: 'N_GAB004'
  },
  {
    txt: 'N_GAB005-甘巴-Gamba',
    code: 'N_GAB005'
  },
  {
    txt: 'N_GAB006-Mayumba-Mayumba',
    code: 'N_GAB006'
  },
  {
    txt: 'N_GAB007-Nyanga-Nyanga',
    code: 'N_GAB007'
  },
  {
    txt: 'N_GAB008-Oguendjo Terminal-Oguendjo Terminal',
    code: 'N_GAB008'
  },
  {
    txt: 'N_GAB009-奥文多-Owendo',
    code: 'N_GAB009'
  },
  {
    txt: 'N_GAB0010-Tchatamba Marine Terminal-Tchatamba Marine Terminal',
    code: 'N_GAB0010'
  },
  {
    txt: 'N_GEO001-Kulevi-Kulevi',
    code: 'N_GEO001'
  },
  {
    txt: 'N_GEO002-Ochamchira-Ochamchira',
    code: 'N_GEO002'
  },
  {
    txt: 'N_GEO003-Supsa-Supsa',
    code: 'N_GEO003'
  },
  {
    txt: 'N_D001-安德纳赫-Andernach',
    code: 'N_D001'
  },
  {
    txt: 'N_D002-Anklam-Anklam',
    code: 'N_D002'
  },
  {
    txt: 'N_D003-Bendorf-Bendorf',
    code: 'N_D003'
  },
  {
    txt: 'N_D004-Borkum-Borkum',
    code: 'N_D004'
  },
  {
    txt: 'N_D005-布腊克-Brake',
    code: 'N_D005'
  },
  {
    txt: 'N_D006-不来梅-Bremen',
    code: 'N_D006'
  },
  {
    txt: 'N_D007-Brunsbuttel-Brunsbuttel',
    code: 'N_D007'
  },
  {
    txt: 'N_D008-burgstaaken-burgstaaken',
    code: 'N_D008'
  },
  {
    txt: 'N_D009-布萨姆-Busum',
    code: 'N_D009'
  },
  {
    txt: 'N_D0010-chempark dormagen-chempark dormagen',
    code: 'N_D0010'
  },
  {
    txt: 'N_D0011-Cologne-Cologne',
    code: 'N_D0011'
  },
  {
    txt: 'N_D0012-库克斯港-Cuxhaven',
    code: 'N_D0012'
  },
  {
    txt: 'N_D0013-Dagebull-Dagebull',
    code: 'N_D0013'
  },
  {
    txt: 'N_D0014-Deggendorf-Deggendorf',
    code: 'N_D0014'
  },
  {
    txt: 'N_D0015-Duisburg (Ruhrort)-Duisburg (Ruhrort)',
    code: 'N_D0015'
  },
  {
    txt: 'N_D0016-Eckernforde-Eckernforde',
    code: 'N_D0016'
  },
  {
    txt: 'N_D0017-埃姆登-Emden',
    code: 'N_D0017'
  },
  {
    txt: 'N_D0018-弗伦斯港-Flensburg',
    code: 'N_D0018'
  },
  {
    txt: 'N_D0019-Frankfurt-Frankfurt',
    code: 'N_D0019'
  },
  {
    txt: 'N_D0020-Germersheim-Germersheim',
    code: 'N_D0020'
  },
  {
    txt: 'N_D0021-格吕克施塔特-Gluckstadt',
    code: 'N_D0021'
  },
  {
    txt: 'N_D0022-Greifswald-Ladebow-Greifswald-Ladebow',
    code: 'N_D0022'
  },
  {
    txt: 'N_D0023-Heiligenhafen-Heiligenhafen',
    code: 'N_D0023'
  },
  {
    txt: 'N_D0024-Helgoland-Helgoland',
    code: 'N_D0024'
  },
  {
    txt: 'N_D0025-Husum (Germany)-Husum (Germany)',
    code: 'N_D0025'
  },
  {
    txt: 'N_D0026-伊策霍-Itzehoe',
    code: 'N_D0026'
  },
  {
    txt: 'N_D0027-卡尔斯鲁-Karlsruhe',
    code: 'N_D0027'
  },
  {
    txt: 'N_D0028-Kehl-Kehl',
    code: 'N_D0028'
  },
  {
    txt: 'N_D0029-基尔-Kiel',
    code: 'N_D0029'
  },
  {
    txt: 'N_D0030-Kiel Canal-Kiel Canal',
    code: 'N_D0030'
  },
  {
    txt: 'N_D0031-Koblenz-Koblenz',
    code: 'N_D0031'
  },
  {
    txt: 'N_D0032-克雷菲尔德-Krefeld',
    code: 'N_D0032'
  },
  {
    txt: 'N_D0033-莱尔-Leer',
    code: 'N_D0033'
  },
  {
    txt: 'N_D0034-List/Sylt-List/Sylt',
    code: 'N_D0034'
  },
  {
    txt: 'N_D0035-Lubeck-Lubeck',
    code: 'N_D0035'
  },
  {
    txt: 'N_D0036-Lubmin-Lubmin',
    code: 'N_D0036'
  },
  {
    txt: 'N_D0037-Ludwigshafen am Rhein-Ludwigshafen am Rhein',
    code: 'N_D0037'
  },
  {
    txt: 'N_D0038-Magdeburg-Magdeburg',
    code: 'N_D0038'
  },
  {
    txt: 'N_D0039-麦恩兹-Mainz',
    code: 'N_D0039'
  },
  {
    txt: 'N_D0040-曼尼姆-Mannheim',
    code: 'N_D0040'
  },
  {
    txt: 'N_D0041-minden-minden',
    code: 'N_D0041'
  },
  {
    txt: 'N_D0042-Neuhaus-Neuhaus',
    code: 'N_D0042'
  },
  {
    txt: 'N_D0043-Neuss-Dusseldorf-Neuss-Dusseldorf',
    code: 'N_D0043'
  },
  {
    txt: 'N_D0044-Neustadt-Neustadt',
    code: 'N_D0044'
  },
  {
    txt: 'N_D0045-Norddeich-Norddeich',
    code: 'N_D0045'
  },
  {
    txt: 'N_D0046-奥尔登堡-Oldenburg',
    code: 'N_D0046'
  },
  {
    txt: 'N_D0047-Oldersum-Oldersum',
    code: 'N_D0047'
  },
  {
    txt: 'N_D0048-Orth-Orth',
    code: 'N_D0048'
  },
  {
    txt: 'N_D0049-帕彭堡-Papenburg',
    code: 'N_D0049'
  },
  {
    txt: 'N_D0050-帕绍-Passau',
    code: 'N_D0050'
  },
  {
    txt: 'N_D0051-布格施塔肯-Puttgarden',
    code: 'N_D0051'
  },
  {
    txt: 'N_D0052-Regensburg-Regensburg',
    code: 'N_D0052'
  },
  {
    txt: 'N_D0053-伦次堡-Rendsburg',
    code: 'N_D0053'
  },
  {
    txt: 'N_D0054-罗斯托克-Rostock',
    code: 'N_D0054'
  },
  {
    txt: 'N_D0055-Sassnitz-Mukran-Sassnitz-Mukran',
    code: 'N_D0055'
  },
  {
    txt: 'N_D0056-Schleswig-Schleswig',
    code: 'N_D0056'
  },
  {
    txt: 'N_D0057-Schwarzenhutten-Schwarzenhutten',
    code: 'N_D0057'
  },
  {
    txt: 'N_D0058-施塔德-Stade',
    code: 'N_D0058'
  },
  {
    txt: 'N_D0059-斯特拉尔松-Stralsund',
    code: 'N_D0059'
  },
  {
    txt: 'N_D0060-Tangermunde-Tangermunde',
    code: 'N_D0060'
  },
  {
    txt: 'N_D0061-Toenning-Toenning',
    code: 'N_D0061'
  },
  {
    txt: 'N_D0062-Ueckermunde-Ueckermunde',
    code: 'N_D0062'
  },
  {
    txt: 'N_D0063-菲罗-Vierow',
    code: 'N_D0063'
  },
  {
    txt: 'N_D0064-Wedel-Wedel',
    code: 'N_D0064'
  },
  {
    txt: 'N_D0065-Weissenthurm-Weissenthurm',
    code: 'N_D0065'
  },
  {
    txt: 'N_D0066-Wesel-Wesel',
    code: 'N_D0066'
  },
  {
    txt: 'N_D0067-威廉港-Wilhelmshaven',
    code: 'N_D0067'
  },
  {
    txt: 'N_D0068-维斯马-Wismar',
    code: 'N_D0068'
  },
  {
    txt: 'N_D0069-Wolgast-Wolgast',
    code: 'N_D0069'
  },
  {
    txt: 'N_D0070-Worth am Rhein-Worth am Rhein',
    code: 'N_D0070'
  },
  {
    txt: 'N_GH001-FPSO Prof John Evans Atta Mills-FPSO Prof John Evans Atta Mills',
    code: 'N_GH001'
  },
  {
    txt: 'N_GH002-FPSO OCTP Terminal (FPSO JAK)-FPSO OCTP Terminal (FPSO JAK)',
    code: 'N_GH002'
  },
  {
    txt: 'N_GH003-FPSO Kwame Nkrumah MV21-FPSO Kwame Nkrumah MV21',
    code: 'N_GH003'
  },
  {
    txt: 'N_GH004-Saltpond Oil Field-Saltpond Oil Field',
    code: 'N_GH004'
  },
  {
    txt: 'N_GR001-阿莱蒂-Achladi',
    code: 'N_GR001'
  },
  {
    txt: 'N_GR002-Agia Trias-Agia Trias',
    code: 'N_GR002'
  },
  {
    txt: 'N_GR003-西奥多罗伊-Agioi Theodoroi',
    code: 'N_GR003'
  },
  {
    txt: 'N_GR004-阿吉亚玛丽娜-Agia Marina',
    code: 'N_GR004'
  },
  {
    txt: 'N_GR005-阿历山德鲁波利斯-Alexandroupolis',
    code: 'N_GR005'
  },
  {
    txt: 'N_GR006-Aliveri Bay-Aliveri Bay',
    code: 'N_GR006'
  },
  {
    txt: 'N_GR007-阿姆菲罗克亚-Amfilochia',
    code: 'N_GR007'
  },
  {
    txt: 'N_GR008-阿哥斯托利昂-Argostoli',
    code: 'N_GR008'
  },
  {
    txt: 'N_GR009-阿斯诺皮格斯-Aspropyrgos',
    code: 'N_GR009'
  },
  {
    txt: 'N_GR0010-Aspra Spitia Ore Terminal-Aspra Spitia Ore Terminal',
    code: 'N_GR0010'
  },
  {
    txt: 'N_GR0011-Astakos Terminal-Astakos Terminal',
    code: 'N_GR0011'
  },
  {
    txt: 'N_GR0012-Ayios Nikolaos-Ayios Nikolaos',
    code: 'N_GR0012'
  },
  {
    txt: 'N_GR0013-Ayia Marina-Ayia Marina',
    code: 'N_GR0013'
  },
  {
    txt: 'N_GR0014-干尼亚-Chania',
    code: 'N_GR0014'
  },
  {
    txt: 'N_GR0015-卡尔基斯-Chalkis',
    code: 'N_GR0015'
  },
  {
    txt: 'N_GR0016-Chios-Chios',
    code: 'N_GR0016'
  },
  {
    txt: 'N_GR0017-科夫-Corfu',
    code: 'N_GR0017'
  },
  {
    txt: 'N_GR0018-Corinth Canal-Corinth Canal',
    code: 'N_GR0018'
  },
  {
    txt: 'N_GR0019-Corinth-Corinth',
    code: 'N_GR0019'
  },
  {
    txt: 'N_GR0020-Drepano-Drepano',
    code: 'N_GR0020'
  },
  {
    txt: 'N_GR0021-埃莱夫西斯-Eleusis',
    code: 'N_GR0021'
  },
  {
    txt: 'N_GR0022-evdilos-evdilos',
    code: 'N_GR0022'
  },
  {
    txt: 'N_GR0023-gavrio-gavrio',
    code: 'N_GR0023'
  },
  {
    txt: 'N_GR0024-基西昂-Gythion',
    code: 'N_GR0024'
  },
  {
    txt: 'N_GR0025-Heraklion-Heraklion',
    code: 'N_GR0025'
  },
  {
    txt: 'N_GR0026-Igoumenitsa-Igoumenitsa',
    code: 'N_GR0026'
  },
  {
    txt: 'N_GR0027-igoumenitsa-igoumenitsa',
    code: 'N_GR0027'
  },
  {
    txt: 'N_GR0028-Ios-Ios',
    code: 'N_GR0028'
  },
  {
    txt: 'N_GR0029-伊斯米亚-Isthmia',
    code: 'N_GR0029'
  },
  {
    txt: 'N_GR0030-伊泰阿-Itea',
    code: 'N_GR0030'
  },
  {
    txt: 'N_GR0031-卡立梅纳斯-Kalymnos',
    code: 'N_GR0031'
  },
  {
    txt: 'N_GR0032-卡拉梅塔-Kalamata',
    code: 'N_GR0032'
  },
  {
    txt: 'N_GR0033-卡立梅纳斯-Kali Limenes',
    code: 'N_GR0033'
  },
  {
    txt: 'N_GR0034-卡拉马基-Kalamaki',
    code: 'N_GR0034'
  },
  {
    txt: 'N_GR0035-Karlovassi-Karlovassi',
    code: 'N_GR0035'
  },
  {
    txt: 'N_GR0036-KARPATHOS-KARPATHOS',
    code: 'N_GR0036'
  },
  {
    txt: 'N_GR0037-卡塔考伦-Katakolon',
    code: 'N_GR0037'
  },
  {
    txt: 'N_GR0038-Katapola-Katapola',
    code: 'N_GR0038'
  },
  {
    txt: 'N_CHI00127-庄河-Zhuanghe',
    code: 'N_CHI00127'
  },
  {
    txt: 'N_TAI001-安平-An-Ping',
    code: 'N_TAI001'
  },
  {
    txt: 'N_TAI002-和平岛-Ho-Ping',
    code: 'N_TAI002'
  },
  {
    txt: 'N_TAI003-花莲-Hualien',
    code: 'N_TAI003'
  },
  {
    txt: 'N_TAI004-马公-Ma-Kung',
    code: 'N_TAI004'
  },
  {
    txt: 'N_TAI005-麦寮-Mailiao',
    code: 'N_TAI005'
  },
  {
    txt: 'N_TAI006-苏澳-Su-Ao',
    code: 'N_TAI006'
  },
  {
    txt: 'N_TAI007-台中-Taichung',
    code: 'N_TAI007'
  },
  {
    txt: 'N_COC001-Port Refuge-Port Refuge',
    code: 'N_COC001'
  },
  {
    txt: 'N_CO001-Aguadulce (Colombia)-Aguadulce (Colombia)',
    code: 'N_CO001'
  },
  {
    txt: 'N_CO002-Cartagena (Colombia)-Cartagena (Colombia)',
    code: 'N_CO002'
  },
  {
    txt: 'N_CO003-Covenas Offshore Terminal-Covenas Offshore Terminal',
    code: 'N_CO003'
  },
  {
    txt: 'N_CO004-马莫纳尔-Mamonal',
    code: 'N_CO004'
  },
  {
    txt: 'N_CO005-Pozos Colorados-Pozos Colorados',
    code: 'N_CO005'
  },
  {
    txt: 'N_CO006-Puerto Bolivar (Colombia)-Puerto Bolivar (Colombia)',
    code: 'N_CO006'
  },
  {
    txt: 'N_CO007-德拉蒙德港-Puerto Drummond',
    code: 'N_CO007'
  },
  {
    txt: 'N_CO008-Puerto Bahia-Puerto Bahia',
    code: 'N_CO008'
  },
  {
    txt: 'N_CO009-奴沃港-Puerto Nuevo',
    code: 'N_CO009'
  },
  {
    txt: 'N_CO0010-里奥哈查-Puerto Brisa',
    code: 'N_CO0010'
  },
  {
    txt: 'N_CO0011-Rio Cordoba-Rio Cordoba',
    code: 'N_CO0011'
  },
  {
    txt: 'N_CO0012-圣玛尔塔-Santa Marta',
    code: 'N_CO0012'
  },
  {
    txt: 'N_CO0013-San Andres Island-San Andres Island',
    code: 'N_CO0013'
  },
  {
    txt: 'N_CO0014-Tolu-Tolu',
    code: 'N_CO0014'
  },
  {
    txt: 'N_CO0015-图马科-Tumaco',
    code: 'N_CO0015'
  },
  {
    txt: 'N_CO0016-图尔博-Turbo',
    code: 'N_CO0016'
  },
  {
    txt: 'N_COM001-Mayotte-Mayotte',
    code: 'N_COM001'
  },
  {
    txt: 'N_CN001-Djeno-Djeno',
    code: 'N_CN001'
  },
  {
    txt: "N_CN002-FPSO N'Kossa 2-FPSO N'Kossa 2",
    code: 'N_CN002'
  },
  {
    txt: 'N_CN003-Yombo Terminal-Yombo Terminal',
    code: 'N_CN003'
  },
  {
    txt: 'N_CON001-Ango-Ango-Ango-Ango',
    code: 'N_CON001'
  },
  {
    txt: 'N_CON002-Banana-Banana',
    code: 'N_CON002'
  },
  {
    txt: 'N_CON003-Boma-Boma',
    code: 'N_CON003'
  },
  {
    txt: 'N_CON004-FSO Kalamu (Moanda Oil Terminal)-FSO Kalamu (Moanda Oil Terminal)',
    code: 'N_CON004'
  },
  {
    txt: 'N_COO001-Arutanga-Arutanga',
    code: 'N_COO001'
  },
  {
    txt: 'N_COO002-Avatiu-Avatiu',
    code: 'N_COO002'
  },
  {
    txt: 'N_CR001-Caldera (Costa Rica)-Caldera (Costa Rica)',
    code: 'N_CR001'
  },
  {
    txt: 'N_CR002-Golfo Dulce-Golfo Dulce',
    code: 'N_CR002'
  },
  {
    txt: 'N_CR003-Golfito-Golfito',
    code: 'N_CR003'
  },
  {
    txt: 'N_CR004-Puerto Limon-Puerto Limon',
    code: 'N_CR004'
  },
  {
    txt: 'N_CR005-Puntarenas-Puntarenas',
    code: 'N_CR005'
  },
  {
    txt: 'N_CR006-Punta Morales-Punta Morales',
    code: 'N_CR006'
  },
  {
    txt: 'N_CR007-Quepos-Quepos',
    code: 'N_CR007'
  },
  {
    txt: 'N_CI001-FPSO Espoir Ivorien-FPSO Espoir Ivorien',
    code: 'N_CI001'
  },
  {
    txt: 'N_CI002-FPSO Baobab Ivoirien MV10-FPSO Baobab Ivoirien MV10',
    code: 'N_CI002'
  },
  {
    txt: "N_CI003-San Pedro (Cote d'Ivoire)-San Pedro (Cote d'Ivoire)",
    code: 'N_CI003'
  },
  {
    txt: 'N_CRO001-Cres-Cres',
    code: 'N_CRO001'
  },
  {
    txt: 'N_CRO002-Kraljevica-Kraljevica',
    code: 'N_CRO002'
  },
  {
    txt: 'N_CRO003-马利洛希尼-Mali Losinj',
    code: 'N_CRO003'
  },
  {
    txt: 'N_CRO004-Metkovic-Metkovic',
    code: 'N_CRO004'
  },
  {
    txt: 'N_CRO005-奥米萨尔杰-Omisalj',
    code: 'N_CRO005'
  },
  {
    txt: 'N_CRO006-Opatija-Opatija',
    code: 'N_CRO006'
  },
  {
    txt: 'N_CRO007-Plomin-Plomin',
    code: 'N_CRO007'
  },
  {
    txt: 'N_CRO008-Porec-Porec',
    code: 'N_CRO008'
  },
  {
    txt: 'N_CRO009-Rab-Rab',
    code: 'N_CRO009'
  },
  {
    txt: 'N_CRO0010-罗维尼-Rovinj',
    code: 'N_CRO0010'
  },
  {
    txt: 'N_CRO0011-Umag-Umag',
    code: 'N_CRO0011'
  },
  {
    txt: 'N_CRO0012-Vela Luka-Vela Luka',
    code: 'N_CRO0012'
  },
  {
    txt: 'N_CRO0013-Vukovar-Vukovar',
    code: 'N_CRO0013'
  },
  {
    txt: 'N_C001-安蒂利亚-Antilla',
    code: 'N_C001'
  },
  {
    txt: 'N_C002-巴腊夸-Baracoa',
    code: 'N_C002'
  },
  {
    txt: 'N_C003-卡巴纳斯-Cabanas',
    code: 'N_C003'
  },
  {
    txt: 'N_C004-西恩富格斯-Cienfuegos',
    code: 'N_C004'
  },
  {
    txt: 'N_C005-Corojal-Corojal',
    code: 'N_C005'
  },
  {
    txt: 'N_C006-Felton-Felton',
    code: 'N_C006'
  },
  {
    txt: 'N_C007-Guantanamo-Guantanamo',
    code: 'N_C007'
  },
  {
    txt: 'N_C008-瓜亚巴尔-Guayabal',
    code: 'N_C008'
  },
  {
    txt: 'N_C009-马瑞尔-Mariel',
    code: 'N_C009'
  },
  {
    txt: 'N_C0010-Matanzas (Cuba)-Matanzas (Cuba)',
    code: 'N_C0010'
  },
  {
    txt: 'N_C0011-莫亚-Moa',
    code: 'N_C0011'
  },
  {
    txt: 'N_C0012-新黑罗纳-Nueva Gerona',
    code: 'N_C0012'
  },
  {
    txt: 'N_C0013-努韦维塔斯-Nuevitas',
    code: 'N_C0013'
  },
  {
    txt: 'N_C0014-Palo Alto-Palo Alto',
    code: 'N_C0014'
  },
  {
    txt: 'N_C0015-帕德雷港-Puerto Padre',
    code: 'N_C0015'
  },
  {
    txt: 'N_C0016-圣卢西亚-Santa Lucia',
    code: 'N_C0016'
  },
  {
    txt: 'N_C0017-圣地亚哥-Santiago de Cuba',
    code: 'N_C0017'
  },
  {
    txt: 'N_C0018-维塔-Vita',
    code: 'N_C0018'
  },
  {
    txt: "N_CUR001-St Michiel's Bay-St Michiel's Bay",
    code: 'N_CUR001'
  },
  {
    txt: 'N_CY001-阿克洛提瑞-Akrotiri',
    code: 'N_CY001'
  },
  {
    txt: 'N_CY002-泽凯利亚-Dhekelia',
    code: 'N_CY002'
  },
  {
    txt: 'N_CY003-阿莫霍斯托斯-Famagusta',
    code: 'N_CY003'
  },
  {
    txt: 'N_CA002-Arrecife de Lanzarote-Arrecife de Lanzarote',
    code: 'N_CA002'
  },
  {
    txt: 'N_CA003-La Estaca-La Estaca',
    code: 'N_CA003'
  },
  {
    txt: 'N_CA004-Los Cristianos-Los Cristianos',
    code: 'N_CA004'
  },
  {
    txt: 'N_CA005-San Sebastian (Canary Is)-San Sebastian (Canary Is)',
    code: 'N_CA005'
  },
  {
    txt: 'N_CA006-Santa Cruz de la Palma-Santa Cruz de la Palma',
    code: 'N_CA006'
  },
  {
    txt: 'N_CA007-Santa Cruz de Tenerife-Santa Cruz de Tenerife',
    code: 'N_CA007'
  },
  {
    txt: 'N_CVI001-普拉亚-Porto Praia',
    code: 'N_CVI001'
  },
  {
    txt: 'N_CVI002-格兰德港-Porto Grande',
    code: 'N_CVI002'
  },
  {
    txt: 'N_CVI003-Porto Ingles-Porto Ingles',
    code: 'N_CVI003'
  },
  {
    txt: 'N_CVI004-Porto Tarrafal-Porto Tarrafal',
    code: 'N_CVI004'
  },
  {
    txt: 'N_CVI005-Porto de Sal Rei-Porto de Sal Rei',
    code: 'N_CVI005'
  },
  {
    txt: 'N_CVI006-Porto Furna-Porto Furna',
    code: 'N_CVI006'
  },
  {
    txt: 'N_CVI007-Porto Vale de Cavaleiros-Porto Vale de Cavaleiros',
    code: 'N_CVI007'
  },
  {
    txt: 'N_CVI008-Sal Island-Sal Island',
    code: 'N_CVI008'
  },
  {
    txt: 'N_CVI009-St. Vincent-St. Vincent',
    code: 'N_CVI009'
  },
  {
    txt: 'N_CAY001-凯门布雷克-Cayman Brac',
    code: 'N_CAY001'
  },
  {
    txt: 'N_CAY002-Georgetown (Cayman Is)-Georgetown (Cayman Is)',
    code: 'N_CAY002'
  },
  {
    txt: 'N_RCH001-巴基托-Barquito',
    code: 'N_RCH001'
  },
  {
    txt: 'N_RCH002-克罗索-Caleta Coloso',
    code: 'N_RCH002'
  },
  {
    txt: 'N_RCH003-Caldera (Chile)-Caldera (Chile)',
    code: 'N_RCH003'
  },
  {
    txt: 'N_RCH004-卡斯特罗-Castro',
    code: 'N_RCH004'
  },
  {
    txt: 'N_RCH005-查尼亚腊尔-Chanaral',
    code: 'N_RCH005'
  },
  {
    txt: 'N_RCH006-科金博-Coquimbo',
    code: 'N_RCH006'
  },
  {
    txt: 'N_RCH007-瓜亚坎-Guayacan',
    code: 'N_RCH007'
  },
  {
    txt: 'N_RCH008-瓦斯科-Huasco',
    code: 'N_RCH008'
  },
  {
    txt: 'N_RCH009-Isla de Pascua-Isla de Pascua',
    code: 'N_RCH009'
  },
  {
    txt: 'N_RCH0010-Isla Guarello-Isla Guarello',
    code: 'N_RCH0010'
  },
  {
    txt: 'N_RCH0011-La Chimba Cove-La Chimba Cove',
    code: 'N_RCH0011'
  },
  {
    txt: 'N_RCH0012-Magellan Strait Area-Magellan Strait Area',
    code: 'N_RCH0012'
  },
  {
    txt: 'N_RCH0013-帕蒂洛斯-Patillos Cove',
    code: 'N_RCH0013'
  },
  {
    txt: 'N_RCH0014-Penco-Penco',
    code: 'N_RCH0014'
  },
  {
    txt: 'N_RCH0015-Port Williams-Port Williams',
    code: 'N_RCH0015'
  },
  {
    txt: 'N_RCH0016-查卡布考港-Puerto Chacabuco',
    code: 'N_RCH0016'
  },
  {
    txt: 'N_RCH0017-蒙特港-Puerto Montt',
    code: 'N_RCH0017'
  },
  {
    txt: 'N_RCH0018-Puerto Natales-Puerto Natales',
    code: 'N_RCH0018'
  },
  {
    txt: 'N_RCH0019-puerto natales-puerto natales',
    code: 'N_RCH0019'
  },
  {
    txt: 'N_RCH0020-Puerto Punta Totoralillo-Puerto Punta Totoralillo',
    code: 'N_RCH0020'
  },
  {
    txt: 'N_RCH0021-Puerto Patache-Puerto Patache',
    code: 'N_RCH0021'
  },
  {
    txt: 'N_RCH0022-彭塔阿雷纳斯-Punta Arenas',
    code: 'N_RCH0022'
  },
  {
    txt: 'N_RCH0023-彭塔湖-Punta Lackwater',
    code: 'N_RCH0023'
  },
  {
    txt: 'N_RCH0024-Quellon-Quellon',
    code: 'N_RCH0024'
  },
  {
    txt: 'N_RCH0025-Quemchi-Quemchi',
    code: 'N_RCH0025'
  },
  {
    txt: 'N_RCH0026-金特罗-Quintero',
    code: 'N_RCH0026'
  },
  {
    txt: 'N_RCH0027-圣维森特港-San Vicente',
    code: 'N_RCH0027'
  },
  {
    txt: 'N_RCH0028-塔尔卡瓦纳-Talcahuano',
    code: 'N_RCH0028'
  },
  {
    txt: 'N_RCH0029-Taltal-Taltal',
    code: 'N_RCH0029'
  },
  {
    txt: 'N_RCH0030-托科皮利亚-Tocopilla',
    code: 'N_RCH0030'
  },
  {
    txt: 'N_RCH0031-Valdivia-Valdivia',
    code: 'N_RCH0031'
  },
  {
    txt: 'N_CHI001-巴河-Bahe',
    code: 'N_CHI001'
  },
  {
    txt: 'N_CHI002-Beipei-Beipei',
    code: 'N_CHI002'
  },
  {
    txt: 'N_CHI003-BZ 28 Terminal-BZ 28 Terminal',
    code: 'N_CHI003'
  },
  {
    txt: 'N_CHI004-BZ 34 Terminal-BZ 34 Terminal',
    code: 'N_CHI004'
  },
  {
    txt: 'N_CHI005-漕泾-Caojing',
    code: 'N_CHI005'
  },
  {
    txt: 'N_CHI006-曹妃甸-Caofeidian',
    code: 'N_CHI006'
  },
  {
    txt: 'N_CHI007-CFD 11 Terminal-CFD 11 Terminal',
    code: 'N_CHI007'
  },
  {
    txt: 'N_CHI008-长江口-Changjiangkou',
    code: 'N_CHI008'
  },
  {
    txt: 'N_CHI009-重庆-Chongqing',
    code: 'N_CHI009'
  },
  {
    txt: 'N_CHI0010-崇明-Chongming',
    code: 'N_CHI0010'
  },
  {
    txt: 'N_CHI0011-Chuansha-Chuansha',
    code: 'N_CHI0011'
  },
  {
    txt: 'N_CHI0012-大铲湾-Da Chan Bay Terminal One',
    code: 'N_CHI0012'
  },
  {
    txt: 'N_CHI0013-大丰港-Dafeng',
    code: 'N_CHI0013'
  },
  {
    txt: 'N_CHI0014-狄港-Digang',
    code: 'N_CHI0014'
  },
  {
    txt: 'N_CHI0015-dongzao-dongzao',
    code: 'N_CHI0015'
  },
  {
    txt: 'N_CHI0016-Dongtai-Dongtai',
    code: 'N_CHI0016'
  },
  {
    txt: 'N_CHI0017-董家口-Dongjiakou',
    code: 'N_CHI0017'
  },
  {
    txt: 'N_CHI0018-Duchang-Duchang',
    code: 'N_CHI0018'
  },
  {
    txt: 'N_CHI0019-fengcheng-fengcheng',
    code: 'N_CHI0019'
  },
  {
    txt: 'N_CHI0020-奉节-Fengjie',
    code: 'N_CHI0020'
  },
  {
    txt: 'N_CHI0021-Fengxian-Fengxian',
    code: 'N_CHI0021'
  },
  {
    txt: 'N_CHI0022-FPSO Nan Hai Sheng Li-FPSO Nan Hai Sheng Li',
    code: 'N_CHI0022'
  },
  {
    txt: 'N_CHI0023-FPSO Hai Yang Shi You 113-FPSO Hai Yang Shi You 113',
    code: 'N_CHI0023'
  },
  {
    txt: 'N_CHI0024-FPSO Nan Hai Fen Jin-FPSO Nan Hai Fen Jin',
    code: 'N_CHI0024'
  },
  {
    txt: 'N_CHI0025-FPSO Bohai Peng Lai-FPSO Bohai Peng Lai',
    code: 'N_CHI0025'
  },
  {
    txt: 'N_CHI0026-涪陵-Fuling',
    code: 'N_CHI0026'
  },
  {
    txt: 'N_CHI0027-Fuqing-Fuqing',
    code: 'N_CHI0027'
  },
  {
    txt: 'N_CHI0028-富阳-Fuyang',
    code: 'N_CHI0028'
  },
  {
    txt: 'N_CHI0029-Ganyu-Ganyu',
    code: 'N_CHI0029'
  },
  {
    txt: 'N_CHI0030-Ganzhou-Ganzhou',
    code: 'N_CHI0030'
  },
  {
    txt: 'N_CHI0031-Guiping-Guiping',
    code: 'N_CHI0031'
  },
  {
    txt: 'N_CHI0032-Gulei-Gulei',
    code: 'N_CHI0032'
  },
  {
    txt: 'N_CHI0033-海门-Haimen (Yangtze River)',
    code: 'N_CHI0033'
  },
  {
    txt: 'N_IS004-Eskifjordur-Eskifjordur',
    code: 'N_IS004'
  },
  {
    txt: 'N_IS005-Faskrudsfjordur-Faskrudsfjordur',
    code: 'N_IS005'
  },
  {
    txt: 'N_IS006-格林达维克-Grindavik',
    code: 'N_IS006'
  },
  {
    txt: 'N_IS007-格伦达菲厄泽-Grundarfjordur',
    code: 'N_IS007'
  },
  {
    txt: 'N_IS008-哈夫纳腓约杜尔-Hafnarfjordur',
    code: 'N_IS008'
  },
  {
    txt: 'N_IS009-Helguvik-Helguvik',
    code: 'N_IS009'
  },
  {
    txt: 'N_IS0010-Hornafjordur-Hornafjordur',
    code: 'N_IS0010'
  },
  {
    txt: 'N_IS0011-Hvammstangi-Hvammstangi',
    code: 'N_IS0011'
  },
  {
    txt: 'N_IS0012-Isafjordur-Isafjordur',
    code: 'N_IS0012'
  },
  {
    txt: 'N_IS0013-Keflavik - Njardvik-Keflavik - Njardvik',
    code: 'N_IS0013'
  },
  {
    txt: 'N_IS0014-Kopavogur-Kopavogur',
    code: 'N_IS0014'
  },
  {
    txt: 'N_IS0015-Litlisandur-Litlisandur',
    code: 'N_IS0015'
  },
  {
    txt: 'N_IS0016-Raufarhofn-Raufarhofn',
    code: 'N_IS0016'
  },
  {
    txt: 'N_IS0017-Reydarfjordur-Reydarfjordur',
    code: 'N_IS0017'
  },
  {
    txt: 'N_IS0018-Saudarkrokur-Saudarkrokur',
    code: 'N_IS0018'
  },
  {
    txt: 'N_IS0019-Seydisfjordur-Seydisfjordur',
    code: 'N_IS0019'
  },
  {
    txt: 'N_IS0020-Siglufjordur-Siglufjordur',
    code: 'N_IS0020'
  },
  {
    txt: 'N_IS0021-Stodhvarfjordur-Stodhvarfjordur',
    code: 'N_IS0021'
  },
  {
    txt: 'N_IS0022-Thorshofn-Thorshofn',
    code: 'N_IS0022'
  },
  {
    txt: 'N_IS0023-Thorlakshofn-Thorlakshofn',
    code: 'N_IS0023'
  },
  {
    txt: 'N_IS0024-维斯特曼纳加-Vestmannaeyjar',
    code: 'N_IS0024'
  },
  {
    txt: 'N_IS0025-Vopnafjordur-Vopnafjordur',
    code: 'N_IS0025'
  },
  {
    txt: 'N_IS0026-VOPNAFJORDUR-VOPNAFJORDUR',
    code: 'N_IS0026'
  },
  {
    txt: 'N_IND001-阿兰港-Alang',
    code: 'N_IND001'
  },
  {
    txt: 'N_IND002-Azhikkal-Azhikkal',
    code: 'N_IND002'
  },
  {
    txt: 'N_IND003-贝迪-Bedi Bunder',
    code: 'N_IND003'
  },
  {
    txt: 'N_IND004-贝布尔-Beypore',
    code: 'N_IND004'
  },
  {
    txt: 'N_IND005-达波尔港-Dabhol',
    code: 'N_IND005'
  },
  {
    txt: 'N_IND006-达何-Dahej',
    code: 'N_IND006'
  },
  {
    txt: 'N_IND007-达哈努-Dahanu',
    code: 'N_IND007'
  },
  {
    txt: 'N_IND008-Deendayal-Deendayal',
    code: 'N_IND008'
  },
  {
    txt: 'N_IND009-达兰塔-Dharamtar',
    code: 'N_IND009'
  },
  {
    txt: 'N_IND0010-丹拉-Dhamra',
    code: 'N_IND0010'
  },
  {
    txt: 'N_IND0011-蒂基-Dighi',
    code: 'N_IND0011'
  },
  {
    txt: 'N_IND0012-FPSO Armada Sterling II-FPSO Armada Sterling II',
    code: 'N_IND0012'
  },
  {
    txt: 'N_IND0013-FPSO Dhirubhai 1-FPSO Dhirubhai 1',
    code: 'N_IND0013'
  },
  {
    txt: 'N_IND0014-FPSO Armada Sterling-FPSO Armada Sterling',
    code: 'N_IND0014'
  },
  {
    txt: 'N_IND0015-冈加哈帕纳姆-Gangavaram',
    code: 'N_IND0015'
  },
  {
    txt: 'N_IND0016-杰伊格-Jaigarh',
    code: 'N_IND0016'
  },
  {
    txt: 'N_IND0017-Jawaharlal Nehru Port-Jawaharlal Nehru Port',
    code: 'N_IND0017'
  },
  {
    txt: 'N_IND0018-Kamarajar-Kamarajar',
    code: 'N_IND0018'
  },
  {
    txt: 'N_IND0019-卡来卡-Karaikal',
    code: 'N_IND0019'
  },
  {
    txt: 'N_IND0020-马格达拉-Magdalla',
    code: 'N_IND0020'
  },
  {
    txt: 'N_IND0021-穆尔德瓦卡-Muldwarka',
    code: 'N_IND0021'
  },
  {
    txt: 'N_IND0022-孟买-Mumbai',
    code: 'N_IND0022'
  },
  {
    txt: 'N_IND0023-Panaji-Panaji',
    code: 'N_IND0023'
  },
  {
    txt: 'N_IND0024-Pindhara-Pindhara',
    code: 'N_IND0024'
  },
  {
    txt: 'N_IND0025-Revdanda-Revdanda',
    code: 'N_IND0025'
  },
  {
    txt: 'N_IND0026-Sanegaon-Sanegaon',
    code: 'N_IND0026'
  },
  {
    txt: 'N_IND0027-Sanghi-Sanghi',
    code: 'N_IND0027'
  },
  {
    txt: 'N_IND0028-Vadinar Terminal-Vadinar Terminal',
    code: 'N_IND0028'
  },
  {
    txt: 'N_IND0029-Valinokkam-Valinokkam',
    code: 'N_IND0029'
  },
  {
    txt: 'N_IND0030-Vengurla-Vengurla',
    code: 'N_IND0030'
  },
  {
    txt: 'N_RI001-阿当湾-Adang Bay',
    code: 'N_RI001'
  },
  {
    txt: 'N_RI002-阿姆马帕尔-Amamapare',
    code: 'N_RI002'
  },
  {
    txt: 'N_RI003-安汶-Ambon',
    code: 'N_RI003'
  },
  {
    txt: 'N_RI004-Anoa Natuna-Anoa Natuna',
    code: 'N_RI004'
  },
  {
    txt: 'N_RI005-Anyer Terminal-Anyer Terminal',
    code: 'N_RI005'
  },
  {
    txt: 'N_RI006-Apar Bay-Apar Bay',
    code: 'N_RI006'
  },
  {
    txt: 'N_RI007-Ardjuna Marine Terminal-Ardjuna Marine Terminal',
    code: 'N_RI007'
  },
  {
    txt: 'N_RI008-Asam Asam-Asam Asam',
    code: 'N_RI008'
  },
  {
    txt: 'N_RI009-巴达斯-Badas',
    code: 'N_RI009'
  },
  {
    txt: 'N_RI0010-Bahodopi-Bahodopi',
    code: 'N_RI0010'
  },
  {
    txt: 'N_RI0011-巴考赫尼-Bakauheni',
    code: 'N_RI0011'
  },
  {
    txt: 'N_RI0012-巴隆甘港-Balongan',
    code: 'N_RI0012'
  },
  {
    txt: 'N_RI0013-Banyuwangi - Meneng-Banyuwangi - Meneng',
    code: 'N_RI0013'
  },
  {
    txt: 'N_RI0014-巴图阿帕-Batu Ampar',
    code: 'N_RI0014'
  },
  {
    txt: 'N_RI0015-Batulicin-Batulicin',
    code: 'N_RI0015'
  },
  {
    txt: 'N_RI0016-保堡-Bau-Bau',
    code: 'N_RI0016'
  },
  {
    txt: 'N_RI0017-BAYAH-BAYAH',
    code: 'N_RI0017'
  },
  {
    txt: 'N_RI0018-Belanak Field Terminal-Belanak Field Terminal',
    code: 'N_RI0018'
  },
  {
    txt: 'N_RI0019-Belida Marine Terminal-Belida Marine Terminal',
    code: 'N_RI0019'
  },
  {
    txt: 'N_RI0020-Benteng-Benteng',
    code: 'N_RI0020'
  },
  {
    txt: 'N_RI0021-贝那特-Benete',
    code: 'N_RI0021'
  },
  {
    txt: 'N_RI0022-比阿克-Biak',
    code: 'N_RI0022'
  },
  {
    txt: 'N_RI0023-Bima Terminal-Bima Terminal',
    code: 'N_RI0023'
  },
  {
    txt: 'N_RI0024-Bintuni-Bintuni',
    code: 'N_RI0024'
  },
  {
    txt: 'N_RI0025-比林卡斯-Biringkasi',
    code: 'N_RI0025'
  },
  {
    txt: 'N_RI0026-Blang Lancang-Blang Lancang',
    code: 'N_RI0026'
  },
  {
    txt: 'N_RI0027-Bojonegara-Bojonegara',
    code: 'N_RI0027'
  },
  {
    txt: 'N_RI0028-邦坦-Bontang',
    code: 'N_RI0028'
  },
  {
    txt: 'N_RI0029-布亚坦-Buatan',
    code: 'N_RI0029'
  },
  {
    txt: 'N_RI00106-Selat Panjang-Selat Panjang',
    code: 'N_RI00106'
  },
  {
    txt: 'N_RI00107-Semangka Bay-Semangka Bay',
    code: 'N_RI00107'
  },
  {
    txt: 'N_RI00108-Singkawang-Singkawang',
    code: 'N_RI00108'
  },
  {
    txt: 'N_RI00109-Sintete-Sintete',
    code: 'N_RI00109'
  },
  {
    txt: 'N_RI00110-South Pulau Laut Coal Terminal-South Pulau Laut Coal Terminal',
    code: 'N_RI00110'
  },
  {
    txt: 'N_RI00111-双溪帕克宁-Sungai Pakning',
    code: 'N_RI00111'
  },
  {
    txt: 'N_RI00112-Susoh-Susoh',
    code: 'N_RI00112'
  },
  {
    txt: 'N_RI00113-塔胡纳-Tahuna',
    code: 'N_RI00113'
  },
  {
    txt: 'N_RI00114-Tambakan Oil Jetty-Tambakan Oil Jetty',
    code: 'N_RI00114'
  },
  {
    txt: 'N_RI00115-Tanjung Balai Asahan-Tanjung Balai Asahan',
    code: 'N_RI00115'
  },
  {
    txt: 'N_RI00116-Tanjung Intan-Tanjung Intan',
    code: 'N_RI00116'
  },
  {
    txt: 'N_RI00117-Tanjung Balai Karimun-Tanjung Balai Karimun',
    code: 'N_RI00117'
  },
  {
    txt: 'N_RI00118-Tanjung Emas-Tanjung Emas',
    code: 'N_RI00118'
  },
  {
    txt: 'N_RI00119-Tanjung Perak-Tanjung Perak',
    code: 'N_RI00119'
  },
  {
    txt: 'N_RI00120-丹戎乌班-Tanjung Uban',
    code: 'N_RI00120'
  },
  {
    txt: 'N_RI00121-Tanah Merah-Tanah Merah',
    code: 'N_RI00121'
  },
  {
    txt: 'N_RI00122-Tanjung Bara Coal Terminal-Tanjung Bara Coal Terminal',
    code: 'N_RI00122'
  },
  {
    txt: 'N_RI00123-Tanjung Jati-Tanjung Jati',
    code: 'N_RI00123'
  },
  {
    txt: 'N_RI00124-Tanjung Peutang-Tanjung Peutang',
    code: 'N_RI00124'
  },
  {
    txt: 'N_RI00125-Tangguh LNG Terminal-Tangguh LNG Terminal',
    code: 'N_RI00125'
  },
  {
    txt: 'N_RI00126-Teluk Bayur-Teluk Bayur',
    code: 'N_RI00126'
  },
  {
    txt: 'N_RI00127-Teluk Tinopo Pulau Pagai-Teluk Tinopo Pulau Pagai',
    code: 'N_RI00127'
  },
  {
    txt: 'N_RI00128-Tenau Kupang-Tenau Kupang',
    code: 'N_RI00128'
  },
  {
    txt: 'N_RI00129-德那第-Ternate',
    code: 'N_RI00129'
  },
  {
    txt: 'N_RI00130-帕鲁-Tiaka Marine Terminal',
    code: 'N_RI00130'
  },
  {
    txt: 'N_RI00131-tolitoli-tolitoli',
    code: 'N_RI00131'
  },
  {
    txt: 'N_RI00132-tual-tual',
    code: 'N_RI00132'
  },
  {
    txt: 'N_RI00133-图邦-Tubang',
    code: 'N_RI00133'
  },
  {
    txt: 'N_RI00134-Uleelheue-Uleelheue',
    code: 'N_RI00134'
  },
  {
    txt: 'N_RI00135-瓦恩格普-Waingapu',
    code: 'N_RI00135'
  },
  {
    txt: 'N_RI00136-Waisarisa-Waisarisa',
    code: 'N_RI00136'
  },
  {
    txt: 'N_RI00137-Waikelo-Waikelo',
    code: 'N_RI00137'
  },
  {
    txt: 'N_RI00138-Wamsasi-Wamsasi',
    code: 'N_RI00138'
  },
  {
    txt: 'N_RI00139-Widuri Marine Terminal-Widuri Marine Terminal',
    code: 'N_RI00139'
  },
  {
    txt: 'N_IR001-阿米拉拜德-Amirabad',
    code: 'N_IR001'
  },
  {
    txt: 'N_IR002-Assaluyeh-Assaluyeh',
    code: 'N_IR002'
  },
  {
    txt: 'N_IR003-Astara-Astara',
    code: 'N_IR003'
  },
  {
    txt: 'N_IR004-Bahregan Oil Centre (BOC)-Bahregan Oil Centre (BOC)',
    code: 'N_IR004'
  },
  {
    txt: 'N_IR005-Bandar Noshahr-Bandar Noshahr',
    code: 'N_IR005'
  },
  {
    txt: 'N_IR006-安扎里港-Bandar Anzali',
    code: 'N_IR006'
  },
  {
    txt: 'N_IR007-霍梅尼港-Bandar Imam Khomeini',
    code: 'N_IR007'
  },
  {
    txt: 'N_IR008-Bandar Jask-Bandar Jask',
    code: 'N_IR008'
  },
  {
    txt: 'N_IR009-Bandar Lengeh-Bandar Lengeh',
    code: 'N_IR009'
  },
  {
    txt: 'N_IR0010-Bandar Taheri-Bandar Taheri',
    code: 'N_IR0010'
  },
  {
    txt: 'N_IR0011-Bushehr-Bushehr',
    code: 'N_IR0011'
  },
  {
    txt: 'N_IR0012-Chabahar-Chabahar',
    code: 'N_IR0012'
  },
  {
    txt: 'N_IR0013-Fereydoon Kenar-Fereydoon Kenar',
    code: 'N_IR0013'
  },
  {
    txt: 'N_IR0014-基什岛-Kish Island',
    code: 'N_IR0014'
  },
  {
    txt: 'N_IR0015-Nekah-Nekah',
    code: 'N_IR0015'
  },
  {
    txt: 'N_IR0016-Qeshm Island-Qeshm Island',
    code: 'N_IR0016'
  },
  {
    txt: 'N_IR0017-锡里岛-Sirri Island',
    code: 'N_IR0017'
  },
  {
    txt: 'N_IR0018-Soroosh Terminal-Soroosh Terminal',
    code: 'N_IR0018'
  },
  {
    txt: 'N_IR0019-Tombak-Tombak',
    code: 'N_IR0019'
  },
  {
    txt: 'N_IRQ001-Al Basra Oil Terminal-Al Basra Oil Terminal',
    code: 'N_IRQ001'
  },
  {
    txt: 'N_IRQ002-祖拜尔-Khor Al Zubair',
    code: 'N_IRQ002'
  },
  {
    txt: 'N_IRQ003-Khor Al Amaya Oil Terminal-Khor Al Amaya Oil Terminal',
    code: 'N_IRQ003'
  },
  {
    txt: 'N_IRL001-Baltimore (Ireland)-Baltimore (Ireland)',
    code: 'N_IRL001'
  },
  {
    txt: 'N_IRL002-Burtonport-Burtonport',
    code: 'N_IRL002'
  },
  {
    txt: 'N_IRL003-Cahirsiveen-Cahirsiveen',
    code: 'N_IRL003'
  },
  {
    txt: 'N_IRL004-Dingle-Dingle',
    code: 'N_IRL004'
  },
  {
    txt: 'N_IRL005-Dundalk (Ireland)-Dundalk (Ireland)',
    code: 'N_IRL005'
  },
  {
    txt: 'N_IRL006-New Ross-New Ross',
    code: 'N_IRL006'
  },
  {
    txt: 'N_IRL007-Rosslare Europort-Rosslare Europort',
    code: 'N_IRL007'
  },
  {
    txt: 'N_IRL008-Shannon Foynes Port-Shannon Foynes Port',
    code: 'N_IRL008'
  },
  {
    txt: 'N_IRL009-Valentia-Valentia',
    code: 'N_IRL009'
  },
  {
    txt: 'N_IRL0010-Westport (Ireland)-Westport (Ireland)',
    code: 'N_IRL0010'
  },
  {
    txt: 'N_IRL0011-Whiddy Island-Whiddy Island',
    code: 'N_IRL0011'
  },
  {
    txt: 'N_IOM001-Douglas-Douglas',
    code: 'N_IOM001'
  },
  {
    txt: 'N_IOM002-Peel-Peel',
    code: 'N_IOM002'
  },
  {
    txt: 'N_IOM003-Port St Mary-Port St Mary',
    code: 'N_IOM003'
  },
  {
    txt: 'N_IOM004-Ramsey-Ramsey',
    code: 'N_IOM004'
  },
  {
    txt: 'N_I001-阿尔盖罗-Alghero',
    code: 'N_I001'
  },
  {
    txt: 'N_I002-Amalfi-Amalfi',
    code: 'N_I002'
  },
  {
    txt: 'N_I003-Baia-Baia',
    code: 'N_I003'
  },
  {
    txt: 'N_I004-卡利亚里-Cagliari',
    code: 'N_I004'
  },
  {
    txt: 'N_I005-卡碧岛-Capri',
    code: 'N_I005'
  },
  {
    txt: 'N_I006-Castellammare di Stabia-Castellammare di Stabia',
    code: 'N_I006'
  },
  {
    txt: 'N_I007-Ciro Marina-Ciro Marina',
    code: 'N_I007'
  },
  {
    txt: 'N_I008-科里利亚诺卡拉布罗-Corigliano Calabro',
    code: 'N_I008'
  },
  {
    txt: 'N_I009-FPSO Firenze-FPSO Firenze',
    code: 'N_I009'
  },
  {
    txt: 'N_I0010-FSO Leonis-FSO Leonis',
    code: 'N_I0010'
  },
  {
    txt: 'N_I0011-Gallipoli (Italy)-Gallipoli (Italy)',
    code: 'N_I0011'
  },
  {
    txt: 'N_I0012-Giardini-Giardini',
    code: 'N_I0012'
  },
  {
    txt: 'N_I0013-焦亚陶罗-Gioia Tauro',
    code: 'N_I0013'
  },
  {
    txt: 'N_I0014-Golfo Aranci-Golfo Aranci',
    code: 'N_I0014'
  },
  {
    txt: 'N_I0015-Ischia-Ischia',
    code: 'N_I0015'
  },
  {
    txt: 'N_I0016-La Maddalena (Sardinia)-La Maddalena (Sardinia)',
    code: 'N_I0016'
  },
  {
    txt: 'N_I0017-Lampedusa-Lampedusa',
    code: 'N_I0017'
  },
  {
    txt: 'N_I0018-利帕里-Lipari',
    code: 'N_I0018'
  },
  {
    txt: 'N_I0019-里窝那-Livorno',
    code: 'N_I0019'
  },
  {
    txt: 'N_I0020-Livorno Offshore LNG Terminal-Livorno Offshore LNG Terminal',
    code: 'N_I0020'
  },
  {
    txt: 'N_I0021-卡腊腊-Marina di Carrara',
    code: 'N_I0021'
  },
  {
    txt: 'N_I0022-马察腊德瓦洛-Mazara del Vallo',
    code: 'N_I0022'
  },
  {
    txt: 'N_I0023-蒙法尔科内-Monfalcone',
    code: 'N_I0023'
  },
  {
    txt: 'N_I0024-Otranto-Otranto',
    code: 'N_I0024'
  },
  {
    txt: 'N_I0025-帕劳-Palau',
    code: 'N_I0025'
  },
  {
    txt: 'N_I0026-Pantelleria-Pantelleria',
    code: 'N_I0026'
  },
  {
    txt: 'N_I0027-诺格罗港-Porto Nogaro',
    code: 'N_I0027'
  },
  {
    txt: 'N_I0028-韦斯梅港-Porto Vesme',
    code: 'N_I0028'
  },
  {
    txt: 'N_I0029-Porto di Vibo Valentia Marina-Porto di Vibo Valentia Marina',
    code: 'N_I0029'
  },
  {
    txt: 'N_I0030-切尔沃港-Porto Cervo',
    code: 'N_I0030'
  },
  {
    txt: 'N_I0031-费拉约港-Portoferraio',
    code: 'N_I0031'
  },
  {
    txt: 'N_I0032-波尔图莱万特-Porto Levante',
    code: 'N_I0032'
  },
  {
    txt: 'N_I0033-Portofino-Portofino',
    code: 'N_I0033'
  },
  {
    txt: 'N_I0034-Positano-Positano',
    code: 'N_I0034'
  },
  {
    txt: 'N_I0035-波扎洛-Pozzallo',
    code: 'N_I0035'
  },
  {
    txt: 'N_I0036-卡拉布里亚雷焦-Reggio Calabria',
    code: 'N_I0036'
  },
  {
    txt: 'N_I0037-Rimini-Rimini',
    code: 'N_I0037'
  },
  {
    txt: 'N_I0038-San Benedetto del Tronto-San Benedetto del Tronto',
    code: 'N_I0038'
  },
  {
    txt: 'N_I0039-Santa Panagia Oil Terminal-Santa Panagia Oil Terminal',
    code: 'N_I0039'
  },
  {
    txt: "N_I0040-圣昂提奥科-Sant' Antioco",
    code: 'N_I0040'
  },
  {
    txt: 'N_I0041-Santa Teresa Gallura-Santa Teresa Gallura',
    code: 'N_I0041'
  },
  {
    txt: 'N_I0042-Sarroch Oil Terminal-Sarroch Oil Terminal',
    code: 'N_I0042'
  },
  {
    txt: 'N_I0043-Savona-Vado-Savona-Vado',
    code: 'N_I0043'
  },
  {
    txt: 'N_I0044-Scarlino-Scarlino',
    code: 'N_I0044'
  },
  {
    txt: 'N_I0045-Sorrento-Sorrento',
    code: 'N_I0045'
  },
  {
    txt: 'N_I0046-泰尔米尼伊梅雷塞-Termini Imerese',
    code: 'N_I0046'
  },
  {
    txt: 'N_I0047-特尔莫利-Termoli',
    code: 'N_I0047'
  },
  {
    txt: 'N_I0048-瓦达-Vada',
    code: 'N_I0048'
  },
  {
    txt: 'N_JAM001-Black River-Black River',
    code: 'N_JAM001'
  },
  {
    txt: 'N_JAM002-Falmouth Port-Falmouth Port',
    code: 'N_JAM002'
  },
  {
    txt: 'N_JAM003-Kingston (Jamaica)-Kingston (Jamaica)',
    code: 'N_JAM003'
  },
  {
    txt: 'N_JAM004-奥科里奥斯-Ocho Rios',
    code: 'N_JAM004'
  },
  {
    txt: 'N_JAM005-Old Harbour-Old Harbour',
    code: 'N_JAM005'
  },
  {
    txt: 'N_JAM006-Port Royal (Jamaica)-Port Royal (Jamaica)',
    code: 'N_JAM006'
  },
  {
    txt: 'N_JAP001-ako anch-ako anch',
    code: 'N_JAP001'
  },
  {
    txt: 'N_JAP002-Amagasaki-Nishinomiya-Ashiya-Amagasaki-Nishinomiya-Ashiya',
    code: 'N_JAP002'
  },
  {
    txt: 'N_JAP003-有川-Aokata',
    code: 'N_JAP003'
  },
  {
    txt: 'N_JAP004-awazu-awazu',
    code: 'N_JAP004'
  },
  {
    txt: 'N_JAP005-福奎-Fukui',
    code: 'N_JAP005'
  },
  {
    txt: 'N_JAP006-Hamochi-Hamochi',
    code: 'N_JAP006'
  },
  {
    txt: 'N_JAP007-Hibi-Hibi',
    code: 'N_JAP007'
  },
  {
    txt: 'N_JAP008-东播磨-Higashi-Harima',
    code: 'N_JAP008'
  },
  {
    txt: 'N_JAP009-海米卡瓦-Himekawa',
    code: 'N_JAP009'
  },
  {
    txt: 'N_JAP0010-平良-Hirara',
    code: 'N_JAP0010'
  },
  {
    txt: 'N_JAP0011-常陆那珂-Hitachinaka',
    code: 'N_JAP0011'
  },
  {
    txt: 'N_JAP0012-Hizen-Oshima-Hizen-Oshima',
    code: 'N_JAP0012'
  },
  {
    txt: 'N_JAP0013-Ishikariwan Shinko-Ishikariwan Shinko',
    code: 'N_JAP0013'
  },
  {
    txt: 'N_JAP0014-Iwafune-Iwafune',
    code: 'N_JAP0014'
  },
  {
    txt: 'N_JAP0015-Kanmon Ko-Kanmon Ko',
    code: 'N_JAP0015'
  },
  {
    txt: 'N_JAP0016-刈田港-Kanda',
    code: 'N_JAP0016'
  },
  {
    txt: 'N_JAP0017-柏崎-Kashiwazaki',
    code: 'N_JAP0017'
  },
  {
    txt: 'N_JAP0018-KATAKAMI-KATAKAMI',
    code: 'N_JAP0018'
  },
  {
    txt: 'N_JAP0019-Keihin-Keihin',
    code: 'N_JAP0019'
  },
  {
    txt: 'N_JAP0020-气仙沼-Kesennuma',
    code: 'N_JAP0020'
  },
  {
    txt: 'N_JAP0021-Kinwan-Kinwan',
    code: 'N_JAP0021'
  },
  {
    txt: 'N_JAP0022-Kochi (Japan)-Kochi (Japan)',
    code: 'N_JAP0022'
  },
  {
    txt: 'N_JAP0023-Kumamoto-Kumamoto',
    code: 'N_JAP0023'
  },
  {
    txt: 'N_JAP0024-大濑户-Matsushima',
    code: 'N_JAP0024'
  },
  {
    txt: 'N_JAP0025-Matsuura-Matsuura',
    code: 'N_JAP0025'
  },
  {
    txt: 'N_JAP0026-Misumi (Kumamoto)-Misumi (Kumamoto)',
    code: 'N_JAP0026'
  },
  {
    txt: 'N_JAP0027-米什马卡瓦诺-Mishima-Kawanoe',
    code: 'N_JAP0027'
  },
  {
    txt: 'N_JAP0028-Misumi (Shimane)-Misumi (Shimane)',
    code: 'N_JAP0028'
  },
  {
    txt: 'N_JAP0029-三川子岛-Mitsukojima',
    code: 'N_JAP0029'
  },
  {
    txt: 'N_JAP0030-Mitajiri-Mitajiri',
    code: 'N_JAP0030'
  },
  {
    txt: 'N_JAP0031-Nago Bay-Nago Bay',
    code: 'N_JAP0031'
  },
  {
    txt: 'N_JAP0032-Nagasu-Nagasu',
    code: 'N_JAP0032'
  },
  {
    txt: 'N_JAP0033-中津-Nakatsu',
    code: 'N_JAP0033'
  },
  {
    txt: 'N_JAP0034-东予-Namikata',
    code: 'N_JAP0034'
  },
  {
    txt: 'N_JAP0035-Naze-Naze',
    code: 'N_JAP0035'
  },
  {
    txt: 'N_JAP0036-Niigata-Higashi-Niigata-Higashi',
    code: 'N_JAP0036'
  },
  {
    txt: 'N_JAP0037-Nishihara Terminal-Nishihara Terminal',
    code: 'N_JAP0037'
  },
  {
    txt: 'N_JAP0038-努马祖-Numazu',
    code: 'N_JAP0038'
  },
  {
    txt: 'N_JAP0039-nyugawa-nyugawa',
    code: 'N_JAP0039'
  },
  {
    txt: 'N_JAP0040-Oarai-Oarai',
    code: 'N_JAP0040'
  },
  {
    txt: 'N_JAP0041-Onomichi-Itozaki-Onomichi-Itozaki',
    code: 'N_JAP0041'
  },
  {
    txt: 'N_JAP0042-小野田-Onoda',
    code: 'N_JAP0042'
  },
  {
    txt: 'N_JAP0043-Otake-Otake',
    code: 'N_JAP0043'
  },
  {
    txt: 'N_JAP0044-Port Yakushima-Port Yakushima',
    code: 'N_JAP0044'
  },
  {
    txt: 'N_JAP0045-苓北-Reihoku',
    code: 'N_JAP0045'
  },
  {
    txt: 'N_JAP0046-界港-Sakai',
    code: 'N_JAP0046'
  },
  {
    txt: 'N_JAP0047-仙台-Satsumasendai',
    code: 'N_JAP0047'
  },
  {
    txt: 'N_JAP0048-Shinminato-Shinminato',
    code: 'N_JAP0048'
  },
  {
    txt: 'N_JAP0049-Shioya-Shioya',
    code: 'N_JAP0049'
  },
  {
    txt: 'N_JAP0050-新宫-Shingu',
    code: 'N_JAP0050'
  },
  {
    txt: 'N_JAP0051-Shima-Shima',
    code: 'N_JAP0051'
  },
  {
    txt: 'N_JAP0052-Shirashima-Shirashima',
    code: 'N_JAP0052'
  },
  {
    txt: 'N_JAP0053-Shinmoji-Shinmoji',
    code: 'N_JAP0053'
  },
  {
    txt: 'N_JAP0054-Shika-Shika',
    code: 'N_JAP0054'
  },
  {
    txt: 'N_JAP0055-相马-Soma',
    code: 'N_JAP0055'
  },
  {
    txt: 'N_JAP0056-STS Area Tsushima-STS Area Tsushima',
    code: 'N_JAP0056'
  },
  {
    txt: 'N_JAP0057-须崎-Susaki',
    code: 'N_JAP0057'
  },
  {
    txt: 'N_JAP0058-塔基哈拉-Takehara',
    code: 'N_JAP0058'
  },
  {
    txt: 'N_JAP0059-Takasu-Takasu',
    code: 'N_JAP0059'
  },
  {
    txt: 'N_JAP0060-户细-Tobata',
    code: 'N_JAP0060'
  },
  {
    txt: 'N_JAP0061-tokushima-tokushima',
    code: 'N_JAP0061'
  },
  {
    txt: 'N_JAP0062-Tsumatsusaka (Matsusaka)-Tsumatsusaka (Matsusaka)',
    code: 'N_JAP0062'
  },
  {
    txt: 'N_JAP0063-Tsumatsusaka (Tsu)-Tsumatsusaka (Tsu)',
    code: 'N_JAP0063'
  },
  {
    txt: 'N_JAP0064-Unoshima-Unoshima',
    code: 'N_JAP0064'
  },
  {
    txt: 'N_JAP0065-Uwajima-Uwajima',
    code: 'N_JAP0065'
  },
  {
    txt: 'N_JAP0066-Wada-Wada',
    code: 'N_JAP0066'
  },
  {
    txt: 'N_JAP0067-Yanai-Yanai',
    code: 'N_JAP0067'
  },
  {
    txt: 'N_JAP0068-横须贺-Yokosuka',
    code: 'N_JAP0068'
  },
  {
    txt: 'N_JAP0069-Yura-Yura',
    code: 'N_JAP0069'
  },
  {
    txt: 'N_JER001-St Helier-St Helier',
    code: 'N_JER001'
  },
  {
    txt: 'N_KAZ001-阿克套-Aktau',
    code: 'N_KAZ001'
  },
  {
    txt: 'N_KAZ002-Kuryk-Kuryk',
    code: 'N_KAZ002'
  },
  {
    txt: 'N_KI001-Betio-Betio',
    code: 'N_KI001'
  },
  {
    txt: 'N_KI002-English Harbour-English Harbour',
    code: 'N_KI002'
  },
  {
    txt: 'N_NK001-Chongjin-Chongjin',
    code: 'N_NK001'
  },
  {
    txt: 'N_NK002-Najin-Najin',
    code: 'N_NK002'
  },
  {
    txt: 'N_NK003-Nampo-Nampo',
    code: 'N_NK003'
  },
  {
    txt: 'N_NK004-Senbong-Senbong',
    code: 'N_NK004'
  },
  {
    txt: 'N_NK005-Songjin-Songjin',
    code: 'N_NK005'
  },
  {
    txt: 'N_SK001-Daesan-Daesan',
    code: 'N_SK001'
  },
  {
    txt: 'N_SK002-Daipori-Daipori',
    code: 'N_SK002'
  },
  {
    txt: 'N_SK003-Donghae-Donghae',
    code: 'N_SK003'
  },
  {
    txt: 'N_SK004-doyang-doyang',
    code: 'N_SK004'
  },
  {
    txt: 'N_SK005-Geoje Terminal-Geoje Terminal',
    code: 'N_SK005'
  },
  {
    txt: 'N_SK006-gohyeon-gohyeon',
    code: 'N_SK006'
  },
  {
    txt: 'N_SK007-Gohyun-Gohyun',
    code: 'N_SK007'
  },
  {
    txt: 'N_SK008-Gunsan-Gunsan',
    code: 'N_SK008'
  },
  {
    txt: 'N_SK009-hongwon-hongwon',
    code: 'N_SK009'
  },
  {
    txt: 'N_SK0010-Incheon-Incheon',
    code: 'N_SK0010'
  },
  {
    txt: 'N_SK0011-janghang-janghang',
    code: 'N_SK0011'
  },
  {
    txt: 'N_SK0012-Jeju-Jeju',
    code: 'N_SK0012'
  },
  {
    txt: 'N_SK0013-Jinhae-Jinhae',
    code: 'N_SK0013'
  },
  {
    txt: 'N_SK0014-Okgye-Okgye',
    code: 'N_SK0014'
  },
  {
    txt: 'N_SK0015-Okpo-Okpo',
    code: 'N_SK0015'
  },
  {
    txt: 'N_SK0016-Pohang Yeongil-Pohang Yeongil',
    code: 'N_SK0016'
  },
  {
    txt: 'N_SK0017-Pyeong Taek-Pyeong Taek',
    code: 'N_SK0017'
  },
  {
    txt: 'N_SK0018-Samcheok-Samcheok',
    code: 'N_SK0018'
  },
  {
    txt: 'N_SK0019-Samcheon Po-Samcheon Po',
    code: 'N_SK0019'
  },
  {
    txt: 'N_SK0020-SEONGSAN-SEONGSAN',
    code: 'N_SK0020'
  },
  {
    txt: 'N_SK0021-Seogwipo-Seogwipo',
    code: 'N_SK0021'
  },
  {
    txt: 'N_SK0022-Sokcho-Sokcho',
    code: 'N_SK0022'
  },
  {
    txt: 'N_SK0023-Tongyeong-Tongyeong',
    code: 'N_SK0023'
  },
  {
    txt: 'N_SK0024-Wando-Wando',
    code: 'N_SK0024'
  },
  {
    txt: 'N_KWT001-Doha (Kuwait)-Doha (Kuwait)',
    code: 'N_KWT001'
  },
  {
    txt: 'N_KWT002-Khor al Muffatta-Khor al Muffatta',
    code: 'N_KWT002'
  },
  {
    txt: 'N_KWT003-米纳阿卜杜拉-Mina Abdulla',
    code: 'N_KWT003'
  },
  {
    txt: 'N_KWT004-米纳艾哈迈迪-Mina Al Ahmadi',
    code: 'N_KWT004'
  },
  {
    txt: 'N_KWT005-米纳萨乌德-Mina Saud',
    code: 'N_KWT005'
  },
  {
    txt: 'N_LAT001-安格尔-Engure',
    code: 'N_LAT001'
  },
  {
    txt: 'N_LAT002-Lielupe-Lielupe',
    code: 'N_LAT002'
  },
  {
    txt: 'N_LAT003-摩斯拉格斯-Mersrags',
    code: 'N_LAT003'
  },
  {
    txt: 'N_LAT004-帕维洛斯塔-Pavilosta',
    code: 'N_LAT004'
  },
  {
    txt: 'N_LAT005-罗亚-Roja',
    code: 'N_LAT005'
  },
  {
    txt: 'N_LAT006-萨拉茨格里瓦-Salacgriva',
    code: 'N_LAT006'
  },
  {
    txt: 'N_LAT007-休尔特-Skulte',
    code: 'N_LAT007'
  },
  {
    txt: 'N_RL001-Selaata-Selaata',
    code: 'N_RL001'
  },
  {
    txt: 'N_RL002-Tripoli (Lebanon)-Tripoli (Lebanon)',
    code: 'N_RL002'
  },
  {
    txt: 'N_RL003-Tyr-Tyr',
    code: 'N_RL003'
  },
  {
    txt: 'N_RL004-Zahrani Terminal-Zahrani Terminal',
    code: 'N_RL004'
  },
  {
    txt: 'N_LY001-阿布卡马什-Abu Kammash',
    code: 'N_LY001'
  },
  {
    txt: 'N_LY002-Bouri-Bouri',
    code: 'N_LY002'
  },
  {
    txt: 'N_LY003-FPSO Farwah-FPSO Farwah',
    code: 'N_LY003'
  },
  {
    txt: 'N_LY004-Khoms-Khoms',
    code: 'N_LY004'
  },
  {
    txt: 'N_LY005-Marsa al Hariga-Marsa al Hariga',
    code: 'N_LY005'
  },
  {
    txt: 'N_LY006-Mellitah-Mellitah',
    code: 'N_LY006'
  },
  {
    txt: 'N_LY007-Qasr Ahmed-Qasr Ahmed',
    code: 'N_LY007'
  },
  {
    txt: 'N_LY008-拉斯拉努夫-Ras Lanuf',
    code: 'N_LY008'
  },
  {
    txt: 'N_LY009-Ras el Hilal-Ras el Hilal',
    code: 'N_LY009'
  },
  {
    txt: 'N_LY0010-Tripoli (Libya)-Tripoli (Libya)',
    code: 'N_LY0010'
  },
  {
    txt: 'N_LY0011-Zawia Terminal-Zawia Terminal',
    code: 'N_LY0011'
  },
  {
    txt: 'N_LY0012-祖埃提纳-Zueitina',
    code: 'N_LY0012'
  },
  {
    txt: 'N_LIT001-Butinge Marine Terminal-Butinge Marine Terminal',
    code: 'N_LIT001'
  },
  {
    txt: 'N_MAS001-Andoany-Andoany',
    code: 'N_MAS001'
  },
  {
    txt: 'N_MAS002-Diego Suarez-Diego Suarez',
    code: 'N_MAS002'
  },
  {
    txt: 'N_MAS003-Ehoala-Ehoala',
    code: 'N_MAS003'
  },
  {
    txt: 'N_MAS004-Majunga-Majunga',
    code: 'N_MAS004'
  },
  {
    txt: 'N_MAS005-Port Saint Louis-Port Saint Louis',
    code: 'N_MAS005'
  },
  {
    txt: 'N_MAS006-Tulear-Tulear',
    code: 'N_MAS006'
  },
  {
    txt: 'N_MAS007-渥海马尔-Vohemar',
    code: 'N_MAS007'
  },
  {
    txt: 'N_MAD001-Canical-Canical',
    code: 'N_MAD001'
  },
  {
    txt: 'N_MAD002-Porto Santo-Porto Santo',
    code: 'N_MAD002'
  },
  {
    txt: 'N_PTM001-Abu Marine Terminal-Abu Marine Terminal',
    code: 'N_PTM001'
  },
  {
    txt: 'N_PTM002-Angsi Marine Terminal-Angsi Marine Terminal',
    code: 'N_PTM002'
  },
  {
    txt: 'N_PTM003-Bakapit-Bakapit',
    code: 'N_PTM003'
  },
  {
    txt: 'N_PTM004-Bintangor-Bintangor',
    code: 'N_PTM004'
  },
  {
    txt: 'N_PTM005-Bunga Raya Marine Terminal-Bunga Raya Marine Terminal',
    code: 'N_PTM005'
  },
  {
    txt: 'N_PTM006-Cakerawala Marine Terminal-Cakerawala Marine Terminal',
    code: 'N_PTM006'
  },
  {
    txt: 'N_PTM007-Cendor Marine Terminal-Cendor Marine Terminal',
    code: 'N_PTM007'
  },
  {
    txt: 'N_PTM008-Dulang Marine Terminal-Dulang Marine Terminal',
    code: 'N_PTM008'
  },
  {
    txt: 'N_PTM009-FPSO MaMPU 1-FPSO MaMPU 1',
    code: 'N_PTM009'
  },
  {
    txt: 'N_PTM0010-FPSO Berantai-FPSO Berantai',
    code: 'N_PTM0010'
  },
  {
    txt: 'N_PTM0011-FPSO Bertam-FPSO Bertam',
    code: 'N_PTM0011'
  },
  {
    txt: 'N_PTM0012-FPSO Bunga Kertas (Penara Terminal)-FPSO Bunga Kertas (Penara Terminal)',
    code: 'N_PTM0012'
  },
  {
    txt: 'N_PTM0013-FPSO Perisai Kamelia-FPSO Perisai Kamelia',
    code: 'N_PTM0013'
  },
  {
    txt: 'N_PTM0014-FSO Fois Nautica Tembikai-FSO Fois Nautica Tembikai',
    code: 'N_PTM0014'
  },
  {
    txt: 'N_PTM0015-FSO Caspian Sea-FSO Caspian Sea',
    code: 'N_PTM0015'
  },
  {
    txt: 'N_PTM0016-FSO Sepat-FSO Sepat',
    code: 'N_PTM0016'
  },
  {
    txt: 'N_PTM0017-FSO Orkid-FSO Orkid',
    code: 'N_PTM0017'
  },
  {
    txt: 'N_PTM0018-Johor-Johor',
    code: 'N_PTM0018'
  },
  {
    txt: 'N_PTM0019-Kertih Port-Kertih Port',
    code: 'N_PTM0019'
  },
  {
    txt: 'N_PTM0020-Kikeh Marine Terminal-Kikeh Marine Terminal',
    code: 'N_PTM0020'
  },
  {
    txt: 'N_PTM0021-Kuah-Kuah',
    code: 'N_PTM0021'
  },
  {
    txt: 'N_PTM0022-Limbang-Limbang',
    code: 'N_PTM0022'
  },
  {
    txt: 'N_PTM0023-Lumut (Malaysia)-Lumut (Malaysia)',
    code: 'N_PTM0023'
  },
  {
    txt: 'N_PTM0024-马六甲-Malacca',
    code: 'N_PTM0024'
  },
  {
    txt: 'N_PTM0025-Malong Terminal-Malong Terminal',
    code: 'N_PTM0025'
  },
  {
    txt: 'N_PTM0026-Pengerang Terminal-Pengerang Terminal',
    code: 'N_PTM0026'
  },
  {
    txt: 'N_PTM0027-巴生港-Port Klang',
    code: 'N_PTM0027'
  },
  {
    txt: 'N_PTM0028-Samalaju-Samalaju',
    code: 'N_PTM0028'
  },
  {
    txt: 'N_PTM0029-Sapangar Bay-Sapangar Bay',
    code: 'N_PTM0029'
  },
  {
    txt: 'N_PTM0030-Sarikei-Sarikei',
    code: 'N_PTM0030'
  },
  {
    txt: 'N_PTM0031-斯皮唐-Sipitang',
    code: 'N_PTM0031'
  },
  {
    txt: 'N_PTM0032-散盖乌当-Sungai Udang',
    code: 'N_PTM0032'
  },
  {
    txt: 'N_PTM0033-Sungai Linggi-Sungai Linggi',
    code: 'N_PTM0033'
  },
  {
    txt: 'N_PTM0034-丹戎马尼-Tanjung Manis',
    code: 'N_PTM0034'
  },
  {
    txt: 'N_PTM0035-约翰巴鲁-Tanjung Langsat',
    code: 'N_PTM0035'
  },
  {
    txt: 'N_PTM0036-丹戎帕拉帕斯-Tanjung Pelepas',
    code: 'N_PTM0036'
  },
  {
    txt: 'N_PTM0037-Teluk Anson-Teluk Anson',
    code: 'N_PTM0037'
  },
  {
    txt: 'N_PTM0038-Teluk Ewa-Teluk Ewa',
    code: 'N_PTM0038'
  },
  {
    txt: 'N_PTM0039-Tioman-Tioman',
    code: 'N_PTM0039'
  },
  {
    txt: 'N_MV001-Male-Male',
    code: 'N_MV001'
  },
  {
    txt: 'N_MLT001-马尔萨什洛克-Marsaxlokk',
    code: 'N_MLT001'
  },
  {
    txt: 'N_ML001-夸贾林-Kwajalein',
    code: 'N_ML001'
  },
  {
    txt: 'N_ML002-马朱罗-Majuro',
    code: 'N_ML002'
  },
  {
    txt: 'N_ML003-Wake Island-Wake Island',
    code: 'N_ML003'
  },
  {
    txt: 'N_MAR001-Fort-de-France-Fort-de-France',
    code: 'N_MAR001'
  },
  {
    txt: 'N_MAR002-Marin (Martinique)-Marin (Martinique)',
    code: 'N_MAR002'
  },
  {
    txt: 'N_MAU001-FPSO Berge Helene-FPSO Berge Helene',
    code: 'N_MAU001'
  },
  {
    txt: 'N_MS001-Port mathurin-Port mathurin',
    code: 'N_MS001'
  },
  {
    txt: 'N_MEX001-阿卡普尔科-Acapulco',
    code: 'N_MEX001'
  },
  {
    txt: 'N_MEX002-阿塔迈拉-Altamira',
    code: 'N_MEX002'
  },
  {
    txt: 'N_MEX003-Cabo San Lucas-Cabo San Lucas',
    code: 'N_MEX003'
  },
  {
    txt: 'N_MEX004-Cayo Arcas Terminal-Cayo Arcas Terminal',
    code: 'N_MEX004'
  },
  {
    txt: 'N_MEX005-Ciudad del Carmen-Ciudad del Carmen',
    code: 'N_MEX005'
  },
  {
    txt: 'N_MEX006-Costa Azul LNG Terminal-Costa Azul LNG Terminal',
    code: 'N_MEX006'
  },
  {
    txt: 'N_MEX007-科苏梅尔-Cozumel',
    code: 'N_MEX007'
  },
  {
    txt: 'N_MEX008-道斯波卡斯-Dos Bocas',
    code: 'N_MEX008'
  },
  {
    txt: 'N_MEX009-Huatulco-Huatulco',
    code: 'N_MEX009'
  },
  {
    txt: 'N_MEX0010-Loreto-Loreto',
    code: 'N_MEX0010'
  },
  {
    txt: 'N_MEX0011-Manzanillo (Mexico)-Manzanillo (Mexico)',
    code: 'N_MEX0011'
  },
  {
    txt: 'N_MEX0012-皮奇林克港-Pichilingue',
    code: 'N_MEX0012'
  },
  {
    txt: 'N_MEX0013-Puerto Progreso-Puerto Progreso',
    code: 'N_MEX0013'
  },
  {
    txt: 'N_MEX0014-巴亚尔塔港-Puerto Vallarta',
    code: 'N_MEX0014'
  },
  {
    txt: 'N_MEX0015-Puerto Morelos-Puerto Morelos',
    code: 'N_MEX0015'
  },
  {
    txt: 'N_MEX0016-Puerto Costa Maya-Puerto Costa Maya',
    code: 'N_MEX0016'
  },
  {
    txt: 'N_MEX0017-Puerto Escondido-Puerto Escondido',
    code: 'N_MEX0017'
  },
  {
    txt: 'N_MEX0018-Punta Venado-Punta Venado',
    code: 'N_MEX0018'
  },
  {
    txt: 'N_MEX0019-San Carlos (Mexico)-San Carlos (Mexico)',
    code: 'N_MEX0019'
  },
  {
    txt: 'N_MEX0020-San Juan de la Costa-San Juan de la Costa',
    code: 'N_MEX0020'
  },
  {
    txt: 'N_MEX0021-Santa Maria (Mexico)-Santa Maria (Mexico)',
    code: 'N_MEX0021'
  },
  {
    txt: 'N_MEX0022-San Marcos-San Marcos',
    code: 'N_MEX0022'
  },
  {
    txt: 'N_MIC001-Pohnpei Harbour-Pohnpei Harbour',
    code: 'N_MIC001'
  },
  {
    txt: 'N_MIC002-托米尔-Tomil Harbour',
    code: 'N_MIC002'
  },
  {
    txt: 'N_MIC003-Weno-Weno',
    code: 'N_MIC003'
  },
  {
    txt: 'N_MOL001-朱朱列什蒂-Giurgiulesti',
    code: 'N_MOL001'
  },
  {
    txt: 'N_MC001-Monaco-Monaco',
    code: 'N_MC001'
  },
  {
    txt: 'N_MTN001-Bijela-Bijela',
    code: 'N_MTN001'
  },
  {
    txt: 'N_MTN002-Tivat-Tivat',
    code: 'N_MTN002'
  },
  {
    txt: 'N_MON001-小海湾-Little Bay',
    code: 'N_MON001'
  },
  {
    txt: 'N_MA001-阿尔豪塞马-Al Hoceima',
    code: 'N_MA001'
  },
  {
    txt: 'N_MA002-纳多尔-Port Nador',
    code: 'N_MA002'
  },
  {
    txt: 'N_MA003-Tan Tan-Tan Tan',
    code: 'N_MA003'
  },
  {
    txt: 'N_MA004-Tanger-Mediterranean-Tanger-Mediterranean',
    code: 'N_MA004'
  },
  {
    txt: 'N_MB001-Macuse-Macuse',
    code: 'N_MB001'
  },
  {
    txt: 'N_MB002-Mozambique Island-Mozambique Island',
    code: 'N_MB002'
  },
  {
    txt: 'N_MB003-Pebane-Pebane',
    code: 'N_MB003'
  },
  {
    txt: 'N_MY001-Dawei-Dawei',
    code: 'N_MY001'
  },
  {
    txt: 'N_MY002-Kawthaung-Kawthaung',
    code: 'N_MY002'
  },
  {
    txt: 'N_MY003-Maday Island Oil Terminal-Maday Island Oil Terminal',
    code: 'N_MY003'
  },
  {
    txt: 'N_MY004-Sittwe-Sittwe',
    code: 'N_MY004'
  },
  {
    txt: 'N_MY005-仰光-Yangon',
    code: 'N_MY005'
  },
  {
    txt: 'N_NAU001-瑙鲁-Nauru',
    code: 'N_NAU001'
  },
  {
    txt: 'N_NL001-阿尔克马-Alkmaar',
    code: 'N_NL001'
  },
  {
    txt: 'N_NL002-Alphen-Alphen',
    code: 'N_NL002'
  },
  {
    txt: 'N_NL003-ameland-ameland',
    code: 'N_NL003'
  },
  {
    txt: 'N_NL004-Breskens-Breskens',
    code: 'N_NL004'
  },
  {
    txt: 'N_NL005-代尔夫宰尔-Delfzijl',
    code: 'N_NL005'
  },
  {
    txt: 'N_NL006-赫尔德-Den Helder',
    code: 'N_NL006'
  },
  {
    txt: 'N_NL007-Dintelmond-Dintelmond',
    code: 'N_NL007'
  },
  {
    txt: 'N_NL008-haaften-haaften',
    code: 'N_NL008'
  },
  {
    txt: 'N_NL009-艾莫伊登-IJmuiden',
    code: 'N_NL009'
  },
  {
    txt: 'N_NL0010-lobith-lobith',
    code: 'N_NL0010'
  },
  {
    txt: 'N_NL0011-莫尔迪克-Moerdijk',
    code: 'N_NL0011'
  },
  {
    txt: 'N_NL0012-尼米甘-Nijmegen',
    code: 'N_NL0012'
  },
  {
    txt: 'N_NL0013-ochten-ochten',
    code: 'N_NL0013'
  },
  {
    txt: 'N_NL0014-Theodorushaven-Theodorushaven',
    code: 'N_NL0014'
  },
  {
    txt: 'N_NL0015-Vlissingen-Vlissingen',
    code: 'N_NL0015'
  },
  {
    txt: 'N_NL0016-West Terschelling-West Terschelling',
    code: 'N_NL0016'
  },
  {
    txt: 'N_NL0017-Zaanstad-Zaanstad',
    code: 'N_NL0017'
  },
  {
    txt: 'N_NC001-伯尤古-Baie Ugue',
    code: 'N_NC001'
  },
  {
    txt: 'N_NC002-ILE DES PINS-ILE DES PINS',
    code: 'N_NC002'
  },
  {
    txt: 'N_NC003-Kouaoua-Kouaoua',
    code: 'N_NC003'
  },
  {
    txt: 'N_NC004-LIFOU-LIFOU',
    code: 'N_NC004'
  },
  {
    txt: 'N_NC005-内普维-Nepoui',
    code: 'N_NC005'
  },
  {
    txt: 'N_NC006-帕高门-Paagoumene',
    code: 'N_NC006'
  },
  {
    txt: 'N_NC007-Poro-Poro',
    code: 'N_NC007'
  },
  {
    txt: 'N_NC008-Prony Port-Prony Port',
    code: 'N_NC008'
  },
  {
    txt: 'N_NC009-蒂奥-Thio',
    code: 'N_NC009'
  },
  {
    txt: 'N_NC0010-Vavouto Port-Vavouto Port',
    code: 'N_NC0010'
  },
  {
    txt: 'N_NZ001-Akaroa-Akaroa',
    code: 'N_NZ001'
  },
  {
    txt: 'N_NZ002-Eastland Port-Eastland Port',
    code: 'N_NZ002'
  },
  {
    txt: 'N_NZ003-FPSO Umuroa-FPSO Umuroa',
    code: 'N_NZ003'
  },
  {
    txt: 'N_NZ004-FPSO Raroa Terminal-FPSO Raroa Terminal',
    code: 'N_NZ004'
  },
  {
    txt: 'N_NZ005-格雷默斯-Greymouth',
    code: 'N_NZ005'
  },
  {
    txt: 'N_NZ006-马斯顿角-Marsden Point',
    code: 'N_NZ006'
  },
  {
    txt: 'N_NZ007-奥塔戈港-Otago Harbour',
    code: 'N_NZ007'
  },
  {
    txt: 'N_NZ008-Picton (New Zealand)-Picton (New Zealand)',
    code: 'N_NZ008'
  },
  {
    txt: 'N_NZ009-Port Taranaki-Port Taranaki',
    code: 'N_NZ009'
  },
  {
    txt: 'N_NZ0010-Taharoa Terminal-Taharoa Terminal',
    code: 'N_NZ0010'
  },
  {
    txt: 'N_NZ0011-Tarakohe-Tarakohe',
    code: 'N_NZ0011'
  },
  {
    txt: 'N_NZ0012-Westport (New Zealand)-Westport (New Zealand)',
    code: 'N_NZ0012'
  },
  {
    txt: 'N_NIC001-Bluefields-Bluefields',
    code: 'N_NIC001'
  },
  {
    txt: 'N_WAN001-Abo Terminal-Abo Terminal',
    code: 'N_WAN001'
  },
  {
    txt: 'N_WAN002-Agbami Oil Terminal-Agbami Oil Terminal',
    code: 'N_WAN002'
  },
  {
    txt: 'N_WAN003-Antan Terminal-Antan Terminal',
    code: 'N_WAN003'
  },
  {
    txt: 'N_RI0030-布拉-Bula',
    code: 'N_RI0030'
  },
  {
    txt: 'N_RI0031-邦榆-Bunyu',
    code: 'N_RI0031'
  },
  {
    txt: 'N_RI0032-Bunati Port-Bunati Port',
    code: 'N_RI0032'
  },
  {
    txt: 'N_RI0033-Camar Marine Terminal-Camar Marine Terminal',
    code: 'N_RI0033'
  },
  {
    txt: 'N_RI0034-Celukan Bawang-Celukan Bawang',
    code: 'N_RI0034'
  },
  {
    txt: 'N_RI0035-Cengkareng-Cengkareng',
    code: 'N_RI0035'
  },
  {
    txt: 'N_RI0036-Cinta-Cinta',
    code: 'N_RI0036'
  },
  {
    txt: 'N_RI0037-契万丹-Ciwandan',
    code: 'N_RI0037'
  },
  {
    txt: 'N_RI0038-Ende-Ende',
    code: 'N_RI0038'
  },
  {
    txt: 'N_RI0039-FPSO Karapan Armada Sterling III-FPSO Karapan Armada Sterling III',
    code: 'N_RI0039'
  },
  {
    txt: 'N_RI0040-FSO Gagak Rimang-FSO Gagak Rimang',
    code: 'N_RI0040'
  },
  {
    txt: 'N_RI0041-FSO Cinta Natomas-FSO Cinta Natomas',
    code: 'N_RI0041'
  },
  {
    txt: 'N_RI0042-FSRU PGN Lampung-FSRU PGN Lampung',
    code: 'N_RI0042'
  },
  {
    txt: 'N_RI0043-FUTONG-FUTONG',
    code: 'N_RI0043'
  },
  {
    txt: 'N_RI0044-Garongkong-Garongkong',
    code: 'N_RI0044'
  },
  {
    txt: 'N_RI0045-Gebe Island-Gebe Island',
    code: 'N_RI0045'
  },
  {
    txt: 'N_RI0046-Gilimanuk-Gilimanuk',
    code: 'N_RI0046'
  },
  {
    txt: 'N_RI0047-Hagu-Hagu',
    code: 'N_RI0047'
  },
  {
    txt: 'N_RI0048-Indramayu-Indramayu',
    code: 'N_RI0048'
  },
  {
    txt: 'N_RI0049-Jabung Batanghari Marine Terminal-Jabung Batanghari Marine Terminal',
    code: 'N_RI0049'
  },
  {
    txt: 'N_RI0050-Jorong-Jorong',
    code: 'N_RI0050'
  },
  {
    txt: 'N_RI0051-卡碧利-Kabil',
    code: 'N_RI0051'
  },
  {
    txt: 'N_RI0052-Kaimana-Kaimana',
    code: 'N_RI0052'
  },
  {
    txt: 'N_RI0053-Kakap Natuna Marine Terminal-Kakap Natuna Marine Terminal',
    code: 'N_RI0053'
  },
  {
    txt: 'N_RI0054-Kaliorang-Kaliorang',
    code: 'N_RI0054'
  },
  {
    txt: 'N_RI0055-Kalbut Situbondo Terminal-Kalbut Situbondo Terminal',
    code: 'N_RI0055'
  },
  {
    txt: 'N_RI0056-卡西姆港-Kasim Marine Terminal',
    code: 'N_RI0056'
  },
  {
    txt: 'N_RI0057-Kempo-Kempo',
    code: 'N_RI0057'
  },
  {
    txt: 'N_RI0058-基姜-Kijang',
    code: 'N_RI0058'
  },
  {
    txt: 'N_RI0059-Kolonodale-Kolonodale',
    code: 'N_RI0059'
  },
  {
    txt: 'N_RI0060-克鲁恩古库-Krueng Geukueh',
    code: 'N_RI0060'
  },
  {
    txt: 'N_RI0061-兰沙-Kuala Langsa',
    code: 'N_RI0061'
  },
  {
    txt: 'N_RI0062-库拉坦姜-Kuala Tanjung',
    code: 'N_RI0062'
  },
  {
    txt: 'N_RI0063-Kuala Beukah-Kuala Beukah',
    code: 'N_RI0063'
  },
  {
    txt: 'N_RI0064-库麦-Kumai',
    code: 'N_RI0064'
  },
  {
    txt: 'N_RI0065-Labuan Amuk Terminal-Labuan Amuk Terminal',
    code: 'N_RI0065'
  },
  {
    txt: 'N_RI0066-Lalang Marine Terminal-Lalang Marine Terminal',
    code: 'N_RI0066'
  },
  {
    txt: 'N_RI0067-Lamongan Shorebase-Lamongan Shorebase',
    code: 'N_RI0067'
  },
  {
    txt: 'N_RI0068-Larantuka-Larantuka',
    code: 'N_RI0068'
  },
  {
    txt: 'N_RI0069-Lawi-Lawi Terminal-Lawi-Lawi Terminal',
    code: 'N_RI0069'
  },
  {
    txt: 'N_RI0070-Lembar-Lembar',
    code: 'N_RI0070'
  },
  {
    txt: 'N_RI0071-鲁克斯马威-Lhokseumawe',
    code: 'N_RI0071'
  },
  {
    txt: 'N_RI0072-鲁克恩格-Lhoknga',
    code: 'N_RI0072'
  },
  {
    txt: 'N_RI0073-Loliodi Oil Terminal-Loliodi Oil Terminal',
    code: 'N_RI0073'
  },
  {
    txt: 'N_RI0074-鲁乌克-Luwuk',
    code: 'N_RI0074'
  },
  {
    txt: 'N_RI0075-Madura-Madura',
    code: 'N_RI0075'
  },
  {
    txt: 'N_RI0076-Majene-Majene',
    code: 'N_RI0076'
  },
  {
    txt: 'N_RI0077-望加锡-Makassar',
    code: 'N_RI0077'
  },
  {
    txt: 'N_RI0078-马拉哈亚提-Malahayati',
    code: 'N_RI0078'
  },
  {
    txt: 'N_RI0079-Manado-Manado',
    code: 'N_RI0079'
  },
  {
    txt: 'N_RI0080-茅米尔-Maumere',
    code: 'N_RI0080'
  },
  {
    txt: 'N_RI0081-Muara Pantai-Muara Pantai',
    code: 'N_RI0081'
  },
  {
    txt: 'N_RI0082-Muara Bangkong-Muara Bangkong',
    code: 'N_RI0082'
  },
  {
    txt: 'N_RI0083-Muara Sabak-Muara Sabak',
    code: 'N_RI0083'
  },
  {
    txt: 'N_RI0084-Namlea-Namlea',
    code: 'N_RI0084'
  },
  {
    txt: 'N_RI0085-努努坎-Nunukan',
    code: 'N_RI0085'
  },
  {
    txt: 'N_RI0086-Oyong Marine Terminal-Oyong Marine Terminal',
    code: 'N_RI0086'
  },
  {
    txt: 'N_RI0087-帕当湾-Padang Bai',
    code: 'N_RI0087'
  },
  {
    txt: 'N_RI0088-Pagerungan Marine Terminal-Pagerungan Marine Terminal',
    code: 'N_RI0088'
  },
  {
    txt: 'N_RI0089-Paiton-Paiton',
    code: 'N_RI0089'
  },
  {
    txt: 'N_RI0090-潘托罗安-Pantoloan',
    code: 'N_RI0090'
  },
  {
    txt: 'N_RI0091-Pare Pare-Pare Pare',
    code: 'N_RI0091'
  },
  {
    txt: 'N_RI0092-Pekanbaru-Pekanbaru',
    code: 'N_RI0092'
  },
  {
    txt: 'N_RI0093-Penuba-Penuba',
    code: 'N_RI0093'
  },
  {
    txt: 'N_RI0094-Plaju & Sungai Gerong-Plaju & Sungai Gerong',
    code: 'N_RI0094'
  },
  {
    txt: 'N_RI0095-Poleng-Poleng',
    code: 'N_RI0095'
  },
  {
    txt: 'N_RI0096-Pulang Pisau-Pulang Pisau',
    code: 'N_RI0096'
  },
  {
    txt: 'N_RI0097-Ramba-Ramba',
    code: 'N_RI0097'
  },
  {
    txt: 'N_RI0098-Rengat-Rengat',
    code: 'N_RI0098'
  },
  {
    txt: 'N_RI0099-reo-reo',
    code: 'N_RI0099'
  },
  {
    txt: 'N_RI00100-Ritabel-Ritabel',
    code: 'N_RI00100'
  },
  {
    txt: 'N_RI00101-桑加塔-Sangatta',
    code: 'N_RI00101'
  },
  {
    txt: 'N_RI00102-Satui-Satui',
    code: 'N_RI00102'
  },
  {
    txt: 'N_RI00103-Saumlaki-Saumlaki',
    code: 'N_RI00103'
  },
  {
    txt: 'N_RI00104-Sebuku Coal Terminal-Sebuku Coal Terminal',
    code: 'N_RI00104'
  },
  {
    txt: 'N_RI00105-塞库庞-Sekupang',
    code: 'N_RI00105'
  },
  {
    txt: 'N_GR0039-卡瓦拉-Kavala',
    code: 'N_GR0039'
  },
  {
    txt: 'N_GR0040-萨莫斯-Kavonisi-Kissamos',
    code: 'N_GR0040'
  },
  {
    txt: 'N_GR0041-kefalos-kefalos',
    code: 'N_GR0041'
  },
  {
    txt: 'N_GR0042-Kos-Kos',
    code: 'N_GR0042'
  },
  {
    txt: 'N_GR0043-Kymassi-Kymassi',
    code: 'N_GR0043'
  },
  {
    txt: 'N_GR0044-拉夫里-Lavrion',
    code: 'N_GR0044'
  },
  {
    txt: 'N_GR0045-Leros Island-Leros Island',
    code: 'N_GR0045'
  },
  {
    txt: 'N_GR0046-limnos-limnos',
    code: 'N_GR0046'
  },
  {
    txt: 'N_GR0047-Marathoupolis-Marathoupolis',
    code: 'N_GR0047'
  },
  {
    txt: 'N_GR0048-MASTICHARI-MASTICHARI',
    code: 'N_GR0048'
  },
  {
    txt: 'N_GR0049-Mesolongi-Mesolongi',
    code: 'N_GR0049'
  },
  {
    txt: 'N_GR0050-米罗斯-Milos Island',
    code: 'N_GR0050'
  },
  {
    txt: 'N_GR0051-米科罗斯-Mykonos',
    code: 'N_GR0051'
  },
  {
    txt: 'N_GR0052-Mytilene-Mytilene',
    code: 'N_GR0052'
  },
  {
    txt: 'N_GR0053-Nauplia-Nauplia',
    code: 'N_GR0053'
  },
  {
    txt: 'N_GR0054-帕什-Pachi',
    code: 'N_GR0054'
  },
  {
    txt: 'N_GR0055-Paroikia-Paroikia',
    code: 'N_GR0055'
  },
  {
    txt: 'N_GR0056-帕特摩斯岛-Patmos',
    code: 'N_GR0056'
  },
  {
    txt: 'N_GR0057-Porto Lagos-Porto Lagos',
    code: 'N_GR0057'
  },
  {
    txt: 'N_GR0058-Poseidonia-Poseidonia',
    code: 'N_GR0058'
  },
  {
    txt: 'N_GR0059-Psakhna-Psakhna',
    code: 'N_GR0059'
  },
  {
    txt: 'N_GR0060-Rafina-Rafina',
    code: 'N_GR0060'
  },
  {
    txt: 'N_GR0061-Revithoussa Terminal-Revithoussa Terminal',
    code: 'N_GR0061'
  },
  {
    txt: 'N_GR0062-罗德斯-Rhodes',
    code: 'N_GR0062'
  },
  {
    txt: 'N_GR0063-Seriphos Island-Seriphos Island',
    code: 'N_GR0063'
  },
  {
    txt: 'N_GR0064-SIFNOS-SIFNOS',
    code: 'N_GR0064'
  },
  {
    txt: 'N_GR0065-skiathos-skiathos',
    code: 'N_GR0065'
  },
  {
    txt: 'N_GR0066-skyros-skyros',
    code: 'N_GR0066'
  },
  {
    txt: 'N_GR0067-Souda Bay-Souda Bay',
    code: 'N_GR0067'
  },
  {
    txt: 'N_GR0068-SOUDA-SOUDA',
    code: 'N_GR0068'
  },
  {
    txt: 'N_GR0069-斯泰利斯-Stylis',
    code: 'N_GR0069'
  },
  {
    txt: 'N_GR0070-Syros Island-Syros Island',
    code: 'N_GR0070'
  },
  {
    txt: 'N_GR0071-Thasos-Thasos',
    code: 'N_GR0071'
  },
  {
    txt: 'N_GR0072-Thira-Thira',
    code: 'N_GR0072'
  },
  {
    txt: 'N_GR0073-Thisvi-Thisvi',
    code: 'N_GR0073'
  },
  {
    txt: 'N_GR0074-提诺斯-Tinos',
    code: 'N_GR0074'
  },
  {
    txt: 'N_GR0075-Vathi-Vathi',
    code: 'N_GR0075'
  },
  {
    txt: 'N_GE001-阿斯阿特-Aasiaat',
    code: 'N_GE001'
  },
  {
    txt: 'N_GE002-Ammassalik-Ammassalik',
    code: 'N_GE002'
  },
  {
    txt: 'N_GE003-伊鲁利萨特-Ilulissat',
    code: 'N_GE003'
  },
  {
    txt: 'N_GE004-Kangilinnguit-Kangilinnguit',
    code: 'N_GE004'
  },
  {
    txt: 'N_GE005-马尼特索克-Maniitsoq',
    code: 'N_GE005'
  },
  {
    txt: 'N_GE006-Nanortalik-Nanortalik',
    code: 'N_GE006'
  },
  {
    txt: 'N_GE007-纳赫沙克-Narsaq',
    code: 'N_GE007'
  },
  {
    txt: 'N_GE008-North Star Bugt-North Star Bugt',
    code: 'N_GE008'
  },
  {
    txt: 'N_GE009-奴克-Nuuk',
    code: 'N_GE009'
  },
  {
    txt: 'N_GE0010-Paamiut-Paamiut',
    code: 'N_GE0010'
  },
  {
    txt: 'N_GE0011-Qaanaaq-Qaanaaq',
    code: 'N_GE0011'
  },
  {
    txt: 'N_GE0012-卡考图克-Qaqortoq',
    code: 'N_GE0012'
  },
  {
    txt: 'N_GE0013-Qasigiannguit-Qasigiannguit',
    code: 'N_GE0013'
  },
  {
    txt: 'N_GE0014-奎克塔索克-Qeqertarsuaq',
    code: 'N_GE0014'
  },
  {
    txt: 'N_GE0015-西西米乌特-Sisimiut',
    code: 'N_GE0015'
  },
  {
    txt: 'N_GE0016-索伦德斯特罗姆福德-Soendre Stroemfjord',
    code: 'N_GE0016'
  },
  {
    txt: 'N_GE0017-尤博纳维克-Upernavik',
    code: 'N_GE0017'
  },
  {
    txt: 'N_GE0018-乌马纳克-Uummannaq',
    code: 'N_GE0018'
  },
  {
    txt: 'N_GRN001-Hillsborough-Hillsborough',
    code: 'N_GRN001'
  },
  {
    txt: "N_GRN002-St George's (Grenada)-St George's (Grenada)",
    code: 'N_GRN002'
  },
  {
    txt: 'N_GRN003-Tyrell Bay-Tyrell Bay',
    code: 'N_GRN003'
  },
  {
    txt: 'N_GD001-Basse-Terre-Basse-Terre',
    code: 'N_GD001'
  },
  {
    txt: 'N_GD002-Folle Anse-Folle Anse',
    code: 'N_GD002'
  },
  {
    txt: 'N_GD003-皮特瑞角-Pointe-a-Pitre',
    code: 'N_GD003'
  },
  {
    txt: 'N_GUA001-Apra Harbor-Apra Harbor',
    code: 'N_GUA001'
  },
  {
    txt: 'N_GCA001-San Jose (Guatemala)-San Jose (Guatemala)',
    code: 'N_GCA001'
  },
  {
    txt: 'N_GUE001-Alderney-Alderney',
    code: 'N_GUE001'
  },
  {
    txt: 'N_GUE002-St Peter Port-St Peter Port',
    code: 'N_GUE002'
  },
  {
    txt: 'N_G001-卡姆萨尔-Port Kamsar',
    code: 'N_G001'
  },
  {
    txt: 'N_GY001-Essequibo River-Essequibo River',
    code: 'N_GY001'
  },
  {
    txt: 'N_GY002-FPSO Liza Destiny-FPSO Liza Destiny',
    code: 'N_GY002'
  },
  {
    txt: 'N_GY003-Georgetown (Guyana)-Georgetown (Guyana)',
    code: 'N_GY003'
  },
  {
    txt: 'N_GY004-Kaituma-Kaituma',
    code: 'N_GY004'
  },
  {
    txt: 'N_GY005-林登-Linden',
    code: 'N_GY005'
  },
  {
    txt: 'N_RH001-海地角-Cap Haitien',
    code: 'N_RH001'
  },
  {
    txt: 'N_RH002-Jacmel-Jacmel',
    code: 'N_RH002'
  },
  {
    txt: 'N_RH003-Labadee-Labadee',
    code: 'N_RH003'
  },
  {
    txt: 'N_RH004-Port au Prince-Port au Prince',
    code: 'N_RH004'
  },
  {
    txt: 'N_RH005-Port Lafito-Port Lafito',
    code: 'N_RH005'
  },
  {
    txt: 'N_RH006-Port-de-Paix-Port-de-Paix',
    code: 'N_RH006'
  },
  {
    txt: 'N_HON001-Coxen Hole-Coxen Hole',
    code: 'N_HON001'
  },
  {
    txt: 'N_HON002-La Ceiba (Honduras)-La Ceiba (Honduras)',
    code: 'N_HON002'
  },
  {
    txt: 'N_HON003-卡斯提利亚港-Puerto Castilla',
    code: 'N_HON003'
  },
  {
    txt: 'N_HON004-roatan-roatan',
    code: 'N_HON004'
  },
  {
    txt: 'N_HON005-San Lorenzo (Honduras)-San Lorenzo (Honduras)',
    code: 'N_HON005'
  },
  {
    txt: 'N_IS001-BILDUDALUR-BILDUDALUR',
    code: 'N_IS001'
  },
  {
    txt: 'N_IS002-Dalvik-Dalvik',
    code: 'N_IS002'
  },
  {
    txt: 'N_IS003-杜皮沃格-Djupivogur',
    code: 'N_IS003'
  },
  {
    txt: 'N_OM005-Qalhat LNG Terminal-Qalhat LNG Terminal',
    code: 'N_OM005'
  },
  {
    txt: 'N_OM006-Shanna-Shanna',
    code: 'N_OM006'
  },
  {
    txt: 'N_OM007-Sultan Qaboos Port-Sultan Qaboos Port',
    code: 'N_OM007'
  },
  {
    txt: 'N_PAK001-Gadani-Gadani',
    code: 'N_PAK001'
  },
  {
    txt: 'N_PAK002-Muhammad Bin Qasim-Muhammad Bin Qasim',
    code: 'N_PAK002'
  },
  {
    txt: 'N_PLW001-Malakal Harbour-Malakal Harbour',
    code: 'N_PLW001'
  },
  {
    txt: 'N_PA001-阿瓜杜尔塞-Aguadulce',
    code: 'N_PA001'
  },
  {
    txt: 'N_PA002-阿尔米兰特-Almirante',
    code: 'N_PA002'
  },
  {
    txt: 'N_PA003-拉斯米纳斯港-Bahia Las Minas',
    code: 'N_PA003'
  },
  {
    txt: 'N_PA004-Bocas del Toro-Bocas del Toro',
    code: 'N_PA004'
  },
  {
    txt: 'N_PA005-Charco Azul Terminal-Charco Azul Terminal',
    code: 'N_PA005'
  },
  {
    txt: 'N_PA006-Chiriqui Grande Terminal-Chiriqui Grande Terminal',
    code: 'N_PA006'
  },
  {
    txt: 'N_PA007-Fort Amador-Fort Amador',
    code: 'N_PA007'
  },
  {
    txt: 'N_PA008-Melones Oil Terminal-Melones Oil Terminal',
    code: 'N_PA008'
  },
  {
    txt: 'N_PA009-Mutis-Mutis',
    code: 'N_PA009'
  },
  {
    txt: 'N_PA0010-Panama Canal-Panama Canal',
    code: 'N_PA0010'
  },
  {
    txt: 'N_PA0011-阿木韦列斯港-Puerto Armuelles',
    code: 'N_PA0011'
  },
  {
    txt: 'N_PA0012-Taboga Island-Taboga Island',
    code: 'N_PA0012'
  },
  {
    txt: 'N_PA0013-Taboguilla Island Terminal-Taboguilla Island Terminal',
    code: 'N_PA0013'
  },
  {
    txt: 'N_PNG001-Basamuk-Basamuk',
    code: 'N_PNG001'
  },
  {
    txt: 'N_PNG002-Bialla-Bialla',
    code: 'N_PNG002'
  },
  {
    txt: 'N_PNG003-库姆尔-Kumul Marine Terminal',
    code: 'N_PNG003'
  },
  {
    txt: 'N_PNG004-路易丝港-Luise Harbour',
    code: 'N_PNG004'
  },
  {
    txt: 'N_PNG005-Napa Napa-Napa Napa',
    code: 'N_PNG005'
  },
  {
    txt: 'N_PNG006-PNG LNG Terminal-PNG LNG Terminal',
    code: 'N_PNG006'
  },
  {
    txt: 'N_PNG007-瓦尼莫-Vanimo',
    code: 'N_PNG007'
  },
  {
    txt: 'N_PE001-Bayovar-Bayovar',
    code: 'N_PE001'
  },
  {
    txt: 'N_PE002-Coishco-Coishco',
    code: 'N_PE002'
  },
  {
    txt: 'N_PE003-康昌-Conchan',
    code: 'N_PE003'
  },
  {
    txt: 'N_PE004-拉潘皮利亚-La Pampilla',
    code: 'N_PE004'
  },
  {
    txt: 'N_PE005-Pampa Melchorita-Pampa Melchorita',
    code: 'N_PE005'
  },
  {
    txt: 'N_PE006-奇卡马港-Puerto Chicama',
    code: 'N_PE006'
  },
  {
    txt: 'N_PE007-Punta Lobitos-Punta Lobitos',
    code: 'N_PE007'
  },
  {
    txt: 'N_PE008-萨曼库-Samanco',
    code: 'N_PE008'
  },
  {
    txt: 'N_PE009-San Nicolas (Peru)-San Nicolas (Peru)',
    code: 'N_PE009'
  },
  {
    txt: 'N_PI001-Alcoy-Alcoy',
    code: 'N_PI001'
  },
  {
    txt: 'N_PI002-Atimonan-Atimonan',
    code: 'N_PI002'
  },
  {
    txt: 'N_PI003-Basco-Basco',
    code: 'N_PI003'
  },
  {
    txt: 'N_PI004-Berong-Berong',
    code: 'N_PI004'
  },
  {
    txt: 'N_PI005-卡加廷德奥罗-Cagayan de Oro',
    code: 'N_PI005'
  },
  {
    txt: 'N_PI006-Calaca-Calaca',
    code: 'N_PI006'
  },
  {
    txt: 'N_PI007-Calapan-Calapan',
    code: 'N_PI007'
  },
  {
    txt: 'N_PI008-库里茅-Currimao',
    code: 'N_PI008'
  },
  {
    txt: 'N_PI009-Dingalan-Dingalan',
    code: 'N_PI009'
  },
  {
    txt: 'N_PI0010-杜马格特-Dumaguete',
    code: 'N_PI0010'
  },
  {
    txt: 'N_PI0011-Dumangas-Dumangas',
    code: 'N_PI0011'
  },
  {
    txt: 'N_PI0012-FPSO Rubicon Intrepid-FPSO Rubicon Intrepid',
    code: 'N_PI0012'
  },
  {
    txt: 'N_PI0013-金古格-Gingoog',
    code: 'N_PI0013'
  },
  {
    txt: 'N_PI0014-Hijo-Hijo',
    code: 'N_PI0014'
  },
  {
    txt: 'N_PI0015-洪达瓜-Hondagua',
    code: 'N_PI0015'
  },
  {
    txt: 'N_PI0016-怡朗（伊洛伊洛）-Iloilo',
    code: 'N_PI0016'
  },
  {
    txt: 'N_PI0017-Jimenez-Jimenez',
    code: 'N_PI0017'
  },
  {
    txt: 'N_PI0018-Jordan-Jordan',
    code: 'N_PI0018'
  },
  {
    txt: 'N_PI0019-Lamitan-Lamitan',
    code: 'N_PI0019'
  },
  {
    txt: 'N_PI0020-黎牙实比-Legazpi',
    code: 'N_PI0020'
  },
  {
    txt: 'N_PI0021-Limay Terminals-Limay Terminals',
    code: 'N_PI0021'
  },
  {
    txt: 'N_PI0022-Maasim-Maasim',
    code: 'N_PI0022'
  },
  {
    txt: 'N_PI0023-Malita-Malita',
    code: 'N_PI0023'
  },
  {
    txt: 'N_PI0024-Malampaya CALM Buoy-Malampaya CALM Buoy',
    code: 'N_PI0024'
  },
  {
    txt: 'N_PI0025-马辛洛克-Masinloc',
    code: 'N_PI0025'
  },
  {
    txt: 'N_PI0026-Nabilid-Nabilid',
    code: 'N_PI0026'
  },
  {
    txt: 'N_PI0027-Nasipit Port-Nasipit Port',
    code: 'N_PI0027'
  },
  {
    txt: 'N_PI0028-Ozamiz-Ozamiz',
    code: 'N_PI0028'
  },
  {
    txt: 'N_PI0029-Pagbilao-Pagbilao',
    code: 'N_PI0029'
  },
  {
    txt: 'N_PI0030-Pagadian-Pagadian',
    code: 'N_PI0030'
  },
  {
    txt: 'N_PI0031-Polambato Port-Polambato Port',
    code: 'N_PI0031'
  },
  {
    txt: 'N_PI0032-伊伦港-Port Irene',
    code: 'N_PI0032'
  },
  {
    txt: 'N_PI0033-Quezon Power Station-Quezon Power Station',
    code: 'N_PI0033'
  },
  {
    txt: 'N_PI0034-图巴-Rio Tuba',
    code: 'N_PI0034'
  },
  {
    txt: 'N_PI0035-Santa Cruz (Philippines - 1)-Santa Cruz (Philippines - 1)',
    code: 'N_PI0035'
  },
  {
    txt: 'N_PI0036-Santa Cruz (Philippines - 2)-Santa Cruz (Philippines - 2)',
    code: 'N_PI0036'
  },
  {
    txt: 'N_PI0037-San Carlos (Philippines)-San Carlos (Philippines)',
    code: 'N_PI0037'
  },
  {
    txt: 'N_PI0038-Santa Maria (Phillipines)-Santa Maria (Phillipines)',
    code: 'N_PI0038'
  },
  {
    txt: 'N_PI0039-Sariaya-Sariaya',
    code: 'N_PI0039'
  },
  {
    txt: 'N_PI0040-塞米拉拉群岛-Semirara',
    code: 'N_PI0040'
  },
  {
    txt: 'N_PI0041-Sual Port-Sual Port',
    code: 'N_PI0041'
  },
  {
    txt: 'N_PI0042-Suba-Nipa-Suba-Nipa',
    code: 'N_PI0042'
  },
  {
    txt: 'N_PI0043-塔诺安-Tanauan',
    code: 'N_PI0043'
  },
  {
    txt: 'N_PI0044-Tubay-Tubay',
    code: 'N_PI0044'
  },
  {
    txt: 'N_PI0045-Virac-Virac',
    code: 'N_PI0045'
  },
  {
    txt: 'N_PL001-波利采-Police',
    code: 'N_PL001'
  },
  {
    txt: 'N_P001-Douro-Douro',
    code: 'N_P001'
  },
  {
    txt: 'N_P002-Faro & Olhao-Faro & Olhao',
    code: 'N_P002'
  },
  {
    txt: 'N_P003-菲格腊达福-Figueira da Foz',
    code: 'N_P003'
  },
  {
    txt: 'N_P004-lajes das flores-lajes das flores',
    code: 'N_P004'
  },
  {
    txt: 'N_P005-Porto da Povoa de Varzim-Porto da Povoa de Varzim',
    code: 'N_P005'
  },
  {
    txt: 'N_P006-vila do porto-vila do porto',
    code: 'N_P006'
  },
  {
    txt: 'N_PR001-Aguirre-Aguirre',
    code: 'N_PR001'
  },
  {
    txt: 'N_PR002-Fajardo-Fajardo',
    code: 'N_PR002'
  },
  {
    txt: 'N_PR003-拉斯马雷亚斯-Las Mareas',
    code: 'N_PR003'
  },
  {
    txt: 'N_Q001-Al Ruwais-Al Ruwais',
    code: 'N_Q001'
  },
  {
    txt: 'N_Q002-Al Shaheen Terminal-Al Shaheen Terminal',
    code: 'N_Q002'
  },
  {
    txt: 'N_Q003-Al Rayyan Marine Terminal-Al Rayyan Marine Terminal',
    code: 'N_Q003'
  },
  {
    txt: 'N_Q004-Doha (Qatar)-Doha (Qatar)',
    code: 'N_Q004'
  },
  {
    txt: 'N_Q005-Hamad Port-Hamad Port',
    code: 'N_Q005'
  },
  {
    txt: 'N_Q006-梅赛伊德-Mesaieed',
    code: 'N_Q006'
  },
  {
    txt: 'N_Q007-拉斯拉凡-Ras Laffan',
    code: 'N_Q007'
  },
  {
    txt: 'N_REU001-Port Reunion-Port Reunion',
    code: 'N_REU001'
  },
  {
    txt: 'N_R001-Basarabi-Basarabi',
    code: 'N_R001'
  },
  {
    txt: 'N_R002-Calarasi-Calarasi',
    code: 'N_R002'
  },
  {
    txt: 'N_R003-Calafat-Calafat',
    code: 'N_R003'
  },
  {
    txt: 'N_R004-Cernavoda-Cernavoda',
    code: 'N_R004'
  },
  {
    txt: 'N_R005-Danube - Black Sea Canal-Danube - Black Sea Canal',
    code: 'N_R005'
  },
  {
    txt: 'N_R006-Danube River-Danube River',
    code: 'N_R006'
  },
  {
    txt: 'N_R007-Drencova-Drencova',
    code: 'N_R007'
  },
  {
    txt: 'N_R008-Drobeta Turnu Severin-Drobeta Turnu Severin',
    code: 'N_R008'
  },
  {
    txt: 'N_R009-Galati-Galati',
    code: 'N_R009'
  },
  {
    txt: 'N_R0010-Giurgiu-Giurgiu',
    code: 'N_R0010'
  },
  {
    txt: 'N_R0011-Harsova-Harsova',
    code: 'N_R0011'
  },
  {
    txt: 'N_R0012-Isaccea-Isaccea',
    code: 'N_R0012'
  },
  {
    txt: 'N_R0013-Macin-Macin',
    code: 'N_R0013'
  },
  {
    txt: 'N_R0014-Mahmudia-Mahmudia',
    code: 'N_R0014'
  },
  {
    txt: 'N_R0015-麦德基迪亚-Medgidia',
    code: 'N_R0015'
  },
  {
    txt: 'N_R0016-米迪耶-Midia',
    code: 'N_R0016'
  },
  {
    txt: 'N_R0017-Moldova Noua-Moldova Noua',
    code: 'N_R0017'
  },
  {
    txt: 'N_R0018-Oltenita-Oltenita',
    code: 'N_R0018'
  },
  {
    txt: 'N_R0019-Orsova-Orsova',
    code: 'N_R0019'
  },
  {
    txt: 'N_R0020-Turcoaia-Turcoaia',
    code: 'N_R0020'
  },
  {
    txt: 'N_R0021-Turnu Magurele-Turnu Magurele',
    code: 'N_R0021'
  },
  {
    txt: 'N_R0022-Zimnicea-Zimnicea',
    code: 'N_R0022'
  },
  {
    txt: 'N_RU001-Aleksandrovsk Sakhalinsky-Aleksandrovsk Sakhalinsky',
    code: 'N_RU001'
  },
  {
    txt: 'N_RU002-Amderma-Amderma',
    code: 'N_RU002'
  },
  {
    txt: 'N_RU003-阿纳德尔-Anadyr',
    code: 'N_RU003'
  },
  {
    txt: 'N_RU004-Anapa-Anapa',
    code: 'N_RU004'
  },
  {
    txt: 'N_RU005-阿斯特拉罕-Astrakhan',
    code: 'N_RU005'
  },
  {
    txt: 'N_RU006-Azov-Azov',
    code: 'N_RU006'
  },
  {
    txt: 'N_RU007-Belomorsk-Belomorsk',
    code: 'N_RU007'
  },
  {
    txt: 'N_RU008-Bering Straits (Russia)-Bering Straits (Russia)',
    code: 'N_RU008'
  },
  {
    txt: 'N_RU009-Beringovsky-Beringovsky',
    code: 'N_RU009'
  },
  {
    txt: 'N_RU0010-博什尼亚科沃-Boshnyakovo',
    code: 'N_RU0010'
  },
  {
    txt: 'N_RU0011-Buzan-Buzan',
    code: 'N_RU0011'
  },
  {
    txt: 'N_RU0012-德卡斯特瑞-De Kastri',
    code: 'N_RU0012'
  },
  {
    txt: 'N_RU0013-Dikson-Dikson',
    code: 'N_RU0013'
  },
  {
    txt: 'N_RU0014-杜丁卡-Dudinka',
    code: 'N_RU0014'
  },
  {
    txt: 'N_RU0015-伊格威基诺特-Egvekinot',
    code: 'N_RU0015'
  },
  {
    txt: 'N_RU0016-FSO Yuri Korchagin-FSO Yuri Korchagin',
    code: 'N_RU0016'
  },
  {
    txt: 'N_RU0017-Gelendzhik-Gelendzhik',
    code: 'N_RU0017'
  },
  {
    txt: 'N_RU0018-伊加尔卡-Igarka',
    code: 'N_RU0018'
  },
  {
    txt: 'N_RU0019-坎达拉克沙-Kandalaksha',
    code: 'N_RU0019'
  },
  {
    txt: 'N_RU0020-Kavkaz-Kavkaz',
    code: 'N_RU0020'
  },
  {
    txt: 'N_RU0021-Komsomolsk-on-Amur-Komsomolsk-on-Amur',
    code: 'N_RU0021'
  },
  {
    txt: 'N_RU0022-Korf-Korf',
    code: 'N_RU0022'
  },
  {
    txt: 'N_RU0023-Kozmino-Kozmino',
    code: 'N_RU0023'
  },
  {
    txt: 'N_RU0024-Krasnogorskiy-Krasnogorskiy',
    code: 'N_RU0024'
  },
  {
    txt: 'N_RU0025-喀朗施塔德-Kronshtadt',
    code: 'N_RU0025'
  },
  {
    txt: 'N_RU0026-Liinakhamari-Liinakhamari',
    code: 'N_RU0026'
  },
  {
    txt: 'N_RU0027-Lomonosov-Lomonosov',
    code: 'N_RU0027'
  },
  {
    txt: 'N_RU0028-马哈奇卡拉-Makhachkala',
    code: 'N_RU0028'
  },
  {
    txt: 'N_RU0029-Moskalvo-Moskalvo',
    code: 'N_RU0029'
  },
  {
    txt: 'N_RU0030-纳里扬马尔-Naryan-Mar',
    code: 'N_RU0030'
  },
  {
    txt: 'N_RU0031-Nikolaevsk on Amur-Nikolaevsk on Amur',
    code: 'N_RU0031'
  },
  {
    txt: 'N_RU0032-新罗西斯克（诺沃罗西斯克）-Novorossiysk',
    code: 'N_RU0032'
  },
  {
    txt: 'N_RU0033-Novyy Port-Novyy Port',
    code: 'N_RU0033'
  },
  {
    txt: 'N_RU0034-Olga-Olga',
    code: 'N_RU0034'
  },
  {
    txt: 'N_RU0035-奥利亚-Olya',
    code: 'N_RU0035'
  },
  {
    txt: 'N_RU0036-Petropavlovsk-Kamchatsky-Petropavlovsk-Kamchatsky',
    code: 'N_RU0036'
  },
  {
    txt: 'N_RU0037-柏维克-Pevek',
    code: 'N_RU0037'
  },
  {
    txt: 'N_RU0038-Plastun-Plastun',
    code: 'N_RU0038'
  },
  {
    txt: 'N_RU0039-波罗纳斯克-Poronaysk',
    code: 'N_RU0039'
  },
  {
    txt: 'N_RU0040-Port Bronka-Port Bronka',
    code: 'N_RU0040'
  },
  {
    txt: 'N_RU0041-波塞特-Posyet',
    code: 'N_RU0041'
  },
  {
    txt: 'N_RU0042-普里戈罗德诺耶-Prigorodnoye',
    code: 'N_RU0042'
  },
  {
    txt: 'N_RU0043-Providenija-Providenija',
    code: 'N_RU0043'
  },
  {
    txt: 'N_RU0044-Rostov-on-Don-Rostov-on-Don',
    code: 'N_RU0044'
  },
  {
    txt: 'N_RU0045-Sabetta-Sabetta',
    code: 'N_RU0045'
  },
  {
    txt: 'N_RU0046-severodvinsk-severodvinsk',
    code: 'N_RU0046'
  },
  {
    txt: 'N_RU0047-斯拉维亚卡-Slavyanka',
    code: 'N_RU0047'
  },
  {
    txt: 'N_RU0048-索维斯卡亚格万-Sovetskaya Gavan',
    code: 'N_RU0048'
  },
  {
    txt: 'N_RU0049-St Petersburg-St Petersburg',
    code: 'N_RU0049'
  },
  {
    txt: 'N_RU0050-Sukhodol-Sukhodol',
    code: 'N_RU0050'
  },
  {
    txt: 'N_RU0051-Svetlaya Reka-Svetlaya Reka',
    code: 'N_RU0051'
  },
  {
    txt: 'N_RU0052-塔甘罗格-Taganrog',
    code: 'N_RU0052'
  },
  {
    txt: 'N_RU0053-泰姆雷克-Temryuk',
    code: 'N_RU0053'
  },
  {
    txt: 'N_RU0054-提克西-Tiksi',
    code: 'N_RU0054'
  },
  {
    txt: 'N_RU0055-乌斯特鲁加-Ust-Luga',
    code: 'N_RU0055'
  },
  {
    txt: 'N_RU0056-Ust Kamchatsk-Ust Kamchatsk',
    code: 'N_RU0056'
  },
  {
    txt: 'N_RU0057-瓦兰杰伊-Varandey',
    code: 'N_RU0057'
  },
  {
    txt: 'N_RU0058-Vitino-Vitino',
    code: 'N_RU0058'
  },
  {
    txt: 'N_RU0059-Vityaz Terminal-Vityaz Terminal',
    code: 'N_RU0059'
  },
  {
    txt: 'N_RU0060-Vorota Arktiki Oil Terminal-Vorota Arktiki Oil Terminal',
    code: 'N_RU0060'
  },
  {
    txt: 'N_RU0061-东方港-Vostochny',
    code: 'N_RU0061'
  },
  {
    txt: 'N_RU0062-Yaroslavl-Yaroslavl',
    code: 'N_RU0062'
  },
  {
    txt: 'N_RU0063-耶斯克-Yeysk',
    code: 'N_RU0063'
  },
  {
    txt: 'N_RU0064-Zhelezny Rog Port-Zhelezny Rog Port',
    code: 'N_RU0064'
  },
  {
    txt: 'N_SAB001-Fort Bay-Fort Bay',
    code: 'N_SAB001'
  },
  {
    txt: 'N_SA001-Ana Chaves-Ana Chaves',
    code: 'N_SA001'
  },
  {
    txt: 'N_SA002-Santo Antonio-Santo Antonio',
    code: 'N_SA002'
  },
  {
    txt: 'N_IBN001-杜巴-Dhuba',
    code: 'N_IBN001'
  },
  {
    txt: 'N_IBN002-Duba Bulk Plant Terminal-Duba Bulk Plant Terminal',
    code: 'N_IBN002'
  },
  {
    txt: 'N_IBN003-Jizan-Jizan',
    code: 'N_IBN003'
  },
  {
    txt: 'N_IBN004-King Fahd Industrial Port (Yanbu)-King Fahd Industrial Port (Yanbu)',
    code: 'N_IBN004'
  },
  {
    txt: 'N_IBN005-阿卜杜拉国王港-King Abdullah Port',
    code: 'N_IBN005'
  },
  {
    txt: 'N_IBN006-Port Tanaqib-Port Tanaqib',
    code: 'N_IBN006'
  },
  {
    txt: 'N_IBN007-Qadimah-Qadimah',
    code: 'N_IBN007'
  },
  {
    txt: 'N_IBN008-拉比格-Rabigh',
    code: 'N_IBN008'
  },
  {
    txt: 'N_IBN009-米斯哈布-Ras al Mishab',
    code: 'N_IBN009'
  },
  {
    txt: 'N_IBN0010-Ras al Ghar-Ras al Ghar',
    code: 'N_IBN0010'
  },
  {
    txt: 'N_IBN0011-海尔哈纳-Ras Al Khair',
    code: 'N_IBN0011'
  },
  {
    txt: 'N_IBN0012-延布-Yanbu Commercial Port',
    code: 'N_IBN0012'
  },
  {
    txt: 'N_SN001-Lyndiane-Lyndiane',
    code: 'N_SN001'
  },
  {
    txt: "N_SN002-M'Bao Terminal-M'Bao Terminal",
    code: 'N_SN002'
  },
  {
    txt: 'N_SN003-Ziguinchor-Ziguinchor',
    code: 'N_SN003'
  },
  {
    txt: 'N_SL001-Nitti-Nitti',
    code: 'N_SL001'
  },
  {
    txt: 'N_SI001-Aola Bay-Aola Bay',
    code: 'N_SI001'
  },
  {
    txt: 'N_SI002-Malloco Bay-Malloco Bay',
    code: 'N_SI002'
  },
  {
    txt: 'N_SI003-Pakera Point-Pakera Point',
    code: 'N_SI003'
  },
  {
    txt: 'N_SI004-Shortland Harbour-Shortland Harbour',
    code: 'N_SI004'
  },
  {
    txt: 'N_SI005-Viru Harbour-Viru Harbour',
    code: 'N_SI005'
  },
  {
    txt: 'N_SOM001-Bossaso-Bossaso',
    code: 'N_SOM001'
  },
  {
    txt: 'N_ZA001-东伦敦-East London',
    code: 'N_ZA001'
  },
  {
    txt: 'N_ZA002-Ngqura-Ngqura',
    code: 'N_ZA002'
  },
  {
    txt: 'N_E001-阿坎纳-Alcanar',
    code: 'N_E001'
  },
  {
    txt: 'N_E002-贝尔梅奥-Bermeo',
    code: 'N_E002'
  },
  {
    txt: 'N_E003-Brens-Brens',
    code: 'N_E003'
  },
  {
    txt: 'N_E004-Cadiz-Cadiz',
    code: 'N_E004'
  },
  {
    txt: 'N_E005-Cartagena (Spain)-Cartagena (Spain)',
    code: 'N_E005'
  },
  {
    txt: 'N_E006-卡里尼奥-Carino',
    code: 'N_E006'
  },
  {
    txt: 'N_E007-Ciutadella-Ciutadella',
    code: 'N_E007'
  },
  {
    txt: 'N_E008-La Coruna-La Coruna',
    code: 'N_E008'
  },
  {
    txt: 'N_E009-Marin (Spain)-Marin (Spain)',
    code: 'N_E009'
  },
  {
    txt: 'N_E0010-梅利利亚-Melilla',
    code: 'N_E0010'
  },
  {
    txt: 'N_E0011-Ondarroa-Ondarroa',
    code: 'N_E0011'
  },
  {
    txt: 'N_E0012-Pobra de Caraminal-Pobra de Caraminal',
    code: 'N_E0012'
  },
  {
    txt: 'N_E0013-Puerto de Santa Pola-Puerto de Santa Pola',
    code: 'N_E0013'
  },
  {
    txt: 'N_E0014-Puerto de Camarinas-Puerto de Camarinas',
    code: 'N_E0014'
  },
  {
    txt: 'N_E0015-Puerto de la Savina-Puerto de la Savina',
    code: 'N_E0015'
  },
  {
    txt: 'N_E0016-Requejada-Requejada',
    code: 'N_E0016'
  },
  {
    txt: 'N_E0017-Riveira-Riveira',
    code: 'N_E0017'
  },
  {
    txt: 'N_E0018-San Esteban de Pravia-San Esteban de Pravia',
    code: 'N_E0018'
  },
  {
    txt: 'N_E0019-San Carlos de la Rapita-San Carlos de la Rapita',
    code: 'N_E0019'
  },
  {
    txt: 'N_E0020-Sant Antoni de Portmany-Sant Antoni de Portmany',
    code: 'N_E0020'
  },
  {
    txt: 'N_E0021-Vallcarca-Vallcarca',
    code: 'N_E0021'
  },
  {
    txt: 'N_E0022-Vilagarcia de Arousa-Vilagarcia de Arousa',
    code: 'N_E0022'
  },
  {
    txt: 'N_E0023-维兰诺瓦-Vilanova i la Geltru',
    code: 'N_E0023'
  },
  {
    txt: 'N_E0024-Vinaros-Vinaros',
    code: 'N_E0024'
  },
  {
    txt: 'N_CL001-汉班托塔-Hambantota',
    code: 'N_CL001'
  },
  {
    txt: 'N_CL002-Norochcholai Peer-Norochcholai Peer',
    code: 'N_CL002'
  },
  {
    txt: 'N_CL003-Oluvil-Oluvil',
    code: 'N_CL003'
  },
  {
    txt: 'N_SEU001-Oranjestad (Sint Eustatius)-Oranjestad (Sint Eustatius)',
    code: 'N_SEU001'
  },
  {
    txt: 'N_ST001-Georgetown (Ascension)-Georgetown (Ascension)',
    code: 'N_ST001'
  },
  {
    txt: 'N_SKN001-Long Point Nevis-Long Point Nevis',
    code: 'N_SKN001'
  },
  {
    txt: 'N_SLU001-Castries-Castries',
    code: 'N_SLU001'
  },
  {
    txt: 'N_SLU002-Cul-de-Sac-Cul-de-Sac',
    code: 'N_SLU002'
  },
  {
    txt: 'N_SPM001-Miquelon-Miquelon',
    code: 'N_SPM001'
  },
  {
    txt: 'N_SPM002-St Pierre-St Pierre',
    code: 'N_SPM002'
  },
  {
    txt: 'N_SBY001-Gustavia-Gustavia',
    code: 'N_SBY001'
  },
  {
    txt: 'N_SMT001-Galisbay-Galisbay',
    code: 'N_SMT001'
  },
  {
    txt: 'N_SUD001-Bashayer 1 Oil Terminal-Bashayer 1 Oil Terminal',
    code: 'N_SUD001'
  },
  {
    txt: 'N_SUD002-Bashayer 2 Oil Terminal-Bashayer 2 Oil Terminal',
    code: 'N_SUD002'
  },
  {
    txt: 'N_SUD003-苏丹港-Port Sudan',
    code: 'N_SUD003'
  },
  {
    txt: 'N_SR001-Nieuw Nickerie-Nieuw Nickerie',
    code: 'N_SR001'
  },
  {
    txt: 'N_S001-Amal-Amal',
    code: 'N_S001'
  },
  {
    txt: 'N_S002-Backviken-Backviken',
    code: 'N_S002'
  },
  {
    txt: 'N_S003-巴尔斯塔-Balsta',
    code: 'N_S003'
  },
  {
    txt: 'N_S004-Barsebacksvaerket-Barsebacksvaerket',
    code: 'N_S004'
  },
  {
    txt: 'N_S005-Bohus-Bohus',
    code: 'N_S005'
  },
  {
    txt: 'N_S006-Donso-Donso',
    code: 'N_S006'
  },
  {
    txt: 'N_S007-Elleholm-Elleholm',
    code: 'N_S007'
  },
  {
    txt: 'N_S008-fiskeback-fiskeback',
    code: 'N_S008'
  },
  {
    txt: 'N_S009-Flivik-Flivik',
    code: 'N_S009'
  },
  {
    txt: 'N_S0010-格夫勒-Gavle',
    code: 'N_S0010'
  },
  {
    txt: 'N_S0011-格瑞斯利海姆-Grisslehamn',
    code: 'N_S0011'
  },
  {
    txt: 'N_S0012-Grums-Grums',
    code: 'N_S0012'
  },
  {
    txt: 'N_S0013-hallekis-hallekis',
    code: 'N_S0013'
  },
  {
    txt: 'N_S0014-海讷桑德-Harnosand',
    code: 'N_S0014'
  },
  {
    txt: 'N_S0015-Jatterson-Jatterson',
    code: 'N_S0015'
  },
  {
    txt: 'N_S0016-Kapellskar-Kapellskar',
    code: 'N_S0016'
  },
  {
    txt: 'N_S0017-斯莫根-Kungshamn',
    code: 'N_S0017'
  },
  {
    txt: 'N_S0018-Lodose-Lodose',
    code: 'N_S0018'
  },
  {
    txt: 'N_S0019-Munksund-Munksund',
    code: 'N_S0019'
  },
  {
    txt: 'N_S0020-Nol-Nol',
    code: 'N_S0020'
  },
  {
    txt: 'N_S0021-Norrtalje-Norrtalje',
    code: 'N_S0021'
  },
  {
    txt: 'N_S0022-勒讷比-Ronneby',
    code: 'N_S0022'
  },
  {
    txt: 'N_S0023-伦德维克-Rundvik',
    code: 'N_S0023'
  },
  {
    txt: 'N_S0024-Sodertalje & Sodertalje Canal-Sodertalje & Sodertalje Canal',
    code: 'N_S0024'
  },
  {
    txt: 'N_S0025-索尔维斯堡-Solvesborg',
    code: 'N_S0025'
  },
  {
    txt: 'N_S0026-大维卡-Stora Vika',
    code: 'N_S0026'
  },
  {
    txt: 'N_S0027-Stroemstad-Stroemstad',
    code: 'N_S0027'
  },
  {
    txt: 'N_S0028-Surte-Surte',
    code: 'N_S0028'
  },
  {
    txt: 'N_S0029-Trollhatte Canal-Trollhatte Canal',
    code: 'N_S0029'
  },
  {
    txt: 'N_S0030-瓦尔维克-Vallvik',
    code: 'N_S0030'
  },
  {
    txt: 'N_S0031-维纳斯堡-Vanersborg',
    code: 'N_S0031'
  },
  {
    txt: 'N_S0032-Varobacka-Varobacka',
    code: 'N_S0032'
  },
  {
    txt: 'N_S0033-佛斯特维克-Vastervik',
    code: 'N_S0033'
  },
  {
    txt: 'N_S0034-维斯特罗斯-Vasteras',
    code: 'N_S0034'
  },
  {
    txt: 'N_CH001-巴斯尔-Basle',
    code: 'N_CH001'
  },
  {
    txt: 'N_SYR001-Banias Terminal-Banias Terminal',
    code: 'N_SYR001'
  },
  {
    txt: 'N_SYR002-Borj Islam-Borj Islam',
    code: 'N_SYR002'
  },
  {
    txt: 'N_SYR003-Latakia-Latakia',
    code: 'N_SYR003'
  },
  {
    txt: 'N_ZZ001-Chake Chake-Chake Chake',
    code: 'N_ZZ001'
  },
  {
    txt: 'N_ZZ002-Dar es Salaam-Dar es Salaam',
    code: 'N_ZZ002'
  },
  {
    txt: 'N_ZZ003-姆夸尼-Mkoani',
    code: 'N_ZZ003'
  },
  {
    txt: 'N_ZZ004-Wete Wete-Wete Wete',
    code: 'N_ZZ004'
  },
  {
    txt: 'N_ZZ005-Zanzibar City-Zanzibar City',
    code: 'N_ZZ005'
  },
  {
    txt: 'N_T001-邦巴功-Bangpakong',
    code: 'N_T001'
  },
  {
    txt: 'N_T002-Benchamas Terminal-Benchamas Terminal',
    code: 'N_T002'
  },
  {
    txt: 'N_T003-Bongkot Terminal-Bongkot Terminal',
    code: 'N_T003'
  },
  {
    txt: 'N_T004-Chanthaburi-Chanthaburi',
    code: 'N_T004'
  },
  {
    txt: 'N_T005-Donsak-Donsak',
    code: 'N_T005'
  },
  {
    txt: 'N_T006-Erawan Terminal-Erawan Terminal',
    code: 'N_T006'
  },
  {
    txt: 'N_T007-FPSO FPF-003-FPSO FPF-003',
    code: 'N_T007'
  },
  {
    txt: 'N_T008-FSO Ratu Songkhla-FSO Ratu Songkhla',
    code: 'N_T008'
  },
  {
    txt: 'N_T009-FSO Aurora-FSO Aurora',
    code: 'N_T009'
  },
  {
    txt: 'N_T0010-FSO Suksan Salamander-FSO Suksan Salamander',
    code: 'N_T0010'
  },
  {
    txt: 'N_T0011-汉农-Khanom',
    code: 'N_T0011'
  },
  {
    txt: 'N_T0012-Koh Si Chang Transhipment Area-Koh Si Chang Transhipment Area',
    code: 'N_T0012'
  },
  {
    txt: 'N_T0013-Koh Si Chang TPP-Koh Si Chang TPP',
    code: 'N_T0013'
  },
  {
    txt: 'N_T0014-克拉彼-Krabi',
    code: 'N_T0014'
  },
  {
    txt: 'N_T0015-Maeklong-Maeklong',
    code: 'N_T0015'
  },
  {
    txt: 'N_T0016-麦普塔普特-Map Ta Phut',
    code: 'N_T0016'
  },
  {
    txt: 'N_T0017-Petchburi Terminal-Petchburi Terminal',
    code: 'N_T0017'
  },
  {
    txt: 'N_T0018-Prachuap Port-Prachuap Port',
    code: 'N_T0018'
  },
  {
    txt: 'N_T0019-Ranong-Ranong',
    code: 'N_T0019'
  },
  {
    txt: 'N_T0020-Rayong IRPC Port-Rayong IRPC Port',
    code: 'N_T0020'
  },
  {
    txt: 'N_T0021-暹罗海港-Siam Seaport',
    code: 'N_T0021'
  },
  {
    txt: 'N_T0022-Sriracha Harbour-Sriracha Harbour',
    code: 'N_T0022'
  },
  {
    txt: 'N_T0023-Sriracha Oil Terminals-Sriracha Oil Terminals',
    code: 'N_T0023'
  },
  {
    txt: 'N_T0024-Surat Thani-Surat Thani',
    code: 'N_T0024'
  },
  {
    txt: 'N_T0025-Tammalang-Tammalang',
    code: 'N_T0025'
  },
  {
    txt: 'N_T0026-Tantawan Marine Terminal-Tantawan Marine Terminal',
    code: 'N_T0026'
  },
  {
    txt: 'N_T0027-Wassana Marine Terminal-Wassana Marine Terminal',
    code: 'N_T0027'
  },
  {
    txt: 'N_TP001-wini-wini',
    code: 'N_TP001'
  },
  {
    txt: 'N_TO001-Eua-Eua',
    code: 'N_TO001'
  },
  {
    txt: 'N_TO002-Neiafu (Vavau)-Neiafu (Vavau)',
    code: 'N_TO002'
  },
  {
    txt: "N_TO003-Nuku'alofa-Nuku'alofa",
    code: 'N_TO003'
  },
  {
    txt: 'N_TT001-Claxton Bay-Claxton Bay',
    code: 'N_TT001'
  },
  {
    txt: 'N_TT002-Cronstadt Island-Cronstadt Island',
    code: 'N_TT002'
  },
  {
    txt: 'N_TT003-Crown Point-Crown Point',
    code: 'N_TT003'
  },
  {
    txt: 'N_TT004-Pointe-a-Pierre-Pointe-a-Pierre',
    code: 'N_TT004'
  },
  {
    txt: 'N_TT005-Point Lisas Ports-Point Lisas Ports',
    code: 'N_TT005'
  },
  {
    txt: 'N_TT006-Port of Spain-Port of Spain',
    code: 'N_TT006'
  },
  {
    txt: 'N_TT007-Scarborough (T & T)-Scarborough (T & T)',
    code: 'N_TT007'
  },
  {
    txt: 'N_TN001-Ashtart Terminal-Ashtart Terminal',
    code: 'N_TN001'
  },
  {
    txt: 'N_TN002-比赛大-Bizerte',
    code: 'N_TN002'
  },
  {
    txt: 'N_TN003-Didon Terminal-Didon Terminal',
    code: 'N_TN003'
  },
  {
    txt: 'N_TN004-FSO Thapsus-FSO Thapsus',
    code: 'N_TN004'
  },
  {
    txt: 'N_TN005-Menzel Bourguiba-Menzel Bourguiba',
    code: 'N_TN005'
  },
  {
    txt: 'N_TN006-Oudna Terminal-Oudna Terminal',
    code: 'N_TN006'
  },
  {
    txt: 'N_TN007-突尼斯-Rades',
    code: 'N_TN007'
  },
  {
    txt: 'N_TN008-苏塞-Sousse',
    code: 'N_TN008'
  },
  {
    txt: 'N_TN009-贾尔季兹-Zarzis',
    code: 'N_TN009'
  },
  {
    txt: 'N_TR001-Akcansa Yalova-Akcansa Yalova',
    code: 'N_TR001'
  },
  {
    txt: 'N_TR002-Bagfas-Iskur Fertilizer Jetty-Bagfas-Iskur Fertilizer Jetty',
    code: 'N_TR002'
  },
  {
    txt: 'N_TR003-巴尔滕-Bartin',
    code: 'N_TR003'
  },
  {
    txt: 'N_TR004-Bodrum-Bodrum',
    code: 'N_TR004'
  },
  {
    txt: 'N_TR005-博鲁森-Borusan',
    code: 'N_TR005'
  },
  {
    txt: 'N_TR006-Bosphorus & Dardanelles Straits-Bosphorus & Dardanelles Straits',
    code: 'N_TR006'
  },
  {
    txt: 'N_TR007-Botas (Ceyhan) Oil Terminal-Botas (Ceyhan) Oil Terminal',
    code: 'N_TR007'
  },
  {
    txt: 'N_TR008-BTC Terminal Ceyhan-BTC Terminal Ceyhan',
    code: 'N_TR008'
  },
  {
    txt: 'N_TR009-Cesme-Cesme',
    code: 'N_TR009'
  },
  {
    txt: 'N_TR0010-博塔斯港-Ceyhan',
    code: 'N_TR0010'
  },
  {
    txt: 'N_TR0011-Darica-Darica',
    code: 'N_TR0011'
  },
  {
    txt: 'N_TR0012-Denbir Port-Denbir Port',
    code: 'N_TR0012'
  },
  {
    txt: 'N_TR0013-Diliskelesi-Diliskelesi',
    code: 'N_TR0013'
  },
  {
    txt: 'N_TR0014-Diler-Hereke-Diler-Hereke',
    code: 'N_TR0014'
  },
  {
    txt: 'N_TR0015-Eren Port-Eren Port',
    code: 'N_TR0015'
  },
  {
    txt: 'N_TR0016-Icdas Port-Icdas Port',
    code: 'N_TR0016'
  },
  {
    txt: 'N_TR0017-伊斯蒂迈尔-Isdemir',
    code: 'N_TR0017'
  },
  {
    txt: 'N_TR0018-Isken Terminal-Isken Terminal',
    code: 'N_TR0018'
  },
  {
    txt: 'N_TR0019-Karasu-Karasu',
    code: 'N_TR0019'
  },
  {
    txt: 'N_TR0020-Karadeniz LPG Terminal-Karadeniz LPG Terminal',
    code: 'N_TR0020'
  },
  {
    txt: 'N_TR0021-Karabiga-Karabiga',
    code: 'N_TR0021'
  },
  {
    txt: 'N_TR0022-Limas-Limas',
    code: 'N_TR0022'
  },
  {
    txt: 'N_TR0023-Martas-Martas',
    code: 'N_TR0023'
  },
  {
    txt: 'N_TR0024-Marmara Ereglisi Terminals-Marmara Ereglisi Terminals',
    code: 'N_TR0024'
  },
  {
    txt: 'N_TR0025-MMK Atakas Port-MMK Atakas Port',
    code: 'N_TR0025'
  },
  {
    txt: 'N_TR0026-内姆鲁特湾-Nemrut Bay',
    code: 'N_TR0026'
  },
  {
    txt: 'N_TR0027-Port Akdeniz-Port Akdeniz',
    code: 'N_TR0027'
  },
  {
    txt: 'N_TR0028-Saraylar-Saraylar',
    code: 'N_TR0028'
  },
  {
    txt: 'N_TR0029-topcular-topcular',
    code: 'N_TR0029'
  },
  {
    txt: 'N_TR0030-Toros-Toros',
    code: 'N_TR0030'
  },
  {
    txt: 'N_TR0031-特拉布宗-Trabzon',
    code: 'N_TR0031'
  },
  {
    txt: 'N_TR0032-Yalova-Yalova',
    code: 'N_TR0032'
  },
  {
    txt: 'N_TR0033-Yesilovacik Harbour-Yesilovacik Harbour',
    code: 'N_TR0033'
  },
  {
    txt: 'N_TR0034-Zeytinburnu-Zeytinburnu',
    code: 'N_TR0034'
  },
  {
    txt: 'N_TKM001-aladzha-aladzha',
    code: 'N_TKM001'
  },
  {
    txt: 'N_TKM002-FSO Oguzhan Marine Terminal-FSO Oguzhan Marine Terminal',
    code: 'N_TKM002'
  },
  {
    txt: 'N_TKM003-Kiyanly-Kiyanly',
    code: 'N_TKM003'
  },
  {
    txt: 'N_TKM004-Okarem-Okarem',
    code: 'N_TKM004'
  },
  {
    txt: 'N_TKM005-Turkmenbashi-Turkmenbashi',
    code: 'N_TKM005'
  },
  {
    txt: 'N_TCI001-Cockburn Harbour-Cockburn Harbour',
    code: 'N_TCI001'
  },
  {
    txt: 'N_TCI002-Grand Turk Port-Grand Turk Port',
    code: 'N_TCI002'
  },
  {
    txt: 'N_TCI003-Providenciales-Providenciales',
    code: 'N_TCI003'
  },
  {
    txt: 'N_TUV001-福纳福提-Funafuti',
    code: 'N_TUV001'
  },
  {
    txt: 'N_UKR001-Belgorod-Dnestrovsky-Belgorod-Dnestrovsky',
    code: 'N_UKR001'
  },
  {
    txt: 'N_UKR002-别尔江斯克-Berdyansk',
    code: 'N_UKR002'
  },
  {
    txt: 'N_UKR003-Chernomorsk-Chernomorsk',
    code: 'N_UKR003'
  },
  {
    txt: 'N_UKR004-Chornomorsk-Chornomorsk',
    code: 'N_UKR004'
  },
  {
    txt: 'N_UKR005-Dneprovsko-Bugsky-Dneprovsko-Bugsky',
    code: 'N_UKR005'
  },
  {
    txt: 'N_UKR006-Dnipropetrovsk-Dnipropetrovsk',
    code: 'N_UKR006'
  },
  {
    txt: 'N_UKR007-耶夫帕托里亚-Evpatoria',
    code: 'N_UKR007'
  },
  {
    txt: 'N_UKR008-Kamysh-Burun-Kamysh-Burun',
    code: 'N_UKR008'
  },
  {
    txt: 'N_UKR009-刻赤-Kerch',
    code: 'N_UKR009'
  },
  {
    txt: 'N_UKR0010-Mykolayiv-Mykolayiv',
    code: 'N_UKR0010'
  },
  {
    txt: 'N_UKR0011-十月镇-Oktyabrsk',
    code: 'N_UKR0011'
  },
  {
    txt: 'N_UKR0012-Port Krym-Port Krym',
    code: 'N_UKR0012'
  },
  {
    txt: 'N_UKR0013-Ust-Dunaisk-Ust-Dunaisk',
    code: 'N_UKR0013'
  },
  {
    txt: 'N_WAN004-Bonny Inshore Terminal-Bonny Inshore Terminal',
    code: 'N_WAN004'
  },
  {
    txt: 'N_WAN005-Bonny Offshore Terminal-Bonny Offshore Terminal',
    code: 'N_WAN005'
  },
  {
    txt: 'N_WAN006-Bonga Offshore Terminal-Bonga Offshore Terminal',
    code: 'N_WAN006'
  },
  {
    txt: 'N_WAN007-Brass Terminal-Brass Terminal',
    code: 'N_WAN007'
  },
  {
    txt: 'N_WAN008-Erha Terminal-Erha Terminal',
    code: 'N_WAN008'
  },
  {
    txt: 'N_WAN009-Escravos Oil Terminal-Escravos Oil Terminal',
    code: 'N_WAN009'
  },
  {
    txt: 'N_WAN0010-Forcados Oil Terminal-Forcados Oil Terminal',
    code: 'N_WAN0010'
  },
  {
    txt: 'N_WAN0011-FPSO Akpo-FPSO Akpo',
    code: 'N_WAN0011'
  },
  {
    txt: 'N_WAN0012-FPSO Usan-FPSO Usan',
    code: 'N_WAN0012'
  },
  {
    txt: 'N_WAN0013-FPSO Armada Perdana (Oyo Field)-FPSO Armada Perdana (Oyo Field)',
    code: 'N_WAN0013'
  },
  {
    txt: 'N_WAN0014-FPSO Sea Eagle-FPSO Sea Eagle',
    code: 'N_WAN0014'
  },
  {
    txt: 'N_WAN0015-FPSO Front Puffin-FPSO Front Puffin',
    code: 'N_WAN0015'
  },
  {
    txt: 'N_WAN0016-FPSO Armada Perkasa (Okoro Field)-FPSO Armada Perkasa (Okoro Field)',
    code: 'N_WAN0016'
  },
  {
    txt: 'N_WAN0017-FSO Virini Prem-FSO Virini Prem',
    code: 'N_WAN0017'
  },
  {
    txt: 'N_WAN0018-FSO Tulja-FSO Tulja',
    code: 'N_WAN0018'
  },
  {
    txt: 'N_WAN0019-FSO Yoho-FSO Yoho',
    code: 'N_WAN0019'
  },
  {
    txt: 'N_WAN0020-Ima Terminal-Ima Terminal',
    code: 'N_WAN0020'
  },
  {
    txt: 'N_WAN0021-Lagos (Nigeria)-Lagos (Nigeria)',
    code: 'N_WAN0021'
  },
  {
    txt: 'N_WAN0022-Odudu Terminal-Odudu Terminal',
    code: 'N_WAN0022'
  },
  {
    txt: 'N_WAN0023-Okono Terminal-Okono Terminal',
    code: 'N_WAN0023'
  },
  {
    txt: 'N_WAN0024-Okwori Terminal-Okwori Terminal',
    code: 'N_WAN0024'
  },
  {
    txt: 'N_WAN0025-Onitsha-Onitsha',
    code: 'N_WAN0025'
  },
  {
    txt: 'N_WAN0026-Pennington-Pennington',
    code: 'N_WAN0026'
  },
  {
    txt: 'N_WAN0027-Tin Can Island-Tin Can Island',
    code: 'N_WAN0027'
  },
  {
    txt: 'N_WAN0028-Ukpokiti Marine Terminal-Ukpokiti Marine Terminal',
    code: 'N_WAN0028'
  },
  {
    txt: 'N_NF001-Norfolk Island-Norfolk Island',
    code: 'N_NF001'
  },
  {
    txt: 'N_MRA001-Saipan-Saipan',
    code: 'N_MRA001'
  },
  {
    txt: 'N_N001-奥黑姆-Aaheim',
    code: 'N_N001'
  },
  {
    txt: 'N_N002-Aandalsnes-Aandalsnes',
    code: 'N_N002'
  },
  {
    txt: 'N_N003-阿格特尼斯-Agotnes',
    code: 'N_N003'
  },
  {
    txt: 'N_N004-阿尔塔-Alta',
    code: 'N_N004'
  },
  {
    txt: 'N_N005-阿尔维克-Alvik',
    code: 'N_N005'
  },
  {
    txt: 'N_N006-Andenes-Andenes',
    code: 'N_N006'
  },
  {
    txt: 'N_N007-奥达尔斯坦根-Ardalstangen',
    code: 'N_N007'
  },
  {
    txt: 'N_N008-Aukra-Aukra',
    code: 'N_N008'
  },
  {
    txt: 'N_N009-Balsfjord-Balsfjord',
    code: 'N_N009'
  },
  {
    txt: 'N_N0010-巴朗根-Ballangen',
    code: 'N_N0010'
  },
  {
    txt: 'N_N0011-Barentsburg-Barentsburg',
    code: 'N_N0011'
  },
  {
    txt: 'N_N0012-巴特斯福德-Batsfjord',
    code: 'N_N0012'
  },
  {
    txt: 'N_N0013-巴利瓦格-Berlevag',
    code: 'N_N0013'
  },
  {
    txt: 'N_N0014-Bergsfjord-Bergsfjord',
    code: 'N_N0014'
  },
  {
    txt: 'N_N0015-bjugn-bjugn',
    code: 'N_N0015'
  },
  {
    txt: 'N_N0016-Borg Havn IKS-Borg Havn IKS',
    code: 'N_N0016'
  },
  {
    txt: 'N_N0017-buvika-buvika',
    code: 'N_N0017'
  },
  {
    txt: 'N_N0018-Dirdal-Dirdal',
    code: 'N_N0018'
  },
  {
    txt: 'N_N0019-Dusavik-Dusavik',
    code: 'N_N0019'
  },
  {
    txt: 'N_N0020-dyrstad-dyrstad',
    code: 'N_N0020'
  },
  {
    txt: 'N_N0021-埃德福德-Eidfjord',
    code: 'N_N0021'
  },
  {
    txt: 'N_N0022-EIKELANDSOSEN-EIKELANDSOSEN',
    code: 'N_N0022'
  },
  {
    txt: 'N_N0023-Elnesvagen-Elnesvagen',
    code: 'N_N0023'
  },
  {
    txt: 'N_N0024-engene-engene',
    code: 'N_N0024'
  },
  {
    txt: 'N_N0025-Etne-Etne',
    code: 'N_N0025'
  },
  {
    txt: 'N_N0026-Fauske-Fauske',
    code: 'N_N0026'
  },
  {
    txt: 'N_N0027-菲堡坦根-Fiborgtangen',
    code: 'N_N0027'
  },
  {
    txt: 'N_N0028-Finnfjord-Finnfjord',
    code: 'N_N0028'
  },
  {
    txt: 'N_N0029-芬斯尼斯-Finnsnes',
    code: 'N_N0029'
  },
  {
    txt: 'N_N0030-Fiska-Fiska',
    code: 'N_N0030'
  },
  {
    txt: 'N_N0031-fiska-fiska',
    code: 'N_N0031'
  },
  {
    txt: 'N_N0032-fjortoft-fjortoft',
    code: 'N_N0032'
  },
  {
    txt: 'N_N0033-弗洛姆-Flam',
    code: 'N_N0033'
  },
  {
    txt: 'N_N0034-FOLLAFOSS-FOLLAFOSS',
    code: 'N_N0034'
  },
  {
    txt: 'N_N0035-Fonnes-Fonnes',
    code: 'N_N0035'
  },
  {
    txt: 'N_N0036-Forus-Forus',
    code: 'N_N0036'
  },
  {
    txt: 'N_N0037-Forde-Forde',
    code: 'N_N0037'
  },
  {
    txt: 'N_N0038-FPSO Skarv-FPSO Skarv',
    code: 'N_N0038'
  },
  {
    txt: 'N_N0039-FPSO Asgard A-FPSO Asgard A',
    code: 'N_N0039'
  },
  {
    txt: 'N_N0040-FPSO Goliat FPSO-FPSO Goliat FPSO',
    code: 'N_N0040'
  },
  {
    txt: 'N_N0041-FPSO Norne-FPSO Norne',
    code: 'N_N0041'
  },
  {
    txt: 'N_N0042-FPSO Alvheim-FPSO Alvheim',
    code: 'N_N0042'
  },
  {
    txt: 'N_N0043-FPSO Petrojarl Knarr-FPSO Petrojarl Knarr',
    code: 'N_N0043'
  },
  {
    txt: 'N_N0044-FPSO Balder FPU-FPSO Balder FPU',
    code: 'N_N0044'
  },
  {
    txt: 'N_N0045-FPSO Jotun A-FPSO Jotun A',
    code: 'N_N0045'
  },
  {
    txt: 'N_N0046-FSO Heidrun B-FSO Heidrun B',
    code: 'N_N0046'
  },
  {
    txt: 'N_N0047-FSO Navion Saga (Volve Field)-FSO Navion Saga (Volve Field)',
    code: 'N_N0047'
  },
  {
    txt: 'N_N0048-Fusa-Fusa',
    code: 'N_N0048'
  },
  {
    txt: 'N_N0049-盖朗厄尔峡湾-Geiranger',
    code: 'N_N0049'
  },
  {
    txt: 'N_N0050-Grenland Harbour-Grenland Harbour',
    code: 'N_N0050'
  },
  {
    txt: 'N_N0051-GUDVANGEN-GUDVANGEN',
    code: 'N_N0051'
  },
  {
    txt: 'N_N0052-haddal-haddal',
    code: 'N_N0052'
  },
  {
    txt: 'N_N0053-HALSA MELOY-HALSA MELOY',
    code: 'N_N0053'
  },
  {
    txt: 'N_N0054-Hammerfall-Hammerfall',
    code: 'N_N0054'
  },
  {
    txt: 'N_N0055-Haroysundet-Haroysundet',
    code: 'N_N0055'
  },
  {
    txt: 'N_N0056-Haram-Haram',
    code: 'N_N0056'
  },
  {
    txt: 'N_N0057-Hareid-Hareid',
    code: 'N_N0057'
  },
  {
    txt: 'N_N0058-Hasvik-Hasvik',
    code: 'N_N0058'
  },
  {
    txt: 'N_N0059-HAUSVIK-HAUSVIK',
    code: 'N_N0059'
  },
  {
    txt: 'N_N0060-Hellvik-Hellvik',
    code: 'N_N0060'
  },
  {
    txt: 'N_N0061-Hellesylt-Hellesylt',
    code: 'N_N0061'
  },
  {
    txt: 'N_N0062-Heroy-Heroy',
    code: 'N_N0062'
  },
  {
    txt: 'N_N0063-Hitra-Hitra',
    code: 'N_N0063'
  },
  {
    txt: 'N_N0064-HOLLEN-HOLLEN',
    code: 'N_N0064'
  },
  {
    txt: 'N_N0065-霍拉-Holla',
    code: 'N_N0065'
  },
  {
    txt: 'N_N0066-Hommelvik-Hommelvik',
    code: 'N_N0066'
  },
  {
    txt: 'N_N0067-霍宁斯沃格-Honningsvag',
    code: 'N_N0067'
  },
  {
    txt: 'N_N0068-Husvik (Sortland)-Husvik (Sortland)',
    code: 'N_N0068'
  },
  {
    txt: 'N_N0069-Ikornnes-Ikornnes',
    code: 'N_N0069'
  },
  {
    txt: 'N_N0070-JELSA-JELSA',
    code: 'N_N0070'
  },
  {
    txt: 'N_N0071-Kalvag Harbour-Kalvag Harbour',
    code: 'N_N0071'
  },
  {
    txt: 'N_N0072-Karmsund-Karmsund',
    code: 'N_N0072'
  },
  {
    txt: 'N_N0073-Karlsoy-Karlsoy',
    code: 'N_N0073'
  },
  {
    txt: 'N_N0074-kirksaeter-kirksaeter',
    code: 'N_N0074'
  },
  {
    txt: 'N_N0075-Kjollefjord-Kjollefjord',
    code: 'N_N0075'
  },
  {
    txt: 'N_N0076-Kjopmannskjar-Kjopmannskjar',
    code: 'N_N0076'
  },
  {
    txt: 'N_N0077-Kristiansund-Kristiansund',
    code: 'N_N0077'
  },
  {
    txt: 'N_N0078-Kristiansand-Kristiansand',
    code: 'N_N0078'
  },
  {
    txt: 'N_N0079-Kvalsund-Kvalsund',
    code: 'N_N0079'
  },
  {
    txt: 'N_N0080-Langoya-Langoya',
    code: 'N_N0080'
  },
  {
    txt: 'N_N0081-莱尔维克-Leirvik',
    code: 'N_N0081'
  },
  {
    txt: 'N_N0082-Leknes-Leknes',
    code: 'N_N0082'
  },
  {
    txt: 'N_N0083-Longyearbyen-Longyearbyen',
    code: 'N_N0083'
  },
  {
    txt: 'N_N0084-LOVUND-LOVUND',
    code: 'N_N0084'
  },
  {
    txt: 'N_N0085-Lyngen-Lyngen',
    code: 'N_N0085'
  },
  {
    txt: 'N_N0086-LYNGSTAD-LYNGSTAD',
    code: 'N_N0086'
  },
  {
    txt: 'N_N0087-莱恩格达尔-Lyngdal',
    code: 'N_N0087'
  },
  {
    txt: 'N_N0088-Lysoeysund-Lysoeysund',
    code: 'N_N0088'
  },
  {
    txt: 'N_N0089-Mehamn-Mehamn',
    code: 'N_N0089'
  },
  {
    txt: 'N_N0090-Melbu-Melbu',
    code: 'N_N0090'
  },
  {
    txt: 'N_N0091-Midsund-Midsund',
    code: 'N_N0091'
  },
  {
    txt: 'N_N0092-Mo I Rana-Mo I Rana',
    code: 'N_N0092'
  },
  {
    txt: 'N_N0093-Moskenes-Moskenes',
    code: 'N_N0093'
  },
  {
    txt: 'N_N0094-Muruvik-Muruvik',
    code: 'N_N0094'
  },
  {
    txt: 'N_N0095-Myre-Myre',
    code: 'N_N0095'
  },
  {
    txt: 'N_N0096-Naerbovagen-Naerbovagen',
    code: 'N_N0096'
  },
  {
    txt: 'N_N0097-Nesna-Nesna',
    code: 'N_N0097'
  },
  {
    txt: 'N_N0098-Nordfjordeid-Nordfjordeid',
    code: 'N_N0098'
  },
  {
    txt: 'N_N0099-Ny Alesund-Ny Alesund',
    code: 'N_N0099'
  },
  {
    txt: 'N_N00100-Nyhamna-Nyhamna',
    code: 'N_N00100'
  },
  {
    txt: 'N_N00101-Oksfjord-Oksfjord',
    code: 'N_N00101'
  },
  {
    txt: 'N_N00102-Olen-Olen',
    code: 'N_N00102'
  },
  {
    txt: 'N_N00103-Ornes-Ornes',
    code: 'N_N00103'
  },
  {
    txt: 'N_N00104-Orsta-Orsta',
    code: 'N_N00104'
  },
  {
    txt: 'N_N00105-Reine-Reine',
    code: 'N_N00105'
  },
  {
    txt: 'N_N00106-Rekefjord-Rekefjord',
    code: 'N_N00106'
  },
  {
    txt: 'N_N00107-Risavika-Risavika',
    code: 'N_N00107'
  },
  {
    txt: 'N_N00108-雷尔维克-Rorvik',
    code: 'N_N00108'
  },
  {
    txt: 'N_N00109-salthella-salthella',
    code: 'N_N00109'
  },
  {
    txt: 'N_N00110-Saltdal-Saltdal',
    code: 'N_N00110'
  },
  {
    txt: 'N_N00111-桑德尼斯君-Sandnessjoen',
    code: 'N_N00111'
  },
  {
    txt: 'N_N00112-Senjahopen-Senjahopen',
    code: 'N_N00112'
  },
  {
    txt: 'N_N00113-Sirevaag-Sirevaag',
    code: 'N_N00113'
  },
  {
    txt: 'N_N00114-Skanland-Skanland',
    code: 'N_N00114'
  },
  {
    txt: 'N_N00115-Slemmestad-Slemmestad',
    code: 'N_N00115'
  },
  {
    txt: 'N_N00116-Slovag-Slovag',
    code: 'N_N00116'
  },
  {
    txt: 'N_N00117-Sogne-Sogne',
    code: 'N_N00117'
  },
  {
    txt: 'N_N00118-瑟雷萨-Sorreisa',
    code: 'N_N00118'
  },
  {
    txt: 'N_N00119-Sortland-Sortland',
    code: 'N_N00119'
  },
  {
    txt: 'N_N00120-Sorfold-Sorfold',
    code: 'N_N00120'
  },
  {
    txt: 'N_N00121-Spjelkavik-Spjelkavik',
    code: 'N_N00121'
  },
  {
    txt: 'N_N00122-Stamsund-Stamsund',
    code: 'N_N00122'
  },
  {
    txt: 'N_N00123-Stjordal-Stjordal',
    code: 'N_N00123'
  },
  {
    txt: 'N_N00124-斯托克马克尼斯-Stokmarknes',
    code: 'N_N00124'
  },
  {
    txt: 'N_N00125-Sture Oil Terminal-Sture Oil Terminal',
    code: 'N_N00125'
  },
  {
    txt: 'N_N00126-surnadal-surnadal',
    code: 'N_N00126'
  },
  {
    txt: 'N_N00127-斯维格鲁瓦-Sveagruva',
    code: 'N_N00127'
  },
  {
    txt: 'N_N00128-斯福尔瓦尔-Svolvaer',
    code: 'N_N00128'
  },
  {
    txt: 'N_N00129-Sykkylven-Sykkylven',
    code: 'N_N00129'
  },
  {
    txt: 'N_N00130-托乌-Tau',
    code: 'N_N00130'
  },
  {
    txt: 'N_N00131-Thamshavn-Thamshavn',
    code: 'N_N00131'
  },
  {
    txt: 'N_N00132-Tjeldbergodden-Tjeldbergodden',
    code: 'N_N00132'
  },
  {
    txt: 'N_N00133-Tyssedal-Tyssedal',
    code: 'N_N00133'
  },
  {
    txt: 'N_N00134-Ulsteinvik-Ulsteinvik',
    code: 'N_N00134'
  },
  {
    txt: 'N_N00135-Ulvik-Ulvik',
    code: 'N_N00135'
  },
  {
    txt: 'N_N00136-UTHAUG-UTHAUG',
    code: 'N_N00136'
  },
  {
    txt: 'N_N00137-Vaagan-Vaagan',
    code: 'N_N00137'
  },
  {
    txt: 'N_N00138-VALSNESET-VALSNESET',
    code: 'N_N00138'
  },
  {
    txt: 'N_N00139-VALLDAL-VALLDAL',
    code: 'N_N00139'
  },
  {
    txt: 'N_N00140-Varoy-Varoy',
    code: 'N_N00140'
  },
  {
    txt: 'N_N00141-沃达尔-Verdal',
    code: 'N_N00141'
  },
  {
    txt: 'N_N00142-Vestnes-Vestnes',
    code: 'N_N00142'
  },
  {
    txt: 'N_N00143-vistdal-vistdal',
    code: 'N_N00143'
  },
  {
    txt: 'N_OM001-Al Duqm-Al Duqm',
    code: 'N_OM001'
  },
  {
    txt: 'N_OM002-Ghassah Bay Port-Ghassah Bay Port',
    code: 'N_OM002'
  },
  {
    txt: 'N_OM003-哈萨卜-Khasab',
    code: 'N_OM003'
  },
  {
    txt: 'N_OM004-Mina al Fahal-Mina al Fahal',
    code: 'N_OM004'
  },
  {
    txt: 'N_GB0056-FPSO Captain FPSO-FPSO Captain FPSO',
    code: 'N_GB0056'
  },
  {
    txt: 'N_GB0057-FPSO Gryphon A-FPSO Gryphon A',
    code: 'N_GB0057'
  },
  {
    txt: 'N_GB0058-FPSO Glen Lyon-FPSO Glen Lyon',
    code: 'N_GB0058'
  },
  {
    txt: 'N_GB0059-FPSO Petrojarl Foinaven-FPSO Petrojarl Foinaven',
    code: 'N_GB0059'
  },
  {
    txt: 'N_GB0060-FPSO Curlew-FPSO Curlew',
    code: 'N_GB0060'
  },
  {
    txt: 'N_GB0061-FPSO Enquest Producer-FPSO Enquest Producer',
    code: 'N_GB0061'
  },
  {
    txt: 'N_GB0062-FPSO Global Producer III-FPSO Global Producer III',
    code: 'N_GB0062'
  },
  {
    txt: 'N_GB0063-FPSO Sevan Hummingbird-FPSO Sevan Hummingbird',
    code: 'N_GB0063'
  },
  {
    txt: 'N_GB0064-FPSO Petrojarl Banff-FPSO Petrojarl Banff',
    code: 'N_GB0064'
  },
  {
    txt: 'N_GB0065-FPSO Anasuria-FPSO Anasuria',
    code: 'N_GB0065'
  },
  {
    txt: 'N_GB0066-FPSO Triton-FPSO Triton',
    code: 'N_GB0066'
  },
  {
    txt: 'N_GB0067-FPSO Haewene Brim-FPSO Haewene Brim',
    code: 'N_GB0067'
  },
  {
    txt: 'N_GB0068-FPSO Bleo Holm-FPSO Bleo Holm',
    code: 'N_GB0068'
  },
  {
    txt: 'N_GB0069-弗雷泽堡-Fraserburgh',
    code: 'N_GB0069'
  },
  {
    txt: 'N_GB0070-FSO Alba F.S.U.-FSO Alba F.S.U.',
    code: 'N_GB0070'
  },
  {
    txt: 'N_GB0071-格雷斯顿-Garlieston',
    code: 'N_GB0071'
  },
  {
    txt: 'N_GB0072-Garston-Garston',
    code: 'N_GB0072'
  },
  {
    txt: 'N_GB0073-格兰散达-Glensanda',
    code: 'N_GB0073'
  },
  {
    txt: 'N_GB0074-古尔-Goole',
    code: 'N_GB0074'
  },
  {
    txt: 'N_GB0075-Gourock-Gourock',
    code: 'N_GB0075'
  },
  {
    txt: 'N_GB0076-格兰奇茅斯-Grangemouth',
    code: 'N_GB0076'
  },
  {
    txt: 'N_GB0077-大雅茅斯-Great Yarmouth',
    code: 'N_GB0077'
  },
  {
    txt: 'N_GB0078-格里诺克-Greenock',
    code: 'N_GB0078'
  },
  {
    txt: 'N_GB0079-格里姆斯比-Grimsby',
    code: 'N_GB0079'
  },
  {
    txt: 'N_GB0080-哈特普尔-Hartlepool',
    code: 'N_GB0080'
  },
  {
    txt: 'N_GB0081-哈里奇-Harwich',
    code: 'N_GB0081'
  },
  {
    txt: 'N_GB0082-Hessle Haven-Hessle Haven',
    code: 'N_GB0082'
  },
  {
    txt: 'N_GB0083-希舍姆-Heysham',
    code: 'N_GB0083'
  },
  {
    txt: 'N_GB0084-霍利海德-Holyhead',
    code: 'N_GB0084'
  },
  {
    txt: 'N_GB0085-洪德角-Hound Point',
    code: 'N_GB0085'
  },
  {
    txt: 'N_GB0086-Howdendyke-Howdendyke',
    code: 'N_GB0086'
  },
  {
    txt: 'N_GB0087-赫尔-Hull',
    code: 'N_GB0087'
  },
  {
    txt: 'N_GB0088-汉特斯敦-Hunterston',
    code: 'N_GB0088'
  },
  {
    txt: 'N_GB0089-Ilfracombe-Ilfracombe',
    code: 'N_GB0089'
  },
  {
    txt: 'N_GB0090-伊明赫姆-Immingham',
    code: 'N_GB0090'
  },
  {
    txt: 'N_GB0091-因沃内斯-Inverness',
    code: 'N_GB0091'
  },
  {
    txt: 'N_GB0092-Inverkeithing Harbour-Inverkeithing Harbour',
    code: 'N_GB0092'
  },
  {
    txt: 'N_GB0093-伊普斯威奇-Ipswich',
    code: 'N_GB0093'
  },
  {
    txt: 'N_GB0094-凯尔隆特-Kilroot',
    code: 'N_GB0094'
  },
  {
    txt: 'N_GB0095-Kilkeel-Kilkeel',
    code: 'N_GB0095'
  },
  {
    txt: "N_GB0096-金兹林-King's Lynn",
    code: 'N_GB0096'
  },
  {
    txt: 'N_GB0097-Kirkcudbright-Kirkcudbright',
    code: 'N_GB0097'
  },
  {
    txt: 'N_GB0098-克科迪-Kirkcaldy',
    code: 'N_GB0098'
  },
  {
    txt: 'N_GB0099-科克沃尔-Kirkwall',
    code: 'N_GB0099'
  },
  {
    txt: 'N_GB00100-基斯霍恩-Kishorn',
    code: 'N_GB00100'
  },
  {
    txt: 'N_GB00101-凯尔-Kyle of Lochalsh',
    code: 'N_GB00101'
  },
  {
    txt: 'N_GB00102-Lamlash Harbour-Lamlash Harbour',
    code: 'N_GB00102'
  },
  {
    txt: 'N_GB00103-Lancaster-Lancaster',
    code: 'N_GB00103'
  },
  {
    txt: 'N_GB00104-拉恩-Larne',
    code: 'N_GB00104'
  },
  {
    txt: 'N_GB00105-Largs-Largs',
    code: 'N_GB00105'
  },
  {
    txt: 'N_GB00106-利斯-Leith',
    code: 'N_GB00106'
  },
  {
    txt: 'N_GB00107-勒威克-Lerwick',
    code: 'N_GB00107'
  },
  {
    txt: 'N_GB00108-小汉普顿-Littlehampton',
    code: 'N_GB00108'
  },
  {
    txt: 'N_GB00109-Liverpool (United Kingdom)-Liverpool (United Kingdom)',
    code: 'N_GB00109'
  },
  {
    txt: 'N_GB00110-Liverpool Bay Terminal-Liverpool Bay Terminal',
    code: 'N_GB00110'
  },
  {
    txt: 'N_GB00111-Llanddulas-Llanddulas',
    code: 'N_GB00111'
  },
  {
    txt: 'N_GB00112-洛什马蒂-Lochmaddy',
    code: 'N_GB00112'
  },
  {
    txt: 'N_GB00113-洛查林-Lochaline',
    code: 'N_GB00113'
  },
  {
    txt: 'N_GB00114-Loch Ryan Port-Loch Ryan Port',
    code: 'N_GB00114'
  },
  {
    txt: 'N_GB00115-Lochboisdale-Lochboisdale',
    code: 'N_GB00115'
  },
  {
    txt: 'N_GB00116-London Thamesport-London Thamesport',
    code: 'N_GB00116'
  },
  {
    txt: 'N_GB00117-伦敦-London',
    code: 'N_GB00117'
  },
  {
    txt: 'N_GB00118-伦敦德里-Londonderry',
    code: 'N_GB00118'
  },
  {
    txt: 'N_GB00119-洛斯托夫特-Lowestoft',
    code: 'N_GB00119'
  },
  {
    txt: 'N_GB00120-麦克达夫-Macduff',
    code: 'N_GB00120'
  },
  {
    txt: 'N_GB00121-Mallaig-Mallaig',
    code: 'N_GB00121'
  },
  {
    txt: 'N_GB00122-Maldon-Maldon',
    code: 'N_GB00122'
  },
  {
    txt: 'N_GB00123-Manchester Ship Canal-Manchester Ship Canal',
    code: 'N_GB00123'
  },
  {
    txt: 'N_GB00124-Medway Ports-Medway Ports',
    code: 'N_GB00124'
  },
  {
    txt: 'N_GB00125-Methil-Methil',
    code: 'N_GB00125'
  },
  {
    txt: 'N_GB00126-米尔福德港-Milford Haven',
    code: 'N_GB00126'
  },
  {
    txt: 'N_GB00127-密斯特雷-Mistley',
    code: 'N_GB00127'
  },
  {
    txt: 'N_GB00128-芒特罗兹-Montrose',
    code: 'N_GB00128'
  },
  {
    txt: 'N_GB00129-莫斯叮-Mostyn',
    code: 'N_GB00129'
  },
  {
    txt: 'N_GB00130-Neath-Neath',
    code: 'N_GB00130'
  },
  {
    txt: 'N_GB00131-新荷兰德-New Holland',
    code: 'N_GB00131'
  },
  {
    txt: 'N_GB00132-Newport (South Wales)-Newport (South Wales)',
    code: 'N_GB00132'
  },
  {
    txt: 'N_USA0041-Conneaut-Conneaut',
    code: 'N_USA0041'
  },
  {
    txt: 'N_USA0042-Coos Bay-Coos Bay',
    code: 'N_USA0042'
  },
  {
    txt: 'N_USA0043-Cordova (Alaska)-Cordova (Alaska)',
    code: 'N_USA0043'
  },
  {
    txt: 'N_USA0044-Cove Point-Cove Point',
    code: 'N_USA0044'
  },
  {
    txt: 'N_USA0045-Craig-Craig',
    code: 'N_USA0045'
  },
  {
    txt: 'N_USA0046-Delaware City-Delaware City',
    code: 'N_USA0046'
  },
  {
    txt: 'N_USA0047-Dillingham-Dillingham',
    code: 'N_USA0047'
  },
  {
    txt: 'N_USA0048-Drift River Marine Terminal-Drift River Marine Terminal',
    code: 'N_USA0048'
  },
  {
    txt: 'N_USA0049-Duluth-Superior-Duluth-Superior',
    code: 'N_USA0049'
  },
  {
    txt: 'N_USA0050-Dutch Harbor-Dutch Harbor',
    code: 'N_USA0050'
  },
  {
    txt: 'N_USA0051-Eastport-Eastport',
    code: 'N_USA0051'
  },
  {
    txt: 'N_USA0052-Eddystone-Eddystone',
    code: 'N_USA0052'
  },
  {
    txt: 'N_USA0053-El Segundo Terminal-El Segundo Terminal',
    code: 'N_USA0053'
  },
  {
    txt: 'N_USA0054-Everett (Washington)-Everett (Washington)',
    code: 'N_USA0054'
  },
  {
    txt: 'N_USA0055-Fairport Harbor-Fairport Harbor',
    code: 'N_USA0055'
  },
  {
    txt: 'N_USA0056-Fall River-Fall River',
    code: 'N_USA0056'
  },
  {
    txt: 'N_USA0057-Fort Pierce-Fort Pierce',
    code: 'N_USA0057'
  },
  {
    txt: 'N_USA0058-FPSO Turritella-FPSO Turritella',
    code: 'N_USA0058'
  },
  {
    txt: 'N_USA0059-FPSO BW Pioneer (Chinook Oil Field)-FPSO BW Pioneer (Chinook Oil Field)',
    code: 'N_USA0059'
  },
  {
    txt: 'N_USA0060-Garfield-Garfield',
    code: 'N_USA0060'
  },
  {
    txt: 'N_USA0061-Georgetown (USA)-Georgetown (USA)',
    code: 'N_USA0061'
  },
  {
    txt: 'N_USA0062-Gloucester (USA)-Gloucester (USA)',
    code: 'N_USA0062'
  },
  {
    txt: 'N_USA0063-Grays Harbor-Grays Harbor',
    code: 'N_USA0063'
  },
  {
    txt: 'N_USA0064-Greater Baton Rouge-Greater Baton Rouge',
    code: 'N_USA0064'
  },
  {
    txt: 'N_USA0065-Greenport Harbor-Greenport Harbor',
    code: 'N_USA0065'
  },
  {
    txt: 'N_USA0066-Gustavus-Gustavus',
    code: 'N_USA0066'
  },
  {
    txt: 'N_USA0067-Haines-Haines',
    code: 'N_USA0067'
  },
  {
    txt: 'N_USA0068-Havana (USA)-Havana (USA)',
    code: 'N_USA0068'
  },
  {
    txt: 'N_USA0069-Hawk Inlet-Hawk Inlet',
    code: 'N_USA0069'
  },
  {
    txt: 'N_USA0070-Hoboken-Hoboken',
    code: 'N_USA0070'
  },
  {
    txt: 'N_USA0071-Hollis-Hollis',
    code: 'N_USA0071'
  },
  {
    txt: 'N_USA0072-Holland Harbor-Holland Harbor',
    code: 'N_USA0072'
  },
  {
    txt: 'N_USA0073-Hoonah-Hoonah',
    code: 'N_USA0073'
  },
  {
    txt: 'N_USA0074-Houma-Houma',
    code: 'N_USA0074'
  },
  {
    txt: 'N_USA0075-HOUGHTON-HOUGHTON',
    code: 'N_USA0075'
  },
  {
    txt: 'N_USA0076-Hueneme-Hueneme',
    code: 'N_USA0076'
  },
  {
    txt: 'N_USA0077-Hydaburg-Hydaburg',
    code: 'N_USA0077'
  },
  {
    txt: 'N_USA0078-Indiana Harbor IN-Indiana Harbor IN',
    code: 'N_USA0078'
  },
  {
    txt: 'N_USA0079-Johnston Island Harbor-Johnston Island Harbor',
    code: 'N_USA0079'
  },
  {
    txt: 'N_USA0080-Kailua Kona-Kailua Kona',
    code: 'N_USA0080'
  },
  {
    txt: 'N_USA0081-Kake-Kake',
    code: 'N_USA0081'
  },
  {
    txt: 'N_USA0082-Kaunakakai-Kaunakakai',
    code: 'N_USA0082'
  },
  {
    txt: 'N_USA0083-Kawaihae-Kawaihae',
    code: 'N_USA0083'
  },
  {
    txt: 'N_USA0084-Kennewick-Kennewick',
    code: 'N_USA0084'
  },
  {
    txt: 'N_USA0085-King Cove-King Cove',
    code: 'N_USA0085'
  },
  {
    txt: 'N_USA0086-Klawock-Klawock',
    code: 'N_USA0086'
  },
  {
    txt: 'N_USA0087-Klickitat-Klickitat',
    code: 'N_USA0087'
  },
  {
    txt: 'N_USA0088-Knik-Knik',
    code: 'N_USA0088'
  },
  {
    txt: 'N_USA0089-Kotzebue-Kotzebue',
    code: 'N_USA0089'
  },
  {
    txt: 'N_USA0090-Krotz Springs-Krotz Springs',
    code: 'N_USA0090'
  },
  {
    txt: 'N_USA0091-Lahaina-Lahaina',
    code: 'N_USA0091'
  },
  {
    txt: 'N_USA0092-Lemont-Lemont',
    code: 'N_USA0092'
  },
  {
    txt: 'N_USA0093-Lewiston-Lewiston',
    code: 'N_USA0093'
  },
  {
    txt: 'N_USA0094-LOOP Terminal-LOOP Terminal',
    code: 'N_USA0094'
  },
  {
    txt: 'N_USA0095-Manistee-Manistee',
    code: 'N_USA0095'
  },
  {
    txt: 'N_USA0096-Marquette-Marquette',
    code: 'N_USA0096'
  },
  {
    txt: 'N_USA0097-Marcus Hook-Marcus Hook',
    code: 'N_USA0097'
  },
  {
    txt: 'N_USA0098-McMurdo Station-McMurdo Station',
    code: 'N_USA0098'
  },
  {
    txt: 'N_USA0099-Melville-Melville',
    code: 'N_USA0099'
  },
  {
    txt: 'N_USA00100-Menominee/Marinette-Menominee/Marinette',
    code: 'N_USA00100'
  },
  {
    txt: 'N_USA00101-Mississippi River-Mississippi River',
    code: 'N_USA00101'
  },
  {
    txt: 'N_USA00102-密西西比河口-Mississippi River Mouth',
    code: 'N_USA00102'
  },
  {
    txt: 'N_USA00103-Morrow-Morrow',
    code: 'N_USA00103'
  },
  {
    txt: 'N_USA00104-Morgan City-Morgan City',
    code: 'N_USA00104'
  },
  {
    txt: 'N_USA00105-Muskogee-Muskogee',
    code: 'N_USA00105'
  },
  {
    txt: 'N_USA00106-Naknek-Naknek',
    code: 'N_USA00106'
  },
  {
    txt: 'N_USA00107-Nantucket Harbor-Nantucket Harbor',
    code: 'N_USA00107'
  },
  {
    txt: 'N_USA00108-Nenana-Nenana',
    code: 'N_USA00108'
  },
  {
    txt: 'N_USA00109-Newport (Oregon)-Newport (Oregon)',
    code: 'N_USA00109'
  },
  {
    txt: 'N_USA00110-Newport (Rhode Island)-Newport (Rhode Island)',
    code: 'N_USA00110'
  },
  {
    txt: 'N_USA00111-New London-New London',
    code: 'N_USA00111'
  },
  {
    txt: 'N_USA00112-New York & New Jersey-New York & New Jersey',
    code: 'N_USA00112'
  },
  {
    txt: 'N_USA00113-Northport-Northport',
    code: 'N_USA00113'
  },
  {
    txt: 'N_USA00114-Northeast Gateway LNG Offshore Terminal-Northeast Gateway LNG Offshore Terminal',
    code: 'N_USA00114'
  },
  {
    txt: 'N_USA00115-Ogdensburg-Ogdensburg',
    code: 'N_USA00115'
  },
  {
    txt: 'N_USA00116-Pasco-Pasco',
    code: 'N_USA00116'
  },
  {
    txt: 'N_USA00117-Pelican-Pelican',
    code: 'N_USA00117'
  },
  {
    txt: 'N_USA00118-Pennsauken-Pennsauken',
    code: 'N_USA00118'
  },
  {
    txt: 'N_USA00119-Pittsburg-Pittsburg',
    code: 'N_USA00119'
  },
  {
    txt: 'N_USA00120-Plaquemines-Plaquemines',
    code: 'N_USA00120'
  },
  {
    txt: 'N_USA00121-Plymouth (USA)-Plymouth (USA)',
    code: 'N_USA00121'
  },
  {
    txt: 'N_USA00122-Port Jefferson-Port Jefferson',
    code: 'N_USA00122'
  },
  {
    txt: 'N_USA00123-Port Manatee-Port Manatee',
    code: 'N_USA00123'
  },
  {
    txt: 'N_USA00124-Portsmouth (USA-New Hampshire)-Portsmouth (USA-New Hampshire)',
    code: 'N_USA00124'
  },
  {
    txt: 'N_USA00125-Port Freeport-Port Freeport',
    code: 'N_USA00125'
  },
  {
    txt: 'N_USA00126-Port Arthur-Port Arthur',
    code: 'N_USA00126'
  },
  {
    txt: 'N_USA00127-Portland (USA-Maine)-Portland (USA-Maine)',
    code: 'N_USA00127'
  },
  {
    txt: 'N_USA00128-Portland (USA-Oregon)-Portland (USA-Oregon)',
    code: 'N_USA00128'
  },
  {
    txt: 'N_USA00129-Port San Luis-Port San Luis',
    code: 'N_USA00129'
  },
  {
    txt: 'N_USA00130-Port Wentworth-Port Wentworth',
    code: 'N_USA00130'
  },
  {
    txt: 'N_USA00131-Port of South Louisiana-Port of South Louisiana',
    code: 'N_USA00131'
  },
  {
    txt: 'N_USA00132-Port Allen-Port Allen',
    code: 'N_USA00132'
  },
  {
    txt: 'N_USA00133-Port Westward-Port Westward',
    code: 'N_USA00133'
  },
  {
    txt: 'N_USA00134-port inland-port inland',
    code: 'N_USA00134'
  },
  {
    txt: 'N_USA00135-PORT DOLOMITE-PORT DOLOMITE',
    code: 'N_USA00135'
  },
  {
    txt: 'N_USA00136-Port of Virginia-Port of Virginia',
    code: 'N_USA00136'
  },
  {
    txt: 'N_USA00137-Port of Davisville-Port of Davisville',
    code: 'N_USA00137'
  },
  {
    txt: 'N_USA00138-Port Manchac-Port Manchac',
    code: 'N_USA00138'
  },
  {
    txt: 'N_USA00139-Port MacKenzie-Port MacKenzie',
    code: 'N_USA00139'
  },
  {
    txt: 'N_USA00140-Port Fourchon-Port Fourchon',
    code: 'N_USA00140'
  },
  {
    txt: 'N_USA00141-Port Clyde-Port Clyde',
    code: 'N_USA00141'
  },
  {
    txt: 'N_USA00142-Port Chester Harbor-Port Chester Harbor',
    code: 'N_USA00142'
  },
  {
    txt: 'N_USA00143-Red Dog-Red Dog',
    code: 'N_USA00143'
  },
  {
    txt: 'N_USA00144-Redwood City-Redwood City',
    code: 'N_USA00144'
  },
  {
    txt: 'N_USA00145-Richmond (California)-Richmond (California)',
    code: 'N_USA00145'
  },
  {
    txt: 'N_USA00146-Richmond (Virginia)-Richmond (Virginia)',
    code: 'N_USA00146'
  },
  {
    txt: 'N_USA00147-Riverhead Terminal-Riverhead Terminal',
    code: 'N_USA00147'
  },
  {
    txt: 'N_USA00148-Rockland-Rockland',
    code: 'N_USA00148'
  },
  {
    txt: 'N_USA00149-Sabine Pass-Sabine Pass',
    code: 'N_USA00149'
  },
  {
    txt: 'N_USA00150-Salem-Salem',
    code: 'N_USA00150'
  },
  {
    txt: 'N_USA00151-Sandwich-Sandwich',
    code: 'N_USA00151'
  },
  {
    txt: 'N_USA00152-Santa Barbara (USA)-Santa Barbara (USA)',
    code: 'N_USA00152'
  },
  {
    txt: 'N_USA00153-Sandusky-Sandusky',
    code: 'N_USA00153'
  },
  {
    txt: 'N_USA00154-Sand Point-Sand Point',
    code: 'N_USA00154'
  },
  {
    txt: 'N_USA00155-santa rosalia north-santa rosalia north',
    code: 'N_USA00155'
  },
  {
    txt: 'N_USA00156-Savannah-Savannah',
    code: 'N_USA00156'
  },
  {
    txt: 'N_USA00157-Saxman-Saxman',
    code: 'N_USA00157'
  },
  {
    txt: 'N_USA00158-Seldovia-Seldovia',
    code: 'N_USA00158'
  },
  {
    txt: 'N_USA00159-St Helens-St Helens',
    code: 'N_USA00159'
  },
  {
    txt: 'N_USA00160-St Petersburg (USA)-St Petersburg (USA)',
    code: 'N_USA00160'
  },
  {
    txt: 'N_USA00161-St Michael-St Michael',
    code: 'N_USA00161'
  },
  {
    txt: 'N_USA00162-Stamford Harbor-Stamford Harbor',
    code: 'N_USA00162'
  },
  {
    txt: 'N_USA00163-Sturgeon Bay-Sturgeon Bay',
    code: 'N_USA00163'
  },
  {
    txt: 'N_USA00164-Sunny Point Terminal-Sunny Point Terminal',
    code: 'N_USA00164'
  },
  {
    txt: 'N_USA00165-Taconite Harbor-Taconite Harbor',
    code: 'N_USA00165'
  },
  {
    txt: 'N_USA00166-The Dalles-The Dalles',
    code: 'N_USA00166'
  },
  {
    txt: 'N_USA00167-Thorne Bay-Thorne Bay',
    code: 'N_USA00167'
  },
  {
    txt: 'N_USA00168-Toledo (USA)-Toledo (USA)',
    code: 'N_USA00168'
  },
  {
    txt: 'N_USA00169-Tongue Point-Tongue Point',
    code: 'N_USA00169'
  },
  {
    txt: 'N_USA00170-Umatilla-Umatilla',
    code: 'N_USA00170'
  },
  {
    txt: 'N_USA00171-US Gulf Lightering Zones-US Gulf Lightering Zones',
    code: 'N_USA00171'
  },
  {
    txt: 'N_USA00172-Vancouver (USA)-Vancouver (USA)',
    code: 'N_USA00172'
  },
  {
    txt: 'N_USA00173-Vicksburg-Vicksburg',
    code: 'N_USA00173'
  },
  {
    txt: 'N_USA00174-Walla Walla-Walla Walla',
    code: 'N_USA00174'
  },
  {
    txt: 'N_USA00175-Waukegan-Waukegan',
    code: 'N_USA00175'
  },
  {
    txt: 'N_USA00176-Whitman County-Whitman County',
    code: 'N_USA00176'
  },
  {
    txt: 'N_USA00177-Whittier-Whittier',
    code: 'N_USA00177'
  },
  {
    txt: 'N_USA00178-Wilmington (USA-N Carolina)-Wilmington (USA-N Carolina)',
    code: 'N_USA00178'
  },
  {
    txt: 'N_USA00179-Wilmington (USA-Delaware)-Wilmington (USA-Delaware)',
    code: 'N_USA00179'
  },
  {
    txt: 'N_USA00180-Winterport-Winterport',
    code: 'N_USA00180'
  },
  {
    txt: 'N_USA00181-Yakutat-Yakutat',
    code: 'N_USA00181'
  },
  {
    txt: 'N_USA00182-Yorktown-Yorktown',
    code: 'N_USA00182'
  },
  {
    txt: 'N_U001-胡安拉卡-Juan Lacaze',
    code: 'N_U001'
  },
  {
    txt: 'N_U002-La Paloma-La Paloma',
    code: 'N_U002'
  },
  {
    txt: 'N_U003-彭塔佩雷拉-Punta Pereira',
    code: 'N_U003'
  },
  {
    txt: 'N_VA001-桑托-Santo',
    code: 'N_VA001'
  },
  {
    txt: 'N_YV001-Bajo Grande Refinery-Bajo Grande Refinery',
    code: 'N_YV001'
  },
  {
    txt: 'N_YV002-Bauxilum-Bauxilum',
    code: 'N_YV002'
  },
  {
    txt: 'N_YV003-Boca Grande (Venezuela)-Boca Grande (Venezuela)',
    code: 'N_YV003'
  },
  {
    txt: 'N_YV004-Bulkwayuu Terminal-Bulkwayuu Terminal',
    code: 'N_YV004'
  },
  {
    txt: 'N_YV005-Catia La Mar-Catia La Mar',
    code: 'N_YV005'
  },
  {
    txt: 'N_YV006-Chichiriviche-Chichiriviche',
    code: 'N_YV006'
  },
  {
    txt: 'N_YV007-库马雷博港-Cumarebo',
    code: 'N_YV007'
  },
  {
    txt: 'N_YV008-FSO Nabarima-FSO Nabarima',
    code: 'N_YV008'
  },
  {
    txt: 'N_YV009-Jose Terminal-Jose Terminal',
    code: 'N_YV009'
  },
  {
    txt: 'N_YV0010-La Ceiba (Venezuela)-La Ceiba (Venezuela)',
    code: 'N_YV0010'
  },
  {
    txt: 'N_YV0011-Las Piedras-Las Piedras',
    code: 'N_YV0011'
  },
  {
    txt: 'N_YV0012-Matanzas (Venezuela)-Matanzas (Venezuela)',
    code: 'N_YV0012'
  },
  {
    txt: 'N_YV0013-Puerto de Hierro-Puerto de Hierro',
    code: 'N_YV0013'
  },
  {
    txt: 'N_YV0014-克鲁斯港-Puerto La Cruz',
    code: 'N_YV0014'
  },
  {
    txt: 'N_YV0015-Puerto Pedernales-Puerto Pedernales',
    code: 'N_YV0015'
  },
  {
    txt: 'N_YV0016-Punta Camacho-Punta Camacho',
    code: 'N_YV0016'
  },
  {
    txt: 'N_YV0017-Venterminales-Venterminales',
    code: 'N_YV0017'
  },
  {
    txt: 'N_YV0018-Venalum Terminal-Venalum Terminal',
    code: 'N_YV0018'
  },
  {
    txt: 'N_VN001-Bourbon Ben Luc-Bourbon Ben Luc',
    code: 'N_VN001'
  },
  {
    txt: 'N_VN002-Cai Mep-Cai Mep',
    code: 'N_VN002'
  },
  {
    txt: 'N_VN003-Cai Lan-Cai Lan',
    code: 'N_VN003'
  },
  {
    txt: 'N_VN004-Campha-Campha',
    code: 'N_VN004'
  },
  {
    txt: 'N_VN005-Cam Ranh-Cam Ranh',
    code: 'N_VN005'
  },
  {
    txt: 'N_VN006-Can Tho-Can Tho',
    code: 'N_VN006'
  },
  {
    txt: 'N_VN007-Cat Lai-Cat Lai',
    code: 'N_VN007'
  },
  {
    txt: 'N_VN008-Chan May Port-Chan May Port',
    code: 'N_VN008'
  },
  {
    txt: 'N_VN009-Chim Sao Marine Terminal-Chim Sao Marine Terminal',
    code: 'N_VN009'
  },
  {
    txt: 'N_VN0010-Chu Lai-Chu Lai',
    code: 'N_VN0010'
  },
  {
    txt: 'N_VN0011-Dai Hung Offshore Terminals-Dai Hung Offshore Terminals',
    code: 'N_VN0011'
  },
  {
    txt: 'N_VN0012-Danang-Danang',
    code: 'N_VN0012'
  },
  {
    txt: 'N_VN0013-Dong Thap-Dong Thap',
    code: 'N_VN0013'
  },
  {
    txt: 'N_VN0014-Dong Nai-Dong Nai',
    code: 'N_VN0014'
  },
  {
    txt: 'N_VN0015-Dung Quat Petroleum Port-Dung Quat Petroleum Port',
    code: 'N_VN0015'
  },
  {
    txt: 'N_VN0016-Duyen Hai Sea Port-Duyen Hai Sea Port',
    code: 'N_VN0016'
  },
  {
    txt: 'N_VN0017-FPSO Armada TGT1-FPSO Armada TGT1',
    code: 'N_VN0017'
  },
  {
    txt: 'N_VN0018-FPSO PTSC Lam Son (Thang Long-Dong Do)-FPSO PTSC Lam Son (Thang Long-Dong Do)',
    code: 'N_VN0018'
  },
  {
    txt: 'N_VN0019-FPSO Chi Linh-FPSO Chi Linh',
    code: 'N_VN0019'
  },
  {
    txt: 'N_VN0020-FPSO Ba Vi-FPSO Ba Vi',
    code: 'N_VN0020'
  },
  {
    txt: 'N_VN0021-FPSO Ruby II-FPSO Ruby II',
    code: 'N_VN0021'
  },
  {
    txt: 'N_VN0022-FPSO Song Doc Pride MV 19-FPSO Song Doc Pride MV 19',
    code: 'N_VN0022'
  },
  {
    txt: 'N_VN0023-FSO Vietsovpetro-02-FSO Vietsovpetro-02',
    code: 'N_VN0023'
  },
  {
    txt: 'N_VN0024-FSO Vietsovpetro 01-FSO Vietsovpetro 01',
    code: 'N_VN0024'
  },
  {
    txt: 'N_VN0025-FSO PTSC Bien Dong 01-FSO PTSC Bien Dong 01',
    code: 'N_VN0025'
  },
  {
    txt: 'N_VN0026-FSO Chi Lang-FSO Chi Lang',
    code: 'N_VN0026'
  },
  {
    txt: 'N_VN0027-FSO Rang Dong-FSO Rang Dong',
    code: 'N_VN0027'
  },
  {
    txt: 'N_VN0028-Hon Chong-Hon Chong',
    code: 'N_VN0028'
  },
  {
    txt: 'N_VN0029-Hon Gai-Hon Gai',
    code: 'N_VN0029'
  },
  {
    txt: 'N_VN0030-Hon La Seaport-Hon La Seaport',
    code: 'N_VN0030'
  },
  {
    txt: 'N_VN0031-Le Mon-Le Mon',
    code: 'N_VN0031'
  },
  {
    txt: 'N_VN0032-My Thoi-My Thoi',
    code: 'N_VN0032'
  },
  {
    txt: 'N_VN0033-Nam Can-Nam Can',
    code: 'N_VN0033'
  },
  {
    txt: 'N_VN0034-Nghe Tinh-Nghe Tinh',
    code: 'N_VN0034'
  },
  {
    txt: 'N_VN0035-Nghi Son-Nghi Son',
    code: 'N_VN0035'
  },
  {
    txt: 'N_VN0036-Phu My-Phu My',
    code: 'N_VN0036'
  },
  {
    txt: 'N_VN0037-Quy Nhon-Quy Nhon',
    code: 'N_VN0037'
  },
  {
    txt: 'N_VN0038-Rong Doi Tay Terminal-Rong Doi Tay Terminal',
    code: 'N_VN0038'
  },
  {
    txt: 'N_VN0039-Saigon-Saigon',
    code: 'N_VN0039'
  },
  {
    txt: 'N_VN0040-Su Tu Vang Offshore Terminal-Su Tu Vang Offshore Terminal',
    code: 'N_VN0040'
  },
  {
    txt: 'N_VN0041-Su Tu Den Offshore Terminal-Su Tu Den Offshore Terminal',
    code: 'N_VN0041'
  },
  {
    txt: 'N_VN0042-Van Phong Bay-Van Phong Bay',
    code: 'N_VN0042'
  },
  {
    txt: 'N_VN0043-Vinh Tan-Vinh Tan',
    code: 'N_VN0043'
  },
  {
    txt: 'N_VN0044-Vung Ang-Vung Ang',
    code: 'N_VN0044'
  },
  {
    txt: 'N_VIB001-Road Harbour-Road Harbour',
    code: 'N_VIB001'
  },
  {
    txt: 'N_VIU001-Limetree Bay-Limetree Bay',
    code: 'N_VIU001'
  },
  {
    txt: 'N_VIU002-Port Alucroix-Port Alucroix',
    code: 'N_VIU002'
  },
  {
    txt: 'N_VIU003-St Thomas-St Thomas',
    code: 'N_VIU003'
  },
  {
    txt: 'N_VIU004-Third Port-Third Port',
    code: 'N_VIU004'
  },
  {
    txt: 'N_WF001-Futuna-Futuna',
    code: 'N_WF001'
  },
  {
    txt: 'N_WF002-Wallis-Wallis',
    code: 'N_WF002'
  },
  {
    txt: 'N_WSA001-Dakhla-Dakhla',
    code: 'N_WSA001'
  },
  {
    txt: 'N_WSA002-Laayoune-Laayoune',
    code: 'N_WSA002'
  },
  {
    txt: 'N_YEM001-Ash Shihr-Ash Shihr',
    code: 'N_YEM001'
  },
  {
    txt: 'N_YEM002-尼什屯-Nishtun',
    code: 'N_YEM002'
  },
  {
    txt: 'USHID-希达尔戈-HIDALGO,TX',
    code: 'USHID'
  },
  {
    txt: 'KANPUR-坎普尔-KANPUR',
    code: 'KANPUR'
  },
  {
    txt: 'HAINING-海宁-null',
    code: 'HAINING'
  },
  {
    txt: 'VU-null-null',
    code: 'VU'
  },
  {
    txt: 'IDBTL-巴都利金-BATULICIN',
    code: 'IDBTL'
  },
  {
    txt: 'HAILING-陵水-null',
    code: 'HAILING'
  },
  {
    txt: 'SVSAD-圣萨尔瓦多-San Salvador',
    code: 'SVSAD'
  },
  {
    txt: 'VU1-SANTO VANUATU-null',
    code: 'VU1'
  },
  {
    txt: 'PERTH-珀斯-PERTH',
    code: 'PERTH'
  },
  {
    txt: 'PYVLL-比耶塔-VILLETA',
    code: 'PYVLL'
  },
  {
    txt: 'HAZIRA-哈兹拉-null',
    code: 'HAZIRA'
  },
  {
    txt: 'N_GB00133-Newport (Isle Of Wight)-Newport (Isle Of Wight)',
    code: 'N_GB00133'
  },
  {
    txt: 'N_GB00134-纽黑文-Newhaven',
    code: 'N_GB00134'
  },
  {
    txt: 'N_GB00135-奥班-Oban',
    code: 'N_GB00135'
  },
  {
    txt: 'N_GB00136-帕德斯托-Padstow',
    code: 'N_GB00136'
  },
  {
    txt: 'N_GB00137-Penryn-Penryn',
    code: 'N_GB00137'
  },
  {
    txt: 'N_GB00138-彭赞斯-Penzance',
    code: 'N_GB00138'
  },
  {
    txt: 'N_GB00139-Perth (United Kingdom)-Perth (United Kingdom)',
    code: 'N_GB00139'
  },
  {
    txt: 'N_GB00140-Peterhead Bay Harbour-Peterhead Bay Harbour',
    code: 'N_GB00140'
  },
  {
    txt: 'N_GB00141-Plymouth (United Kingdom)-Plymouth (United Kingdom)',
    code: 'N_GB00141'
  },
  {
    txt: 'N_GB00142-普尔-Poole',
    code: 'N_GB00142'
  },
  {
    txt: 'N_GB00143-Port Lamont-Port Lamont',
    code: 'N_GB00143'
  },
  {
    txt: 'N_GB00144-Portsmouth (United Kingdom)-Portsmouth (United Kingdom)',
    code: 'N_GB00144'
  },
  {
    txt: 'N_GB00145-彭林港-Port Penrhyn',
    code: 'N_GB00145'
  },
  {
    txt: 'N_GB00146-Portland (United Kingdom)-Portland (United Kingdom)',
    code: 'N_GB00146'
  },
  {
    txt: 'N_GB00147-塔尔博特港-Port Talbot',
    code: 'N_GB00147'
  },
  {
    txt: 'N_GB00148-Portavogie-Portavogie',
    code: 'N_GB00148'
  },
  {
    txt: 'N_GB00149-Port Sutton Bridge-Port Sutton Bridge',
    code: 'N_GB00149'
  },
  {
    txt: 'N_GB00150-埃伦港-Port Ellen',
    code: 'N_GB00150'
  },
  {
    txt: 'N_GB00151-拉姆斯盖特-Ramsgate',
    code: 'N_GB00151'
  },
  {
    txt: 'N_GB00152-罗斯西-Rothesay',
    code: 'N_GB00152'
  },
  {
    txt: 'N_GB00153-莱亚-Rye',
    code: 'N_GB00153'
  },
  {
    txt: 'N_GB00154-sandbank-sandbank',
    code: 'N_GB00154'
  },
  {
    txt: 'N_GB00155-斯卡洛韦-Scalloway',
    code: 'N_GB00155'
  },
  {
    txt: 'N_GB00156-斯克拉布斯特-Scrabster',
    code: 'N_GB00156'
  },
  {
    txt: 'N_GB00157-西厄姆-Seaham',
    code: 'N_GB00157'
  },
  {
    txt: 'N_GB00158-Sharpness and Gloucester-Sharpness and Gloucester',
    code: 'N_GB00158'
  },
  {
    txt: 'N_GB00159-希尔内斯-Sheerness',
    code: 'N_GB00159'
  },
  {
    txt: 'N_GB00160-肖勒姆-Shoreham',
    code: 'N_GB00160'
  },
  {
    txt: 'N_GB00161-Shotton-Shotton',
    code: 'N_GB00161'
  },
  {
    txt: 'N_GB00162-西洛斯-Silloth',
    code: 'N_GB00162'
  },
  {
    txt: "N_GB00163-St Mary's (Isles of Scilly)-St Mary's (Isles of Scilly)",
    code: 'N_GB00163'
  },
  {
    txt: 'N_GB00164-STS Southwold-STS Southwold',
    code: 'N_GB00164'
  },
  {
    txt: 'N_GB00165-Tetney Terminal-Tetney Terminal',
    code: 'N_GB00165'
  },
  {
    txt: 'N_GB00166-Torbay Harbour-Torbay Harbour',
    code: 'N_GB00166'
  },
  {
    txt: 'N_GB00167-Trent Wharves-Trent Wharves',
    code: 'N_GB00167'
  },
  {
    txt: 'N_GB00168-Tyne-Tyne',
    code: 'N_GB00168'
  },
  {
    txt: 'N_GB00169-乌格-Uig',
    code: 'N_GB00169'
  },
  {
    txt: 'N_GB00170-沃伦角-Warrenpoint',
    code: 'N_GB00170'
  },
  {
    txt: 'N_USA001-Adak-Adak',
    code: 'N_USA001'
  },
  {
    txt: 'N_USA002-Akutan-Akutan',
    code: 'N_USA002'
  },
  {
    txt: 'N_USA003-Alabama Inland Ports-Alabama Inland Ports',
    code: 'N_USA003'
  },
  {
    txt: 'N_USA004-Albany (USA)-Albany (USA)',
    code: 'N_USA004'
  },
  {
    txt: 'N_USA005-Alcan-Alcan',
    code: 'N_USA005'
  },
  {
    txt: 'N_USA006-Alexandria (USA)-Alexandria (USA)',
    code: 'N_USA006'
  },
  {
    txt: 'N_USA007-Alpena-Alpena',
    code: 'N_USA007'
  },
  {
    txt: 'N_USA008-Angoon-Angoon',
    code: 'N_USA008'
  },
  {
    txt: 'N_USA009-Arlington-Arlington',
    code: 'N_USA009'
  },
  {
    txt: 'N_USA0010-Baltimore (USA)-Baltimore (USA)',
    code: 'N_USA0010'
  },
  {
    txt: 'N_USA0011-Bangor (USA)-Bangor (USA)',
    code: 'N_USA0011'
  },
  {
    txt: "N_USA0012-Barber's Point Harbor-Barber's Point Harbor",
    code: 'N_USA0012'
  },
  {
    txt: 'N_USA0013-Bar Harbor-Bar Harbor',
    code: 'N_USA0013'
  },
  {
    txt: 'N_USA0014-Bay City-Bay City',
    code: 'N_USA0014'
  },
  {
    txt: 'N_USA0015-Benicia-Benicia',
    code: 'N_USA0015'
  },
  {
    txt: 'N_USA0016-Bering Straits (USA)-Bering Straits (USA)',
    code: 'N_USA0016'
  },
  {
    txt: 'N_USA0017-Bethel-Bethel',
    code: 'N_USA0017'
  },
  {
    txt: 'N_USA0018-Bienville-Bienville',
    code: 'N_USA0018'
  },
  {
    txt: 'N_USA0019-Black Rock Harbor-Black Rock Harbor',
    code: 'N_USA0019'
  },
  {
    txt: 'N_USA0020-Boston (USA)-Boston (USA)',
    code: 'N_USA0020'
  },
  {
    txt: 'N_USA0021-Bridgeport (Connecticut)-Bridgeport (Connecticut)',
    code: 'N_USA0021'
  },
  {
    txt: 'N_USA0022-Bucks County Port-Bucks County Port',
    code: 'N_USA0022'
  },
  {
    txt: 'N_USA0023-Buffington-Buffington',
    code: 'N_USA0023'
  },
  {
    txt: 'N_USA0024-Burns Harbor-Burns Harbor',
    code: 'N_USA0024'
  },
  {
    txt: 'N_USA0025-Burlington-Burlington',
    code: 'N_USA0025'
  },
  {
    txt: 'N_USA0026-CALCITE-CALCITE',
    code: 'N_USA0026'
  },
  {
    txt: 'N_USA0027-Camas-Washougal-Camas-Washougal',
    code: 'N_USA0027'
  },
  {
    txt: 'N_USA0028-Cape Cod Canal-Cape Cod Canal',
    code: 'N_USA0028'
  },
  {
    txt: 'N_USA0029-Cape Girardeau-Cape Girardeau',
    code: 'N_USA0029'
  },
  {
    txt: 'N_USA0030-Cape Charles-Cape Charles',
    code: 'N_USA0030'
  },
  {
    txt: 'N_USA0031-Charleston-Charleston',
    code: 'N_USA0031'
  },
  {
    txt: 'N_USA0032-Charlevoix-Charlevoix',
    code: 'N_USA0032'
  },
  {
    txt: 'N_USA0033-Chesapeake-Chesapeake',
    code: 'N_USA0033'
  },
  {
    txt: 'N_USA0034-Cherry Point-Cherry Point',
    code: 'N_USA0034'
  },
  {
    txt: 'N_USA0035-Clarkston-Clarkston',
    code: 'N_USA0035'
  },
  {
    txt: 'N_USA0036-Clayton-Clayton',
    code: 'N_USA0036'
  },
  {
    txt: 'N_USA0037-Coeymans-Coeymans',
    code: 'N_USA0037'
  },
  {
    txt: 'N_USA0038-Columbia/Snake River System-Columbia/Snake River System',
    code: 'N_USA0038'
  },
  {
    txt: 'N_USA0039-Cold Bay-Cold Bay',
    code: 'N_USA0039'
  },
  {
    txt: 'N_USA0040-哥伦比亚河口-Columbia River Mouth',
    code: 'N_USA0040'
  },
  {
    txt: 'N_UKR0014-尤日内-Yuzhny',
    code: 'N_UKR0014'
  },
  {
    txt: 'N_UKR0015-Zaporizhzhia-Zaporizhzhia',
    code: 'N_UKR0015'
  },
  {
    txt: 'N_UA001-Ahmed Bin Rashid Port-Ahmed Bin Rashid Port',
    code: 'N_UA001'
  },
  {
    txt: 'N_UA002-Al Jeer-Al Jeer',
    code: 'N_UA002'
  },
  {
    txt: 'N_UA003-AL MIRFA-AL MIRFA',
    code: 'N_UA003'
  },
  {
    txt: 'N_UA004-Al Jazeera Port-Al Jazeera Port',
    code: 'N_UA004'
  },
  {
    txt: 'N_UA005-Hamriyah Free Zone Port-Hamriyah Free Zone Port',
    code: 'N_UA005'
  },
  {
    txt: 'N_UA006-Hulaylah-Hulaylah',
    code: 'N_UA006'
  },
  {
    txt: 'N_UA007-Kalba-Kalba',
    code: 'N_UA007'
  },
  {
    txt: 'N_UA008-Khalifa Port-Khalifa Port',
    code: 'N_UA008'
  },
  {
    txt: 'N_UA009-Mina Al Hamriya-Mina Al Hamriya',
    code: 'N_UA009'
  },
  {
    txt: 'N_UA0010-Mubarraz Island-Mubarraz Island',
    code: 'N_UA0010'
  },
  {
    txt: 'N_UA0011-Mubarek Terminal-Mubarek Terminal',
    code: 'N_UA0011'
  },
  {
    txt: 'N_UA0012-Musaffah-Musaffah',
    code: 'N_UA0012'
  },
  {
    txt: 'N_UA0013-拉希德港-Port Rashid',
    code: 'N_UA0013'
  },
  {
    txt: 'N_UA0014-Port of Al Rafiq-Port of Al Rafiq',
    code: 'N_UA0014'
  },
  {
    txt: 'N_UA0015-Ras Al Khaimah-Ras Al Khaimah',
    code: 'N_UA0015'
  },
  {
    txt: 'N_UA0016-Ruwais-Ruwais',
    code: 'N_UA0016'
  },
  {
    txt: 'N_UA0017-Sharjah Offshore Terminal-Sharjah Offshore Terminal',
    code: 'N_UA0017'
  },
  {
    txt: 'N_UA0018-sila-sila',
    code: 'N_UA0018'
  },
  {
    txt: 'N_UA0019-Umm Al Nar Petroleum Port-Umm Al Nar Petroleum Port',
    code: 'N_UA0019'
  },
  {
    txt: 'N_UA0020-Zirku Island-Zirku Island',
    code: 'N_UA0020'
  },
  {
    txt: 'N_GB001-Aberdeen (United Kingdom)-Aberdeen (United Kingdom)',
    code: 'N_GB001'
  },
  {
    txt: 'N_GB002-Aberdovey-Aberdovey',
    code: 'N_GB002'
  },
  {
    txt: 'N_GB003-Arbroath-Arbroath',
    code: 'N_GB003'
  },
  {
    txt: 'N_GB004-阿德里希格-Ardrishaig',
    code: 'N_GB004'
  },
  {
    txt: 'N_GB005-Ardglass-Ardglass',
    code: 'N_GB005'
  },
  {
    txt: 'N_GB006-Ardrossan (United Kingdom)-Ardrossan (United Kingdom)',
    code: 'N_GB006'
  },
  {
    txt: 'N_GB007-埃尔-Ayr',
    code: 'N_GB007'
  },
  {
    txt: 'N_GB008-巴罗因弗内斯-Barrow-in-Furness',
    code: 'N_GB008'
  },
  {
    txt: 'N_GB009-巴瑞-Barry',
    code: 'N_GB009'
  },
  {
    txt: 'N_GB0010-Barrow upon Humber-Barrow upon Humber',
    code: 'N_GB0010'
  },
  {
    txt: 'N_GB0011-贝尔法斯特-Belfast',
    code: 'N_GB0011'
  },
  {
    txt: 'N_GB0012-Berwick-upon-Tweed-Berwick-upon-Tweed',
    code: 'N_GB0012'
  },
  {
    txt: 'N_GB0013-比德福德-Bideford',
    code: 'N_GB0013'
  },
  {
    txt: 'N_GB0014-Bird Port-Bird Port',
    code: 'N_GB0014'
  },
  {
    txt: 'N_GB0015-布莱斯-Blyth',
    code: 'N_GB0015'
  },
  {
    txt: 'N_GB0016-Boston (United Kingdom)-Boston (United Kingdom)',
    code: 'N_GB0016'
  },
  {
    txt: 'N_GB0017-布瑞福特湾港-Braefoot Bay',
    code: 'N_GB0017'
  },
  {
    txt: 'N_GB0018-布里德沃特-Bridgwater',
    code: 'N_GB0018'
  },
  {
    txt: 'N_GB0019-布瑞德林顿-Bridlington',
    code: 'N_GB0019'
  },
  {
    txt: 'N_GB0020-布里斯托尔-Bristol',
    code: 'N_GB0020'
  },
  {
    txt: 'N_GB0021-布莱特林西-Brightlingsea',
    code: 'N_GB0021'
  },
  {
    txt: 'N_GB0022-Brodick Harbour-Brodick Harbour',
    code: 'N_GB0022'
  },
  {
    txt: 'N_GB0023-Buckie-Buckie',
    code: 'N_GB0023'
  },
  {
    txt: 'N_GB0024-巴提斯兰德-Burntisland',
    code: 'N_GB0024'
  },
  {
    txt: 'N_GB0025-Caernarfon-Caernarfon',
    code: 'N_GB0025'
  },
  {
    txt: 'N_GB0026-Cairnryan-Cairnryan',
    code: 'N_GB0026'
  },
  {
    txt: 'N_GB0027-Caledonian Canal-Caledonian Canal',
    code: 'N_GB0027'
  },
  {
    txt: 'N_GB0028-加的夫-Cardiff',
    code: 'N_GB0028'
  },
  {
    txt: 'N_GB0029-Carnlough-Carnlough',
    code: 'N_GB0029'
  },
  {
    txt: 'N_GB0030-Castle Bay-Castle Bay',
    code: 'N_GB0030'
  },
  {
    txt: 'N_GB0031-Charlestown (United Kingdom)-Charlestown (United Kingdom)',
    code: 'N_GB0031'
  },
  {
    txt: 'N_GB0032-考勒瑞恩-Coleraine',
    code: 'N_GB0032'
  },
  {
    txt: 'N_GB0033-Conwy-Conwy',
    code: 'N_GB0033'
  },
  {
    txt: 'N_GB0034-考帕切-Corpach',
    code: 'N_GB0034'
  },
  {
    txt: 'N_GB0035-考威斯-Cowes',
    code: 'N_GB0035'
  },
  {
    txt: 'N_GB0036-Creeksea-Creeksea',
    code: 'N_GB0036'
  },
  {
    txt: 'N_GB0037-Crinan Canal-Crinan Canal',
    code: 'N_GB0037'
  },
  {
    txt: 'N_GB0038-Cromarty Firth-Cromarty Firth',
    code: 'N_GB0038'
  },
  {
    txt: 'N_GB0039-Dartmouth (United Kingdom)-Dartmouth (United Kingdom)',
    code: 'N_GB0039'
  },
  {
    txt: 'N_GB0040-多佛尔-Dover',
    code: 'N_GB0040'
  },
  {
    txt: 'N_GB0041-Dover Strait-Dover Strait',
    code: 'N_GB0041'
  },
  {
    txt: 'N_GB0042-丹巴-Dunbar',
    code: 'N_GB0042'
  },
  {
    txt: 'N_GB0043-邓迪-Dundee',
    code: 'N_GB0043'
  },
  {
    txt: 'N_GB0044-Dunoon-Dunoon',
    code: 'N_GB0044'
  },
  {
    txt: 'N_GB0045-法斯兰-Faslane',
    code: 'N_GB0045'
  },
  {
    txt: 'N_GB0046-费力克斯托-Felixstowe',
    code: 'N_GB0046'
  },
  {
    txt: 'N_GB0047-Fingringhoe-Fingringhoe',
    code: 'N_GB0047'
  },
  {
    txt: 'N_GB0048-芬纳特-Finnart',
    code: 'N_GB0048'
  },
  {
    txt: 'N_GB0049-费什加德-Fishguard',
    code: 'N_GB0049'
  },
  {
    txt: 'N_GB0050-Fishnish-Fishnish',
    code: 'N_GB0050'
  },
  {
    txt: 'N_GB0051-Fleetwood-Fleetwood',
    code: 'N_GB0051'
  },
  {
    txt: 'N_GB0052-Folkestone-Folkestone',
    code: 'N_GB0052'
  },
  {
    txt: 'N_GB0053-Forth Ports-Forth Ports',
    code: 'N_GB0053'
  },
  {
    txt: 'N_GB0054-福韦-Fowey',
    code: 'N_GB0054'
  },
  {
    txt: 'N_GB0055-FPSO Armada Kraken-FPSO Armada Kraken',
    code: 'N_GB0055'
  }
]
