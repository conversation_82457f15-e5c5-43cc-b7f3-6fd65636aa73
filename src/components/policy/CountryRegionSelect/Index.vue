<template>
  <el-select v-model="selected" placeholder="请选择国家/地区" class="w-100" filterable clearable>
    <el-option v-for="(name, value) in options" :key="value" :value="value" :label="name" />
  </el-select>
</template>

<script>
import regions from '@/utils/regions'

export default {
  name: 'CountryRegionSelect',
  props: {
    value: {
      type: [Number, String],
      default: ''
    },
    disabledValues: {
      type: Array,
      default: () => []
    },
    only: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      regions,
      selected: ''
    }
  },
  computed: {
    options() {
      const options = {}
      Object.keys(this.regions).forEach((k) => {
        if (!this.disabledValues.includes(k)) {
          if (this.only.length) {
            if (this.only.includes(k)) {
              options[k] = this.regions[k]
            }
          } else {
            options[k] = this.regions[k]
          }
        }
      })

      return options
    }
  },
  watch: {
    value(value) {
      if (value) {
        this.selected = value
      }
    },
    selected(value) {
      this.$emit('input', value)
    }
  }
}
</script>
