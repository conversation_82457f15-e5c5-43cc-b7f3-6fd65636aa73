<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-10 00:05:29
 * @LastEditors: yanb
 * @LastEditTime: 2024-07-31 16:00:39
-->
<template slot="label">
  <div>
    <policy-product
      :subjects="subjects"
      :products="products"
      @product-change="(p) => (product = p)"
      @subject-change="handleSubjectChange"
      :subject.sync="form.subject_id"
      :subjectCategoryIds.sync="form.subject_category_ids"
      :manualConditions.sync="form.manual_conditions"
      :product.sync="form.product_id"
      :newGoods.sync="form.is_new"
      :isExceptGoods.sync="isExceptGoods"
      class="m-extra-large-b"
    />

    <el-alert
      class="m-extra-large-t transport-alert"
      style="margin-bottom: 20px"
      :closable="false"
      type="error"
      title="本投保界面仅适用于海运、空运、公路、铁路运输方式，不适用于起运地至目的地的运输中包含快递形式的运输（如DHL、FedEx、UPS、顺丰、EMS等快递服务）。"
    >
    </el-alert>

    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-position="top"
      :disabled="product?.id === undefined || isExceptGoods !== 0"
    >
      <form-block class="m-extra-large-b" title="投保人/被保人信息">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="policyholder" label="投保人(同开票抬头)">
              <el-autocomplete
                v-model="form.policyholder"
                :fetch-suggestions="(value, cb) => fetchSuggestions('policyholder', value, cb)"
                @select="selectSuggestion"
                @blur="() => checkBlacklist('投保人', 'name', form.policyholder)"
                placeholder="请输入投保人"
                value="form.policyholder"
                clearable
                class="w-100"
              >
              </el-autocomplete>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="insured">
              <template slot="label">
                被保人 (具有可保利益的货主)
                <span class="m-mini-l text-danger">
                  <el-checkbox v-model="isSame">同投保人</el-checkbox>
                </span>
              </template>
              <el-autocomplete
                v-model="form.insured"
                :fetch-suggestions="(value, cb) => fetchSuggestions('insured', value, cb)"
                @select="selectSuggestion"
                @blur="() => checkBlacklist('被保人', 'name', form.insured)"
                placeholder="请输入被保人 (具有可保利益的货主)"
                clearable
                class="w-100"
              ></el-autocomplete>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="policyholder_type" label="投保人类型" :required="policyHolderTypeRequired">
              <template slot="label" v-if="product?.company?.identifier === 'PICC'">
                投保人类型
                <span class="m-mini-l text-danger" style="margin-right: 5px"> 是否为境外客户 </span>
                <el-radio v-model="form.policyholder_overseas" :label="1">是</el-radio>
                <el-radio v-model="form.policyholder_overseas" :label="0">否</el-radio>
              </template>
              <el-select placeholder="请选择投保人类型" v-model="form.policyholder_type" class="w-100">
                <el-option
                  v-for="type in clientTypes"
                  :key="type.value"
                  :value="type.value"
                  :label="type.label"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="insured_type" label="被保人类型" :required="insuredTypeRequired">
              <template slot="label" v-if="product?.company?.identifier === 'PICC'">
                被保人类型
                <span class="m-mini-l text-danger" style="margin-right: 5px"> 是否为境外客户 </span>
                <el-radio v-model="form.insured_overseas" :label="1">是</el-radio>
                <el-radio v-model="form.insured_overseas" :label="0">否</el-radio>
              </template>
              <el-select placeholder="请选择被保人类型" v-model="form.insured_type" class="w-100">
                <el-option
                  v-for="type in clientTypes"
                  :key="type.value"
                  :value="type.value"
                  :label="type.label"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item
              prop="policyholder_idcard_no"
              :label="policyholderIdCardNoLabel"
              :required="policyHolderIdCardNoRequired"
            >
              <el-input
                v-model="form.policyholder_idcard_no"
                placeholder="请输入投保人证件号码"
                @blur="() => checkBlacklist('投保人证件号码', 'idcard_no', form.policyholder_idcard_no)"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="insured_idcard_no" :label="insuredIdCardNoLabel" :required="insuredIdCardNoRequired">
              <el-input
                v-model="form.insured_idcard_no"
                @blur="() => checkBlacklist('被保人证件号码', 'idcard_no', form.insured_idcard_no)"
                placeholder="请输入被保人证件号码"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48" v-if="idcardDateShow">
          <el-col :span="12" v-show="idcardDateShow">
            <el-form-item
              prop="policyholder_idcard_issue_date"
              label="投保人证件有效起始时间"
              :required="policyholderIdcardDateRequired"
              :style="
                form.policyholder_type === 0 && product?.company?.identifier === 'PICC' ? 'visibility: hidden' : ''
              "
            >
              <el-date-picker
                v-model="form.policyholder_idcard_issue_date"
                class="w-100"
                type="date"
                placeholder="请选择投保人证件有效起始时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-show="idcardDateShow">
            <el-form-item
              prop="insured_idcard_issue_date"
              label="被保人证件有效起始时间"
              :required="insuredIdcardDateRequired"
              :style="form.insured_type === 0 && product?.company?.identifier === 'PICC' ? 'visibility: hidden' : ''"
            >
              <el-date-picker
                v-model="form.insured_idcard_issue_date"
                class="w-100"
                type="date"
                placeholder="请选择被保人证件有效起始时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48" v-if="idcardDateShow">
          <el-col :span="12" v-show="idcardDateShow">
            <el-form-item
              prop="policyholder_idcard_valid_till"
              label="投保人证件有效结束时间"
              :required="policyholderIdcardDateRequired"
              :style="
                form.policyholder_type === 0 && product?.company?.identifier === 'PICC' ? 'visibility: hidden' : ''
              "
            >
              <template slot="label">
                投保人证件有效结束时间
                <span class="m-mini-l text-danger">
                  <el-checkbox v-model="form.policyholder_forever" :true-label="1" :false-label="0">长期</el-checkbox>
                </span>
              </template>
              <el-date-picker
                v-model="form.policyholder_idcard_valid_till"
                class="w-100"
                type="date"
                placeholder="请选择投保人证件有效结束时间"
                :disabled="form.policyholder_forever === 1"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-show="idcardDateShow">
            <el-form-item
              prop="insured_idcard_valid_till"
              label="被保人证件有效结束时间"
              :required="insuredIdcardDateRequired"
              :style="form.insured_type === 0 && product?.company?.identifier === 'PICC' ? 'visibility: hidden' : ''"
            >
              <template slot="label">
                被保人证件有效结束时间
                <span class="m-mini-l text-danger">
                  <el-checkbox v-model="form.insured_forever" :true-label="1" :false-label="0">长期</el-checkbox>
                </span>
              </template>
              <el-date-picker
                v-model="form.insured_idcard_valid_till"
                class="w-100"
                type="date"
                placeholder="请选择被保人证件有效结束时间"
                :disabled="form.insured_forever === 1"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item
              prop="policyholder_phone_number"
              label="投保人电话"
              :required="policyholderPhoneNumberRequired"
            >
              <el-input v-model="form.policyholder_phone_number" placeholder="请输入投保人电话" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="insured_phone_number" label="被保人电话" :required="insuredPhoneNumberRequired">
              <el-input v-model="form.insured_phone_number" placeholder="请输入被保人电话" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="policyholder_address" label="投保人地址" :required="policyholderAddressRequired">
              <input-address v-model="form.policyholder_address" :company="product?.company?.identifier" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="insured_address" label="被保人地址" :required="insuredAddressRequired">
              <input-address v-model="form.insured_address" :company="product?.company?.identifier" />
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" title="货物信息">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="goods_type_id" label="货物分类">
              <goods-types :productId.sync="product.id" v-model="form.goods_type_id" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="packing_method_id" label="包装方式">
              <packing-methods :productId.sync="product.id" v-model="form.packing_method_id" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="goods_name" label="货物名称">
              <el-input
                v-model="form.goods_name"
                placeholder="请输入货物名称"
                type="textarea"
                autoresize
                :rows="3"
                @blur="handleValidateGoodsName"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="goods_amount" label="包装件数">
              <el-input
                v-model="form.goods_amount"
                placeholder="请输入包装件数"
                :type="product?.company?.identifier === 'TPIC' ? 'number' : 'textarea'"
                autoresize
                :rows="3"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b">
        <template #title>
          <span>运输信息</span>
          <el-popover placement="top-start" title="投保提醒" width="520" trigger="hover">
            <template>
              <p>
                （1）船名航次/航班号/车牌号：根据运输工具填写，例如：船名/航次 ABB CCC/088E，航班号 MU8888，车牌号
                沪A8G888。若暂无运输工具信息，先填写“待定”；后续运输工具信息确定后，请联系业务人员批改承保信息。
              </p>
              <p>（2）起运地/目的地描述：如门到门，填写仓库所在城市名称即可，如上海、洛杉矶(Los Angeles)。</p>
              <p>
                （3）起运时间：按运输条款的起运时间填写。如仓至仓条款，起运时间填写工厂或仓库装车起运时间；如港至港，则填写大船起运时间。
              </p>
            </template>
            <span slot="reference" style="margin-left: 5px; font-size: 12px; color: #ff7f4c">
              如何填写<i class="fas fa-question"></i>
            </span>
          </el-popover>
        </template>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="transport_method_id" label="运输方式">
              <transport-methods
                :productId.sync="product.id"
                defaultMethod="公路运输"
                @loadingMethods="(m) => (loadingMethods = m)"
                v-model="form.transport_method_id"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="loading_method_id" label="装载方式">
              <loading-methods
                :productId.sync="product.id"
                :loading-methods="loadingMethods"
                v-model="form.loading_method_id"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="departure" label="起运地">
              <div class="d-flex w-100">
                <el-cascader
                  v-model="form.departure"
                  :style="{ width: '500px' }"
                  :options="regions"
                  :props="{
                    expandTrigger: 'hover',
                    filterable: true,
                    value: 'value',
                    label: 'value',
                    children: 'city'
                  }"
                ></el-cascader>
                <tpic-ports
                  v-model="form.departure_port"
                  :suggestion="form.departure"
                  class="m-mini-l"
                  style="min-width: 150px"
                  v-if="product?.company?.identifier === 'TPIC'"
                />
                <el-input
                  type="textarea"
                  autosize
                  placeholder="保单地址"
                  v-model="form.departure_addr"
                  class="flex-fill m-mini-l"
                  clearable
                ></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="destination" label="目的地">
              <div class="d-flex w-100">
                <el-cascader
                  v-model="form.destination"
                  :style="{ width: '500px' }"
                  :options="regions"
                  :props="{
                    expandTrigger: 'hover',
                    filterable: true,
                    value: 'value',
                    label: 'value',
                    children: 'city'
                  }"
                ></el-cascader>
                <tpic-ports
                  v-model="form.destination_port"
                  :suggestion="form.destination"
                  class="m-mini-l"
                  style="min-width: 150px"
                  v-if="product?.company?.identifier === 'TPIC'"
                />
                <el-input
                  type="textarea"
                  autosize
                  placeholder="保单地址"
                  v-model="form.destination_addr"
                  class="flex-fill m-mini-l"
                  clearable
                ></el-input>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="transmit" label="中转地">
              <div class="d-flex w-100">
                <el-cascader
                  :style="{ width: '500px' }"
                  clearable
                  v-model="form.transmit"
                  :options="regions"
                  :props="{
                    expandTrigger: 'hover',
                    filterable: true,
                    value: 'value',
                    label: 'value',
                    children: 'city'
                  }"
                />
                <tpic-ports
                  v-model="form.transmit_port"
                  :suggestion="form.transmit"
                  class="m-mini-l"
                  style="min-width: 150px"
                  v-if="product?.company?.identifier === 'TPIC'"
                />
                <el-input
                  type="textarea"
                  autosize
                  placeholder="保单地址"
                  v-model="form.transmit_addr"
                  class="flex-fill m-mini-l"
                  clearable
                ></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="transport_no">
              <template slot="label">
                <span>运输工具号</span>
                <span class="text-danger">（请务必填写真实信息，否则可能影响理赔）</span>
              </template>
              <el-input v-model="form.transport_no" placeholder="请输入运输工具号" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="invoice_no">
              <template slot="label">
                <span>发票号</span>
                <span class="text-danger" v-if="product?.company?.identifier !== 'PICC'"
                  >（运单号和发票号必选填一个）</span
                >
              </template>
              <el-input v-model="form.invoice_no" placeholder="请输入发票号" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="waybill_no">
              <template slot="label">
                <span>运单号</span>
                <span class="text-danger" v-if="product?.company?.identifier !== 'PICC'"
                  >（运单号和发票号必选填一个）</span
                >
              </template>
              <el-input v-model="form.waybill_no" placeholder="请输入运单号" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="shipping_date" label="起运日期">
              <el-date-picker
                :picker-options="shippingDateOptions"
                v-model="form.shipping_date"
                class="w-100"
                type="date"
                placeholder="请选择起运日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="product?.company?.identifier === 'PICC' && form.transport_method_id == 2">
            <el-form-item prop="ship_construction_year" label="船舶建造年">
              <el-date-picker
                v-model="form.ship_construction_year"
                class="w-100"
                type="year"
                placeholder="请选择船舶建造年"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="anti_dated_file" v-if="showAntiDatedFileUploader">
              <template slot="label">
                <span>倒签保函</span>
                <span class="text-danger">
                  <el-link class="m-mini-l" :href="product?.additional?.anti_dated_file">
                    <small style="color: red">点击下载倒签保函</small>
                  </el-link>
                </span>
              </template>
              <div class="d-flex">
                <upload-file v-model="form.anti_dated_file"></upload-file>
                <el-link
                  v-if="typeof form.anti_dated_file === 'string' && form.anti_dated_file"
                  class="m-extra-large-l"
                  :href="form.anti_dated_file"
                >
                  点击查看
                </el-link>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" title="费用信息">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="coverage" label="保险金额（元）">
              <el-input
                clearable
                type="number"
                v-model="form.coverage"
                placeholder="请输入保险金额"
                @focus.once="() => ($route.query.from === 'edit' ? $alert('修改保险金额需要上传盖章发票', '提醒') : '')"
              >
              </el-input>
              <span>{{ form.coverage | chineseCoverage }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-alert
              v-if="form.coverage > product?.additional?.coverage"
              title="保险金额超出提醒"
              description="保险金额超限请转人工审核进行询价"
              show-icon
              :closable="false"
              type="warning"
            >
            </el-alert>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" title="其他信息">
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="sticky_note" label="工作编号">
              <el-input
                v-model="form.sticky_note"
                placeholder="此内容不作为投保使用，仅方便您核对账单使用"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="custom_file" label="投保附件">
              <div class="d-flex">
                <upload-file v-model="form.custom_file"></upload-file>
                <el-link
                  v-if="typeof form.custom_file === 'string' && form.custom_file"
                  class="m-extra-large-l"
                  :href="form.custom_file"
                >
                  点击查看
                </el-link>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="remark">
              <template slot="label">
                <span>备注</span>
                <span class="text-danger">（以下内容不展示在保单上）</span>
              </template>
              <el-input
                v-model="form.remark"
                type="textarea"
                autoresize
                :rows="3"
                placeholder="备注不作为投保依据"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" center>
        <div class="m-extra-large-b">
          <el-checkbox @change="disabledSubmit = !disabledSubmit" slot="title"></el-checkbox>
          <span style="cursor: pointer" @click="showNotice = !showNotice"> &nbsp;我已详细阅读《投保须知》的内容 </span>
        </div>

        <template v-if="showNotice">
          <el-alert title="投保须知" type="warning" class="m-extra-large-b" :closable="false">
            <template v-if="product.additional !== undefined">
              <div style="text-align: left" v-html="product.additional.notice"></div>
            </template>
          </el-alert>
        </template>
        <el-button icon="far fa-save" @click="handleSave" v-if="$route.query.from !== 'edit'">暂存</el-button>
        <el-button type="primary" icon="el-icon-document-checked" :disabled="disabledSubmit" @click="submitPreview">
          提交预览
        </el-button>
      </form-block>
    </el-form>
    <inform :visible.sync="informVisible" :detail="product?.additional?.inform || ''" @checking="checkingInform" />
  </div>
</template>

<script>
import areadata from '@/utils/areadata.json'
import PolicyProduct from '@/components/policy/PolicyDomesticProduct'
import GoodsTypes from '@/components/policy/GoodsTypes'
import PackingMethods from '@/components/policy/PackingMethods'
import LoadingMethods from '@/components/policy/LoadingMethods'
import TransportMethods from '@/components/policy/TransportMethods'
import Inform from '@/components/policy/Inform'
import { getSubjects } from '@/apis/subject'
import { getProducts } from '@/apis/product'
import { checkIfExist, getSuggestions, getGoodsSuggestion } from '@/apis/policy'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'
import { digitUppercase } from '@/utils'
import AddressParser from '@/utils/addressparser'
import TPICPorts from '@/components/policy/TpicPorts'
import InputAddress from '@/components/policy/InputAddress'
import * as blacklistApi from '@/apis/blacklist'

const PRODUCT_TYPE = 1

export default {
  name: 'DomesticForm',
  components: {
    PolicyProduct,
    GoodsTypes,
    PackingMethods,
    LoadingMethods,
    TransportMethods,
    'tpic-ports': TPICPorts,
    Inform,
    'input-address': InputAddress
  },
  props: {
    policy: {
      type: Object,
      default: () => {}
    }
  },
  filters: {
    chineseCoverage(value) {
      return digitUppercase(value)
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    showAntiDatedFileUploader() {
      const antiDatedDays = this.antiDatedDays
      const shippingDate = dayjs(this.form.shipping_date)
      if (antiDatedDays > 0 && shippingDate.isValid()) {
        return dayjs().isAfter(shippingDate.add(antiDatedDays, 'day'))
      }

      return false
    },
    shippingDateOptions() {
      const antiDatedDays = this.antiDatedDays
      return {
        disabledDate(time) {
          if (antiDatedDays === undefined || antiDatedDays <= 0) {
            return time.getTime() <= dayjs(Date.now()).subtract(1, 'day').toDate()
          }
        }
      }
    },
    regions() {
      const disabledRegions = this.product?.additional?.disabled_regions?.split('|') || []
      return areadata.filter((d) => {
        return !disabledRegions.includes(d.value)
      })
    },
    policyholderIdCardNoLabel() {
      if (this.form.policyholder_type === 1) {
        if (this.form.policyholder_overseas === 1) {
          return '投保人护照号'
        } else {
          return '投保人身份证号'
        }
      } else {
        return '投保人统一社会信用代码'
      }
    },
    insuredIdCardNoLabel() {
      if (this.form.insured_type === 1) {
        if (this.form.insured_overseas === 1) {
          return '被保人护照号'
        } else {
          return '被保人身份证号'
        }
      } else {
        return '被保人统一社会信用代码'
      }
    },
    policyHolderTypeRequired() {
      if (['HUATAI', 'TPIC', 'DIC'].includes(this.product?.company?.identifier)) {
        return true
      }
      return false
    },
    policyHolderIdCardNoRequired() {
      if (['HUATAI', 'TPIC', 'DIC'].includes(this.product?.company?.identifier)) {
        return true
      }
      if (this.product?.company?.identifier == 'PICC' && this.form.policyholder_type == 1) {
        return true
      }
      return false
    },
    policyholderIdcardDateRequired() {
      if (['DIC'].includes(this.product?.company?.identifier)) {
        return true
      }
      if (
        this.product?.company?.identifier == 'PICC' &&
        this.form.policyholder_type == 1 &&
        this.form.policyholder_overseas == 0
      ) {
        return true
      }
      return false
    },
    policyholderPhoneNumberRequired() {
      if (['DIC'].includes(this.product?.company?.identifier)) {
        return true
      }
      return false
    },
    policyholderAddressRequired() {
      if (['DIC'].includes(this.product?.company?.identifier)) {
        return true
      }
      return false
    },
    policyholderIdCardDateStyle() {
      if (this.product?.company?.identifier === 'PICC' && this.form.policyholder_type === 0) {
        return 'visibility: hidden'
      }
      return ''
    },
    insuredTypeRequired() {
      if (['HUATAI', 'TPIC', 'DIC'].includes(this.product?.company?.identifier)) {
        return true
      }
      return false
    },
    insuredIdCardNoRequired() {
      if (['HUATAI', 'TPIC', 'DIC'].includes(this.product?.company?.identifier)) {
        return true
      }
      if (this.product?.company?.identifier == 'PICC' && this.form.insured_type == 1) {
        return true
      }
      return false
    },
    insuredIdcardDateRequired() {
      if (
        this.product?.company?.identifier == 'PICC' &&
        this.form.insured_type == 1 &&
        this.form.insured_overseas == 0
      ) {
        return true
      }
      return false
    },
    insuredPhoneNumberRequired() {
      return false
    },
    insuredAddressRequired() {
      return false
    },
    insuredIdCardDateStyle() {
      if (this.product?.company?.identifier === 'PICC' && this.form.insured_type === 0) {
        return 'visibility: hidden'
      }
      return ''
    },
    idcardDateShow() {
      if (['DIC'].includes(this.product?.company?.identifier)) {
        return true
      }
      if (
        this.product?.company?.identifier === 'PICC' &&
        (this.form.policyholder_type == 1 || this.form.insured_type == 1)
      ) {
        return true
      }
      return false
    },
    antiDatedDays() {
      let date = this?.product?.additional?.anti_dated_days
      if (this?.product?.additional?.anti_date_is_in_transports == 1) {
        date = this?.product?.additional?.anti_date_transport_data.find(
          (item) => item.transport_id == this.form.transport_method_id
        )?.value
      }
      return date
    }
  },
  data() {
    return {
      isSame: false,
      isExceptGoods: null,
      showNotice: false,
      subjects: [],
      products: {},
      product: {},
      disabledSubmit: true,
      informVisible: false,
      loadingMethods: [],
      clientTypes: [
        {
          label: '个人客户',
          value: 1
        },
        {
          label: '团体客户',
          value: 0
        }
      ],
      lastGoodsName: '',
      form: {
        type: PRODUCT_TYPE,
        is_new: -1,
        subject_id: -1,
        manual_conditions: '',
        subject_category_ids: [],
        product_id: -1,
        policyholder: '',
        policyholder_address: '',
        policyholder_phone_number: '',
        policyholder_type: 0,
        policyholder_overseas: 0,
        policyholder_idcard_no: '',
        policyholder_idcard_issue_date: '',
        policyholder_idcard_valid_till: '',
        insured: '',
        insured_address: '',
        insured_phone_number: '',
        insured_type: 0,
        insured_overseas: 0,
        insured_idcard_no: '',
        insured_idcard_issue_date: '',
        insured_idcard_valid_till: '',
        sticky_note: '',
        goods_type_id: '',
        packing_method_id: '',
        loading_method_id: '',
        goods_name: '',
        goods_amount: '',
        transport_method_id: '',
        departure: [],
        departure_port: '',
        departure_addr: '',
        destination: [],
        destination_port: '',
        destination_addr: '',
        transmit: [],
        transmit_port: '',
        transmit_addr: '',
        transport_no: '',
        invoice_no: '',
        waybill_no: '',
        anti_dated_file: '',
        shipping_date: '',
        coverage: '',
        remark: '',
        custom_file: '',
        ship_construction_year: '',
        execpt_goods_alert: [],
        with_user_special_and_deductible: 0,
        deductible: '',
        special_agreement: ''
      },
      rules: {
        policyholder: [
          { required: true, message: '请输入投保人姓名', trigger: 'blur' },
          { min: 1, max: 128, message: '长度在 1 到 128 个字符', trigger: 'blur' }
        ],
        insured: [
          { required: true, message: '请输入被保人姓名', trigger: 'blur' },
          { min: 1, max: 128, message: '长度在 1 到 128 个字符', trigger: 'blur' }
        ],
        insured_address: [
          {
            validator: (rule, value, callback) => {
              if (new TextEncoder().encode(`${value} ${this.form.insured_phone_number}`).length > 150) {
                callback(new Error('被保人地址+电话长度不能超过150个字符'))
              } else {
                callback()
              }
            }
          }
        ],
        insured_phone_number: [
          {
            validator: (rule, value, callback) => {
              if (new TextEncoder().encode(`${value} ${this.form.insured_address}`).length > 150) {
                callback(new Error('被保人地址+电话长度不能超过150个字符'))
              } else {
                callback()
              }
            }
          }
        ],
        policyolder_phone_number: [
          {
            validator: (rule, value, callback) => {
              if (new TextEncoder().encode(`${value}${this.form.policyholder_address}`).length > 150) {
                callback(new Error('投保人地址+电话长度不能超过150个字符'))
              } else {
                callback()
              }
            }
          }
        ],
        policyholder_address: [
          {
            validator: (rule, value, callback) => {
              if (new TextEncoder().encode(`${value}${this.form.policyholder_phone_number}`).length > 150) {
                callback(new Error('投保人地址+电话长度不能超过150个字符'))
              } else {
                callback()
              }
            }
          }
        ],
        goods_type_id: [{ required: true, message: '请选择货物类别', trigger: ['change', 'blur'] }],
        packing_method_id: [{ required: true, message: '请选择包装方式', trigger: 'change' }],
        goods_name: [{ required: true, message: '请输入货物名称', trigger: 'blur' }],
        goods_amount: [{ required: true, message: '请输入包装件数', trigger: 'blur' }],
        transport_method_id: [{ required: true, message: '请选择运输方式', trigger: 'change' }],
        loading_method_id: [{ required: true, message: '请选择装载方式', trigger: 'change' }],
        departure: [{ required: true, message: '请选择起运地', trigger: 'change' }],
        // departure_addr:
        //   this.$route.query.from !== 'edit'
        //     ? [{ required: true, message: '请输入起运地保单地址', trigger: 'blur' }]
        //     : [],
        destination: [{ required: true, message: '请选择目的地', trigger: 'change' }],
        // destination_addr:
        //   this.$route.query.from !== 'edit'
        //     ? [{ required: true, message: '请输入目的地保单地址', trigger: 'blur' }]
        //     : [],
        transport_no: [
          { required: true, message: '请输入运输工具号', trigger: 'blur' },
          { min: 1, max: 128, message: '运输工具号长度错误', trigger: 'blur' }
        ],
        shipping_date: [{ required: true, type: 'date', message: '请选择起运日期', trigger: 'change' }],
        coverage: [{ required: true, message: '请输入保险金额', trigger: 'blue' }],
        invoice_no: [
          { min: 2, message: '发票号长度错误', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (
                this.form.waybill_no === '' &&
                this.form.invoice_no === '' &&
                this.product?.company?.identifier !== 'PICC'
              ) {
                callback(new Error('当运单号为空时发票号必填'))
              } else {
                if (value) {
                  checkIfExist({
                    type: PRODUCT_TYPE,
                    policy_id: this.$route.query.from !== 'copy' ? this.$route.params.id : null,
                    company: this.product?.company?.identifier,
                    column: 'invoice_no',
                    value: value
                  }).then((r) => {
                    r.is_exist ? callback(new Error('发票号已经存在')) : callback()
                  })
                } else {
                  callback()
                }

                this.$refs.form.clearValidate('waybill_no')
              }
            },
            trigger: 'blur'
          }
        ],
        waybill_no: [
          { min: 2, message: '运单号长度错误', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (
                this.form.invoice_no === '' &&
                this.form.waybill_no === '' &&
                this.product?.company?.identifier !== 'PICC'
              ) {
                callback(new Error('当发票号为空时运单号必填'))
              } else {
                if (value) {
                  checkIfExist({
                    type: PRODUCT_TYPE,
                    policy_id: this.$route.query.from !== 'copy' ? this.$route.params.id : null,
                    company: this.product?.company?.identifier,
                    column: 'waybill_no',
                    value: value
                  }).then((r) => {
                    r.is_exist ? callback(new Error('运单号已经存在')) : callback()
                  })
                } else {
                  callback()
                }

                this.$refs.form.clearValidate('invoice_no')
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    policy: {
      deep: true,
      immediate: true,
      handler(value) {
        if (Object.keys(value).length > 0) {
          this.form = Object.assign({}, this.form, value)
        }
      }
    },
    'form.policyholder_type'(value) {
      if (value === 1 && this.form.policyholder_overseas === 0) {
        this.rules.policyholder_idcard_issue_date = [
          { required: true, type: 'date', message: '请投保人证件有效起始时间', trigger: 'change' }
        ]
        this.rules.policyholder_idcard_valid_till = [
          { required: true, type: 'date', message: '请投保人证件有效结束时间', trigger: 'change' }
        ]
      } else {
        delete this.rules.policyholder_idcard_issue_date
        delete this.rules.policyholder_idcard_valid_till
      }
      this.$refs.form.clearValidate()
    },
    'form.policyholder_overseas'(value) {
      if (value === 1 && this.form.policyholder_type === 0) {
        delete this.rules.policyholder_idcard_no
      } else {
        this.rules.policyholder_idcard_no = [
          { required: false, message: '请输入投保人证件号', trigger: 'blur' },
          { min: 18, max: 18, message: '投保人证件号格式错误', trigger: 'blur' }
        ]
      }
      if (value === 1 && this.form.policyholder_type === 1) {
        delete this.rules.policyholder_idcard_issue_date
        delete this.rules.policyholder_idcard_valid_till
      }
      this.$refs.form.clearValidate()
    },
    'form.insured_type'(value) {
      if (value === 1 && this.form.insured_overseas == 0) {
        this.rules.insured_idcard_issue_date = [
          { required: true, type: 'date', message: '请被保人证件有效起始时间', trigger: 'change' }
        ]
        this.rules.insured_idcard_valid_till = [
          { required: true, type: 'date', message: '请被保人证件有效结束时间', trigger: 'change' }
        ]
      } else {
        delete this.rules.insured_idcard_issue_date
        delete this.rules.insured_idcard_valid_till
      }
      if (value === 1 && this.user.platform_id === 6) {
        this.form.insured_phone_number = '111111'
        this.form.insured_address = '代理'
        this.form.insured_idcard_issue_date = new Date('2020-09-01')
        this.form.insured_idcard_valid_till = new Date('2030-09-01')
      }
      this.$refs.form.clearValidate()
    },
    'form.insured_overseas'(value) {
      if (value === 1 && this.form.insured_type === 0) {
        delete this.rules.insured_idcard_no
      } else {
        this.rules.insured_idcard_no = [
          { required: false, message: '请输入被保人证件号', trigger: 'blur' },
          { min: 18, max: 18, message: '被保人证件号格式错误', trigger: 'blur' }
        ]
      }
      if (value === 1 && this.form.insured_type === 1) {
        delete this.rules.insured_idcard_issue_date
        delete this.rules.insured_idcard_valid_till
      }
      this.$refs.form.clearValidate()
    },
    'form.subject_id'() {
      this.fetchProducts()
    },
    // hack: 线上环境不知道为什么 subject_id 变更的时候 product_id 还是 1 按理来说应该是同步的
    'form.product_id'(value, oldValue) {
      if (this.$route.query.from === 'edit' && oldValue === -1) {
        this.fetchProducts()
      }
    },
    product(value) {
      if (value.id !== this.form.product_id) {
        this.form.transport_method_id = ''
        this.form.packing_method_id = ''
        this.form.loading_method_id = ''
        this.form.goods_type_id = ''
        this.form.product_id = value.id
      }

      // 验证货物名称
      this.form.execpt_goods_alert = []
      this.handleValidateGoodsName(true)

      if (value?.company?.identifier == 'PICC') {
        this.rules.policyholder_type = [{ required: false, message: '请选择投保人类型', trigger: 'change' }]
        if (this.form.policyholder_overseas === 1 && this.form.policyholder_type === 0) {
          delete this.rules.policyholder_idcard_no
        } else {
          this.rules.policyholder_idcard_no = [
            { required: false, message: '请输入投保人证件号', trigger: 'blur' },
            { min: 18, max: 18, message: '投保人证件号格式错误', trigger: 'blur' }
          ]
        }
        this.rules.insured_type = [{ required: false, message: '请选择被保人类型', trigger: 'change' }]
        if (this.form.insured_overseas === 1 && this.form.insured_type === 0) {
          delete this.rules.insured_idcard_no
        } else {
          this.rules.insured_idcard_no = [
            { required: false, message: '请输入被保人证件号', trigger: 'blur' },
            { min: 18, max: 18, message: '被保人证件号格式错误', trigger: 'blur' }
          ]
        }
      } else if (['HUATAI', 'PINGAN', 'TPIC', 'DIC', 'CPIC'].includes(value.company?.identifier)) {
        this.rules.policyholder_idcard_no = [
          { required: true, message: '请输入投保人证件号', trigger: 'blur' },
          { min: 18, max: 18, message: '投保人证件号格式错误', trigger: 'blur' }
        ]
        this.rules.insured_idcard_no = [
          { required: true, message: '请输入被保人证件号', trigger: 'blur' },
          { min: 18, max: 18, message: '被保人证件号格式错误', trigger: 'blur' }
        ]
      } else {
        this.form.policyholder_type = 0
        this.form.policyholder_idcard_no = ''
        this.form.policyholder_idcard_issue_date = ''
        this.form.policyholder_idcard_valid_till = ''
        this.form.insured_type = 0
        this.form.insured_idcard_no = ''
        this.form.insured_idcard_issue_date = ''
        this.form.insured_idcard_valid_till = ''
        delete this.rules.policyholder_type
        delete this.rules.policyholder_idcard_no
        delete this.rules.insured_type
        delete this.rules.insured_idcard_no
      }
      this.$refs.form.clearValidate()
    },
    'form.policyholder': {
      deep: true,
      immediate: true,
      handler(value) {
        if (this.isSame) {
          this.form.insured = value
        }
      }
    },
    'form.policyholder_phone_number': {
      deep: true,
      immediate: true,
      handler(value) {
        if (this.isSame) {
          this.form.insured_phone_number = value
        }
      }
    },
    'form.policyholder_address': {
      deep: true,
      immediate: true,
      handler(value) {
        if (this.isSame) {
          this.form.insured_address = value
        }
      }
    },
    isSame(value) {
      if (value) {
        this.form.insured = this.form.policyholder
        this.form.insured_phone_number = this.form.policyholder_phone_number
        this.form.insured_address = this.form.policyholder_address
        this.form.insured_idcard_no = this.form.policyholder_idcard_no
        this.form.insured_type = this.form.policyholder_type
        this.form.insured_overseas = this.form.policyholder_overseas
        this.form.insured_idcard_issue_date = this.form.policyholder_idcard_issue_date
        this.form.insured_idcard_valid_till = this.form.policyholder_idcard_valid_till
      }
    },
    'form.shipping_date': {
      deep: true,
      immediate: true,
      handler(value) {
        if (this.antiDatedDays > 0 && this.$route.query.from !== 'edit') {
          if (dayjs().isAfter(dayjs(value).add(this.antiDatedDays, 'day'))) {
            this.$set(this.rules, 'anti_dated_file', [
              { required: true, message: '请上传倒签保函', trigger: ['blur', 'change'] }
            ])
          } else {
            this.$delete(this.rules, 'anti_dated_file')
          }

          this.$refs.form.clearValidate('anti_dated_file')
        }
      }
    },
    'form.departure_addr'(value, oldValue) {
      if (!oldValue && this.$route.query.from !== undefined) {
        return
      }

      const { province, city } = AddressParser.parse(value)
      if (city && this.form.departure[1] !== city) {
        this.form.departure = [province, city]
      }
    },
    'form.destination_addr'(value, oldValue) {
      if (!oldValue && this.$route.params.from !== undefined) {
        return
      }

      const { province, city } = AddressParser.parse(value)
      if (city && this.form.destination[1] !== city) {
        this.form.destination = [province, city]
      }
    },
    'form.transmit_addr'(value, oldValue) {
      if (!oldValue && this.$route.params.from !== undefined) {
        return
      }
      const { province, city } = AddressParser.parse(value)
      if (city && this.form.transmit[1] !== city) {
        this.form.transmit = [province, city]
      }
    },
    'form.policyholder_forever'(value) {
      if (value === 1) {
        var date = '9999-12-31'
        if (this.product?.company?.identifier == 'PICC') {
          date = '2199-12-31'
        }
        this.form.policyholder_idcard_valid_till = new Date(date)
      } else {
        this.form.policyholder_idcard_valid_till = ''
      }
    },
    'form.insured_forever'(value) {
      if (value === 1) {
        var date = '9999-12-31'
        if (this.product?.company?.identifier == 'PICC') {
          date = '2199-12-31'
        }
        this.form.insured_idcard_valid_till = new Date(date)
      } else {
        this.form.insured_idcard_valid_till = ''
      }
    },
    'form.coverage'(value) {
      if (value >= 1000000 && this.form.transport_method_id == 2 && this.product?.company?.identifier === 'PICC') {
        this.$set(this.rules, 'ship_construction_year', [
          { required: true, message: '请填写船舶建造年', trigger: ['blur', 'change'] }
        ])
      } else {
        delete this.rules.ship_construction_year
      }
    }
  },
  created() {
    if (this.$route.query.from === undefined) {
      this.form.policyholder = this.user.name
      this.form.shipping_date = Date.now()
    }

    getSubjects().then((r) => (this.subjects = r.data))
  },
  methods: {
    async checkBlacklist(name, column, value) {
      try {
        const { data } = await blacklistApi.check({
          column,
          value,
          product_type: PRODUCT_TYPE
        })

        if (data.is_blocked) {
          this.$alert(
            `<b class="text-danger">${name}为黑名单用户，请立即停止投保！</b><br />  任何试图规避规则或利用系统漏洞的行为均将被系统完整记录并追踪。对于经核实的恶意投保行为，本公司保留依法追责的权利。如需正常投保，请联系客服核实相关信息。`,
            `信息异常`,
            {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定'
            }
          )
        }
      } catch (e) {
        console.log(e)
      }
    },
    async handleValidateGoodsName(force = true) {
      this.form.with_user_special_and_deductible = 0

      // 排除人工审核
      if (this.form.subject_id === 3) {
        return
      }

      if (this.form.goods_name && (this.form.goods_name !== this.lastGoodsName || force)) {
        this.lastGoodsName = this.form.goods_name
        const { data } = await getGoodsSuggestion({
          subject_id: this.form.subject_id,
          company_branch_id: this.product?.company?.branch?.id || 0,
          product_type: PRODUCT_TYPE,
          keyword: this.form.goods_name
        })

        if (data.condition.matched) {
          const message =
            data.condition.action === 1
              ? `根据您所填写货物名称，系统判定此货物<span class="text-danger">${
                  data.condition.keyword
                }</span>需要附加免赔和特别约定，请确认是否附加？若不附加，请选择<span class="text-danger">人工审核</span>投保。<br />免赔：${
                  data.condition.deductible || ''
                }<br />特别约定：${data.condition.special_agreement || ''}`
              : `根据您所填写货物名称，系统判定此货物<span class="text-danger">${data.condition.keyword}</span>需要转人工审核投保，请确认是否转人工审核？`
          this.$confirm(message, '投保提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            dangerouslyUseHTMLString: true
          })
            .then(() => {
              this.form.with_user_special_and_deductible = 1
              if (data.condition.action === 1) {
                this.form.deductible = data.condition.deductible
                this.form.special_agreement = data.condition.special_agreement
              }
              // 转人工审核
              if (data.condition.action === 2) {
                this.form.subject_id = 3
                this.form.with_user_special_and_deductible = 0
                this.form.deductible = ''
                this.form.special_agreement = ''
              }
            })
            .catch(() => {
              this.form.deductible = ''
              this.form.special_agreement = ''
              // 转人工审核
              if (data.condition.action === 2) {
                this.form.goods_name = ''
                this.lastGoodsName = ''
              } else {
                this.form.with_user_special_and_deductible = 0
                this.form.subject_id = 3
              }
            })
        } else {
          this.form.deductible = ''
          this.form.special_agreement = ''
        }

        if (data.subject.subject) {
          const description =
            data.subject.subject === '人工审核'
              ? `根据您所填写货物名称，系统判断此货物可能为<span class="text-danger">人工审核货物</span>，请再次确认您的货物是否为<span class="text-danger">人工审核货物</span>？如果为<span class="text-danger">人工审核货物</span>，请转至<span class="text-danger">人工审核</span>端口投保或联系业务人员咨询。`
              : `根据您所填写货物名称，系统判断此货物可能为<span class="text-danger">${data.subject.subject}</span>，请再次确认您的货物是否为<span class="text-danger">${data.subject.subject}</span>？如果为<span class="text-danger">${data.subject.subject}</span>，请转至<span class="text-danger">${data.subject.subject}</span>端口投保或联系业务人员咨询。`

          this.$confirm(description, '投保提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning',
            dangerouslyUseHTMLString: true,
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
            .then(() => {
              this.$alert('请正确选择货物类别投保或联系业务人员。', '投保提醒')
              // why: 我只要删了它就绕不过这个验证!!
              this.form.goods_name = ''
              this.lastGoodsName = ''
              this.form.execpt_goods_alert = []
            })
            .catch(() => {
              this.form.execpt_goods_alert = [data.subject.subject, description]
            })
        }
      }
    },
    fetchSuggestions(column, value, cb) {
      getSuggestions(this.product?.company?.id, column, value).then((r) => {
        const data = r.data.map((item) => {
          item.value = item[column]
          return item
        })

        cb(data)
      })
    },
    selectSuggestion(value) {
      delete value.value

      Object.assign(this.form, {}, value)

      this.$refs.form.clearValidate(Object.keys(value))
    },
    handleSubjectChange(subjectId) {
      if (parseInt(subjectId, 10) === 0 || subjectId === -1 || subjectId === this.form.subject_id) {
        return
      }

      this.$set(this.form, 'subject_id', subjectId)

      this.$emit('update:subjectId', subjectId)
    },
    handlePreview() {
      this.$refs.form.validate((valid, errors) => {
        if (valid) {
          const data = Object.assign({}, this.form)

          if (this.form.coverage > this.product?.additional?.coverage) {
            return this.$message.error('保险金额超限请转人工审核进行询价')
          }

          if (this.form.transmit && !this.form.transmit_addr) {
            // return this.$message.error('中转地保单地址不能为空')
          }

          this.$emit(
            'on-preview',
            this.product,
            this.marshalData(Object.assign({}, this.policy)),
            this.marshalData(data)
          )
        } else {
          window.alert_validate_errors(errors)
        }
      })
    },
    handleSave() {
      const data = Object.assign({}, this.form)

      this.$emit('on-save', this.product, this.marshalData(data))
    },
    marshalData(data) {
      if (Object.keys(data).length <= 0) {
        return data
      }

      for (const field of [
        'shipping_date',
        'policyholder_idcard_issue_date',
        'policyholder_idcard_valid_till',
        'insured_idcard_issue_date',
        'insured_idcard_valid_till'
      ]) {
        if (data[field]) {
          data[field] = dayjs(data[field]).isValid() ? dayjs(data[field]).format('YYYY-MM-DD') : ''
        }
      }

      if (data.ship_construction_year) {
        data.ship_construction_year = dayjs(data.ship_construction_year).isValid()
          ? dayjs(data.ship_construction_year).format('YYYY')
          : ''
      }

      for (const fileField of ['anti_dated_file', 'custom_file']) {
        if (typeof data[fileField] === 'string') {
          data[fileField] = ''
        }
      }

      data.departure = data.departure_addr
        ? data.departure.join('-') + ':' + data.departure_addr
        : data.departure.join('-')

      data.transmit = data.transmit_addr
        ? data.transmit?.join('-') + ':' + data.transmit_addr
        : data.transmit?.join('-')

      data.destination = data.destination_addr
        ? data.destination.join('-') + ':' + data.destination_addr
        : data.destination.join('-')

      data.product_id = this.product?.id

      return data
    },
    fetchProducts() {
      let filter = {
        filter: {
          type: PRODUCT_TYPE,
          subject_id: this.form.subject_id
        }
      }

      if (this.$route.params.id !== undefined && this.$route.query.from === 'edit') {
        filter.id = this.form.product_id

        // hack: 编辑模式阻止 id 为 -1 的请求
        if (filter.id === -1) {
          return
        }
      }

      getProducts(filter).then((r) => {
        this.products = r.data
      })
    },
    submitPreview() {
      if (
        this?.product?.channel?.platform?.includes(this.user.platform_id) &&
        this?.product?.additional?.inform?.length > 1
      ) {
        this.informVisible = true
        return
      }
      this.handlePreview()
    },
    checkingInform() {
      this.informVisible = false
      this.handlePreview()
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-form {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}
::v-deep .transport-alert .el-alert__title {
  font-size: 16px;
}
</style>
