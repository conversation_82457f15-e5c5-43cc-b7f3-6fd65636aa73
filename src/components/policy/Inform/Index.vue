<template>
  <el-dialog
    :visible.sync="visible"
    title="客户告知书"
    width="520px"
    :before-close="() => $emit('update:visible', false)"
  >
    <span v-html="detail || ''"></span>
    <span slot="footer" class="dialog-footer">
      <el-button @click="() => $emit('update:visible', false)">取 消</el-button>
      <el-button type="primary" @click="checking">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'Inform',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: String,
      default: ''
    }
  },
  methods: {
    checking() {
      this.$emit('checking')
    }
  }
}
</script>
