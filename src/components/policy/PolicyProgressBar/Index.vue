<template>
  <el-tabs :value="active.toString()" stretch>
    <el-tab-pane v-for="(step, index) in steps" :key="index" :disabled="active !== index" :label="step"></el-tab-pane>
  </el-tabs>
</template>

<script>
export default {
  name: 'PolicyProgressBar',
  props: {
    active: {
      type: Number,
      default: 0
    },
    steps: {
      type: Array,
      default: () => []
    }
  }
}
</script>
