<template>
  <div id="preview-body" class="policy-wrap">
    <el-card
      shadow="never"
      class="define-card my-4 shadow-sm"
      :body-style="{ padding: '0' }"
      v-if="product.id !== undefined"
    >
      <div class="preview-header">
        <el-row class="flex heading">
          <el-col>保险公司</el-col>
          <el-col>保险金额</el-col>
          <el-col>运费</el-col>
          <el-col>费率（‱万分之）</el-col>
          <el-col>保费（元）</el-col>
        </el-row>
        <el-row class="flex">
          <el-col>{{ product.company_branch }}</el-col>
          <el-col>{{ product.additional.coverage }}</el-col>
          <el-col v-html="viewData.coverage"></el-col>
          <el-col v-html="viewData.user_rate"></el-col>
          <el-col>{{ data?.user_premium?.toFixed(2) }}</el-col>
        </el-row>
      </div>
    </el-card>
    <div class="p-extra-large m-extra-large-t bg-white flex-fill o-hidden o-y-auto">
      <define-details :data="detail" />
    </div>
    <el-card shadow="never" class="define-card my-4 shadow-sm m-extra-large-t">
      <center>
        <el-row>
          <el-button icon="el-icon-back" @click="$emit('on-back')">返回修改</el-button>
          <el-button type="primary" icon="el-icon-circle-check" @click="$emit('on-submit')">立即投保</el-button>
        </el-row>
      </center>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'PreviewIntl',
  props: {
    product: {
      type: Object,
      default: () => {}
    },
    data: {
      type: Object,
      default: () => {}
    },
    originData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    viewData() {
      if (this.$route.query.from !== 'edit') {
        return this.data
      }

      let viewData = {}
      Object.keys(this.data).forEach((key) => {
        if (!(typeof this.data[key] === 'object')) {
          if (this.data[key] !== this.originData[key]) {
            viewData[key] = `<span style="color: red;">${this.data[key]}</span>`
          } else {
            viewData[key] = this.data[key]
          }
        } else {
          if (this.data[key]?.id !== this.originData[key]?.id) {
            viewData[key] = {
              id: this.data[key].id,
              name: `<span style="color: red;">${this.data[key]?.name}</span>`
            }
          } else {
            viewData[key] = this.data[key]
          }
        }
      })

      return viewData
    },
    detail() {
      return {
        title: ' ',
        data: [
          {
            title: '保险内容',
            groups: [
              { label: '主条款', value: this.viewData?.clauses?.main, row: true },
              { label: '附加险', value: this.viewData?.clauses?.additional, row: true },
              { label: '免赔', value: this.product.additional?.deductible, row: true },
              { label: '特别约定', value: this.product.additional?.special_agreement, row: true }
            ]
          },
          {
            title: '基本信息',
            groups: [
              { label: '投保人', value: this.viewData.policyholder },
              { label: '被保人', value: this.viewData.insured },
              { label: '投保人地址', value: this.viewData.policyholder_address },
              { label: '被保人地址', value: this.viewData.insured_address },
              { label: '投保人电话', value: this.viewData.policyholder_phone_number },
              { label: '被保人电话', value: this.viewData.insured_phone_number }
            ]
          },
          {
            title: '货物信息',
            groups: [
              { label: '货物类别', value: this.viewData.goods_type.name },
              { label: '装载方式', value: this.viewData.loading_method.name },
              { label: '运输方式', value: this.viewData.transport_method.name },
              { label: '包装方式', value: this.viewData.packing_method.name },
              { label: '货物名称', value: `<pre>${this.viewData.goods_name}</pre>` },
              { label: '包装件数', value: `<pre>${this.viewData.goods_amount}</pre>` }
            ]
          },
          {
            title: '运输信息',
            groups: [
              { label: '运单号', value: this.viewData.waybill_no },
              { label: '发票号', value: this.viewData.invoice_no },
              { label: '车牌号', value: this.viewData.transport_no },
              { label: '起运日期', value: this.viewData.shipping_date },
              { label: '起运地', value: this.viewData.departure?.replace(':', '-') },
              { label: '中转地', value: this.viewData.transmit?.replace(':', '-') },
              { label: '目的地', value: this.viewData.destination?.replace(':', '-'), row: true }
            ]
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-header {
  .flex {
    display: flex;
    flex: 1;
    text-align: left;
    height: 40px;
    line-height: 40px;

    .el-col {
      padding-left: 10px;
    }
  }

  .heading {
    background-color: #4aa0b5;
    color: #fff;
    font-weight: bold;
  }
}
</style>
