<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-10 00:05:29
 * @LastEditors: yanb
 * @LastEditTime: 2024-07-29 17:48:55
-->
<template slot="label">
  <div>
    <policy-product
      :subjects="subjects"
      :products="products"
      @product-change="(p) => (product = p)"
      @subject-change="handleSubjectChange"
      :subject.sync="form.subject_id"
      :subjectCategoryIds.sync="form.subject_category_ids"
      :manualConditions.sync="form.manual_conditions"
      :product.sync="form.product_id"
      :isExceptGoods.sync="isExceptGoods"
      class="m-extra-large-b"
    />

    <intl-import :product-id="product.id ?? -1" :importable="product?.id === undefined || isExceptGoods !== 0" />

    <el-alert
      class="m-extra-large-t transport-alert"
      style="margin-bottom: 20px"
      :closable="false"
      type="error"
      title="本投保界面仅适用于海运、空运、公路、铁路运输方式，不适用于起运地至目的地的运输中包含快递形式的运输（如DHL、FedEx、UPS、顺丰、EMS等快递服务）。"
    >
    </el-alert>

    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-position="top"
      :disabled="product?.id === undefined || isExceptGoods !== 0"
    >
      <form-block class="m-extra-large-b" title="投保人/被保人信息">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="policyholder" label="投保人(同开票抬头)">
              <el-autocomplete
                v-model="form.policyholder"
                :fetch-suggestions="(value, cb) => fetchSuggestions('policyholder', value, cb)"
                @select="selectSuggestion"
                @blur="() => checkBlacklist('投保人', 'name', form.policyholder)"
                placeholder="请输入投保人"
                value="form.policyholder"
                clearable
                class="w-100"
              >
              </el-autocomplete>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="insured">
              <template slot="label">
                被保人 (具有可保利益的货主)
                <span class="m-mini-l text-danger">
                  <el-checkbox v-model="isSame">同投保人</el-checkbox>
                </span>
              </template>
              <el-autocomplete
                v-model="form.insured"
                :fetch-suggestions="(value, cb) => fetchSuggestions('insured', value, cb)"
                @select="selectSuggestion"
                placeholder="请输入被保人 (具有可保利益的货主)"
                @blur="() => checkBlacklist('被保人', 'name', form.insured)"
                clearable
                class="w-100"
              ></el-autocomplete>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48" v-if="['PICC', 'TPIC', 'PINGAN'].includes(product?.company?.identifier)">
          <el-col :span="12">
            <el-form-item
              prop="policyholder_type"
              label="投保人类型"
              :required="product?.company?.identifier === 'TPIC'"
            >
              <template slot="label" v-if="product?.company?.identifier === 'PICC'">
                投保人类型
                <span class="m-mini-l text-danger" style="margin-right: 5px"> 是否为境外客户 </span>
                <el-radio v-model="form.policyholder_overseas" :label="1">是</el-radio>
                <el-radio v-model="form.policyholder_overseas" :label="0">否</el-radio>
              </template>
              <el-select placeholder="请选择投保人类型" v-model="form.policyholder_type" class="w-100">
                <el-option
                  v-for="type in clientTypes"
                  :key="type.value"
                  :value="type.value"
                  :label="type.label"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="insured_type" label="被保人类型" :required="product?.company?.identifier === 'TPIC'">
              <template slot="label" v-if="product?.company?.identifier === 'PICC'">
                被保人类型
                <span class="m-mini-l text-danger" style="margin-right: 5px"> 是否为境外客户 </span>
                <el-radio v-model="form.insured_overseas" :label="1">是</el-radio>
                <el-radio v-model="form.insured_overseas" :label="0">否</el-radio>
              </template>
              <el-select placeholder="请选择被保人类型" v-model="form.insured_type" class="w-100">
                <el-option
                  v-for="type in clientTypes"
                  :key="type.value"
                  :value="type.value"
                  :label="type.label"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          :gutter="48"
          v-if="
            ['TPIC', 'PINGAN'].includes(product?.company?.identifier) ||
            ((form.policyholder_type == 1 || form.insured_type == 1) && product?.company?.identifier === 'PICC')
          "
        >
          <el-col :span="12">
            <el-form-item
              prop="policyholder_idcard_no"
              :label="policyholderIdCardNoLabel"
              :required="['TPIC'].includes(product?.company?.identifier) && form.policyholder_type == 1"
              :style="
                ['PICC'].includes(product?.company?.identifier) && form.policyholder_type === 0
                  ? 'visibility: hidden'
                  : ''
              "
            >
              <el-input
                v-model="form.policyholder_idcard_no"
                :placeholder="form.policyholder_type === 1 ? '请输入投保人证件号' : '请输入投保人统一社会信用代码'"
                clearable
                @blur="() => checkBlacklist('投保人证件号码', 'idcard_no', form.policyholder_idcard_no)"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              prop="insured_idcard_no"
              :label="insuredIdCardNoLabel"
              :required="['TPIC', 'PINGAN'].includes(product?.company?.identifier) && form.insured_type == 1"
              :style="
                ['PICC'].includes(product?.company?.identifier) && form.insured_type === 0 ? 'visibility: hidden' : ''
              "
            >
              <el-input
                v-model="form.insured_idcard_no"
                :placeholder="form.insured_type === 1 ? '请输入被保人证件号' : '请输入被保人统一社会信用代码'"
                clearable
                @blur="() => checkBlacklist('被保人证件号码', 'idcard_no', form.insured_idcard_no)"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          :gutter="48"
          v-if="(form.policyholder_type == 1 || form.insured_type == 1) && product?.company?.identifier === 'PICC'"
        >
          <el-col :span="12">
            <el-form-item
              prop="policyholder_idcard_issue_date"
              label="投保人证件有效起始时间"
              :required="form.policyholder_type == 1 && form.policyholder_overseas == 0"
              :style="form.policyholder_type === 0 ? 'visibility: hidden' : ''"
            >
              <el-date-picker
                v-model="form.policyholder_idcard_issue_date"
                class="w-100"
                type="date"
                placeholder="请选择投保人证件有效起始时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              prop="insured_idcard_issue_date"
              label="被保人证件有效起始时间"
              :required="form.insured_type == 1 && form.insured_overseas == 0"
              :style="form.insured_type === 0 ? 'visibility: hidden' : ''"
            >
              <el-date-picker
                v-model="form.insured_idcard_issue_date"
                class="w-100"
                type="date"
                placeholder="请选择被保人证件有效起始时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          :gutter="48"
          v-if="(form.policyholder_type == 1 || form.insured_type == 1) && product?.company?.identifier === 'PICC'"
        >
          <el-col :span="12">
            <el-form-item
              prop="policyholder_idcard_valid_till"
              label="投保人证件有效结束时间"
              :required="form.policyholder_type == 1 && form.policyholder_overseas == 0"
              :style="form.policyholder_type === 0 ? 'visibility: hidden' : ''"
            >
              <template slot="label">
                投保人证件有效结束时间
                <span class="m-mini-l text-danger">
                  <el-checkbox v-model="form.policyholder_forever" :true-label="1" :false-label="0">长期</el-checkbox>
                </span>
              </template>
              <el-date-picker
                v-model="form.policyholder_idcard_valid_till"
                class="w-100"
                type="date"
                placeholder="请选择投保人证件有效结束时间"
                :disabled="form.policyholder_forever === 1"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              prop="insured_idcard_valid_till"
              label="被保人证件有效结束时间"
              :required="form.insured_type === 1 && form.insured_overseas === 0"
              :style="form.insured_type === 0 ? 'visibility: hidden' : ''"
            >
              <template slot="label">
                被保人证件有效结束时间
                <span class="m-mini-l text-danger">
                  <el-checkbox v-model="form.insured_forever" :true-label="1" :false-label="0">长期</el-checkbox>
                </span>
              </template>
              <el-date-picker
                v-model="form.insured_idcard_valid_till"
                class="w-100"
                type="date"
                placeholder="请选择被保人证件有效结束时间"
                :disabled="form.insured_forever === 1"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="policyholder_phone_number" label="投保人电话">
              <el-input v-model="form.policyholder_phone_number" placeholder="请输入投保人电话" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="insured_phone_number" label="被保人电话">
              <el-input v-model="form.insured_phone_number" placeholder="请输入被保人电话" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="policyholder_address" label="投保人地址">
              <el-input v-model="form.policyholder_address" placeholder="请输入投保人地址" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="insured_address" label="被保人地址">
              <el-input v-model="form.insured_address" placeholder="请输入被保人地址" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" title="货物信息">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="goods_type_id" label="货物分类">
              <goods-types :productId.sync="product.id" v-model="form.goods_type_id" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="packing_method_id" label="包装方式">
              <packing-methods :productId.sync="product.id" v-model="form.packing_method_id" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="goods_name" label="货物名称">
              <el-input
                v-model="form.goods_name"
                placeholder="请输入货物名称"
                type="textarea"
                autoresize
                :rows="3"
                @blur="handleValidateGoodsName"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="goods_amount" label="包装件数">
              <el-input
                v-model="form.goods_amount"
                placeholder="请输入包装件数"
                :type="product?.company?.identifier === 'TPIC' ? 'number' : 'textarea'"
                autoresize
                :rows="3"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="shipping_mark" label="唛头">
              <el-input
                v-model="form.shipping_mark"
                placeholder="请输入唛头"
                type="textarea"
                autoresize
                :rows="3"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" title="贸易类型">
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="insure_type">
              <el-select v-model="form.insure_type" placeholder="请选择贸易类型" class="flex w-100">
                <el-option
                  v-for="option in product?.additional?.insure_types || []"
                  :key="option.value"
                  :value="option.value"
                  :label="option.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b">
        <template #title>
          <span>运输信息</span>
          <el-popover placement="top-start" title="投保提醒" width="520" trigger="hover">
            <template>
              <p>
                （1）船名航次/航班号/车牌号：根据运输工具填写，例如：船名/航次 ABB CCC/088E，航班号 MU8888，车牌号
                沪A8G888。若暂无运输工具信息，先填写“待定”；后续运输工具信息确定后，请联系业务人员批改承保信息。
              </p>
              <p>（2）起运地/目的地描述：如门到门，填写仓库所在城市名称即可，如上海、洛杉矶(Los Angeles)。</p>
              <p>
                （3）起运时间：按运输条款的起运时间填写。如仓至仓条款，起运时间填写工厂或仓库装车起运时间；如港至港，则填写大船起运时间。
              </p>
            </template>
            <span slot="reference" style="margin-left: 5px; font-size: 12px; color: #ff7f4c">
              如何填写<i class="fas fa-question"></i>
            </span>
          </el-popover>
        </template>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="transport_method_id" label="运输方式">
              <transport-methods
                :productId.sync="product.id"
                defaultMethod="水路运输"
                @loadingMethods="(m) => (loadingMethods = m)"
                v-model="form.transport_method_id"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="loading_method_id" label="装载方式">
              <loading-methods
                :productId.sync="product.id"
                :loading-methods="loadingMethods"
                v-model="form.loading_method_id"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="departure" label="起运地(国/地区)">
              <country-region-select
                v-model="form.departure"
                :only="[1].includes(form.insure_type) ? ['ASCN-中国'] : []"
                :disabled-values="
                  product?.additional?.disabled_regions
                    ?.split('|')
                    .concat([2, 3].includes(form.insure_type) ? ['ASCN-中国'] : [])
                "
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item prop="departure_port" label="起运地">
              <div class="d-flex w-100">
                <tpic-intl-ports v-if="product?.company?.identifier === 'TPIC'" v-model="form.departure_port" />
                <el-input
                  v-if="product?.company?.identifier === 'TPIC'"
                  v-model="form.departure_addr"
                  class="m-mini-l"
                  clearable
                ></el-input>
                <el-input v-else v-model="form.departure_port" clearable></el-input>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="destination" label="目的地国家(国/地区)">
              <country-region-select
                v-model="form.destination"
                :only="[2].includes(form.insure_type) ? ['ASCN-中国'] : []"
                :disabled-values="
                  product?.additional?.disabled_regions
                    ?.split('|')
                    .concat([1, 3].includes(form.insure_type) ? ['ASCN-中国'] : [])
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="destination_port" label="目的地">
              <div class="d-flex w-100">
                <tpic-intl-ports v-if="product?.company?.identifier === 'TPIC'" v-model="form.destination_port" />
                <el-input
                  v-if="product?.company?.identifier === 'TPIC'"
                  v-model="form.destination_addr"
                  class="m-mini-l"
                  clearable
                ></el-input>
                <el-input v-else v-model="form.destination_port" clearable></el-input>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="transmit" label="中转地">
              <div class="d-flex w-100">
                <country-region-select
                  v-model="form.transmit"
                  :disabled-values="product?.additional?.disabled_regions?.split('|')"
                />
                <tpic-intl-ports
                  v-model="form.transmit_port"
                  class="m-mini-l"
                  v-if="product?.company?.identifier === 'TPIC'"
                />
                <el-input class="m-mini-l" v-model="form.transmit_addr" clearable placeholder="中转地"></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="payable_at" label="赔付地">
              <el-input clearable v-model.trim="form.payable_at" placeholder="赔付地(港口)"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="invoice_no">
              <template slot="label">
                <span>发票号</span>
                <span class="text-danger">（运单号和发票号必选填一个）</span>
              </template>
              <el-input v-model="form.invoice_no" placeholder="请输入发票号" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="waybill_no">
              <template slot="label">
                <span>提/运单号</span>
                <span class="text-danger">（运单号和发票号必选填一个）</span>
              </template>
              <el-input v-model="form.waybill_no" placeholder="请输入运单号" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="contract_no">
              <template slot="label">
                <span>合同号</span>
              </template>
              <el-input v-model="form.contract_no" placeholder="请输入合同号" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="transport_no" label="船名航次/航班号/车牌号">
              <el-input v-model="form.transport_no" placeholder="请输入船名航次/航班号/车牌号" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="shipping_date" label="起运日期">
              <el-date-picker
                :picker-options="shippingDateOptions"
                v-model="form.shipping_date"
                class="w-100"
                type="date"
                placeholder="请选择起运日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="shipping_date_print_format" label="起运日期打印格式">
              <el-select v-model="form.shipping_date_print_format" class="w-100">
                <el-option :value="1" :label="shippingDatePrintFormatLabel"></el-option>
                <el-option v-if="product?.company?.identifier !== 'PICC'" :value="2" label="AS PER B/L"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12" v-if="product?.company?.identifier === 'PICC' && form.transport_method_id == 2">
            <el-form-item prop="ship_construction_year" label="船舶建造年">
              <el-date-picker
                v-model="form.ship_construction_year"
                class="w-100"
                type="year"
                placeholder="请选择船舶建造年"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="anti_dated_file" v-if="showAntiDatedFileUploader">
              <template slot="label">
                <span>倒签保函</span>
                <span class="text-danger">
                  <el-link class="m-mini-l" target="_blank" :href="product?.additional?.anti_dated_file">
                    <small style="color: red">点击下载倒签保函</small>
                  </el-link>
                </span>
                <span class="text-danger"> .pdf, .jpeg, .png, .jpg文件，大小不能超过 4M</span>
              </template>
              <div class="d-flex">
                <upload-file accept=".pdf,.jpeg,.png,.jpg" :limitSize="4" v-model="form.anti_dated_file"></upload-file>
                <el-link
                  v-if="typeof form.anti_dated_file === 'string' && form.anti_dated_file"
                  class="m-extra-large-l"
                  :href="form.anti_dated_file"
                >
                  点击查看
                </el-link>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" title="保单信息">
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item
              prop="is_new"
              label="是否为二手货/旧器材/旧货、已受损货物、退运货物、进口转运货（释意：进口货物连续进行国内运输但仅计划投保国内运输段）"
            >
              <el-radio-group :disabled="$route.query.from === 'edit'" v-model="form.is_new">
                <el-radio :label="0">是</el-radio>
                <el-radio :label="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="main_clause_id">
              <template #label>
                <!-- // eslint-disable-next-line prettier/prettier -->
                <span>主险（<span class="text-danger">请点击“资料中心”菜单，查看相关保险条款</span>）</span>
              </template>
              <template v-if="this.form.transport_method_id && mainClauses.length > 0">
                <el-radio
                  v-model="form.main_clause_id"
                  :label="clause.id"
                  v-for="clause in mainClauses"
                  :key="clause.id"
                  :disabled="$route.query.from === 'edit'"
                >
                  <el-popover width="300" placement="top-start" title="条款内容" trigger="hover">
                    <p>{{ clause.content }}</p>
                    <p v-if="clause.clause_file">
                      <el-link :href="clause.clause_file" target="_blank" download>下载条款文件</el-link>
                    </p>
                    <span slot="reference">{{ clause.name }}</span>
                  </el-popover>
                </el-radio>
              </template>
              <template v-else>
                <el-alert title="当前条件下暂无可用主险" :closable="false" type="warning"></el-alert>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="additional_clause_ids">
              <template #label>
                <!-- // eslint-disable-next-line prettier/prettier -->
                <span> 附加险（<span class="text-danger">请点击“资料中心”菜单，查看相关保险条款</span>） </span>
              </template>
              <template v-if="form.main_clause_id">
                <el-checkbox-group v-if="additionalClauses.length > 0" v-model="form.additional_clause_ids">
                  <el-checkbox v-for="clause in additionalClauses" :key="clause.id" :label="clause.id">
                    <el-popover width="300" placement="top-start" title="条款内容" trigger="hover">
                      <p>{{ clause.content }}</p>
                      <p v-if="clause.clause_file">
                        <el-link :href="clause.clause_file" target="_blank" download>下载条款文件</el-link>
                      </p>
                      <span slot="reference">{{ clause.name }}</span>
                    </el-popover>
                  </el-checkbox>
                </el-checkbox-group>
                <template v-else>
                  <el-alert title="当前主险暂无可用附加险" :closable="false" type="warning"></el-alert>
                </template>
              </template>
              <template v-else>
                <el-alert title="请先选择主险" :closable="false" type="warning"></el-alert>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="is_credit" label="信用证">
              <el-checkbox v-model="form.is_credit" :true-label="1" :false-label="0">是否做信用凭证</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="credit_no" label="信用证号">
              <el-input v-model="form.credit_no" placeholder="信用证号" :disabled="!form.is_credit" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item label="条款内容">
              <el-alert class="m-extra-large-b" type="error" v-if="form.is_credit">
                如需修改信用证条款请提前跟客服/业务确认，否则可能会承保失败！
              </el-alert>
              <el-input v-model="form.clause_content" type="textarea" :disabled="!form.is_credit" :rows="5"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b">
        <template #title>
          <span>费用信息</span>
          <el-popover placement="top-start" title="保费计算公式" width="520" trigger="hover">
            <template> 保费=发票金额*加成比例（默认为110%）*费率*汇率 </template>
            <span slot="reference" style="margin-left: 5px; font-size: 12px; color: #ff7f4c">
              保费计算公式<i class="fas fa-question"></i>
            </span>
          </el-popover>
        </template>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="invoice_amount" label="发票金额">
              <el-input
                clearable
                type="number"
                v-model="form.invoice_amount"
                placeholder="请输入发票金额"
                @focus.once="() => ($route.query.from === 'edit' ? $alert('修改发票金额需要上传盖章发票', '提醒') : '')"
              >
              </el-input>
              <span>{{ form.invoice_amount | chineseCoverage }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="invoice_currency_id" label="发票币种">
              <currencies v-model="form.invoice_currency_id" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-row :gutter="48">
              <el-col :span="12">
                <el-form-item prop="bonus" label="加成比例(%)">
                  <el-input v-model="form.bonus" placeholder="请输入加成比例" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="coverage" label="保险金额">
              <el-input
                clearable
                type="number"
                v-model="form.coverage"
                placeholder="保险金额"
                @focus.once="
                  () =>
                    $route.query.from === 'edit'
                      ? $alert({ message: '修改保险金额需要上传盖章发票', type: 'warning' })
                      : ''
                "
              >
              </el-input>
              <span>{{ form.coverage | chineseCoverage }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="invoice_currency_id" label="保险金额币种">
              <currencies v-model="form.coverage_currency_id" :currency.sync="currency" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-alert
              v-if="form.coverage * currency?.rate > product?.additional?.coverage"
              title="保险金额超出提醒"
              description="保险金额超限请转人工审核进行询价"
              show-icon
              :closable="false"
              type="warning"
            >
            </el-alert>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" title="其他信息">
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="sticky_note" label="工作编号">
              <el-input
                v-model="form.sticky_note"
                placeholder="此内容不作为投保使用，仅方便您核对账单使用"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="custom_file" label="投保附件">
              <div class="d-flex">
                <upload-file v-model="form.custom_file"></upload-file>
                <el-link
                  v-if="typeof form.custom_file === 'string' && form.custom_file"
                  class="m-extra-large-l"
                  :href="form.custom_file"
                >
                  点击查看
                </el-link>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="remark">
              <template slot="label">
                <span>备注</span>
                <span class="text-danger">（以下内容不展示在保单上）</span>
              </template>
              <el-input
                v-model="form.remark"
                type="textarea"
                autoresize
                :rows="3"
                placeholder="备注不作为投保依据"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <form-block class="m-extra-large-b" center>
        <div class="m-extra-large-b">
          <el-checkbox @change="disabledSubmit = !disabledSubmit" slot="title"></el-checkbox>
          <span style="cursor: pointer" @click="showNotice = !showNotice"> &nbsp;我已详细阅读《投保须知》的内容 </span>
        </div>

        <template v-if="showNotice">
          <el-alert title="投保须知" type="warning" class="m-extra-large-b" :closable="false">
            <template v-if="product.additional !== undefined">
              <div style="text-align: left" v-html="product.additional.notice"></div>
            </template>
          </el-alert>
        </template>
        <el-button icon="far fa-save" @click="handleSave" v-if="$route.query.from !== 'edit'">暂存</el-button>
        <el-button type="primary" icon="el-icon-document-checked" :disabled="disabledSubmit" @click="submitPreview">
          提交预览
        </el-button>
      </form-block>
    </el-form>
    <inform :visible.sync="informVisible" :detail="product?.additional?.inform || ''" @checking="checkingInform" />
  </div>
</template>

<script>
import PolicyProduct from '@/components/policy/PolicyIntlProduct'
import GoodsTypes from '@/components/policy/GoodsTypes'
import PackingMethods from '@/components/policy/PackingMethods'
import LoadingMethods from '@/components/policy/LoadingMethods'
import TransportMethods from '@/components/policy/TransportMethods'
import Currencies from '@/components/policy/Currencies'
import CountryRegionSelect from '@/components/policy/CountryRegionSelect'
import Inform from '@/components/policy/Inform'
import { getSubjects } from '@/apis/subject'
import { getProducts } from '@/apis/product'
import { checkIfExist, getSuggestions, getGoodsSuggestion } from '@/apis/policy'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'
import { digitUppercase } from '@/utils'
import TPICIntlPorts from '@/components/policy/TpicIntlPorts'
import IntlImport from '@/components/policy/IntlImport'
import * as blacklistApi from '@/apis/blacklist'

const PRODUCT_TYPE = 2

export default {
  name: 'IntlForm',
  props: {
    policy: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    PolicyProduct,
    GoodsTypes,
    PackingMethods,
    LoadingMethods,
    TransportMethods,
    Currencies,
    CountryRegionSelect,
    'tpic-intl-ports': TPICIntlPorts,
    Inform,
    IntlImport
  },
  data() {
    return {
      showNotice: false,
      isSame: false,
      isExceptGoods: null,
      subjects: [],
      products: [],
      product: {},
      disabledSubmit: true,
      informVisible: false,
      currency: {},
      loadingMethods: [],
      clientTypes: [
        {
          label: '个人客户',
          value: 1
        },
        {
          label: '团体客户',
          value: 0
        }
      ],
      lastGoodsName: '',
      form: {
        type: PRODUCT_TYPE,
        is_new: -1,
        product_id: -1,
        subject_id: -1,
        manual_conditions: '',
        subject_category_ids: [],
        policyholder: '',
        policyholder_address: '',
        policyholder_phone_number: '',
        policyholder_type: 0,
        policyholder_overseas: 0,
        policyholder_idcard_no: '',
        policyholder_idcard_issue_date: '',
        policyholder_idcard_valid_till: '',
        insured: '',
        insured_address: '',
        insured_phone_number: '',
        insured_type: 0,
        insured_overseas: 0,
        insured_idcard_no: '',
        insured_idcard_issue_date: '',
        insured_idcard_valid_till: '',
        sticky_note: '',
        goods_type_id: '',
        packing_method_id: '',
        goods_name: '',
        goods_amount: '',
        transport_method_id: '',
        loading_method_id: '',
        departure: '',
        departure_port: '',
        departure_addr: '',
        destination: '',
        destination_port: '',
        destination_addr: '',
        transmit: '',
        transmit_port: '',
        transmit_addr: '',
        transport_no: '',
        invoice_no: '',
        waybill_no: '',
        contract_no: '',
        anti_dated_file: '',
        is_credit: 0,
        credit_no: '',
        invoice_currency_id: '',
        coverage_currency_id: '',
        shipping_mark: '',
        coverage: '',
        payable_at: '',
        shipping_date: '',
        shipping_date_print_format: '',
        insure_type: '',
        bonus: 10,
        invoice_amount: '',
        remark: '',
        custom_file: '',
        main_clause_id: '',
        clause_content: '',
        additional_clause_ids: [],
        is_sanctionist: false,
        is_only_port: false,
        ship_construction_year: '',
        execpt_goods_alert: [],
        with_user_special_and_deductible: 0,
        deductible: '',
        special_agreement: ''
      },
      rules: {
        is_new: [
          { required: true, message: '请选择是否全新货物', trigger: ['blur', 'change'] },
          {
            validator: (rule, value, callback) => {
              if ([0, 1].includes(value)) {
                callback()
              } else {
                callback(new Error('请选择是否全新货物'))
              }
            }
          }
        ],
        policyholder: [
          { required: true, message: '请输入投保人姓名', trigger: 'blur' },
          { min: 1, max: 128, message: '长度在 1 到 128 个字符', trigger: 'blur' }
        ],
        insured: [
          { required: true, message: '请输入被保人姓名', trigger: 'blur' },
          { min: 1, max: 128, message: '长度在 1 到 128 个字符', trigger: 'blur' }
        ],
        insured_address: [
          {
            validator: (rule, value, callback) => {
              if (new TextEncoder().encode(`${value} ${this.form.insured_phone_number}`).length > 150) {
                callback(new Error('被保人地址+电话长度不能超过150个字符'))
              } else {
                callback()
              }
            }
          }
        ],
        insured_phone_number: [
          {
            validator: (rule, value, callback) => {
              if (new TextEncoder().encode(`${value} ${this.form.insured_address}`).length > 150) {
                callback(new Error('被保人地址+电话长度不能超过150个字符'))
              } else {
                callback()
              }
            }
          }
        ],
        policyolder_phone_number: [
          {
            validator: (rule, value, callback) => {
              if (new TextEncoder().encode(`${value}${this.form.policyholder_address}`).length > 150) {
                callback(new Error('投保人地址+电话长度不能超过150个字符'))
              } else {
                callback()
              }
            }
          }
        ],
        policyholder_address: [
          {
            validator: (rule, value, callback) => {
              if (new TextEncoder().encode(`${value}${this.form.policyholder_phone_number}`).length > 150) {
                callback(new Error('投保人地址+电话长度不能超过150个字符'))
              } else {
                callback()
              }
            }
          }
        ],
        goods_type_id: [{ required: true, message: '请选择货物类别', trigger: ['change', 'blur'] }],
        packing_method_id: [{ required: true, message: '请选择包装方式', trigger: 'change' }],
        goods_name: [{ required: true, message: '请输入货物名称', trigger: 'blur' }],
        goods_amount: [{ required: true, message: '请输入包装件数', trigger: 'blur' }],
        transport_method_id: [{ required: true, message: '请选择运输方式', trigger: 'change' }],
        loading_method_id: [{ required: true, message: '请选择装载方式', trigger: 'change' }],
        insure_type: [{ required: true, message: '请选择贸易类型', trigger: ['change', 'blur'] }],
        departure: [{ required: true, message: '请选择起运地', trigger: 'change' }],
        departure_port: [{ required: true, message: '请输入起运地', trigger: 'blur' }],
        destination: [{ required: true, message: '请选择目的地', trigger: 'change' }],
        destination_port: [{ required: true, message: '请输入目的地', trigger: 'blur' }],
        transport_no: [
          { required: true, message: '请输入船名航次/航班号/车牌号', trigger: 'blur' },
          { min: 2, max: 128, message: '船名航次/航班号/车牌号长度错误', trigger: 'blur' }
        ],
        shipping_date: [{ required: true, type: 'date', message: '请选择起运日期', trigger: 'change' }],
        coverage: [{ required: true, message: '请输入保险金额', trigger: 'blue' }],
        invoice_amount: [{ required: true, message: '请填写发票金额', trigger: 'blur' }],
        invoice_currency_id: [{ required: true, message: '请选择发票币种', trigger: 'change' }],
        coverage_currency_id: [{ required: true, message: '请选择保险金额币种', trigger: 'change' }],
        payable_at: [{ required: true, message: '请填写赔付地信息', trigger: ['blur', 'change'] }],
        main_clause_id: [{ required: true, message: '请选择主险', trigger: ['blur', 'change'] }],
        clause_content: [{ required: true, message: '请输入条款内容', trigger: ['blur', 'change'] }],
        shipping_date_print_format: [
          { required: true, message: '请选择起运日期打印格式', trigger: ['blur', 'change'] }
        ],
        bonus: [{ required: true, message: '请输入加点', trigger: 'blur' }],
        invoice_no: [
          { min: 2, message: '发票号长度错误', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (this.form.waybill_no === '' && this.form.invoice_no === '') {
                callback(new Error('当运单号为空时发票号必填'))
              } else {
                if (value) {
                  checkIfExist({
                    type: PRODUCT_TYPE,
                    policy_id: this.$route.query.from !== 'copy' ? this.$route.params.id : null,
                    company: this.product?.company?.identifier,
                    column: 'invoice_no',
                    value: value
                  }).then((r) => {
                    r.is_exist ? callback(new Error('发票号已经存在')) : callback()
                  })
                } else {
                  callback()
                }

                this.$refs.form.clearValidate('waybill_no')
              }
            },
            trigger: 'blur'
          }
        ],
        waybill_no: [
          { min: 2, message: '运单号长度错误', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (this.form.invoice_no === '' && this.form.waybill_no === '') {
                callback(new Error('当发票号为空时运单号必填'))
              } else {
                if (value) {
                  checkIfExist({
                    type: PRODUCT_TYPE,
                    policy_id: this.$route.query.from !== 'copy' ? this.$route.params.id : null,
                    company: this.product?.company?.identifier,
                    column: 'waybill_no',
                    value: value
                  }).then((r) => {
                    r.is_exist ? callback(new Error('运单号已经存在')) : callback()
                  })
                } else {
                  callback()
                }

                this.$refs.form.clearValidate('invoice_no')
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  filters: {
    chineseCoverage(value) {
      return digitUppercase(value)
    }
  },
  watch: {
    product(value) {
      if (value.id !== this.form.product_id) {
        this.form.product_id = value.id
        this.form.transport_method_id = ''
        this.form.packing_method_id = ''
        this.form.loading_method_id = ''
        this.form.goods_type_id = ''
        this.form.main_clause_id = ''
        this.form.additional_clause_ids = []
      }

      // 验证货物名称
      this.form.execpt_goods_alert = []
      this.handleValidateGoodsName(true)

      if (value?.company?.identifier == 'PICC') {
        this.rules.policyholder_type = [{ required: false, message: '请选择投保人类型', trigger: 'change' }]
        if (this.form.policyholder_overseas === 1 && this.form.policyholder_type === 0) {
          delete this.rules.policyholder_idcard_no
        } else {
          this.rules.policyholder_idcard_no = [
            { required: false, message: '请输入投保人证件号', trigger: 'blur' }
            // { min: 18, max: 18, message: '投保人证件号格式错误', trigger: 'blur' }
          ]
        }
        this.rules.insured_type = [{ required: false, message: '请选择被保人类型', trigger: 'change' }]
        if (this.form.insured_overseas === 1 && this.form.insured_type === 0) {
          delete this.rules.insured_idcard_no
        } else {
          this.rules.insured_idcard_no = [
            { required: false, message: '请输入被保人证件号', trigger: 'blur' }
            // { min: 18, max: 18, message: '被保人证件号格式错误', trigger: 'blur' }
          ]
        }
      } else if (['TPIC'].includes(value.company?.identifier)) {
        this.rules.policyholder_idcard_no = [{ required: true, message: '请输入投保人证件号', trigger: 'blur' }]
        this.rules.insured_idcard_no = [{ required: true, message: '请输入被保人证件号', trigger: 'blur' }]
      } else {
        this.form.policyholder_type = 0
        this.form.policyholder_idcard_no = ''
        this.form.policyholder_idcard_issue_date = ''
        this.form.policyholder_idcard_valid_till = ''
        this.form.insured_type = 0
        this.form.insured_idcard_no = ''
        this.form.insured_idcard_issue_date = ''
        this.form.insured_idcard_valid_till = ''
        delete this.rules.policyholder_type
        delete this.rules.policyholder_idcard_no
        delete this.rules.insured_type
        delete this.rules.insured_idcard_no
      }
      this.$refs.form.clearValidate()
    },
    'form.transport_method_id'(val, oldVal) {
      if (oldVal !== '' && val !== oldVal) {
        this.form.main_clause_id = ''
        this.form.additional_clause_ids = []
      }
    },
    'form.invoice_currency_id'(val, oldVal) {
      this.form.coverage_currency_id = val
      if (
        this.form.coverage * this.currency?.rate >= 1000000 &&
        this.form.transport_method_id == 2 &&
        this.product?.company?.identifier === 'PICC'
      ) {
        this.$set(this.rules, 'ship_construction_year', [
          { required: true, message: '请填写船舶建造年', trigger: ['blur', 'change'] }
        ])
      } else {
        delete this.rules.ship_construction_year
      }
      if (this.$route.query.from === 'edit' && oldVal !== '' && val !== oldVal) {
        this.$alert('修改发票金额币种需要上传盖章发票', '提醒')
      }
    },
    'form.bonus'(val) {
      if (val < 0) {
        this.form.bonus = 0
      }

      if (val > 10) {
        this.form.bonus = 10
      }

      this.form.coverage = (this.form.invoice_amount * (this.form.bonus / 100 + 1)).toFixed(2)
    },
    'form.insure_type'(val) {
      if (parseInt(val, 10) === 1) {
        this.form.departure = 'ASCN-中国'
        // this.form.destination = ''
      }

      if (parseInt(val, 10) === 2) {
        // this.form.departure = ''
        this.form.destination = 'ASCN-中国'
      }
    },
    'form.destination_port'(val) {
      if (this.$route.query.from === undefined || this.form.payable_at === '') {
        this.form.payable_at = val
      }
    },
    'form.invoice_amount'(val) {
      if (val) {
        this.form.coverage = (val * ((this.form.bonus || 0) / 100 + 1)).toFixed(2)
      }
    },
    'form.transmit_addr'(value) {
      if (value && this.product?.company?.identifier === 'PICC') {
        this.$set(this.rules, 'transmit', [{ required: true, message: '请选择中转地国家或地区', trigger: 'change' }])
      } else {
        this.$delete(this.rules, 'transmit')
      }
      this.$refs.form.clearValidate('transmit')
    },
    'form.shipping_date': {
      deep: true,
      immediate: true,
      handler(value) {
        if (this.antiDatedDays > 0 && this.$route.query.from !== 'edit') {
          if (dayjs().isAfter(dayjs(value).add(this.antiDatedDays, 'day'))) {
            this.$set(this.rules, 'anti_dated_file', [
              { required: true, message: '请上传倒签保函', trigger: ['blur', 'change'] }
            ])
          } else {
            this.$delete(this.rules, 'anti_dated_file')
          }

          this.$refs.form.clearValidate('anti_dated_file')
        }
      }
    },
    'form.policyholder': {
      deep: true,
      immediate: true,
      handler(value) {
        if (this.isSame) {
          this.form.insured = value
        }
      }
    },
    'form.policyholder_phone_number': {
      deep: true,
      immediate: true,
      handler(value) {
        if (this.isSame) {
          this.form.insured_phone_number = value
        }
      }
    },
    'form.policyholder_address': {
      deep: true,
      immediate: true,
      handler(value) {
        if (this.isSame) {
          this.form.insured_address = value
        }
      }
    },
    'form.is_new'(newVal, oldVal) {
      if (oldVal !== -1 && newVal !== oldVal) {
        this.form.main_clause_id = ''
        this.form.additional_clause_ids = []
      }
    },
    'form.main_clause_id'(newVal, oldVal) {
      if (oldVal !== '' && newVal !== oldVal) {
        this.form.additional_clause_ids = []
      }
    },
    isSame(value) {
      if (value) {
        this.form.insured = this.form.policyholder
        this.form.insured_phone_number = this.form.policyholder_phone_number
        this.form.insured_address = this.form.policyholder_address
        this.form.insured_idcard_no = this.form.policyholder_idcard_no
        this.form.insured_type = this.form.policyholder_type
        this.form.insured_overseas = this.form.policyholder_overseas
        this.form.insured_idcard_issue_date = this.form.policyholder_idcard_issue_date
        this.form.insured_idcard_valid_till = this.form.policyholder_idcard_valid_till
      }
    },
    policy: {
      deep: true,
      immediate: true,
      handler(value) {
        if (Object.keys(value).length > 0) {
          this.form = Object.assign({}, this.form, value)
        }
      }
    },
    'form.policyholder_type'(value) {
      if (value === 1 && this.form.policyholder_overseas == 0) {
        this.rules.policyholder_idcard_issue_date = [
          { required: true, type: 'date', message: '请投保人证件有效起始时间', trigger: 'change' }
        ]
        this.rules.policyholder_idcard_valid_till = [
          { required: true, type: 'date', message: '请投保人证件有效结束时间', trigger: 'change' }
        ]
      } else {
        delete this.rules.policyholder_idcard_issue_date
        delete this.rules.policyholder_idcard_valid_till
      }
      this.$refs.form.clearValidate()
    },
    'form.policyholder_overseas'(value) {
      if (value === 1 && this.form.policyholder_type === 0) {
        delete this.rules.policyholder_idcard_no
      } else {
        this.rules.policyholder_idcard_no = [
          { required: false, message: '请输入投保人证件号', trigger: 'blur' }
          // { min: 18, max: 18, message: '投保人证件号格式错误', trigger: 'blur' }
        ]
      }
      if (value === 1 && this.form.policyholder_type === 1) {
        delete this.rules.policyholder_idcard_issue_date
        delete this.rules.policyholder_idcard_valid_till
      }
      this.$refs.form.clearValidate()
    },
    'form.insured_type'(value) {
      if (value === 1 && this.form.insured_overseas == 0) {
        this.rules.insured_idcard_issue_date = [
          { required: true, type: 'date', message: '请被保人证件有效起始时间', trigger: 'change' }
        ]
        this.rules.insured_idcard_valid_till = [
          { required: true, type: 'date', message: '请被保人证件有效结束时间', trigger: 'change' }
        ]
      } else {
        delete this.rules.insured_idcard_issue_date
        delete this.rules.insured_idcard_valid_till
      }
      this.$refs.form.clearValidate()
    },
    'form.insured_overseas'(value) {
      if (value === 1 && this.form.insured_type === 0) {
        delete this.rules.insured_idcard_no
      } else {
        this.rules.insured_idcard_no = [
          { required: false, message: '请输入被保人证件号', trigger: 'blur' }
          // { min: 18, max: 18, message: '被保人证件号格式错误', trigger: 'blur' }
        ]
      }
      if (value === 1 && this.form.insured_type === 1) {
        delete this.rules.insured_idcard_issue_date
        delete this.rules.insured_idcard_valid_till
      }
      this.$refs.form.clearValidate()
    },
    'form.subject_id'() {
      this.fetchProducts()
    },
    // hack: 线上环境不知道为什么 subject_id 变更的时候 product_id 还是 1 按理来说应该是同步的
    'form.product_id'(value, oldValue) {
      if (this.$route.query.from === 'edit' && oldValue === -1) {
        this.fetchProducts()
      }
    },
    clauseContents(value) {
      this.form.clause_content = value
    },
    'form.is_credit'(value) {
      if (!value) {
        this.form.clause_content = this.clauseContents
        this.form.credit_no = ''
      }
    },
    'form.policyholder_forever'(value) {
      if (value === 1) {
        this.form.policyholder_idcard_valid_till = new Date('2199-12-31')
      } else {
        this.form.policyholder_idcard_valid_till = ''
      }
    },
    'form.insured_forever'(value) {
      if (value === 1) {
        this.form.insured_idcard_valid_till = new Date('2199-12-31')
      } else {
        this.form.insured_idcard_valid_till = ''
      }
    },
    'form.departure'(value, oldValue) {
      let message = ''
      const only_port_regions = this.product?.additional?.only_port_regions?.split('|') || []
      if (only_port_regions.includes(value)) {
        message += '所选国家/地区仅承保到港口，不承保内陆运输，需附加仓到港条款，条款内容见预览件  <br />\n'
      }

      const sanctionist = this.product?.additional?.sanctionist?.split('|') || []
      if (sanctionist.includes(value)) {
        message += '所选国家/地区为被制裁国家/地区，需附加制裁条款，条款内容见预览件 <br />\n'
      }

      const warStrikeRegions = this.product?.additional?.war_strike_regions?.split('|') || []
      if (warStrikeRegions.includes(value)) {
        message += '所选国家/地区不承保战争险和罢工险\n'

        // 批改不清空，批改不允许修改主险
        if (oldValue !== '' && value !== oldValue) {
          if (this.$route.query.from !== 'edit') {
            this.form.main_clause_id = ''
          }
          this.form.additional_clause_ids = []
        }
      }

      if (message) {
        this.$alert(message, '投保提醒', {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true
        })
      }
    },
    'form.destination'(value, oldValue) {
      let message = ''
      const only_port_regions = this.product?.additional?.only_port_regions?.split('|') || []
      if (only_port_regions.includes(value)) {
        message += '所选国家/地区仅承保到港口，不承保内陆运输，需附加仓到港条款，条款内容见预览件  <br />\n'
      }

      const sanctionist = this.product?.additional?.sanctionist?.split('|') || []
      if (sanctionist.includes(value)) {
        message += '所选国家/地区为被制裁国家/地区，需附加制裁条款，条款内容见预览件 <br />\n'
      }

      const warStrikeRegions = this.product?.additional?.war_strike_regions?.split('|') || []
      if (warStrikeRegions.includes(value)) {
        message += '所选国家/地区不承保战争险和罢工险\n'

        // 批改不清空，批改不允许修改主险
        if (oldValue !== '' && value !== oldValue) {
          if (this.$route.query.from !== 'edit') {
            this.form.main_clause_id = ''
          }
          this.form.additional_clause_ids = []
        }
      }

      if (message) {
        this.$alert(message, '投保提醒', {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true
        })
      }
    },
    'form.coverage'(value) {
      if (
        value * this.currency?.rate >= 1000000 &&
        this.form.transport_method_id == 2 &&
        this.product?.company?.identifier === 'PICC'
      ) {
        this.$set(this.rules, 'ship_construction_year', [
          { required: true, message: '请填写船舶建造年', trigger: ['blur', 'change'] }
        ])
      } else {
        delete this.rules.ship_construction_year
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    mainClauses() {
      let majorClauses =
        this.product?.clauses?.filter((c) => c.transport_method_ids.includes(this.form.transport_method_id)) || []

      if (!this.form.is_new) {
        majorClauses = majorClauses.filter((c) => c.is_allow_used) || []
      }

      const warStrikeRegions = this.product?.additional?.war_strike_regions?.split('|') || []
      if (warStrikeRegions.includes(this.form.departure) || warStrikeRegions.includes(this.form.destination)) {
        majorClauses = majorClauses.filter((c) => !c.is_disabled_war_strike) || []
      }

      return majorClauses
    },
    additionalClauses() {
      let additionalClauses =
        this.product?.clauses?.find((c) => c.id === this.form.main_clause_id)?.additional_clauses || []

      // 如果不是新货，只能选择允许旧货的附加条款
      if (!this.form.is_new) {
        additionalClauses = additionalClauses.filter((c) => c.is_allow_used)
      }

      const warStrikeRegions = this.product?.additional?.war_strike_regions?.split('|') || []
      if (warStrikeRegions.includes(this.form.departure) || warStrikeRegions.includes(this.form.destination)) {
        additionalClauses = additionalClauses.filter((c) => !c.is_disabled_war_strike) || []
      }

      // 仓至港国家不允许选择仓至仓条款
      const only_port_regions = this.product?.additional?.only_port_regions?.split('|') || []
      if (only_port_regions.includes(this.form.insure_type === 2 ? this.form.departure : this.form.destination)) {
        additionalClauses = additionalClauses.filter((c) => !c.is_warehouse_to_warehouse) || []
      }

      return additionalClauses
    },
    clauseContents() {
      let content = ''
      if (!this.form.is_credit) {
        if (this.form.main_clause_id) {
          const clauses = this.product?.clauses?.find((c) => c.id === this.form.main_clause_id)
          content += (clauses?.content || '') + '\n'
          if (this.form.additional_clause_ids.length > 0) {
            clauses?.additional_clauses
              ?.filter((e) => this.form.additional_clause_ids.includes(e.id))
              .forEach((item) => {
                content += (item?.content || '') + '\n'
              })
          }
        }
      } else {
        content = this.form.clause_content
      }

      return content === undefined ? '' : content
    },
    shippingDatePrintFormatLabel() {
      return dayjs(this.form.shipping_date).isValid()
        ? dayjs(this.form.shipping_date).format('MMM.DD, YYYY')
        : '请选择起运日期'
    },
    showAntiDatedFileUploader() {
      const antiDatedDays = this.antiDatedDays
      const shippingDate = dayjs(this.form.shipping_date)
      if (antiDatedDays > 0 && shippingDate.isValid()) {
        return dayjs().isAfter(shippingDate.add(antiDatedDays, 'day'))
      }

      return false
    },
    shippingDateOptions() {
      const antiDatedDays = this.antiDatedDays
      return {
        disabledDate(time) {
          if (antiDatedDays === undefined || antiDatedDays <= 0) {
            return time.getTime() <= dayjs(Date.now()).subtract(1, 'day').toDate()
          }
        }
      }
    },
    policyholderIdCardNoLabel() {
      if (this.form.policyholder_type === 1) {
        if (this.form.policyholder_overseas === 1) {
          return '投保人护照号'
        } else {
          return '投保人身份证号'
        }
      } else {
        return '投保人统一社会信用代码'
      }
    },
    insuredIdCardNoLabel() {
      if (this.form.insured_type === 1) {
        if (this.form.insured_overseas === 1) {
          return '被保人护照号'
        } else {
          return '被保人身份证号'
        }
      } else {
        return '被保人统一社会信用代码'
      }
    },
    antiDatedDays() {
      let date = this?.product?.additional?.anti_dated_days
      if (this?.product?.additional?.anti_date_is_in_transports == 1) {
        date = this?.product?.additional?.anti_date_transport_data.find(
          (item) => item.transport_id == this.form.transport_method_id
        )?.value
      }
      return date
    }
  },
  created() {
    if (this.$route.query.from === undefined) {
      this.form.policyholder = this.user.name
      this.form.shipping_date = Date.now()
    }

    getSubjects().then((r) => (this.subjects = r.data))
  },
  methods: {
    async checkBlacklist(name, column, value) {
      try {
        const { data } = await blacklistApi.check({
          column,
          value,
          product_type: PRODUCT_TYPE
        })

        if (data.is_blocked) {
          this.$alert(
            `<b class="text-danger">${name}为黑名单用户，请立即停止投保！</b><br />  任何试图规避规则或利用系统漏洞的行为均将被系统完整记录并追踪。对于经核实的恶意投保行为，本公司保留依法追责的权利。如需正常投保，请联系客服核实相关信息。`,
            `信息异常`,
            {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定'
            }
          )
        }
      } catch (e) {
        console.log(e)
      }
    },
    async handleValidateGoodsName(force = false) {
      this.form.with_user_special_and_deductible = 0

      // 排除人工审核
      if (this.form.subject_id === 3) {
        return
      }

      if (this.form.goods_name && (this.form.goods_name !== this.lastGoodsName || force)) {
        this.lastGoodsName = this.form.goods_name
        const { data } = await getGoodsSuggestion({
          subject_id: this.form.subject_id,
          company_branch_id: this.product?.company?.branch?.id || 0,
          product_type: PRODUCT_TYPE,
          keyword: this.form.goods_name
        })

        if (data.condition.matched) {
          const message =
            data.condition.action === 1
              ? `根据您所填写货物名称，系统判定此货物<span class="text-danger">${
                  data.condition.keyword
                }</span>需要附加免赔和特别约定，请确认是否附加？若不附加，请选择<span class="text-danger">人工审核</span>投保。<br />免赔：${
                  data.condition.deductible || ''
                }<br />特别约定：${data.condition.special_agreement || ''}`
              : `根据您所填写货物名称，系统判定此货物<span class="text-danger">${data.condition.keyword}</span>需要转人工审核投保，请确认是否转人工审核？`
          this.$confirm(message, '投保提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            dangerouslyUseHTMLString: true
          })
            .then(() => {
              this.form.with_user_special_and_deductible = 1
              if (data.condition.action === 1) {
                this.form.deductible = data.condition.deductible
                this.form.special_agreement = data.condition.special_agreement
              }
              // 转人工审核
              if (data.condition.action === 2) {
                this.form.subject_id = 3
                this.form.with_user_special_and_deductible = 0
                this.form.deductible = ''
                this.form.special_agreement = ''
              }
            })
            .catch(() => {
              this.form.deductible = ''
              this.form.special_agreement = ''
              // 转人工审核
              if (data.condition.action === 2) {
                this.form.goods_name = ''
                this.lastGoodsName = ''
              } else {
                this.form.with_user_special_and_deductible = 0
                this.form.subject_id = 3
              }
            })
        } else {
          this.form.deductible = ''
          this.form.special_agreement = ''
        }

        if (data.subject.subject) {
          const description =
            data.subject.subject === '人工审核'
              ? `根据您所填写货物名称，系统判断此货物可能为<span class="text-danger">人工审核货物</span>，请再次确认您的货物是否为<span class="text-danger">人工审核货物</span>？如果为<span class="text-danger">人工审核货物</span>，请转至<span class="text-danger">人工审核</span>端口投保或联系业务人员咨询。`
              : `根据您所填写货物名称，系统判断此货物可能为<span class="text-danger">${data.subject.subject}</span>，请再次确认您的货物是否为<span class="text-danger">${data.subject.subject}</span>？如果为<span class="text-danger">${data.subject.subject}</span>，请转至<span class="text-danger">${data.subject.subject}</span>端口投保或联系业务人员咨询。`

          this.$confirm(description, '投保提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning',
            dangerouslyUseHTMLString: true,
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
            .then(() => {
              this.$alert('请正确选择货物类别投保或联系业务人员。', '投保提醒')
              // why: 我只要删了它就绕不过这个验证!!
              this.form.goods_name = ''
              this.lastGoodsName = ''
              this.form.execpt_goods_alert = []
            })
            .catch(() => {
              this.form.execpt_goods_alert = [data.subject.subject, description]
            })
        }
      }
    },
    fetchSuggestions(column, value, cb) {
      getSuggestions(this.product?.company?.id, column, value).then((r) => {
        const data = r.data.map((item) => {
          item.value = item[column]
          return item
        })

        cb(data)
      })
    },
    selectSuggestion(value) {
      delete value.value

      Object.assign(this.form, {}, value)

      this.$refs.form.clearValidate(Object.keys(value))
    },
    handleSubjectChange(subjectId) {
      if (parseInt(subjectId, 10) === 0 || subjectId === -1 || subjectId === this.form.subject_id) {
        return
      }

      this.$set(this.form, 'subject_id', subjectId)
      this.$emit('update:subjectId', subjectId)
    },
    handlePreview() {
      this.$refs.form.validate((valid, errors) => {
        if (valid) {
          if (this.form.coverage * this.currency?.rate > this.product?.additional?.coverage) {
            return this.$message.error('保险金额超限请转人工审核进行询价')
          }

          if (this.form.transmit && !this.form.transmit_addr) {
            // return this.$message.error('中转地保单地址不能为空')
          }

          const data = Object.assign({}, this.form)

          if (!data.deductible) {
            data.deductible = this.product?.additional?.deductible
            data.special_agreement = this.product?.additional?.special_agreement
          }

          const sanctionist = this.product?.additional?.sanctionist?.split('|') || []
          if (sanctionist.includes(this.form.departure) || sanctionist.includes(this.form.destination)) {
            data.is_sanctionist = true
          }

          const only_port_regions = this.product?.additional?.only_port_regions?.split('|') || []
          if (only_port_regions.includes(this.form.departure) || only_port_regions.includes(this.form.destination)) {
            data.is_only_port = true
          }

          this.$emit(
            'on-preview',
            this.product,
            this.marshalData(Object.assign({}, this.policy)),
            this.marshalData(data)
          )
        } else {
          window.alert_validate_errors(errors)
        }
      })
    },
    handleSave() {
      const data = Object.assign({}, this.form)

      this.$emit('on-save', this.product, this.marshalData(data))
    },
    marshalData(data) {
      if (Object.keys(data).length <= 0) {
        return data
      }

      for (const field of [
        'shipping_date',
        'policyholder_idcard_issue_date',
        'policyholder_idcard_valid_till',
        'insured_idcard_issue_date',
        'insured_idcard_valid_till'
      ]) {
        if (data[field]) {
          data[field] = dayjs(data[field]).isValid() ? dayjs(data[field]).format('YYYY-MM-DD') : ''
        }
      }
      if (data.ship_construction_year) {
        data.ship_construction_year = dayjs(data.ship_construction_year).isValid()
          ? dayjs(data.ship_construction_year).format('YYYY')
          : ''
      }

      for (const fileField of ['anti_dated_file', 'custom_file']) {
        if (typeof data[fileField] === 'string') {
          data[fileField] = ''
        }
      }

      if (this.product?.company?.identifier == 'TPIC') {
        data.departure = data.departure_addr ? data.departure + ':' + data.departure_addr : data.departure
        data.transmit = data.transmit_addr ? data.transmit + ':' + data.transmit_addr : data.transmit
        data.destination = data.destination_addr ? data.destination + ':' + data.destination_addr : data.destination
      } else {
        data.transmit = data.transmit ? data.transmit + ':' + data.transmit_addr : data.transmit_addr
      }

      data.product_id = this.product?.id

      return data
    },
    fetchProducts() {
      let filter = {
        filter: {
          type: PRODUCT_TYPE,
          subject_id: this.form.subject_id
        }
      }

      if (this.$route.params.id !== undefined && this.$route.query.from === 'edit') {
        filter.id = this.form.product_id

        // hack: 编辑模式阻止 id 为 -1 的请求
        if (filter.id === -1) {
          return
        }
      }

      getProducts(filter).then((r) => {
        this.products = r.data
      })
    },
    submitPreview() {
      if (
        this?.product?.channel?.platform?.includes(this.user.platform_id) &&
        this?.product?.additional?.inform?.length > 1
      ) {
        this.informVisible = true
        return
      }
      this.handlePreview()
    },
    checkingInform() {
      this.informVisible = false
      this.handlePreview()
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-form {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}
::v-deep .transport-alert .el-alert__title {
  font-size: 16px;
}
</style>
