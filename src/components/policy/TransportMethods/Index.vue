<template>
  <el-select placeholder="请选择运输方式" v-model="transportMethodId" class="w-100">
    <el-option v-for="m in transportMethods" :key="m.id" :value="m.id" :label="m.display_name"></el-option>
  </el-select>
</template>

<script>
import { getProductTransportMethods } from '@/apis/product'

export default {
  name: 'ComponentTransportMethods',
  props: {
    value: {
      require: true,
      type: [String, Number]
    },
    productId: {
      require: false,
      type: [String, Number]
    },
    defaultMethod: {
      type: String,
      default: '公路运输'
    },
    values: {
      require: false,
      type: Array
    }
  },
  data() {
    return {
      transportMethods: [],
      transportMethodId: ''
    }
  },
  watch: {
    value(value) {
      this.transportMethodId = value
    },
    transportMethodId(value) {
      this.$emit('input', value)
      this.$emit('loadingMethods', this.transportMethods.find((m) => m.id === value)?.loading_methods || [])
    },
    productId(value) {
      if (value) {
        this.fetchProductTransportMethods()
      }
    },
    values(values) {
      if (values.length > 0) {
        this.transportMethods = values
        this.setDefault()
      }
    }
  },
  methods: {
    fetchProductTransportMethods() {
      if (this.productId <= 0) {
        return
      }
      getProductTransportMethods(this.productId).then((r) => {
        this.transportMethods = r.data
        this.setDefault()
      })
    },
    setDefault() {
      if (this.$route.query.from === undefined) {
        const method = this.transportMethods.find((m) => m.display_name === this.defaultMethod)
        if (method) {
          this.transportMethodId = method.id
        }
      }

      if (this.transportMethodId) {
        this.$emit(
          'loadingMethods',
          this.transportMethods.find((m) => m.id === this.transportMethodId)?.loading_methods || []
        )
      }
    }
  }
}
</script>
