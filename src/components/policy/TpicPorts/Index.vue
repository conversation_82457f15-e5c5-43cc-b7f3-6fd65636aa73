<template>
  <div>
    <el-select placeholder="请选择港口" v-model="port" class="w-100" filterable required>
      <el-option v-for="tp in taipingPorts" :key="tp.code" :value="tp.txt" :label="tp.txt"></el-option>
    </el-select>
  </div>
</template>

<script>
import taipingPorts from './tpic-ports'

export default {
  name: 'TpicPorts',
  props: {
    value: {
      require: true,
      type: String
    },
    suggestion: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      taipingPorts,
      port: ''
    }
  },
  watch: {
    value(value) {
      this.port = value
    },
    suggestion(value) {
      if (value.length > 1) {
        const suggestionAddr = value[1].slice(0, 2)
        const taipingPort = this.taipingPorts.find((p) => p.txt.indexOf(suggestionAddr) > -1)
        if (taipingPort) {
          this.port = taipingPort.txt
        }
      }
    },
    port(value) {
      this.$emit('input', value)
    }
  },
  mounted() {
    this.port = this.value
  }
}
</script>
