<template>
  <el-dialog :visible.sync="visible" title="修改密码" :before-close="handleBeforeClose" destroy-on-close width="520px">
    <el-form ref="form" :model="form" :rules="rules" label-suffix=":" width="520px">
      <el-form-item prop="old_password" label="原密码">
        <el-input type="password" v-model="form.old_password" placeholder="请输入旧密码" show-password></el-input>
      </el-form-item>
      <el-form-item prop="password" label="新密码">
        <el-input
          type="password"
          v-model="form.password"
          placeholder="请输入新密码"
          show-password
          auto-complete="new-password"
        ></el-input>
      </el-form-item>
      <el-form-item prop="password_confirmation" label="确认新密码">
        <el-input
          type="password"
          v-model="form.password_confirmation"
          placeholder="请确认新密码"
          show-password
          auto-complete="new-password"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleBeforeClose" icon="fas fa-times">取消</el-button>
        <el-button type="primary" @click="handleSubmit" icon="fas fa-check">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'AccountPasswordForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        old_password: '',
        password: '',
        password_confirmation: ''
      },
      rules: {
        old_password: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        password_confirmation: [{ required: true, message: '请确认密码', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)

          this.$emit('update:visible', false)

          this.$refs.form.resetFields()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
