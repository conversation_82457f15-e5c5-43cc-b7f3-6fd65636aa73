<template>
  <el-dialog :visible.sync="visible" title="充值记录" :before-close="handleBeforeClose" destroy-on-close width="800px">
    <define-table :attrs="{ border: true }" :cols="cols" :data="data" :paging="paging" :paging-events="pagingEvents" />
  </el-dialog>
</template>

<script>
export default {
  name: 'AccountPayments',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    },
    currentPage: {
      type: Number,
      default: 1
    },
    total: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      cols: [
        { label: '交易流水号', prop: 'order_no' },
        { label: '充值金额', prop: 'amount' },
        {
          label: '充值类型',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.charge_type) {
                case 1:
                  return '真实充值'
                case 2:
                  return '虚拟充值'
                case 3:
                  return '补缴欠款'
                case 4:
                  return '支付保险公司'
                case 5:
                  return '系统扣费'
                case 6:
                  return '代理充值'
                case 7:
                  return '代理扣费'
                default:
                  return '未知'
              }
            }
          }
        },
        {
          label: '状态',
          width: 160,
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.status) {
                case 1:
                  return '已提交'
                case 2:
                  return '已充值'
                case 3:
                  return '已退回'
                default:
                  return '未知'
              }
            }
          }
        },
        { label: '退回原因', prop: 'reason', width: '150' },
        { label: '交易时间', prop: 'operated_at', width: '150' }
      ],
      pagingEvents: {
        currentChange: (page) => {
          this.$emit('page-update', page)
        }
      },
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      }
    }
  },
  watch: {
    currentPage(value) {
      this.paging.currentPage = value
    },
    total(value) {
      this.paging.total = value
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
