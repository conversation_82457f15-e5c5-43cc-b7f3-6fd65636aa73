<template>
  <el-dialog
    :visible.sync="visible"
    title="修改发票信息"
    :before-close="handleBeforeClose"
    destroy-on-close
    width="520px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-suffix=":" width="520px">
      <el-form-item prop="company_name" label="单位名称">
        <el-input v-model="form.company_name" placeholder="请输入单位名称"></el-input>
      </el-form-item>
      <el-form-item prop="tax_no" label="纳税人识别号">
        <el-input v-model="form.tax_no" placeholder="请输入纳税人识别号"></el-input>
      </el-form-item>
      <el-form-item prop="registered_addr" label="注册地址">
        <el-input v-model="form.registered_addr" placeholder="请输入注册地址"></el-input>
      </el-form-item>
      <el-form-item prop="registered_phone_number" label="注册电话">
        <el-input v-model="form.registered_phone_number" placeholder="请输入注册电话"></el-input>
      </el-form-item>
      <el-form-item prop="bank_name" label="开户银行">
        <el-input v-model="form.bank_name" placeholder="请输入开户银行"></el-input>
      </el-form-item>
      <el-form-item prop="bankcard_no" label="银行账户">
        <el-input v-model="form.bankcard_no" placeholder="请输入银行账户"></el-input>
      </el-form-item>
      <el-form-item prop="recipient" label="收件人姓名">
        <el-input v-model="form.recipient" placeholder="请输入收件人姓名"></el-input>
      </el-form-item>
      <el-form-item prop="recipient_phone_number" label="收件人电话">
        <el-input v-model="form.recipient_phone_number" placeholder="请输入收件人电话"></el-input>
      </el-form-item>
      <el-form-item prop="delivery_address" label="配送地址">
        <el-input v-model="form.delivery_address" placeholder="请输入配送地址"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleBeforeClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'AccountInvoiceForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: [Object, Array],
      default: () => {}
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(value) {
        if (Object.keys(value).length > 0) {
          this.form = Object.assign({}, value)
        }
      }
    }
  },
  data() {
    return {
      form: {
        company_name: '',
        bankcard_no: '',
        bank_name: '',
        tax_no: '',
        registered_addr: '',
        registered_phone_number: '',
        recipient: '',
        recipient_phone_number: '',
        delivery_address: ''
      },
      rules: {
        company_name: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
        tax_no: [{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)

          this.$emit('update:visible', false)

          this.$refs.form.resetFields()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
