<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2023-12-21 14:11:23
 * @LastEditors: yanb
 * @LastEditTime: 2024-07-26 17:34:05
-->
<template>
  <el-dialog
    :visible.sync="visible"
    title="修改个人信息"
    :before-close="handleBeforeClose"
    destroy-on-close
    width="520px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-suffix=":" width="520px">
      <el-form-item prop="name" label="姓名">
        <el-input :disabled="true" v-model="form.name" placeholder="请输入姓名"></el-input>
      </el-form-item>
      <el-form-item prop="phone_number" label="联系电话">
        <el-input v-model="form.phone_number" placeholder="请输入联系电话"></el-input>
      </el-form-item>
      <el-form-item prop="idcard_no" label="身份证号">
        <el-input v-model="form.idcard_no" placeholder="请输入身份证号"></el-input>
      </el-form-item>
      <el-form-item prop="email" label="邮箱">
        <el-input v-model="form.email" placeholder="请输入邮箱"></el-input>
      </el-form-item>
      <el-form-item prop="address" label="地址">
        <el-input type="textarea" autoresize :rows="3" v-model="form.address" placeholder="请输入地址"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleBeforeClose" icon="fas fa-times">取消</el-button>
        <el-button type="primary" @click="handleSubmit" icon="fas fa-check">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'AccountProfileForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(value) {
        if (value.name !== undefined) {
          this.form = Object.assign({}, value)
        }
      }
    }
  },
  data() {
    return {
      form: {
        phone_number: '',
        idcard_no: '',
        email: '',
        address: ''
      },
      rules: {
        phone_number: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        idcard_no: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
        email: [{ required: true, message: '请输入邮箱地址', trigger: 'blur' }],
        address: [{ required: true, message: '请输入地址', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('update:visible', false)

          this.$emit('submit', this.form)

          this.$refs.form.resetFields()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
