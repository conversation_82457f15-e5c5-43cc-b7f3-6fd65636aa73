<template>
  <el-dialog :visible.sync="visible" :title="title" :before-close="handleClose" destroy-on-close width="520px">
    <el-form ref="form" :model="form" :rules="rules" label-suffix=":" width="520px">
      <el-form-item prop="bankcard_no" label="银行卡号">
        <el-input v-model="form.bankcard_no" placeholder="请输入银行卡号"></el-input>
      </el-form-item>
      <el-form-item prop="bank_name" label="开户行">
        <el-input v-model="form.bank_name" placeholder="请输入开户行"></el-input>
      </el-form-item>
      <el-form-item prop="cardholder" label="联系人">
        <el-input v-model="form.cardholder" placeholder="请输入联系人"></el-input>
      </el-form-item>
      <el-form-item prop="phone_number" label="联系电话">
        <el-input v-model="form.phone_number" placeholder="请输入联系电话"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'AccountBankcardForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(value) {
        if (Object.keys(value).length > 0) {
          this.form = Object.assign({}, value)
        }
      }
    }
  },
  computed: {
    title() {
      return this.form.id !== undefined ? '修改银行卡' : '添加银行卡'
    }
  },
  data() {
    return {
      form: {
        bankcard_no: '',
        bank_name: '',
        cardholder: '',
        phone_number: ''
      },
      rules: {
        bankcard_no: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }],
        bank_name: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
        cardholder: [{ required: true, message: '请输入持卡人', trigger: 'blur' }],
        phone_number: [{ required: true, message: '请输入联系方式', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()

      this.form = {
        bankcard_no: '',
        bank_name: '',
        cardholder: '',
        phone_number: ''
      }
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', Object.assign({}, this.form))

          this.handleClose()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
