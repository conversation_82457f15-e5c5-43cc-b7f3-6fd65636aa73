<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 10:42:14
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-08 14:50:46
-->
<template>
  <div class="policy-wrap">
    <site-breadcrumb>
      <div class="m-mini-x">
        <el-button
          icon="fas fa-edit"
          v-if="editable"
          type="primary"
          @click="$router.push({ name: 'PoliciesGroupEmployee', params: { policyGroupId: $route.params.id, step: 1 } })"
        >
          修改保单
        </el-button>
        <el-button
          icon="fas fa-users"
          type="primary"
          @click="$router.push({ name: 'PoliciesGroupPayments', params: { id: $route.params.id } })"
        >
          支付记录
        </el-button>
        <el-button
          icon="el-icon-document-copy"
          type="primary"
          @click="$router.push({ name: 'PoliciesGroupEmployee', params: { id: $route.params.id } })"
        >
          {{ policy.status === 0 ? '继续投保' : '人员列表' }}
        </el-button>
        <el-popconfirm
          title="确定作废吗?"
          class="m-mini-l"
          @confirm="handleDestroy"
          v-if="[0, 7, 10].includes(policy.status)"
        >
          <el-button icon="fas fa-trash" slot="reference" type="primary"> 作废 </el-button>
        </el-popconfirm>
        <el-button icon="el-icon-download" type="primary" v-if="downloadable" @click="download">下载保单</el-button>
      </div>
    </site-breadcrumb>
    <div class="m-extra-large-x p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <div class="info-box" v-if="this.policy.status === 10">
        <el-alert
          center
          title="保单已退回"
          :description="this.policy.sendback_reason"
          type="warning"
          effect="dark"
          show-icon
          :closable="false"
        >
        </el-alert>
      </div>
      <define-details :data="detail"></define-details>
    </div>
  </div>
</template>

<script>
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import { buildDownloadHref, destroyDraft } from '@/apis/policy'
import { getGroupPolicy } from '@/apis/groupProduct'
import dayjs from 'dayjs'

export default {
  name: 'PoliciesGroupDetails',
  components: {
    SiteBreadcrumb
  },
  data() {
    return {
      policy: {},
      policyGroup: {},
      certTypes: {
        '-1': '请选择证件类型',
        '01': '身份证',
        '02': '军官证',
        '03': '学生证',
        '04': '台胞证',
        '06': '护照',
        '07': '港澳返乡证',
        '08': '出生证明（未成年人）',
        '09': '营业执照',
        10: '工商登记号',
        11: '组织机构代码',
        13: '统一社会信用代码',
        14: '港澳台居民居住证',
        99: '其他'
      }
    }
  },
  computed: {
    editable() {
      return [10].includes(this.policy.status)
    },
    invalidable() {
      return [0].includes(this.policy.status)
    },
    downloadable() {
      return [5].includes(this.policy.status)
    },
    thirdPlatform() {
      return this.policyGroup?.product?.additional?.third_platform
    },
    detail() {
      let zhongyi = {
        title: '保单详情 - ' + this.policy?.status_text,
        label_width: '200px',
        data: [
          {
            title: '投保人信息',
            groups: [
              { label: '企业法人', value: this.policy?.policyholder },
              { label: '手机号', value: this.policy?.policyholder_phone_number },
              { label: '证件类型', value: this.certTypes[this.policyGroup?.extra_info?.policyholder_idcard_type] },
              { label: '证件号', value: this.policy?.policyholder_idcard_no }
            ]
          },
          {
            title: '被保联系人',
            groups: [
              { label: '企业名称', value: this.policy?.insured },
              { label: '联系电话', value: this.policy?.insured_phone_number },
              { label: '证件类型', value: this.certTypes[this.policyGroup?.extra_info?.insured_idcard_type] },
              { label: '证件号', value: this.policy?.insured_idcard_no }
            ]
          },
          {
            title: '投保信息',
            groups: [
              { label: '出单公司', value: this.policy?.company_branch?.name },
              { label: '保单号', value: this.policy?.policy_no },
              { label: '投保产品', value: this.policyGroup?.product?.name },
              { label: '投保套餐', value: this.policyGroup?.plan?.title },
              { label: '在保人数', value: this.policyGroup?.insured_employee_count },
              { label: '保费', value: this.policy?.user_premium },
              {
                label: '更新时间',
                value: this.policy?.updated_at && dayjs(this.policy?.updated_at).format('YYYY-MM-DD HH:mm:ss')
              },
              {
                label: '投保时间',
                value: this.policy?.submitted_at && dayjs(this.policy?.submitted_at).format('YYYY-MM-DD HH:mm:ss')
              },
              { label: '起保日期', value: this.policyGroup?.start_at },
              { label: '终保日期', value: this.policyGroup?.end_at }
            ]
          },
          {
            title: '标的信息',
            groups: [
              { label: '省份', value: this.policyGroup?.extra_info?.object_address[0] },
              {
                label: '市区',
                value: this.policyGroup?.extra_info?.object_address[1]
              },
              {
                label: '详细地址',
                value: this.policyGroup?.extra_info?.object_address_detail
              },
              {
                label: '',
                value: ''
              }
            ]
          }
        ]
      }
      let standard = {
        title: '保单详情 - ' + this.policy?.status_text,
        label_width: '200px',
        data: [
          {
            title: '投保联系人',
            groups: [
              { label: '姓名', value: this.policyGroup?.contact_name },
              { label: '联系电话', value: this.policyGroup?.contact_phone }
            ]
          },
          {
            title: '投保信息',
            groups: [
              { label: '出单公司', value: this.policy?.company_branch?.name },
              { label: '保单号', value: this.policy?.policy_no },
              { label: '投保产品', value: this.policyGroup?.product?.name },
              { label: '投保套餐', value: this.policyGroup?.plan?.title },
              { label: '投保单位', value: this.policy?.policyholder },
              { label: '被保单位', value: this.policy?.insured },
              { label: '统一社会信用代码', value: this.policy?.policyholder_idcard_no },
              { label: '统一社会信用代码', value: this.policy?.insured_idcard_no },
              { label: '在保人数', value: this.policyGroup?.insured_employee_count },
              { label: '保费', value: this.policy?.user_premium },
              {
                label: '更新时间',
                value: this.policy?.updated_at && dayjs(this.policy?.updated_at).format('YYYY-MM-DD HH:mm:ss')
              },
              {
                label: '投保时间',
                value: this.policy?.submitted_at && dayjs(this.policy?.submitted_at).format('YYYY-MM-DD HH:mm:ss')
              },
              { label: '起保日期', value: this.policyGroup?.start_at },
              { label: '终保日期', value: this.policyGroup?.end_at }
            ]
          },
          {
            title: '文件信息',
            groups: [
              {
                label: '被保单位营业执照',
                value: '点击查看',
                isLink: true,
                target: '_blank',
                to: this.policyGroup?.attachment?.business_license_file
              },
              {
                label: '委托书',
                value: '点击查看',
                isLink: true,
                target: '_blank',
                to: this.policyGroup?.attachment?.authorization_file
              },
              {
                label: '人员清单',
                value: '点击查看',
                isLink: true,
                target: '_blank',
                to: this.policyGroup?.attachment?.staff_list_file
              },
              {
                label: '人员清单盖章文件',
                value: '点击查看',
                isLink: true,
                target: '_blank',
                to: this.policyGroup?.attachment?.staff_stamp_list_file
              },
              {
                label: '投保单',
                value: '点击查看',
                isLink: true,
                target: '_blank',
                to: this.policyGroup?.attachment?.application_file
              },
              {
                label: '投保单盖章文件',
                value: '点击查看',
                isLink: true,
                target: '_blank',
                to: this.policyGroup?.attachment?.application_stamp_file
              },
              {
                label: '其它文件',
                value: '点击查看',
                isLink: true,
                row: true,
                target: '_blank',
                to: this.policyGroup?.attachment?.extra_file
              }
            ]
          },
          {
            title: '发票信息',
            groups: [
              {
                label: '发票类型',
                value: this.policyGroup?.invoice_info?.type
              },
              { label: '', value: '' },
              { label: '发票抬头', value: this.policyGroup?.invoice_info?.title },
              { label: '纳税人识别号', value: this.policyGroup?.invoice_info?.tax_no },
              { label: '开户行', value: this.policyGroup?.invoice_info?.bank_name },
              { label: '银行卡号', value: this.policyGroup?.invoice_info?.bankcard_no },
              { label: '联系电话', value: this.policyGroup?.invoice_info?.phone_number },
              { label: '注册地址', value: this.policyGroup?.invoice_info?.registered_addr }
            ]
          }
        ]
      }

      let view = standard
      switch (this.thirdPlatform) {
        case 'API_GROUP_ZY':
          view = zhongyi
          break
        default:
      }

      return view
    }
  },
  methods: {
    download() {
      if (this.policyGroup?.policy_id) {
        window.open(buildDownloadHref(this.policyGroup.policy_id))
      }
    },
    fetchDetail() {
      getGroupPolicy(this.$route.params.id).then((r) => {
        this.policyGroup = r.data
        this.policy = r.data.policy

        if (!this.policy.policy_no) {
          const text = {
            0: '未提交',
            1: '已提交',
            2: '审核中',
            3: '已支付',
            4: '已审核',
            5: '已出单',
            6: '已退保',
            7: '已作废',
            8: '批改中',
            9: '退保中',
            10: '已退回',
            11: '待确认',
            12: '待支付'
          }
          this.policyGroup.policy.policy_no = `--${text[this.policy.status]}--`
        }

        const invoiceExplain = {
          none: '不需要发票',
          normal: '普通发票',
          special: '增值税专用发票'
        }
        this.policyGroup.invoice_info.type = invoiceExplain[this.policyGroup.invoice_info.type]
      })
    },
    handleDestroy() {
      destroyDraft(this.policy.id).then(() => {
        this.$message.success('作废成功')

        this.$router.push({ name: 'PoliciesGroup' })
      })
    }
  },
  created() {
    this.fetchDetail()
  }
}
</script>

<style lang="scss" scoped>
.policy-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.info-box {
  padding-top: 12px;
}
</style>
