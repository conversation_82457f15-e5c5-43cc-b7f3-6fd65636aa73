<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 10:42:14
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-08 14:50:46
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100">
    <el-card class="table-wrap" shadow="never">
      <define-table :data="data" :cols="cols" />
    </el-card>
  </div>
</template>

<script>
import { getGroupEndorses, buildEndorseDownloadHref } from '../../apis/groupProduct'
import dayjs from 'dayjs'

export default {
  name: 'PoliciesGroupEndorse',
  data() {
    return {
      data: [],
      cols: [
        { label: '批次号', prop: 'batch_no' },
        { label: '批单号', prop: 'endorse_no' },
        {
          label: '批单文件',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.status === 1 && scoped.row.endorse_no) {
                return (
                  <el-button type="primary" onClick={() => window.open(this.handleDownload(scoped.row.id))}>
                    点击下载
                  </el-button>
                )
              } else {
                return '-'
              }
            }
          }
        },
        {
          label: '添加时间',
          prop: 'created_at',
          scopedSlots: {
            default: (scoped) => {
              return dayjs(scoped.row.created_at).format('YYYY-MM-DD HH:mm:ss')
            }
          }
        },
        {
          label: '状态',
          prop: 'status',
          scopedSlots: {
            default: (scoped) => {
              const text = {
                0: '处理中',
                1: '已通过',
                2: '已退回',
                3: '已提交'
              }
              return <span>{text[scoped.row.status]}</span>
            }
          }
        },
        { label: '备注', prop: 'content' }
      ],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      pagingEvents: {
        currentChange: (page) => {
          this.fetchEndorses(this.filter, page)
        }
      }
    }
  },
  methods: {
    fetchEndorses() {
      getGroupEndorses(this.$route.params.id).then((r) => {
        this.data = r.data
      })
    },
    handleDownload(id) {
      window.open(buildEndorseDownloadHref(id))
    }
  },
  created() {
    this.fetchEndorses()
  }
}
</script>

<style lang="scss" scoped>
.policy-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
