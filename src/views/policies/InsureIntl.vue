<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-10 00:05:29
 * @LastEditors: yanb
 * @LastEditTime: 2024-03-20 10:08:20
-->
<template>
  <div class="policy-form p-extra-large-x o-hidden-x w-100">
    <policy-progress-bar :active.sync="currentStep" :steps="steps" />
    <el-alert
      :title="'您正在复制保单 ' + orderNo"
      type="warning"
      style="margin-bottom: 10px"
      :closable="false"
      v-if="$route.query.from === 'copy'"
    />
    <intl-form v-show="currentStep === 0" :policy="policy" @on-save="handleSave" @on-preview="handlePreview" />
    <preview-intl
      v-if="currentStep === 1"
      :payment-method.sync="paymentMethod"
      :data="data"
      :origin-data="originData"
      :product="product"
      @on-back="handleBack"
      @on-save="handleSave"
      @on-submit="handleSubmit"
    />
  </div>
</template>

<script>
import { getPolicyDetails, previewPolicy, insurePolicy, saveAsDraft, updatePolicy } from '@/apis/policy'
import IntlForm from '@/components/policy/IntlForm'
import PreviewIntl from '@/components/policy/PreviewIntl'
import PolicyProgressBar from '@/components/policy/PolicyProgressBar'
import { Loading } from 'element-ui'
import countriesRegions from '@/utils/regions'

const PRODUCT_TYPE = 2

export default {
  name: 'InsureInternational',
  components: {
    IntlForm,
    PreviewIntl,
    PolicyProgressBar
  },
  data() {
    return {
      orderNo: '',
      paymentMethod: 1,
      policy: {},
      product: {},
      countriesRegions,
      originData: {},
      data: {},
      steps: ['1.填写保单', '2.计算保费', '3.提交审核', '4.保单生效'],
      currentStep: 0
    }
  },
  created() {
    if (this.$route.params.id) {
      getPolicyDetails(this.$route.params.id).then((r) => {
        const data = r.data
        this.orderNo = data.order_no
        let [departure, departureAddr] = data.detail?.departure?.split(':') || []
        let [destination, destinationAddr] = data.detail?.destination?.split(':') || []
        let [transmit, transmitAddr] = data.detail?.transmit?.split(':') || []
        if (this.countriesRegions[departure] === undefined && !departureAddr) {
          ;[departureAddr, departure] = [departure, departureAddr]
        }
        if (this.countriesRegions[destination] === undefined && !destinationAddr) {
          ;[destinationAddr, destination] = [destination, destinationAddr]
        }
        if (this.countriesRegions[transmit] === undefined && !transmitAddr) {
          ;[transmitAddr, transmit] = [transmit, transmitAddr]
        }

        let policy = {
          type: PRODUCT_TYPE,
          is_new: data?.detail?.is_new,
          product_id: data.product.id,
          subject_id: data.detail?.subject?.id || -1,
          subject_category_ids: data.detail?.subject_categories?.map((e) => e.id) || [],
          manual_conditions: data.detail?.manual_conditions,
          policyholder: data.policyholder,
          policyholder_address: data.policyholder_address,
          policyholder_phone_number: data.policyholder_phone_number,
          policyholder_type: data.policyholder_type,
          policyholder_overseas: data.policyholder_overseas,
          policyholder_idcard_no: data.policyholder_idcard_no,
          policyholder_idcard_issue_date: data.policyholder_idcard_issue_date
            ? Date.parse(data.policyholder_idcard_issue_date)
            : null,
          policyholder_idcard_valid_till: data.policyholder_idcard_valid_till
            ? Date.parse(data.policyholder_idcard_valid_till)
            : null,
          insured: data.insured,
          insured_address: data.insured_address,
          insured_type: data.insured_type,
          insured_overseas: data.insured_overseas,
          insured_phone_number: data.insured_phone_number,
          insured_idcard_no: data.insured_idcard_no,
          insured_idcard_issue_date: data.insured_idcard_issue_date ? Date.parse(data.insured_idcard_issue_date) : null,
          insured_idcard_valid_till: data.insured_idcard_valid_till ? Date.parse(data.insured_idcard_valid_till) : null,
          sticky_note: data.sticky_note,
          goods_type_id: data.detail?.goods_type?.id,
          loading_method_id: data.detail?.loading_method?.id,
          packing_method_id: data.detail?.packing_method?.id,
          goods_name: data.detail?.goods_name,
          goods_amount: data.detail?.goods_amount,
          transport_method_id: data.detail?.transport_method?.id,
          ship_construction_year: data.detail?.ship_construction_year,
          departure: departure,
          departure_port: data.detail?.departure_port,
          departure_addr: departureAddr,
          transmit: transmit,
          transmit_port: data.detail?.transmit_port,
          transmit_addr: transmitAddr,
          destination: destination,
          destination_port: data.detail?.destination_port,
          destination_addr: destinationAddr,
          insure_type: data.detail?.insure_type,
          payable_at: data.detail?.payable_at,
          transport_no: data.detail?.transport_no,
          shipping_mark: data.detail?.shipping_mark || '',
          shipping_date: Date.parse(data.detail?.shipping_date),
          shipping_date_print_format: parseInt(data.detail?.shipping_date_print_format, 10)
        }

        // 编辑需要填充所有的
        if (['edit', 'continue'].includes(this.$route.query.from)) {
          this.paymentMethod = data.payment_method
          policy = Object.assign(policy, {
            id: data.id,
            coverage: data.coverage,
            invoice_no: data.detail?.invoice_no,
            waybill_no: data.detail?.waybill_no,
            contract_no: data.detail?.contract_no,
            invoice_amount: data.detail?.invoice_amount || 0,
            invoice_currency_id: data.detail?.invoice_currency?.id,
            coverage_currency_id: data.detail?.coverage_currency?.id,
            bonus: data.detail?.bonus,
            remark: data.remark,
            anti_dated_file: data.detail?.anti_dated_file,
            custom_file: data.detail?.custom_file,
            is_credit: data.detail?.is_credit || 0,
            credit_no: data.detail?.credit_no,
            clause_content: data.detail?.clause_content || '',
            main_clause_id: data?.main_clause_id,
            additional_clause_ids: data?.additional_clause_ids
          })
        }

        const ticket = data.tickets.find((e) => e.status === 4)?.revision || {}
        if (ticket?.shipping_date !== undefined) {
          ticket.shipping_date = Date.parse(ticket.shipping_date)
        }

        policy = Object.assign({}, policy, ticket)
        console.log(policy)

        this.policy = policy
      })
    }
  },
  methods: {
    handleBack() {
      this.currentStep--
    },
    async handlePreview(product, originData, form) {
      this.product = product

      const loading = Loading.service()
      if (Object.keys(originData).length > 0 && this.$route.query.from === 'edit') {
        await previewPolicy(Object.assign({}, originData)).then((r) => {
          if (originData.id !== undefined) {
            this.originData = Object.assign({}, r.data, { id: originData.id })
            this.originData.policy_id = originData.id
          } else {
            this.originData = Object.assign({}, r.data, originData)
          }
        })
      }

      await previewPolicy(form)
        .then((r) => {
          if (form.id !== undefined) {
            this.data = Object.assign({}, r.data, { id: form.id })
            this.data.policy_id = form.id
          } else {
            this.data = Object.assign({}, r.data, form)
          }

          this.data.anti_dated_file = form.anti_dated_file
          this.data.custom_file = form.custom_file
        })
        .finally(() => loading.close())

      this.currentStep++
    },
    handleSave(product, data) {
      if (this.$route.params.id) {
        if (['edit', 'continue'].includes(this.$route.query.from)) {
          data.policy_id = this.$route.params.id
        }
      }

      const loading = Loading.service()
      saveAsDraft(data)
        .then((data) => {
          this.$message.success('保存成功')

          this.$router.push({ name: 'PoliciesIntlDetails', params: { id: data.id } })
        })
        .finally(() => loading.close())
    },
    cleanData(data) {
      delete data.user_rate
      delete data.user_premium
      delete data.packing_method
      delete data.loading_method
      delete data.transport_method
      delete data.goods_type
      delete data.subject
      delete data.subject_category
      delete data.clauses
      delete data.coverage_currency

      return data
    },
    handleSubmit() {
      const loading = Loading.service()
      this.data.payment_method = this.paymentMethod
      if (this.data.id !== undefined && this.$route.query.from === 'edit') {
        if (this.$route.query.ticket_id !== undefined) {
          this.data.ticket_id = this.$route.query.ticket_id
          if (this.data.custom_file === '' && this.policy.custom_file !== '') {
            this.data.custom_file = this.policy.custom_file
          }
          if (this.data.anti_dated_file === '' && this.policy.anti_dated_file !== '') {
            this.data.anti_dated_file = this.policy.anti_dated_file
          }
        }
        updatePolicy(this.data.id, this.cleanData(this.data))
          .then((r) => {
            this.$message.success('提交成功')
            if (this.paymentMethod === 2 && r?.data?.payment_url) {
              window.open(r.data.payment_url, '_blank')
            }

            this.$router.push({ name: 'PoliciesIntlDetails', params: { id: this.data.id } })
          })
          .finally(() => loading.close())
      } else {
        if (this.$route.query.from === 'copy') {
          delete this.data.id
          delete this.data.policy_id
        }

        insurePolicy(this.cleanData(this.data))
          .then((r) => {
            this.$message.success('提交成功')
            this.$store.dispatch('auth/refreshUser')
            if (this.paymentMethod === 2 && r?.data?.payment_url) {
              window.open(r.data.payment_url, '_blank')
            }

            this.$router.push({ name: 'PoliciesIntl' })
          })
          .finally(() => loading.close())
      }
    }
  }
}
</script>
