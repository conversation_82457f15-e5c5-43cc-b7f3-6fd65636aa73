<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-10 00:05:29
 * @LastEditors: yanb
 * @LastEditTime: 2023-06-28 09:59:09
-->
<template>
  <div class="policy-form p-extra-large-x w-100">
    <policy-progress-bar :active.sync="currentStep" :steps="steps" />
    <el-alert
      :title="'您正在复制保单 ' + orderNo"
      type="warning"
      style="margin-bottom: 10px"
      :closable="false"
      v-if="$route.query.from === 'copy'"
    />
    <cbec-form v-show="currentStep === 0" :policy="policy" @on-save="handleSave" @on-preview="handlePreview" />
    <preview-cbec
      v-if="currentStep === 1"
      :data="data"
      :origin-data="originData"
      :product="product"
      @on-back="handleBack"
      @on-save="handleSave"
      @on-submit="handleSubmit"
    />
  </div>
</template>

<script>
import { getPolicyDetails, previewPolicy, insurePolicy, saveAsDraft, updatePolicy } from '@/apis/policy'
import CbecForm from '@/components/policy/CbecForm'
import PreviewCbec from '@/components/policy/PreviewCbec'
import PolicyProgressBar from '@/components/policy/PolicyProgressBar'
import { Loading } from 'element-ui'
import countriesRegions from '@/utils/regions'

const PRODUCT_TYPE = 7

export default {
  name: 'InsureCbec',
  components: {
    'cbec-form': CbecForm,
    'preview-cbec': PreviewCbec,
    'policy-progress-bar': PolicyProgressBar
  },
  data() {
    return {
      orderNo: '',
      policy: {},
      product: {},
      countriesRegions,
      originData: {},
      data: {},
      steps: ['1.填写保单', '2.计算保费', '3.提交审核', '4.保单生效'],
      currentStep: 0
    }
  },
  created() {
    if (this.$route.params.id) {
      getPolicyDetails(this.$route.params.id).then((r) => {
        const data = r.data
        this.orderNo = data.order_no
        let [transmit, transmitAddr] = data.detail?.transmit?.split(':') || []
        if (this.countriesRegions[transmit] === undefined && !transmitAddr) {
          ;[transmitAddr, transmit] = [transmit, transmitAddr]
        }

        let policy = {
          type: PRODUCT_TYPE,
          is_new: data?.detail?.is_new,
          product_id: data.product.id,
          subject_id: data.detail?.subject?.id || -1,
          subject_category_ids: data.detail?.subject_categories?.map((e) => e.id) || [],
          policyholder: data.policyholder,
          policyholder_address: data.policyholder_address,
          policyholder_phone_number: data.policyholder_phone_number,
          policyholder_type: data.policyholder_type,
          policyholder_overseas: data.policyholder_overseas,
          policyholder_idcard_no: data.policyholder_idcard_no,
          policyholder_idcard_issue_date: data.policyholder_idcard_issue_date
            ? Date.parse(data.policyholder_idcard_issue_date)
            : null,
          policyholder_idcard_valid_till: data.policyholder_idcard_valid_till
            ? Date.parse(data.policyholder_idcard_valid_till)
            : null,
          insured: data.insured,
          insured_address: data.insured_address,
          insured_type: data.insured_type,
          insured_overseas: data.insured_overseas,
          insured_phone_number: data.insured_phone_number,
          insured_idcard_no: data.insured_idcard_no,
          insured_idcard_issue_date: data.insured_idcard_issue_date ? Date.parse(data.insured_idcard_issue_date) : null,
          insured_idcard_valid_till: data.insured_idcard_valid_till ? Date.parse(data.insured_idcard_valid_till) : null,
          sticky_note: data.sticky_note,
          loading_method_id: data.detail?.loading_method?.id,
          goods_name: data.detail?.goods_name,
          goods_amount: data.detail?.goods_amount,
          transport_method_id: data.detail?.transport_method?.id,
          departure: data.detail?.departure,
          departure_port: data.detail?.departure_port,
          transmit: transmit,
          transmit_addr: transmitAddr,
          destination: data.detail?.destination,
          destination_port: data.detail?.destination_port,
          insure_type: data.detail?.insure_type,
          payable_at: data.detail?.payable_at,
          transport_no: data.detail?.transport_no,
          shipping_mark: data.detail?.shipping_mark || '',
          shipping_date: Date.parse(data.detail?.shipping_date),
          shipping_date_print_format: data.detail?.shipping_date_print_format?.toString(),
          coverage_scope: data?.detail?.coverage_scope || -1,
          company_branch_id: data?.company_branch?.id || -1,
          destination_type_id: data.detail?.destination_type?.id || '',
          delivery_method_id: data.detail?.delivery_method?.id || '',
          express_company_id: data.detail?.express_company?.id || ''
        }

        // 编辑需要填充所有的
        if (['edit', 'continue'].includes(this.$route.query.from)) {
          policy = Object.assign(policy, {
            id: data.id,
            coverage: data.coverage,
            invoice_no: data.detail?.invoice_no,
            weight: data.detail?.weight || 0,
            waybill_no: data.detail?.waybill_no,
            contract_no: data.detail?.contract_no,
            invoice_amount: data.detail?.invoice_amount || 0,
            invoice_currency_id: data.detail?.invoice_currency?.id,
            coverage_currency_id: data.detail?.coverage_currency?.id,
            bonus: data.detail?.bonus,
            remark: data.remark,
            anti_dated_file: data.detail?.anti_dated_file,
            custom_file: data.detail?.custom_file,
            express_no: data.detail?.express_no,
            shipment_id: data.detail?.shipment_id || '',
            goods_type_id: data.detail?.goods_type?.id,
            packing_method_id: data.detail?.packing_method?.id,
            main_clause_id: data?.main_clause_id,
            additional_clause_ids: data?.additional_clause_ids,
            is_credit: data.detail?.is_credit || 0,
            credit_no: data.detail?.credit_no,
            clause_content: data.detail?.clause_content || ''
          })
        }

        const ticket = data.tickets.find((e) => e.status === 4)?.revision || {}
        if (ticket?.shipping_date !== undefined) {
          ticket.shipping_date = Date.parse(ticket.shipping_date)
        }

        policy = Object.assign({}, policy, ticket)

        this.policy = policy
      })
    }
  },
  methods: {
    handleBack() {
      this.currentStep--
    },
    async handlePreview(product, originData, form) {
      this.product = product

      const loading = Loading.service()
      if (Object.keys(originData).length > 0 && this.$route.query.from === 'edit') {
        await previewPolicy(Object.assign({}, originData)).then((r) => {
          if (originData.id !== undefined) {
            this.originData = Object.assign({}, r.data, { id: originData.id })
            this.originData.policy_id = originData.id
          } else {
            this.originData = Object.assign({}, r.data, originData)
          }
        })
      }

      await previewPolicy(form)
        .then((r) => {
          if (form.id !== undefined) {
            this.data = Object.assign({}, r.data, { id: form.id })
            this.data.policy_id = form.id
          } else {
            this.data = Object.assign({}, r.data, form)
          }

          this.data.anti_dated_file = form.anti_dated_file
          this.data.custom_file = form.custom_file
        })
        .finally(() => loading.close())

      this.currentStep++
    },
    handleSave(product, data) {
      if (this.$route.params.id) {
        if (['edit', 'continue'].includes(this.$route.query.from)) {
          data.policy_id = this.$route.params.id
        }
      }

      const loading = Loading.service()
      saveAsDraft(data)
        .then((data) => {
          this.$message.success('保存成功')

          this.$router.push({ name: 'PoliciesCbecDetails', params: { id: data.id } })
        })
        .finally(() => loading.close())
    },
    cleanData(data) {
      delete data.user_rate
      delete data.user_premium
      delete data.packing_method
      delete data.loading_method
      delete data.transport_method
      delete data.goods_type
      delete data.subject
      delete data.subject_category
      delete data.clauses
      delete data.coverage_currency
      delete data.destination_type
      delete data.delivery_method
      delete data.express_company

      return data
    },
    handleSubmit() {
      const loading = Loading.service()
      if (this.data.id !== undefined && this.$route.query.from === 'edit') {
        if (this.$route.query.ticket_id !== undefined) {
          this.data.ticket_id = this.$route.query.ticket_id
        }
        updatePolicy(this.data.id, this.cleanData(this.data))
          .then(() => {
            this.$message.success('提交成功')

            this.$router.push({ name: 'PoliciesCbecDetails', params: { id: this.data.id } })
          })
          .finally(() => loading.close())
      } else {
        if (this.$route.query.from === 'copy') {
          delete this.data.id
          delete this.data.policy_id
        }

        insurePolicy(this.cleanData(this.data))
          .then(() => {
            this.$message.success('提交成功')
            this.$store.dispatch('auth/refreshUser')

            this.$router.push({ name: 'PoliciesCbec' })
          })
          .finally(() => loading.close())
      }
    }
  }
}
</script>
