<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 11:07:40
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 16:03:43
-->
<template>
  <div class="policy-wrap w-100">
    <div class="m-extra-large-x p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <div class="w-100 m-extra-large-b">
        <el-button icon="el-icon-download" type="primary">
          <a class="download" :download="`${policy.policy_no}.pdf`" :href="policy.download_url"> 下载保单 </a>
        </el-button>
      </div>
      <el-alert show-icon title="温馨提示" type="warning" :closable="false">
        <template> 历史保单数据无法在本平台进行批改，请前往老平台登录处理。 </template>
      </el-alert>

      <define-details :data.sync="data"></define-details>
    </div>
  </div>
</template>

<script>
import { getHistoryPolicyDetail } from '@/apis/policy'
import dayjs from 'dayjs'

export default {
  name: 'PoliciesHistoryDetails',
  data() {
    return {
      policy: {}
    }
  },
  computed: {
    domestic() {
      return {
        title: '保单详情 - ' + this.policy?.status_text,
        data: [
          {
            title: '系统信息',
            groups: [
              { label: '保单号', value: this.policy?.policy_no },
              { label: '出单时间', value: this.policy?.issued_at },
              { label: '投保单号', value: this.policy?.apply_no },
              { label: '流水号', value: this.policy?.order_no },
              { label: '保险金额(元)', value: this.policy?.coverage },
              { label: '保费(元)', value: this.policy?.premium },
              { label: '投保用户', value: this.policy?.user?.name },
              { label: '投保时间', value: this.policy?.submitted_at },
              { label: '第三方标识号', value: this.policy?.trade_order_no, row: true }
            ]
          },
          {
            title: '产品信息',
            groups: [
              { label: '标的', value: this.policy?.detail?.subject },
              { label: '保险公司', value: this.policy?.company_branch },
              { label: '保险产品', value: this.policy?.detail?.product?.name },
              { label: '产品代码', value: this.policy?.detail?.product?.code }
            ]
          },
          {
            title: '基本信息',
            groups: [
              { label: '投保人', value: this.policy?.policyholder },
              { label: '被保人', value: this.policy?.insured },
              {
                label: '投保人地址',
                value: this.policy?.policyholder_address
              },
              {
                label: '被保人地址',
                value: this.policy?.insured_address
              },
              { label: '投保人电话', value: this.policy?.policyholder_phone_number },
              { label: '被保人电话', value: this.policy?.insured_phone_number }
            ]
          },
          {
            title: '货物信息',
            groups: [
              { label: '货物类别', value: this.policy?.detail?.goods_type },
              { label: '装载方式', value: this.policy?.detail?.loading_method },
              { label: '运输方式', value: this.policy?.detail?.transport_method },
              { label: '包装方式', value: this.policy?.detail?.packing_method },
              { label: '货物名称', value: `<pre>${this.policy?.detail?.goods_name || ''}</pre>` },
              { label: '包装件数', value: `<pre>${this.policy?.detail?.goods_amount || ''}</pre>` }
            ]
          },
          {
            title: '运输信息',
            groups: [
              { label: '运单号', value: this.policy?.detail?.waybill_no },
              { label: '发票号', value: this.policy?.detail?.invoice_no },
              { label: '运输工具号', value: this.policy?.detail?.transport_no },
              { label: '起运日期', value: this.policy?.detail?.shipping_date },
              { label: '起运地', value: this.policy?.detail?.departure?.replace(':', '-') },
              { label: '目的地', value: this.policy?.detail?.destination?.replace(':', '-') },
              { label: '中转地', value: this.policy?.detail?.transmit },
              {
                label: '倒签保函',
                isLink: true,
                value: '点击查看',
                to: this.policy?.detail?.anti_dated_file
              }
            ]
          },
          {
            title: '保险内容',
            groups: [
              { label: '主条款', value: this.policy?.detail?.main_clause, row: true },
              { label: '附加条款', value: this.policy?.detail?.additional_clause, row: true },
              {
                label: '免赔',
                value: this.policy?.detail?.deductible,
                row: true
              },
              {
                label: '特别约定',
                value: this.policy?.detail?.special,
                row: true
              }
            ]
          },
          {
            title: '其他',
            groups: [{ label: '备注', value: this.policy?.remark, row: true }]
          }
        ]
      }
    },
    intl() {
      return {
        title: '保单详情 - ' + this.policy?.status_text,
        data: [
          {
            title: '系统信息',
            groups: [
              { label: '保单号', value: this.policy?.policy_no },
              { label: '出单时间', value: this.policy?.issued_at },
              { label: '投保单号', value: this.policy?.apply_no },
              { label: '流水号', value: this.policy?.order_no },
              {
                label: '发票金额(' + this.policy?.detail?.invoice_currency + ')',
                value: this.policy?.detail?.invoice_amount
              },
              {
                label: '保险金额(' + this.policy?.detail?.coverage_currency + ')',
                value: this.policy?.coverage
              },
              { label: '保费(元)', value: this.policy?.premium },
              { label: '投保用户', value: this.policy?.user?.name },
              { label: '投保时间', value: this.policy?.submitted_at },
              { label: '第三方标识号', value: this.policy?.trade_order_no }
            ]
          },
          {
            title: '产品信息',
            groups: [
              { label: '标的', value: this.policy?.detail?.subject },
              { label: '保险公司', value: this.policy?.company_branch },
              { label: '保险产品', value: this.policy?.detail?.product?.name },
              { label: '产品代码', value: this.policy?.detail?.product?.code }
            ]
          },
          {
            title: '基本信息',
            groups: [
              { label: '投保人', value: this.policy?.policyholder },
              { label: '被保人', value: this.policy?.insured },
              {
                label: '投保人地址',
                value: this.policy?.policyholder_address
              },
              {
                label: '被保人地址',
                value: this.policy?.insured_address
              },
              { label: '投保人电话', value: this.policy?.policyholder_phone_number },
              { label: '被保人电话', value: this.policy?.insured_phone_number }
            ]
          },
          {
            title: '货物信息',
            groups: [
              { label: '货物类别', value: this.policy?.detail?.goods_type },
              { label: '装载方式', value: this.policy?.detail?.loading_method },
              { label: '运输方式', value: this.policy?.detail?.transport_method },
              { label: '包装方式', value: this.policy?.detail?.packing_method },
              { label: '货物名称', value: `<pre>${this.policy?.detail?.goods_name || ''}</pre>` },
              { label: '包装件数', value: `<pre>${this.policy?.detail?.goods_amount || ''}</pre>` },
              { label: '唛头', value: `<pre>${this.policy?.detail?.shipping_mark || ''}</pre>`, row: true }
            ]
          },
          {
            title: '运输信息',
            groups: [
              { label: '提/运单号', value: this.policy?.detail?.waybill_no },
              { label: '发票号', value: this.policy?.detail?.invoice_no },
              { label: '起运日期', value: this.policy?.detail?.shipping_date },
              { label: '起运地(国)', value: this.policy?.detail?.departure },
              { label: '起运地', value: this.policy?.detail?.departure_port },
              { label: '目的地(国)', value: this.policy?.detail?.destination },
              { label: '目的地', value: this.policy?.detail?.destination_port },
              { label: '中转地', value: this.policy?.detail?.transmit },
              { label: '运输工具号', value: this.policy?.detail?.transport_no },
              { label: '起运日期打印格式	', value: this.shippingDatePrintFormat },
              {
                label: '倒签保函',
                isLink: true,
                value: '点击查看',
                to: this.policy?.detail?.anti_dated_file,
                row: true
              }
            ]
          },
          {
            title: '保险内容',
            groups: [
              { label: '条款', value: this.policy?.detail?.main_clause, row: true },
              { label: '附加条款', value: this.policy?.detail?.additional_clauses, row: true },
              {
                label: '免赔',
                value: this.policy?.detail?.deductible,
                row: true
              },
              {
                label: '特别约定',
                value: this.policy?.detail?.special,
                row: true
              }
            ]
          },
          {
            title: '其他',
            groups: [{ label: '备注', value: this.policy?.remark, row: true }]
          }
        ]
      }
    },
    shippingDatePrintFormat() {
      return parseInt(this.policy?.detail?.shipping_date_print_format, 10) === 1
        ? dayjs(this.policy?.detail?.shipping_date).format('MMM.DD, YYYY')
        : 'AS PER B/L'
    },
    data() {
      return this.policy?.flag === 'CARGO_CHN' ? this.domestic : this.intl
    }
  },
  created() {
    getHistoryPolicyDetail(this.$route.params.id).then((r) => {
      this.policy = r.data
    })
  }
}
</script>

<style>
.topbar {
  display: hidden;
}

.download,
.download:hover,
.download:focus,
.download:active {
  color: #ffffff;
  text-decoration: none;
}
</style>
