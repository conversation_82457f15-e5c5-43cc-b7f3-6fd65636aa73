<template>
  <div class="policy-wrap">
    <site-breadcrumb>
      <div class="m-mini-x">
        <template v-if="policy.status === 0 && policy?.user?.id === user.id">
          <el-button
            icon="fas fa-play"
            type="primary"
            @click="
              $router.push({
                name: 'PayInfoPage',
                params: { id: $route.params.id },
                query: { product_id: policy.product.id }
              })
            "
          >
            继续投保
          </el-button>
          <el-popconfirm title="确定作废吗?" class="m-mini-l" @confirm="handleDestroy">
            <el-button icon="fas fa-trash" slot="reference" type="primary"> 作废 </el-button>
          </el-popconfirm>
        </template>
        <el-button icon="el-icon-document-copy" class="m-mini-l" type="primary" @click="hrefPayHistoryPage">
          支付记录
        </el-button>
        <template v-if="policy.status === 5 && policy?.user?.id === user.id">
          <el-button-group class="m-mini-x">
            <el-button icon="el-icon-edit" autofocus @click="editFile">修改</el-button>
            <el-button icon="el-icon-share" @click="surrender.visible = true">退保</el-button>
          </el-button-group>
          <el-button icon="el-icon-download" type="primary" @click="download"> 下载保单 </el-button>
        </template>
      </div>
    </site-breadcrumb>
    <div class="m-extra-large-x p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <el-row v-if="policy?.tickets?.length > 0">
        <el-col>
          <el-alert
            v-for="(ticket, idx) in policy?.tickets"
            show-icon
            :title="`批改记录 [${ticket.status_text}] - ${ticket.submitted_at}`"
            :key="idx"
            :type="ticket.status === 2 ? 'success' : 'warning'"
            :closable="false"
            :class="{ 'm-extra-large-t': idx > 0 }"
          >
            <template>
              <div v-if="ticket.reason"><b>退回理由：</b> {{ ticket.reason }}</div>
              <div v-html="ticket.content"></div>
            </template>
          </el-alert>
        </el-col>
      </el-row>
      <el-row v-if="showAlert">
        <el-col>
          <el-alert show-icon :title="alertTitle" type="warning" :closable="false">
            <template>
              <div v-html="alertDescription"></div>
            </template>
          </el-alert>
        </el-col>
      </el-row>
      <define-details :data="data"></define-details>
    </div>
    <!-- 退保弹窗 -->
    <el-dialog title="提示" :visible.sync="surrender.visible" width="30%" :before-close="handleClose">
      <el-input type="textarea" :rows="4" placeholder="请输入退保原因" v-model="reason"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="surrender.visible = false">取 消</el-button>
        <el-button type="primary" @click="sureReturn">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import { getPolicyDetails, buildDownloadHref, policySurrender, destroyDraft } from '@/apis/policy'
import { mapGetters } from 'vuex'

export default {
  name: 'PoliciesOtherDetails',
  components: {
    SiteBreadcrumb
  },
  data() {
    return {
      surrender: {
        visible: false
      },
      policy: {},
      // 退保原因
      reason: ''
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    showAlert() {
      return [6, 10].includes(this.policy?.status)
    },
    alertTitle() {
      const statuses = {
        6: '已退保',
        10: '已退回'
      }
      return `该保单${statuses[this.policy?.status]}`
    },
    alertDescription() {
      const statuses = {
        6: {
          title: '退保原因:',
          content: this.policy?.surrender_reason
        },
        10: {
          title: '退回原因:',
          content: this.policy?.sendback_reason
        }
      }
      return `${statuses[this.policy?.status].title} ${statuses[this.policy?.status].content}`
    },
    data() {
      const _data = {
        title: '保单详情 - ' + this.policy?.status_text,
        data: [
          {
            title: '投保联系人',
            groups: [
              { label: '姓名', value: this.policy?.detail?.applicant_contact_name },
              { label: '电话', value: this.policy?.detail?.applicant_contact_phone }
            ]
          },
          {
            title: '投保信息',
            groups: [
              { label: '表单号', value: this.policy?.policy_no },
              { label: '流水号', value: this.policy?.order_no },
              { label: '投保人', value: this.policy?.policyholder },
              { label: '被保人', value: this.policy?.insured },
              { label: '起保日期', value: this.policy?.detail?.start_at },
              { label: '终保日期', value: this.policy?.detail?.end_at }
            ]
          },
          {
            _id: 'policy',
            title: '投保资料',
            groups: []
          },
          {
            title: '发票信息',
            groups: [
              { label: '发票类型', value: this.invoiceType },
              { label: '发票抬头', value: this.policy?.detail?.invoice_content?.title },
              { label: '纳税人识别号', value: this.policy?.detail?.invoice_content?.tax_no },
              { label: '开户行', value: this.policy?.detail?.invoice_content?.bank },
              { label: '账号', value: this.policy?.detail?.invoice_content?.card_no },
              { label: '地址', value: this.policy?.detail?.invoice_content?.company_address },
              { label: '电话', value: this.policy?.detail?.invoice_content?.company_phone },
              { label: '', value: '' }
            ]
          }
        ]
      }

      let _groups = []

      if (this.policy?.detail?.addition) {
        let _temp = []
        this.policy?.detail?.addition.forEach((j) => {
          if (j.type.includes('file')) {
            _temp.push({
              label: j.title,
              value: '查看文件',
              isLink: true,
              target: '_blank',
              to: j.value
            })
          } else {
            _temp.push({ label: j.title, value: j.value })
          }
        })
        _groups = _temp
      } else {
        _data.data = _data.data.filter((item) => !item._id)
      }

      _data.data.map((item) => {
        if (item._id && item._id === 'policy') {
          if (_groups.length % 2 !== 0) {
            _groups.push({ label: '', value: '' })
          }
          item.groups = _groups
        }
      })
      return _data
    },
    invoiceType() {
      switch (this.policy?.detail?.invoice_content?.invoice_type) {
        case 'plain':
          return '普票'
        case 'special':
          return '专票'
        default:
          return '无'
      }
    }
  },
  methods: {
    editFile() {
      this.$router.push({
        name: 'EditPage',
        params: {
          id: this.$route.params.id
        }
      })
    },
    handleDestroy() {
      destroyDraft(this.$route.params.id).then(() => {
        this.$message.success('作废成功')

        this.fetchDetail()
      })
    },
    download() {
      window.open(buildDownloadHref(this.$route.params.id))
    },
    fetchDetail() {
      getPolicyDetails(this.$route.params.id).then((r) => {
        r.data.detail.addition = JSON.parse(r.data.detail.addition)
        r.data.detail.invoice_content = JSON.parse(r.data.detail.invoice_content)
        this.policy = r.data
      })
    },
    // 跳转支付记录
    hrefPayHistoryPage() {
      this.$router.push({
        name: 'PayHistory',
        params: {
          id: this.$route.params.id
        }
      })
    },
    // 退保弹窗关闭
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(() => {
          done()
        })
        .catch(() => {})
    },
    // 确认退保
    sureReturn() {
      if (this.reason === '') {
        this.$message({
          type: 'error',
          message: '请填写退保原因'
        })
        return
      }
      policySurrender(this.$route.params.id, { reason: this.reason }).then(() => {
        this.$message({
          type: 'success',
          message: '提交成功'
        })
      })
      this.surrender.visible = false

      this.fetchDetail()
    }
  },
  created() {
    this.fetchDetail()
  }
}
</script>

<style lang="scss" scoped>
.policy-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
