<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 10:42:14
 * @LastEditors: yanb
 * @LastEditTime: 2023-10-27 14:42:59
-->
<template>
  <div class="policy-wrap">
    <site-breadcrumb>
      <div class="m-mini-x" v-if="user.id === policy?.user?.id">
        <el-button
          v-if="[0, 1, 2, 5, 11, 15].includes(policy?.status)"
          icon="el-icon-s-check"
          type="primary"
          @click="handleDownloadConfirmationPdf"
        >
          下载投保单确认件
        </el-button>
        <el-button
          icon="el-icon-document-copy"
          type="primary"
          @click="
            $router.push({ name: 'PoliciesInsureCbec', params: { id: $route.params.id }, query: { from: 'copy' } })
          "
        >
          复制保单
        </el-button>
        <el-button
          icon="el-icon-document"
          type="primary"
          v-if="policy?.status === 14 && policy?.tickets.find((ticket) => ticket.status === 4)"
          @click="
            $router.push({
              name: 'PoliciesInsureCbec',
              params: { id: $route.params.id },
              query: { from: 'edit', ticket_id: policy?.tickets.find((ticket) => ticket.status === 4)?.id }
            })
          "
        >
          补充资料(修改工单)
        </el-button>
        <el-button
          v-if="[0, 10, 13].includes(policy.status) && !policy?.policy_no"
          icon="fas fa-play"
          type="primary"
          @click="
            $router.push({
              name: 'PoliciesInsureCbec',
              params: { id: $route.params.id },
              query: { from: 'continue' }
            })
          "
        >
          继续投保
        </el-button>
        <el-popconfirm
          v-if="[0, 10].includes(policy.status)"
          title="确定作废吗?"
          class="m-mini-l"
          @confirm="handleDestroy"
        >
          <el-button icon="fas fa-trash" slot="reference" type="primary"> 作废 </el-button>
        </el-popconfirm>
        <template v-if="policy.status === 5">
          <el-button
            type="primary"
            icon="el-icon-edit"
            @click="
              $router.push({
                name: 'PoliciesInsureCbec',
                params: { id: $route.params.id },
                query: { from: 'edit' }
              })
            "
          >
            修改
          </el-button>
          <el-button
            v-if="policy?.is_can_policy_paper"
            type="primary"
            icon="el-icon-document"
            @click="policyRequestVisible = true"
          >
            申请纸质保单
          </el-button>
          <el-button type="primary" icon="el-icon-share" @click="surrender.visible = true"> 退保 </el-button>
          <el-button
            icon="el-icon-download"
            type="primary"
            @click="download"
            :disabled="!policy.downloadable"
            :loading="!policy.downloadable"
          >
            {{ policy.downloadable ? '下载保单' : '保单生成中' }}
          </el-button>
        </template>

        <surrender :visible.sync="surrender.visible" @submit="handleSubmitSurrender" />
      </div>
    </site-breadcrumb>
    <div class="m-extra-large-x p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <el-row v-if="policy?.tickets?.length > 0">
        <el-col>
          <el-alert
            v-for="(ticket, idx) in policy?.tickets"
            show-icon
            :title="`批改记录 [${ticket.status_text}] - ${ticket.submitted_at}`"
            :key="idx"
            :type="ticket.status === 2 ? 'success' : 'warning'"
            :closable="false"
            :class="{ 'm-extra-large-t': idx > 0 }"
          >
            <template>
              <div v-if="ticket.reason && [3, 4].includes(ticket.status)"><b>退回理由：</b> {{ ticket.reason }}</div>
              <div v-html="ticket.content"></div>
            </template>
          </el-alert>
        </el-col>
      </el-row>

      <el-row v-if="showAlert" class="m-extra-large-t">
        <el-col>
          <el-alert show-icon :title="alertTitle" type="warning" :closable="false">
            <template>
              <div v-html="alertDescription"></div>
            </template>
          </el-alert>
        </el-col>
      </el-row>

      <template v-if="policy?.papers?.length > 0">
        <el-alert
          v-for="paper in policy.papers"
          :key="paper.id"
          show-icon
          :title="paper.status | paperStatusTitle"
          :type="paper.status | paperStatusStyle"
          :closable="false"
          class="m-extra-large-t"
        >
          <template v-if="paper.status === 1">
            您于 {{ paper.created_at }} 申请的纸质保单正在处理中请耐心等待，如有疑问请联系客服
          </template>
          <template v-if="paper.status === 2">
            <div v-if="paper.type === 3">
              您于 {{ paper.created_at }} 申请的纸质保单已打印完成，请联系客服获取自取信息
            </div>
            <div v-else>
              您于 {{ paper.created_at }} 申请的纸质保单已通过 <b>{{ paper.express_company }}</b> 快递配送，快递单号：
              <b>{{ paper.express_no }}</b>
            </div>
          </template>
          <template v-if="paper.status === 3">
            您于 {{ paper.created_at }} 申请的纸质保单因提交批改已被取消，如有疑问请联系客服
            <div>
              退回理由: <b>{{ paper.reason }}</b>
            </div>
          </template>
        </el-alert>
      </template>

      <define-details :data="data"></define-details>
    </div>

    <policy-paper
      :policy="policy"
      :suggestions="policyPapers"
      :visible.sync="policyRequestVisible"
      @submit="handlePolicyPaperSubmit"
    />
  </div>
</template>

<script>
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import PolicyPaper from '@/components/policy/PolicyPaper'
import Surrender from '@/components/policy/Surrender'
import {
  getPolicyDetails,
  buildDownloadHref,
  policySurrender,
  applyPolicyPaper,
  destroyDraft,
  getPolicyPapers,
  downloadConfirmationLetter
} from '@/apis/policy'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'

export default {
  name: 'PoliciesCbecDetails',
  components: {
    SiteBreadcrumb,
    PolicyPaper,
    Surrender
  },
  data() {
    return {
      surrender: {
        visible: false
      },
      policyRequestVisible: false,
      policy: {},
      policyPapers: []
    }
  },
  filters: {
    paperStatusTitle(status) {
      const statuses = {
        1: '纸质保单申请处理中',
        2: '纸质保单已发出',
        3: '纸质保单申请已退回'
      }

      return statuses[status] || '未知状态'
    },
    paperStatusStyle(status) {
      const styles = {
        1: 'warning',
        2: 'success',
        3: 'error'
      }

      return styles[status] || 'info'
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    data() {
      return {
        title: '保单详情 - ' + this.policy?.status_text,
        data: [
          {
            title: '系统信息',
            groups: [
              { label: '保单号', value: this.policy?.policy_no },
              { label: '出单时间', value: this.policy?.issued_at },
              { label: '投保单号', value: this.policy?.apply_no },
              { label: '流水号', value: this.policy?.order_no },
              { label: '投保用户', value: this.policy?.user?.name },
              { label: '投保时间', value: this.policy?.submitted_at },
              { label: '第三方标识号', value: this.policy?.trade_order_no }
            ]
          },
          {
            title: '产品信息',
            groups: [
              {
                label: '保障范围',
                value: {
                  1: '保上架',
                  2: '保签收'
                }[this.policy?.detail?.coverage_scope]
              },
              { label: '保险公司', value: this.policy?.company_branch?.name },
              { label: '保险产品', value: this.policy?.product?.name },
              { label: '产品代码', value: this.policy?.product?.code }
            ]
          },
          {
            title: '基本信息',
            groups: [
              { label: '投保人', value: this.policy?.policyholder },
              { label: '被保人', value: this.policy?.insured },
              {
                label: '投保人类型',
                value: this.policy?.policyholder_type === 1 ? '个人客户' : '团体客户',
                hide: !['TPIC', 'PICC'].includes(this.policy?.company?.identifier)
              },
              {
                label: '被保人类型',
                value: this.policy?.insured_type === 1 ? '个人客户' : '团体客户',
                hide: !['TPIC', 'PICC'].includes(this.policy?.company?.identifier)
              },
              {
                label: '投保人是否为境外客户',
                value: this.policy?.policyholder_overseas === 1 ? '是' : '否',
                hide: !['PICC'].includes(this.policy?.company?.identifier)
              },
              {
                label: '被保人是否为境外客户',
                value: this.policy?.insured_overseas === 1 ? '是' : '否',
                hide: !['PICC'].includes(this.policy?.company?.identifier)
              },
              {
                label: '投保人证件号',
                value: this.policy?.policyholder_idcard_no,
                hide: !['PICC', 'TPIC'].includes(this.policy?.company?.identifier)
              },
              {
                label: '被保人证件号',
                value: this.policy?.insured_idcard_no,
                hide: !['PICC', 'TPIC'].includes(this.policy?.company?.identifier)
              },
              {
                label: '投保人证件号起始时间',
                value: this.policy?.policyholder_idcard_issue_date,
                hide: !['PICC'].includes(this.policy?.company?.identifier)
              },
              {
                label: '被保人证件号起始时间',
                value: this.policy?.insured_idcard_issue_date,
                hide: !['PICC'].includes(this.policy?.company?.identifier)
              },
              {
                label: '投保人证件号结束时间',
                value: this.policy?.policyholder_idcard_valid_till,
                hide: !['PICC'].includes(this.policy?.company?.identifier)
              },
              {
                label: '被保人证件号结束时间',
                value: this.policy?.insured_idcard_valid_till,
                hide: !['PICC'].includes(this.policy?.company?.identifier)
              },
              {
                label: '投保人地址',
                value: this.policy?.policyholder_address
              },
              {
                label: '被保人地址',
                value: this.policy?.insured_address
              },
              { label: '投保人电话', value: this.policy?.policyholder_phone_number },
              { label: '被保人电话', value: this.policy?.insured_phone_number }
            ]
          },
          {
            title: '货物信息',
            groups: [
              { label: '货物类别', value: this.policy?.detail?.goods_type?.name },
              { label: '装载方式', value: this.policy?.detail?.loading_method?.name },
              { label: '货物实重', value: this.policy?.detail?.weight + ' KG' },
              { label: '运输方式', value: this.policy?.detail?.transport_method?.name },
              { label: '包装方式', value: this.policy?.detail?.packing_method?.name },
              { label: '货物名称', value: `<pre>${this.policy?.detail?.goods_name}</pre>` },
              { label: '包装件数', value: `<pre>${this.policy?.detail?.goods_amount}</pre>` },
              { label: '唛头', value: `<pre>${this.policy?.detail?.shipping_mark || ''}</pre>`, row: true }
            ]
          },
          {
            title: '运输信息',
            groups: [
              {
                label: '贸易类型',
                value: { 1: '出口投保', 2: '进口投保', 3: '境外运输' }[this.policy?.detail?.insure_type]
              },
              { label: '提/运单号', value: this.policy?.detail?.waybill_no },
              { label: '发票号', value: this.policy?.detail?.invoice_no },
              { label: '原单号', value: this.policy?.detail?.contract_no },
              { label: '起运日期', value: this.policy?.detail?.shipping_date },
              { label: '起运地(国/地区)', value: this.policy?.detail?.departure },
              { label: '起运地', value: this.policy?.detail?.departure_port },
              { label: '目的地(国/地区)', value: this.policy?.detail?.destination },
              { label: '目的地详细地址或仓库代码', value: this.policy?.detail?.destination_port },
              { label: '中转地', value: this.policy?.detail?.transmit },
              { label: '赔付地', value: this.policy?.detail?.payable_at },
              { label: '目的地类型', value: this.policy?.detail?.destination_type?.name },
              { label: '派送方式', value: this.policy?.detail?.delivery_method?.name },
              { label: '快递公司', value: this.policy?.detail?.express_company?.name || '' },
              { label: '快递单号', value: this.policy?.detail?.express_no },
              { label: 'SHIPMENT ID', value: this.policy?.detail?.shipment_id },
              { label: '运输工具号', value: this.policy?.detail?.transport_no },
              {
                label: '倒签保函',
                isLink: true,
                value: '点击查看',
                hide: !this.policy?.detail?.anti_dated_file,
                to: this.policy?.detail?.anti_dated_file
              }
            ]
          },
          {
            title: '保险内容',
            groups: [
              { label: '条款', value: this.policy?.product?.main_clause, row: true },
              { label: '附加条款', value: this.policy?.product?.additional_clauses, row: true },
              {
                label: '免赔',
                value: this.policy?.detail?.deductible,
                row: true
              },
              {
                label: '特别约定',
                value: this.policy?.detail?.special,
                row: true
              },
              {
                label: '条款内容',
                value: `<pre>${this.policy?.detail?.clause_content}</pre>`,
                row: true
              }
            ]
          },
          {
            title: '保费信息',
            groups: [
              {
                label: '发票金额(' + this.policy?.detail?.invoice_currency?.name + ')',
                value: this.policy?.detail?.invoice_amount
              },
              {
                label: '保险金额(' + this.policy?.detail?.coverage_currency?.name + ')',
                value: this.policy?.coverage
              },
              { label: '费率(‱)', value: this.policy?.user_rate },
              { label: '保费(元)', value: this.policy?.premium }
            ]
          },
          {
            title: '其他',
            groups: [
              { label: '备注', value: this.policy?.remark, row: true },
              { label: '工作编号', value: this.policy?.sticky_note },
              {
                label: '投保附件',
                isLink: true,
                value: '点击查看',
                hide: !this.policy?.detail?.custom_file,
                to: this.policy?.detail?.custom_file
              }
            ]
          }
        ]
      }
    },
    confirmationPdfData() {
      return [
        {
          title: '被保险人信息',
          dataset: [
            { label: '被保险人', subtitle: 'Insured', value: this.policy?.insured },
            { label: '被保险人通讯地址', subtitle: 'Address', value: this.policy?.insured_address },
            { label: '被保险人电话', subtitle: 'Office Phone or Mobile', value: this.policy?.insured_phone_number }
          ]
        },
        {
          title: '货物信息',
          dataset: [
            {
              label: '唛头',
              subtitle: 'Marks',
              value: this.policy?.detail?.shipping_mark || '',
              wrap: true
            },
            {
              label: '货物名称',
              subtitle: 'Description',
              value: this.policy?.detail?.goods_name,
              wrap: true
            },
            {
              label: '货物实重',
              subtitle: 'Weight',
              value: this.policy?.detail?.weight + ' KG',
              wrap: true
            },
            {
              label: '数量单位',
              subtitle: 'Quantity',
              value: this.policy?.detail?.goods_amount,
              wrap: true
            },
            { label: '发票号', subtitle: 'Invoice Number', value: this.policy?.detail?.invoice_no },

            {
              label: '提单/运单号',
              subtitle: 'WAYBILL NO OR B/L NO',
              value: this.policy?.detail?.waybill_no
            },
            { label: '原单号', subtitle: 'Original No', value: this.policy?.detail?.contract_no },
            {
              label: '发票金额',
              subtitle: 'Invoice Amount',
              value: this.policy?.detail?.invoice_amount + this.policy?.detail?.coverage_currency.name
            },
            {
              label: '保险金额',
              subtitle: 'Amount Insured',
              value: this.policy?.coverage + this.policy?.detail?.coverage_currency?.name
            },
            {
              label: '船名/车号',
              subtitle: 'Per Conveyance S.S.',
              value: this.policy?.detail?.transport_no,
              row: true
            },
            { label: '起运地', subtitle: 'From', value: this.policy?.detail?.departure_port },
            { label: '转运地:', subtitle: 'Via', value: this.policy?.detail?.transmit },
            { label: '目的地详细地址或仓库代码', subtitle: 'To', value: this.policy?.detail?.destination_port },
            { label: '赔付地点', subtitle: 'Claim Payable at', value: this.policy?.detail?.payable_at },
            { label: '起运日期', subtitle: 'Slg. on or abt', value: this.policy?.detail?.shipping_date },
            { label: '目的地类型', subtitle: 'Destination Type', value: this.policy?.detail?.destination_type?.name },
            { label: '派送方式', subtitle: 'Delivery Method', value: this.policy?.detail?.delivery_method?.name },
            { label: '快递公司', subtitle: 'Express Company', value: this.policy?.detail?.express_company?.name || '' },
            { label: '快递单号', subtitle: 'Express Number', value: this.policy?.detail?.express_no },
            { label: 'SHIPMENT ID', subtitle: 'Shipment ID', value: this.policy?.detail?.shipment_id }
          ]
        },
        {
          title: '保险条件',
          dataset: [
            {
              label: '保障范围',
              subtitle: 'Scope Of Coverage',
              value: {
                1: '保上架',
                2: '保签收'
              }[this.policy?.detail?.coverage_scope]
            },
            {
              label: '免赔',
              subtitle: 'Deductible',
              value: this.policy?.detail?.deductible,
              wrap: true
            },
            {
              label: '特别约定',
              subtitle: 'Special Agreement',
              value: this.policy?.detail?.special_agreement,
              wrap: true
            },
            {
              label: '条款内容',
              subtitle: 'Conditions',
              value: this.policy?.detail?.clause_content,
              wrap: true
            }
          ]
        }
      ]
    },
    showAlert() {
      return [6, 10, 13].includes(this.policy?.status)
    },
    alertTitle() {
      const statuses = {
        6: '已退保',
        10: '已退回',
        13: '保单退回(补充资料)'
      }
      return `该保单${statuses[this.policy?.status]}`
    },
    alertDescription() {
      const statuses = {
        6: {
          title: '退保原因:',
          content: this.policy?.surrender_reason
        },
        10: {
          title: '退回原因:',
          content: this.policy?.sendback_reason
        },
        13: {
          title: '退回原因:',
          content: this.policy?.sendback_reason
        }
      }

      return `${statuses[this.policy?.status].title} ${statuses[this.policy?.status].content}`
    },
    shippingDatePrintFormat() {
      return parseInt(this.policy?.detail?.shipping_date_print_format, 10) === 1
        ? dayjs(this.policy?.detail?.shipping_date).format('MMM.DD, YYYY')
        : 'AS PER B/L'
    }
  },
  methods: {
    download() {
      window.open(buildDownloadHref(this.$route.params.id))
    },
    handleSubmitSurrender(data) {
      policySurrender(this.$route.params.id, data).then(() => {
        this.$message.success('申请退保成功')

        this.fetchDetail()
      })
    },
    fetchDetail() {
      getPolicyDetails(this.$route.params.id).then((r) => {
        this.policy = r.data
      })
    },
    handlePolicyPaperSubmit(data) {
      applyPolicyPaper(this.$route.params.id, data).then(() => {
        this.$message.success('申请成功')

        this.fetchDetail()
      })
    },
    handleDestroy() {
      destroyDraft(this.$route.params.id).then(() => {
        this.$message.success('作废成功')

        this.$router.push({ name: 'PoliciesCbec' })
      })
    },
    async handleDownloadConfirmationPdf() {
      const data = await downloadConfirmationLetter({
        data: this.confirmationPdfData
      })
      const linkSource = `data:application/pdf;base64,${data.data}`
      const downloadLink = document.createElement('a')
      downloadLink.href = linkSource
      downloadLink.download = `${this.policy?.insured}.pdf`
      downloadLink.click()
    }
  },
  created() {
    this.fetchDetail()

    getPolicyPapers().then((r) => (this.policyPapers = r.data))
  }
}
</script>

<style lang="scss" scoped>
.confirmation-pdf-content {
  /deep/ .data-label-text {
    width: 350px !important;
  }
}
.policy-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
