<template>
  <div class="insurance-other w-100">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '0' }">
      <el-form ref="searchBtn" :model="searchList" label-width="80px" :inline="true">
        <el-form-item label="保险分类">
          <el-select v-model="searchList.type" placeholder="请选择保险分类">
            <el-option :label="item.name" :value="item.id" v-for="(item, index) in searchType" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险公司">
          <el-select v-model="searchList.company" placeholder="请选择保险公司">
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="(item, index) in searchCompany"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" icon="el-icon-search" @click="searchLis">搜索</el-button>
          <el-button type="primary" icon="fas fa-eraser" @click="clearList">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '0' }">
      <div v-for="item in productList" :key="item.id" style="margin: 0; padding: 0">
        <div class="item d-flex justify-content-between">
          <ul class="left-content w-100">
            <li class="d-flex justify-content-between align-items-center margin-bottom-10">
              <span class="productName">{{ item.name }}</span>
              <img :src="item.company.logo" alt="" style="height: 35px" />
            </li>
            <li class="d-flex flex-start features margin-bottom-10">
              <span v-for="(k, index) in item.label" :key="index">{{ k }}</span>
            </li>
            <li class="d-flex flex-wrap justify-content-between margin-bottom-10">
              <div class="w-46" v-for="(i, j) in item.benefit" :key="j">
                <div class="d-flex justify-content-between margin-bottom-10">
                  <span class="option">{{ i.name }}</span>
                  <span>{{ i.amount }}</span>
                </div>
              </div>
            </li>
          </ul>
          <ul class="right-content w-25 d-flex justify-content-center flex-column align-items-center">
            <span class="margin-bottom-10">¥ {{ item.reference_price }}</span>
            <el-button type="primary" class="w-50" @click="hrefNextPage(item)">查看详情</el-button>
          </ul>
        </div>
        <el-divider></el-divider>
      </div>
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage1"
        align="center"
        layout="total, prev, pager, next"
        :total="totalPage"
      >
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import { otherInsurance, categoriesList } from '@/apis/otherInsurance'
import { getCompanies } from '@/apis/company'

export default {
  name: 'InsuranceOther',
  data() {
    return {
      //分页
      currentPage1: 1,
      totalPage: 0,
      // 搜索列表
      searchList: {
        type: '',
        company_id: ''
      },
      // 搜索分类列表
      searchType: [],
      // 搜索公司
      searchCompany: [],
      productList: []
    }
  },
  mounted() {
    // 获取列表信息
    const page = {
      page: 1
    }
    this.getListInfo(page)
    categoriesList().then((r) => {
      this.searchType = r.data
    })

    getCompanies().then((r) => {
      if (r.data.length > 0) {
        r.data.forEach((e) => {
          this.searchCompany.push({
            value: e.id,
            label: e.name
          })
        })
      }
    })
  },
  methods: {
    // 获取列表信息
    getListInfo(data) {
      otherInsurance(data).then((r) => {
        const _temp = r.data
        _temp.forEach((item) => {
          item.label = item.label?.split('｜')
        })
        _temp.forEach((item) => {
          item.benefit = JSON.parse(item.benefit)
        })
        this.productList = _temp
        this.currentPage1 = r.meta.current_page
        this.totalPage = r.meta.total
      })
    },
    //  搜索列表
    searchLis() {
      const q = {
        page: 1,
        filter: {
          category_id: this.searchList.type === '' ? null : this.searchList.type,
          company_id: this.searchList.company === '' ? null : this.searchList.company
        }
      }

      this.getListInfo(q)
    },
    //重置
    clearList() {
      this.searchList = {
        type: '',
        company: ''
      }
    },
    //  分页事件
    handleCurrentChange(val) {
      const page = {
        page: val,
        filter: {
          category_id: this.searchList.type === '' ? null : this.searchList.type,
          company_id: this.searchList.company === '' ? null : this.searchList.company
        }
      }
      this.getListInfo(page)
    },
    //  跳转到下个页面
    hrefNextPage(item) {
      this.$router.push({ name: 'PoliciesInsureOtherDeatil', params: { id: item.id } })
    }
  }
}
</script>

<style lang="scss" scoped>
.insurance-other {
  padding: 0 20px;
  .box-card-options {
    padding: 20px;
    margin-bottom: 20px;
    .subtitle {
      padding-right: 20px;
    }
    .productName {
      font-size: 24px;
    }
    .features span {
      display: inline-block;
      margin-right: 10px;
      border: 1px solid $app-color-primary;
      color: $app-color-primary;
      padding: 0 4px;
    }
    .right-content span {
      font-size: 24px;
      font-weight: bold;
    }
  }
}
// 本页面button组建按钮样式
.el-button {
  font-size: 14px;
  font-weight: normal;
}
// li标签去小圆点
ul {
  padding-left: 0;
  li {
    list-style: none;
  }
}
// 宽46%
.w-46 {
  width: 46%;
  .option {
    font-weight: bold;
  }
}
// margin间距
.margin-bottom-10 {
  margin-bottom: 10px !important;
}
.el-form-item {
  margin-bottom: 0 !important;
}
.features {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}
</style>
