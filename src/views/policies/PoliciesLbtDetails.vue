<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 10:42:14
 * @LastEditors: yanb
 * @LastEditTime: 2024-06-26 15:26:16
-->
<template>
  <div class="policy-wrap">
    <site-breadcrumb>
      <div class="m-mini-x" v-if="user.id === policy?.user?.id">
        <el-button
          icon="el-icon-document-copy"
          type="primary"
          @click="
            $router.push({ name: 'PoliciesInsureLbt', params: { id: $route.params.id }, query: { from: 'copy' } })
          "
        >
          复制保单
        </el-button>
        <el-button
          icon="el-icon-document"
          type="primary"
          v-if="policy?.status === 14 && policy?.tickets.find((ticket) => ticket.status === 4)"
          @click="
            $router.push({
              name: 'PoliciesInsureLbt',
              params: { id: $route.params.id },
              query: { from: 'edit', ticket_id: policy?.tickets.find((ticket) => ticket.status === 4)?.id }
            })
          "
        >
          补充资料(修改工单)
        </el-button>
        <el-button
          v-if="[0, 10, 13].includes(policy.status) && !policy?.policy_no"
          icon="fas fa-play"
          type="primary"
          @click="
            $router.push({
              name: 'PoliciesInsureLbt',
              params: { id: $route.params.id },
              query: { from: 'continue' }
            })
          "
        >
          继续投保
        </el-button>
        <el-popconfirm
          v-if="[0, 10].includes(policy.status)"
          title="确定作废吗?"
          class="m-mini-l"
          @confirm="handleDestroy"
        >
          <el-button icon="fas fa-trash" slot="reference" type="primary"> 作废 </el-button>
        </el-popconfirm>
        <template v-if="policy.status === 5">
          <el-button
            type="primary"
            icon="el-icon-edit"
            @click="
              $router.push({
                name: 'PoliciesInsureLbt',
                params: { id: $route.params.id },
                query: { from: 'edit' }
              })
            "
          >
            修改
          </el-button>
          <el-button type="primary" icon="el-icon-share" @click="surrender.visible = true"> 退保 </el-button>
          <el-button
            icon="el-icon-download"
            type="primary"
            @click="download"
            :disabled="!policy.downloadable"
            :loading="!policy.downloadable"
          >
            {{ policy.downloadable ? '下载保单' : '保单生成中' }}
          </el-button>
        </template>

        <surrender :visible.sync="surrender.visible" @submit="handleSubmitSurrender" />
      </div>
    </site-breadcrumb>
    <div class="m-extra-large-x p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <el-row v-if="policy?.tickets?.length > 0">
        <el-col>
          <el-alert
            v-for="(ticket, idx) in policy?.tickets"
            show-icon
            :title="`批改记录 [${ticket.status_text}] - ${ticket.submitted_at}`"
            :key="idx"
            :type="ticket.status === 2 ? 'success' : 'warning'"
            :closable="false"
            :class="{ 'm-extra-large-t': idx > 0 }"
          >
            <template>
              <div v-if="ticket.reason && [3, 4].includes(ticket.status)"><b>退回理由：</b> {{ ticket.reason }}</div>
              <div v-html="ticket.content"></div>
            </template>
          </el-alert>
        </el-col>
      </el-row>

      <el-row v-if="showAlert">
        <el-col>
          <el-alert
            show-icon
            :title="alertTitle"
            :description="alertDescription"
            type="warning"
            :closable="false"
          ></el-alert>
        </el-col>
      </el-row>
      <define-details :data="data"></define-details>
    </div>
  </div>
</template>

<script>
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import { getPolicyDetails, buildDownloadHref, policySurrender, destroyDraft } from '@/apis/policy'
import { mapGetters } from 'vuex'

export default {
  name: 'PoliciesLbtDetails',
  components: {
    SiteBreadcrumb
  },
  data() {
    return {
      surrender: {
        visible: false
      },
      policy: {}
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    data() {
      return {
        title: '保单详情 - ' + this.policy?.status_text,
        data: [
          {
            title: '系统信息',
            groups: [
              { label: '保单号', value: this.policy?.policy_no },
              { label: '出单时间', value: this.policy?.issued_at },
              { label: '投保单号', value: this.policy?.apply_no },
              { label: '流水号', value: this.policy?.order_no },
              { label: '投保用户', value: this.policy?.user?.name },
              { label: '投保时间', value: this.policy?.submitted_at },
              { label: '第三方标识号', value: this.policy?.trade_order_no, row: true }
            ]
          },
          {
            title: '产品信息',
            groups: [
              { label: '标的', value: this.policy?.detail?.subject?.name },
              { label: '保险公司', value: this.policy?.company_branch?.name },
              { label: '保险产品', value: this.policy?.product?.name },
              { label: '产品代码', value: this.policy?.product?.code }
            ]
          },
          {
            title: '基本信息',
            groups: [
              { label: '投保人', value: this.policy?.policyholder },
              { label: '被保人', value: this.policy?.insured },
              {
                label: '投保人地址',
                value: this.policy?.policyholder_address
              },
              {
                label: '被保人地址',
                value: this.policy?.insured_address
              },
              { label: '投保人电话', value: this.policy?.policyholder_phone_number },
              { label: '被保人电话', value: this.policy?.insured_phone_number }
            ]
          },
          {
            title: '货物信息',
            groups: [
              { label: '货物类别', value: this.policy?.detail?.goods_type?.name },
              { label: '装载方式', value: this.policy?.detail?.loading_method?.name },
              { label: '运输方式', value: this.policy?.detail?.transport_method?.name },
              { label: '包装方式', value: this.policy?.detail?.packing_method?.name },
              { label: '货物名称', value: `<pre>${this.policy?.detail?.goods_name}</pre>` },
              { label: '包装件数', value: `<pre>${this.policy?.detail?.goods_amount}</pre>` }
            ]
          },
          {
            title: '运输信息',
            groups: [
              { label: '运单号', value: this.policy?.detail?.waybill_no },
              { label: '发票号', value: this.policy?.detail?.invoice_no },
              { label: '车牌号', value: this.policy?.detail?.transport_no },
              { label: '起运日期', value: this.policy?.detail?.shipping_date },
              { label: '起运地', value: this.policy?.detail?.departure?.replace(':', '-') },
              { label: '目的地', value: this.policy?.detail?.destination?.replace(':', '-') },
              { label: '中转地', value: this.policy?.detail?.transmit },
              {
                label: '倒签保函',
                isLink: true,
                value: '点击查看',
                hide: !this.policy?.detail?.anti_dated_file,
                to: this.policy?.detail?.anti_dated_file
              }
            ]
          },
          {
            title: '保险内容',
            groups: [
              { label: '主条款', value: this.policy?.detail?.main_clause, row: true },
              { label: '附加条款', value: this.policy?.detail?.additional_clause, row: true },
              {
                label: '免赔',
                value: this.policy?.detail?.deductible,
                row: true
              },
              {
                label: '特别约定',
                value: this.policy?.detail?.special,
                row: true
              }
            ]
          },
          {
            title: '费用信息',
            groups: [
              { label: '保险金额(元)', value: this.policy?.actual_coverage },
              { label: '运费(元)', value: this.policy?.coverage },
              { label: '保费(元)', value: this.policy?.premium }
            ]
          },
          {
            title: '其他',
            groups: [
              { label: '备注', value: this.policy?.remark, row: true },
              { label: '工作编号', value: this.policy?.sticky_note, row: true },
              {
                label: '投保附件',
                isLink: true,
                value: '点击查看',
                hide: !this.policy?.detail?.custom_file,
                to: this.policy?.detail?.custom_file
              }
            ]
          }
        ]
      }
    },
    showAlert() {
      return [6, 10, 13].includes(this.policy?.status)
    },
    alertTitle() {
      const statuses = {
        6: '已退保',
        10: '已退回',
        13: '保单退回(补充资料)'
      }
      return `该保单${statuses[this.policy?.status]}`
    },
    alertDescription() {
      const statuses = {
        6: {
          title: '退保原因:',
          content: this.policy?.surrender_reason
        },
        10: {
          title: '退回原因:',
          content: this.policy?.sendback_reason
        },
        13: {
          title: '退回原因:',
          content: this.policy?.sendback_reason
        }
      }
      return `${statuses[this.policy?.status].title} ${statuses[this.policy?.status].content}`
    }
  },
  methods: {
    download() {
      window.open(buildDownloadHref(this.$route.params.id))
    },
    handleSubmitSurrender(data) {
      policySurrender(this.$route.params.id, data).then(() => {
        this.$message.success('申请退保成功')

        this.fetchDetail()
      })
    },
    fetchDetail() {
      getPolicyDetails(this.$route.params.id).then((r) => {
        this.policy = r.data
      })
    },
    handleDestroy() {
      destroyDraft(this.$route.params.id).then(() => {
        this.$message.success('作废成功')

        this.$router.push({ name: 'PoliciesLbt' })
      })
    }
  },
  created() {
    this.fetchDetail()
  }
}
</script>

<style lang="scss" scoped>
.policy-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
