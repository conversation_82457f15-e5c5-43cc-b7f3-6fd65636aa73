<template>
  <div class="w-100 other-insure-detail">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '0' }">
      <div slot="header" class="clearfix">
        <span>产品详情</span>
      </div>
      <div class="main-content">
        <div class="d-flex justify-content-between align-items-center">
          <span class="title">{{ pageInfo.name }}</span>
          <img style="max-height: 50px" :src="pageInfo.company.logo" alt="" />
        </div>
        <div class="d-flex justify-content-between">
          <div class="subtitle">
            <div class="subtitle-name">
              <span v-for="(item, index) in pageInfo.label" :key="index">{{ item }}</span>
            </div>
            <div class="subtitle-price">¥ {{ pageInfo.reference_price }}</div>
            <div class="subtitle-time">保障期限: {{ pageInfo.insurance_period }}</div>
          </div>
          <el-button type="primary" class="insuredNow" @click="submit"> 立即投保 </el-button>
        </div>
        <div>
          <p class="options_item">保障权益</p>
          <table class="table">
            <tbody>
              <tr v-for="(item, index) in pageInfo.benefit" :key="index">
                <th scope="row" width="10%">{{ item.name }}</th>
                <td width="20%">{{ item.amount }}</td>
                <td>{{ item.content }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div>
          <p class="options_item">免赔</p>
          <p class="options_item_box">{{ pageInfo.deductible }}</p>
        </div>
        <div>
          <p class="options_item">特别约定</p>
          <p class="options_item_box">
            {{ pageInfo.special_agreement }}
          </p>
        </div>
        <div>
          <p class="options_item">投保须知</p>
          <p class="options_item_box">
            投保前请您仔细阅读:
            <el-link :href="pageInfo.clause_file" type="primary" target="_blank">产品条款</el-link>&nbsp;
            <span v-for="(item, index) in pageInfo.related_file">
              <el-link
                type="primary"
                target="_blank"
                :href="item.file"
                :download="item.name + '.' + item.file.split('.').pop().toLowerCase()"
                >{{ item.name }}</el-link
              >&nbsp;
            </span>
            <span v-html="pageInfo.notice"> </span>
          </p>
        </div>
      </div>
    </el-card>
    <inform :visible.sync="informVisible" :detail="pageInfo?.inform || ''" @checking="checkingInform" />
  </div>
</template>

<script>
import { otherInsuranceDetail } from '@/apis/otherInsurance'
import { mapGetters } from 'vuex'
import Inform from '@/components/policy/Inform'
export default {
  name: 'otherInsureDetail',
  components: { Inform },
  data() {
    return {
      // 保险期限
      insurance_period: {
        1: '一个月',
        2: '二个月',
        3: '三个月',
        4: '四个月',
        5: '五个月',
        6: '六个月',
        7: '七个月',
        8: '八个月',
        9: '九个月',
        10: '十个月',
        11: '十一个月',
        12: '一年',
        24: '两年'
      },
      pageInfo: {},
      informVisible: false
    }
  },
  computed: {
    ...mapGetters('auth', ['user'])
  },
  created() {
    //  获取详情
    this.getDetailList()
  },
  methods: {
    getDetailList() {
      otherInsuranceDetail(this.$route.params.id).then((res) => {
        const _temp = res.data
        _temp.label = _temp.label?.split('｜')
        _temp.benefit = JSON.parse(_temp.benefit)
        _temp.related_file = JSON.parse(_temp.related_file)
        _temp.insurance_period = this.insurance_period[_temp.insurance_period]
        this.pageInfo = _temp
      })
    },
    handleInsure() {
      this.$router.push({ name: 'InsureOtherForm', params: { id: this.pageInfo.id } })
    },
    submit() {
      if (this?.pageInfo?.channel?.platform?.includes(this.user.platform_id) && this?.pageInfo?.inform?.length > 1) {
        this.informVisible = true
        return
      }
      this.handleInsure()
    },
    checkingInform() {
      this.informVisible = false
      this.handleInsure()
    }
  }
}
</script>

<style lang="scss" scoped>
.other-insure-detail {
  padding: 0 20px;
  .main-content {
    padding: 20px;
    .title {
      color: #212529;
      font-size: 28px;
    }
    .subtitle {
      .subtitle-name {
        margin-bottom: 48px;
        span {
          display: inline-block;
          padding-right: 12px;
        }
      }
      .subtitle-price {
        color: #dc3545;
        font-size: 24px;
        margin-bottom: 4px;
      }
      .subtitle-time {
        color: #6c757d;
      }
    }
    .insuredNow {
      align-self: flex-end;
      width: 90px;
      height: 38px;
    }
  }
}
//    本页table表格样式
.table {
  width: 100%;
  color: #212529;
  border: 1px solid #dee2e6;
  border-collapse: collapse;
}
.table th,
td {
  padding: 12px;
  border: 1px solid #dee2e6;
}
//    本页每条权益样式
.options_item {
  font-size: 16px;
  font-weight: 700 !important;
  margin-bottom: 16px;
  color: #212529;
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 15px;
    background-color: #ff7f4c;
    margin-right: 8px;
  }
}
//    本页每条权益盒子
.options_item_box {
  border: 1px solid #dee2e6;
  padding: 24px 16px;
}
</style>
