<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 10:42:14
 * @LastEditors: yanb
 * @LastEditTime: 2021-08-10 16:36:52
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <policy-progress-bar v-if="isUnsubmitStatus" :active.sync="currentStep" :steps="steps" />
    <site-breadcrumb v-if="!isUnsubmitStatus">
      <div class="m-mini-x">
        <el-button icon="el-icon-download" @click="download" type="primary">导出人员</el-button>
        <el-button
          icon="el-icon-document-copy"
          type="primary"
          @click="$router.push({ name: 'PoliciesGroupEndorse', params: { id: $route.params.id } })"
        >
          批单管理
        </el-button>
      </div>
    </site-breadcrumb>
    <el-card class="table-wrap" shadow="never" v-if="unpaidPayments.length > 0 && isApproved">
      <el-alert
        title="温馨提示"
        description="您当前投保产品为见费出单请完成支付以完成投保流程"
        type="warning"
        show-icon
        :closable="false"
      ></el-alert>

      <define-table :data="unpaidPayments" :cols="unpaidPaymentCols" />
    </el-card>
    <el-card class="table-wrap m-extra-large-t" shadow="never" v-if="!isUnsubmitStatus">
      <div class="d-flex w-100" slot="header">
        <b
          >保单号 {{ policyOrderNo }} - {{ policyStatus }}, 在保人员: {{ insuredEmployeeCount }}人, 保费:
          {{ userPremium }}
        </b>
        <i class="flex-fill m-mini-r"></i>
        <el-input
          class="m-mini-r"
          :style="{ width: '300px' }"
          v-model="filter.search"
          placeholder="关键词：如身份证号等"
        ></el-input>
        <el-button icon="el-icon-search" type="primary" @click="fetchInsuredEmployee(filter)">查询</el-button>
      </div>
      <define-table
        :data="inServiceData"
        :cols="inServiceCols"
        :paging="inServicePaging"
        :paging-events="inServicePagingEvents"
      />
    </el-card>
    <el-card class="m-extra-large-t table-wrap" shadow="never">
      <div class="d-flex w-100" slot="header">
        <i class="flex-fill m-mini-r"></i>
        <el-button type="primary" v-if="editable" @click="manuallyAdd()">手动添加</el-button>
        <el-button type="primary" v-if="editable" @click="batchAdd()">批量导入</el-button>
        <el-button type="primary" @click="downloadImportExcel(importTemplateLink)">下载人员模板</el-button>
        <el-input
          class="m-extra-large-l m-mini-r"
          :style="{ width: '300px' }"
          v-model="unsubmitFilter.search"
          placeholder="关键词：如身份证号等"
        />
        <el-button icon="el-icon-search" type="primary" @click="fetchUnsubmitEmployee(unsubmitFilter)">查询</el-button>
      </div>
      <define-table :data="data" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
    <el-card class="m-extra-large-t text-center" shadow="never" v-if="editable">
      <el-button
        type="primary"
        icon="fas fa-arrow-left"
        v-if="isUnsubmitStatus"
        @click="$router.push({ name: 'PoliciesInsureGroup', params: { policyGroupId: $route.params.id, step: 1 } })"
        >上一步</el-button
      >
      <el-button type="primary" id="vhref" icon="fas fa-calculator" @click="handleClacPremium">计算保费</el-button>
    </el-card>

    <employee-form :visible.sync="employeeFormVisible" :jobs="jobs" @submit="handleAddEmployee" ref="employeeForm" />
    <employee-replace-form
      :visible.sync="employeeReplaceForm.visible"
      :data="employeeReplaceForm.data"
      @submit="handleReplaceEmployee"
    />
    <employee-edit-form
      :visible.sync="employeeEditForm.visible"
      :data="employeeEditForm.data"
      :jobs="jobs"
      @submit="handleEditEmployee"
    />
    <employee-import-form
      :visible.sync="employeeImportFormVisible"
      :download.sync="importTemplateLink"
      @submit="handleImportEmployee"
    />
    <premium-form
      :visible.sync="premiumForm.visible"
      :isEndorse="isEndorse"
      :payment-type="policyGroup?.product?.additional?.payment_type"
      :payment-method="policyGroup?.policy?.payment_method"
      :revision-template-file="policyGroup?.product?.additional?.revision_template_file"
      :premium="premiumForm.premium"
      @submit="handleSubmitEmployee"
    />

    <el-dialog
      :visible.sync="paymentDialog.visible"
      title="在线支付"
      destory-on-close
      width="450px"
      :before-close="() => (paymentDialog.visible = false)"
      class="no-padding"
    >
      <el-form :model="paymentDialog.data" ref="paymentForm" label-suffix=":" label-position="top">
        <el-alert
          type="success"
          :closable="false"
          :title="`您正在支付订单 ${paymentDialog.payment.order_no}`"
          :description="`支付金额: ${paymentDialog.payment.amount} 元`"
        ></el-alert>
        <el-alert
          v-if="paymentDialog.payment.method === -1"
          class="m-extra-large-t"
          type="warning"
          :closable="false"
          show-icon
          title="温馨提示"
          description="支付方式一旦确定后续将无法更改"
        ></el-alert>
        <el-form-item
          v-if="paymentDialog.payment.method === -1"
          label="支付方式"
          :rules="[{ required: true, message: '请选择支付方式' }]"
        >
          <el-select v-model="paymentDialog.data.method" placeholder="请选择支付方式" style="width: 100%">
            <el-option :value="1" label="中意公对公转账"></el-option>
            <el-option :value="2" label="中意微信支付"></el-option>
            <el-option :value="3" label="中意支付宝支付"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="邮箱" :rules="[{ required: true, type: 'email', message: '请输入邮箱地址' }]">
          <el-input type="email" v-model="paymentDialog.data.email" placeholder="请输入邮箱地址"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" style="width: 100%" icon="fas fa-arrow-right" @click="handlePay">
            立即支付
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import EmployeeForm from '@/components/policy/Group/EmployeeForm'
import EmployeeReplaceForm from '@/components/policy/Group/EmployeeReplaceForm'
import EmployeeEditForm from '@/components/policy/Group/EmployeeEditForm'
import EmployeeImportForm from '@/components/policy/Group/EmployeeImportForm'
import PremiumForm from '@/components/policy/Group/PremiumForm'
import PolicyProgressBar from '@/components/policy/PolicyProgressBar'
import { tokenKey } from '@/config'
import {
  modifyInsuredEmployee,
  getGroupPolicy,
  getGroupProductPlan,
  getUnsubmitEmployee,
  deleteUnsubmitEmployee,
  clacEmployeePremium,
  submitEmployee,
  getInsuredEmployee,
  importEmployeesList
} from '@/apis/groupProduct'
import { unpaidPayments, getUnpaidPayment } from '@/apis/policy'
import { Loading } from 'element-ui'

export default {
  name: 'PoliciesGroupEmployee',
  components: {
    SiteBreadcrumb,
    EmployeeForm,
    EmployeeReplaceForm,
    EmployeeImportForm,
    EmployeeEditForm,
    PremiumForm,
    PolicyProgressBar
  },
  data() {
    return {
      steps: ['1.选择套餐', '2.填写投保信息', '3.添加保障人员', '4.计算保费', '5.提交成功'],
      currentStep: 2,
      policyGroup: null,
      jobs: [],
      isUnsubmitStatus: false,
      employeeFormVisible: false,
      userPremium: 0,
      insuredEmployeeCount: 0,
      paymentDialog: {
        visible: false,
        payment: {},
        data: {
          email: '',
          method: null
        }
      },
      premiumForm: {
        visible: false,
        premium: ''
      },
      isEndorse: false,
      employeeImportFormVisible: false,
      employeeReplaceForm: {
        visible: false,
        data: {}
      },
      employeeEditForm: {
        visible: false,
        data: {}
      },
      inServiceData: [],
      inServiceCols: [
        { label: '姓名', prop: 'name' },
        { label: '身份证号码	', width: '150', prop: 'idcard_no' },
        { label: '手机号码	', width: '120', prop: 'mobile' },
        { label: '职位类别', prop: 'job.code' },
        { label: '职位名称', prop: 'job_name' },
        { label: '保费', prop: 'fee' },
        { label: '添加时间', width: '150', prop: 'created_at' },
        {
          label: '状态',
          prop: 'status',
          scopedSlots: {
            default: (scoped) => {
              const text = {
                0: '无效',
                1: '未提交',
                2: '已提交',
                3: '审核中',
                4: '已生效',
                5: '已替换',
                6: '已批减'
              }
              return <span>{text[scoped.row.status]}</span>
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          width: '180',
          scopedSlots: {
            default: (scoped) => {
              if (!this.editable) {
                return '-'
              }

              return (
                <div>
                  <el-popconfirm title="确定批减吗？" onConfirm={() => this.handleInsuredEmployeeRemove(scoped.row)}>
                    <el-button slot="reference" size="mini" type="primary" icon="fas fa-user-slash">
                      批减
                    </el-button>
                  </el-popconfirm>
                  <el-button
                    type="danger"
                    icon="fas fa-user-edit"
                    class="m-mini-l"
                    size="mini"
                    onClick={() => this.handleInsuredEmployeeReplace(scoped.row)}
                  >
                    替换
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      filter: {
        search: ''
      },
      unsubmitFilter: {
        search: ''
      },
      // 分页器 不传递 -> 没有分页
      inServicePaging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      inServicePagingEvents: {
        currentChange: (page) => {
          this.fetchDetail()
          this.fetchInsuredEmployee(this.filter, page)
        }
      },
      data: [],
      cols: [
        { label: '姓名', prop: 'name', width: '100', fixed: 'left' },
        { label: '身份证号码	', width: '150', prop: 'idcard_no', fixed: 'left' },
        { label: '手机号码	', width: '120', prop: 'mobile' },
        { label: '职位类别', prop: 'job.code' },
        { label: '职位名称', prop: 'job_name', width: 200 },
        {
          label: '保费',
          prop: 'fee'
        },
        { label: '添加时间', width: '150', prop: 'created_at' },
        {
          label: '标识',
          prop: 'action',
          scopedSlots: {
            default: (scoped) => {
              const text = {
                0: '新增',
                1: '替换',
                2: '批减'
              }
              return <span>{text[scoped.row.action]}</span>
            }
          }
        },
        {
          label: '状态',
          prop: 'status',
          scopedSlots: {
            default: (scoped) => {
              const text = {
                0: '无效',
                1: '未提交',
                2: '已提交',
                3: '审核中',
                4: '已生效',
                5: '已替换',
                6: '已批减'
              }
              return <span>{text[scoped.row.status]}</span>
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          width: '180',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.status === 1) {
                if (scoped.row.action === 0) {
                  return (
                    <div>
                      <el-popconfirm title="确定删除吗？" onConfirm={() => this.handleEmployCancel(scoped.row.id)}>
                        <el-button size="mini" slot="reference" type="danger" icon="fas fa-times">
                          删除
                        </el-button>
                      </el-popconfirm>
                      <el-button
                        size="mini"
                        type="primary"
                        icon="fas fa-edit"
                        slot="reference"
                        onClick={() => this.handleUnsubmitEmployeeEdit(scoped.row)}
                        class="m-mini-l"
                      >
                        修改
                      </el-button>
                    </div>
                  )
                } else {
                  return (
                    <div>
                      <el-popconfirm title="确定删除吗？" onConfirm={() => this.handleEmployCancel(scoped.row.id)}>
                        <el-button type="danger" icon="fas fa-times" size="mini" slot="reference">
                          撤消
                        </el-button>
                      </el-popconfirm>
                    </div>
                  )
                }
              } else {
                const text = {
                  0: '无效',
                  1: '未提交',
                  2: '已提交',
                  3: '审核中',
                  4: '已生效',
                  5: '已替换',
                  6: '已批减'
                }
                return <span>{text[scoped.row.status]}</span>
              }
            }
          }
        }
      ],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      pagingEvents: {
        currentChange: (page) => {
          this.fetchUnsubmitEmployee(this.unsubmitFilter, page)
        }
      },
      unpaidPayments: [],
      unpaidPaymentCols: [
        { label: '订单号', prop: 'order_no' },
        { label: '金额', prop: 'amount' },
        { label: '支付方式', prop: 'method_name' },
        { label: '创建时间', prop: 'created_at' },
        {
          label: '操作',
          fixed: 'right',
          width: '180',
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-button
                  type="primary"
                  icon="fas fa-credit-card"
                  onClick={() => {
                    this.paymentDialog.visible = true
                    this.paymentDialog.payment = scoped.row
                  }}
                >
                  立即支付
                </el-button>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    this.refresh()
  },
  methods: {
    handleAddEmployee(data) {
      data.policy_group_id = this.$route.params.id
      modifyInsuredEmployee(data).then(() => {
        this.fetchUnsubmitEmployee()
        this.employeeFormVisible = false

        this.$refs.employeeForm.resetFields()
      })
    },
    handleReplaceEmployee(data) {
      modifyInsuredEmployee(data).then(() => {
        this.employeeReplaceForm.visible = false
        new Promise((resolve) => {
          this.refresh()
          resolve(true)
        }).then(() => document.getElementById('vhref').scrollIntoView({ block: 'end' }))
      })
    },
    handleEditEmployee(data) {
      modifyInsuredEmployee(data).then(() => {
        this.employeeEditForm.visible = false
        this.refresh()
      })
    },
    handleImportEmployee(data) {
      const loading = Loading.service()
      importEmployeesList(this.$route.params.id, data)
        .then(() => {
          loading.close()
          this.refresh()
        })
        .finally(() => {
          loading.close()
        })
    },
    handleSubmitEmployee(data) {
      const loading = Loading.service()
      submitEmployee(this.$route.params.id, data)
        .then(() => {
          loading.close()
          this.refresh()
        })
        .finally(() => {
          loading.close()
        })
    },
    handleClacPremium() {
      clacEmployeePremium(this.$route.params.id).then((r) => {
        if (r.data?.alerts?.enable) {
          this.$alert(r.data?.alerts?.message).then(() => {
            this.premiumForm = {
              visible: true,
              premium: r.data.money
            }
          })
        } else {
          this.premiumForm = {
            visible: true,
            premium: r.data.money
          }
        }
      })
    },
    download() {
      let baseUrl = process.env.VUE_APP_BASE_API
      if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
        baseUrl = `${window.location.origin}${baseUrl}`
      }

      let link =
        `${baseUrl}group-policies/employees/download/${this.policyGroup.id}?token=` +
        window.localStorage.getItem(tokenKey)

      window.open(link)
    },
    downloadImportExcel(link) {
      window.open(link)
    },
    handlePay() {
      this.$refs.paymentForm.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          getUnpaidPayment(this.policyGroup?.policy?.id, this.paymentDialog.payment.id, {
            method: this.paymentDialog.data.method,
            extra: {
              email: this.paymentDialog.data.email
            }
          })
            .then((r) => {
              loading.close()
              this.paymentDialog.visible = false
              if (r.type === 'refresh') {
                this.$message.success('支付成功')

                this.refresh()
              } else {
                window.open(r.data, '_blank')

                this.$alert(
                  `您正在支付订单 ${this.paymentDialog.payment.order_no} <br/> 金额 <b class="text-primary">¥ ${this.paymentDialog.payment.amount}</b> 元`,
                  '在线支付',
                  {
                    confirmButtonText: '我已支付完成',
                    dangerouslyUseHTMLString: true,
                    callback: () => {
                      this.refresh()
                    }
                  }
                )
              }
            })
            .finally(() => loading.close())
        }
      })
    },
    fetchDetail() {
      getGroupPolicy(this.$route.params.id).then((r) => {
        this.policyGroup = r.data
        this.isUnsubmitStatus = [0, 10].includes(this.policyGroup.policy.status)
        this.userPremium = this.policyGroup.policy.user_premium
        this.insuredEmployeeCount = this.policyGroup.insured_employee_count
        if (
          [5, 8].includes(this.policyGroup.policy.status) &&
          this.policyGroup?.product?.additional?.third_platform !== 'API_GROUP_ZY'
        ) {
          this.isEndorse = true
        }

        this.fetchUnpaidPayments()
        this.fetchJobs(this.policyGroup.group_plan_id)
      })
    },
    fetchJobs(planId) {
      getGroupProductPlan(planId).then((r) => {
        this.jobs = r.data.jobs
      })
    },
    fetchUnsubmitEmployee(filter = {}, page = 1) {
      getUnsubmitEmployee(this.$route.params.id, {
        filter: filter,
        page: page
      }).then((r) => {
        this.data = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    fetchInsuredEmployee(filter = {}, page = 1) {
      getInsuredEmployee(this.$route.params.id, {
        filter: filter,
        page: page
      }).then((r) => {
        this.inServiceData = r.data

        this.inServicePaging.currentPage = r.meta.current_page
        this.inServicePaging.pageSize = r.meta.per_page
        this.inServicePaging.total = r.meta.total
      })
    },
    handleEmployCancel(employeeId) {
      deleteUnsubmitEmployee(employeeId).then(() => {
        this.refresh()
      })
    },
    handleInsuredEmployeeRemove(employee) {
      if (!this.isOperable([0, 1])) {
        return this.$message.error('不允许同时进行批增或替换')
      }
      let form = {
        policy_group_id: employee.policy_group_id,
        action: 'remove',
        employee_id: employee.id
      }

      modifyInsuredEmployee(form).then(() => {
        new Promise((resolve) => {
          this.refresh()
          resolve(true)
        }).then(() => document.getElementById('vhref').scrollIntoView({ block: 'end' }))
      })
    },
    handleInsuredEmployeeReplace(employee) {
      if (!this.isOperable([0, 2])) {
        return this.$message.error('不允许同时进行批增批减')
      }

      let replace = {
        policy_group_id: employee.policy_group_id,
        action: 'replace',
        employee_id: employee.id,
        name: employee.name,
        idcard_no: employee.idcard_no,
        mobile: employee.mobile
      }

      this.employeeReplaceForm = {
        visible: true,
        data: replace
      }
    },
    handleUnsubmitEmployeeEdit(employee) {
      let edit = {
        policy_group_id: employee.policy_group_id,
        action: 'edit',
        employee_id: employee.id,
        name: employee.name,
        idcard_no: employee.idcard_no,
        mobile: employee.mobile,
        job_id: employee.job_id,
        job_name: employee.job_name
      }
      this.employeeEditForm = {
        visible: true,
        data: edit
      }
    },
    isOperable(conflictActions) {
      if (
        (this.policyGroup?.product?.additional?.third_platform === 'API_GROUP_ZY' &&
          this.policyGroup?.product?.additional?.payment_type === 2) ||
        this.policyGroup?.product?.additional?.third_platform === 'PINGAN'
      ) {
        return !this.data.some((e) => conflictActions.includes(e.action))
      } else {
        return true
      }
    },
    refresh() {
      this.fetchDetail()
      this.fetchUnsubmitEmployee()
      this.fetchInsuredEmployee()
    },
    fetchUnpaidPayments() {
      unpaidPayments(this.policyGroup?.policy?.id).then((r) => {
        this.unpaidPayments = r.data
      })
    },
    manuallyAdd() {
      if ([1, 2, 6, 7].includes(this.policyGroup?.policy?.status)) {
        this.$message.warning('当前保单状态不允许进行人员修改')
        return false
      }

      if (!this.isOperable([1, 2])) {
        return this.$message.error('不允许同时进行批增/批减/替换操作')
      }

      this.employeeFormVisible = true
    },
    batchAdd() {
      if ([1, 2, 6, 7].includes(this.policyGroup?.policy?.status)) {
        this.$message.warning('当前保单状态不允许进行人员修改')
        return false
      }

      if (!this.isOperable([1, 2])) {
        return this.$message.error('不允许同时进行批增/批减/替换操作')
      }

      this.employeeImportFormVisible = true
    }
  },
  computed: {
    editable() {
      return [0, 5, 10].includes(this.policyGroup?.policy?.status) && this.unpaidPayments.length <= 0
    },
    isApproved() {
      return this.policyGroup?.policy?.status === 4 || this.policyGroup?.policy?.status === 8
    },
    policyOrderNo() {
      if (this.policyGroup) {
        return this.policyGroup.policy.order_no
      }

      return ''
    },
    policyStatus() {
      const text = {
        0: '暂存单',
        1: '已提交',
        2: '审核中',
        3: '已支付',
        4: '已审核',
        5: '已出单',
        6: '已退保',
        7: '已作废',
        8: '批改中',
        9: '退保中',
        10: '已退回',
        11: '待确认',
        12: '待支付'
      }

      if (this.policyGroup) {
        return text[this.policyGroup.policy.status]
      }

      return '未知状态'
    },
    importTemplateLink() {
      if (this.policyGroup?.plan?.id) {
        const planId = this.policyGroup?.plan?.id

        let baseUrl = process.env.VUE_APP_BASE_API
        if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
          baseUrl = `${window.location.origin}${baseUrl}`
        }
        return `${baseUrl}group-products/plans/${planId}/template?token=` + window.localStorage.getItem(tokenKey)
      }

      return '#'
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
