<template>
  <div class="w-100 editTable">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '0' }">
      <el-form ref="form" :model="form" label-width="120px">
        <h1>投保联系人</h1>
        <el-row type="flex" justify="space-between">
          <el-col :span="10">
            <el-form-item label="投保联系人">
              <el-input v-model="form.applicant_contact_name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="电话">
              <el-input v-model="form.applicant_contact_phone"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <h1>基本信息</h1>
        <el-row type="flex" justify="space-between">
          <el-col :span="10">
            <el-form-item label="投保人">
              <el-input v-model="form.policyholder"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="被保人">
              <el-input v-model="form.insured"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="10">
            <el-form-item label="起保日期">
              <el-input v-model="form.start_at" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="被保日期">
              <el-input v-model="form.end_at" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <h1>保单相关信息</h1>
        <el-row type="flex" justify="space-between" v-for="(item, index) in form.addition" :key="index">
          <el-col :span="10">
            <el-form-item :label="item.title">
              <el-input v-model="item.value" v-if="item.type === 'text'"></el-input>
              <el-input v-model="item.value" type="textarea" v-if="item.type === 'textarea'"></el-input>
              <upload-file v-model="item.value" v-if="item.type === 'file'"></upload-file>
            </el-form-item>
          </el-col>
        </el-row>
        <h1>发票相关信息</h1>
        <el-row>
          <el-form-item lable="">
            <el-radio v-model="form.invoice_type" label="no" disabled>不需要发票</el-radio>
            <el-radio v-model="form.invoice_type" label="plain" disabled>普通发票</el-radio>
            <el-radio v-model="form.invoice_type" label="special" disabled>增值税专用发票</el-radio>
          </el-form-item>
        </el-row>
        <el-row type="flex" justify="space-between" v-if="form.invoice_type !== 'no'">
          <el-col :span="10">
            <el-form-item label="发票抬头">
              <el-input v-model="form.title" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="纳税人识别号">
              <el-input v-model="form.tax_no" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between" v-if="form.invoice_type !== 'no'">
          <el-col :span="10">
            <el-form-item label="开户行">
              <el-input v-model="form.bank" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="账号">
              <el-input v-model="form.card_no" disabled></el-input>
            </el-form-item>
          </el-col> </el-row
        ><el-row type="flex" justify="space-between" v-if="form.invoice_type !== 'no'">
          <el-col :span="10">
            <el-form-item label="公司地址">
              <el-input v-model="form.company_address" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="公司电话">
              <el-input v-model="form.company_phone" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="">
          <el-button type="primary" @click="submitFrom">提交</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { checkDetail, editFrom } from '@/apis/otherInsurance'

export default {
  name: 'edit',
  data() {
    return {
      form: {
        applicant_contact_name: '投保联系人',
        applicant_contact_phone: '电话',
        policyholder: '投保人',
        insured: '被保人',
        start_at: '起保日期',
        end_at: '终保日期',
        invoice_type: 'special',
        title: '发票抬头',
        tax_no: '纳税人识别号',
        bank: '开户行',
        card_no: '账号',
        company_address: '公司地址',
        company_phone: '公司电话'
      }
    }
  },
  mounted() {
    this.getFromData()
  },
  methods: {
    getFromData() {
      const _id = this.$route.params.id
      checkDetail(_id).then((res) => {
        res.data.detail.addition = JSON.parse(res.data.detail.addition)
        res.data.detail.invoice_content = JSON.parse(res.data.detail.invoice_content)
        const _temp = {
          applicant_contact_name: res.data.detail?.applicant_contact_name ?? '',
          applicant_contact_phone: res.data.detail?.applicant_contact_phone ?? '',
          policyholder: res.data.policyholder ?? '',
          insured: res.data.insured ?? '',
          start_at: res.data.detail?.start_at ?? '',
          end_at: res.data.detail?.end_at ?? '',
          invoice_type: res.data.detail?.invoice_content?.invoice_type ?? 'no',
          title: res.data.detail?.invoice_content?.title ?? '',
          tax_no: res.data.detail?.invoice_content?.tax_no ?? '',
          bank: res.data.detail?.invoice_content?.bank ?? '',
          card_no: res.data.detail?.invoice_content?.card_no ?? '',
          company_address: res.data.detail?.invoice_content?.company_address ?? '',
          company_phone: res.data.detail?.invoice_content?.company_phone ?? '',
          addition: res.data.detail.addition
        }
        this.form = _temp
      })
    },
    // 提交
    submitFrom() {
      const _editData = Object.assign({}, this.form, { type: 4 })
      if (_editData?.addition.length) {
        _editData?.addition.forEach((item) => {
          _editData[item.name] = item.value
        })
      }
      delete _editData.addition
      delete _editData.invoice_type
      delete _editData.title
      delete _editData.tax_no
      delete _editData.bank
      delete _editData.card_no
      delete _editData.company_address
      delete _editData.company_phone
      editFrom(this.$route.params.id, _editData).then(() => {
        this.$message({
          type: 'success',
          message: '修改申请提交成功'
        })

        this.$router.go(-1)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.editTable {
  padding: 0 20px;
  .box-card-options {
    padding: 20px 15px;
  }
}
// h1
h1 {
  // position: relative;
  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 18px;
    background-color: #ff7f4c;
    margin-right: 10px;
  }
}
</style>
