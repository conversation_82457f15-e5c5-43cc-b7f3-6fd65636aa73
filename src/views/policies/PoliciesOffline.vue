<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 11:07:40
 * @LastEditors: yanb
 * @LastEditTime: 2024-04-28 15:16:18
-->
<template>
  <div class="policy-list p-extra-large-x p-extra-large-b">
    <policy-search :statuses="statuses" @search="handlePolicySearch"></policy-search>
    <el-card class="m-extra-large-t table-wrap" shadow="never">
      <div class="d-flex w-100" slot="header">
        <el-button type="primary" icon="fas fa-download" @click="handleExport">导出列表</el-button>
      </div>
      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import PolicySearch from '@/components/policy/PolicySearch'
import { getPolicies, buildDownloadHref, buildExportHref, buildDownloadFilesHref } from '@/apis/policy'
import { mapGetters } from 'vuex'

const PRODUCT_TYPE = 6

export default {
  name: 'PoliciesOffline',
  components: { PolicySearch },
  data() {
    return {
      searchData: {},
      // 表格数据
      tableData: [],
      meta: {},
      statuses: [
        { value: 0, label: '暂存单' },
        { value: 1, label: '已提交' },
        { value: 5, label: '已出单' },
        { value: 6, label: '已退保' },
        { value: 7, label: '已作废' },
        { value: 8, label: '批改中' },
        { value: 9, label: '退保申请中' },
        { value: 10, label: '已退回' },
        { value: 13, label: '保单退回(补充资料)' },
        { value: 14, label: '工单退回(补充资料)' }
      ],
      // 表格列配置
      cols: [
        {
          label: '保单号/流水号',
          width: 180,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label class="text-blue">{scoped.row.policy_no ? scoped.row.policy_no : '尚未出单 ~'}</label>
                  <br />
                  <small>{scoped.row.order_no}</small>
                </div>
              )
            }
          }
        },
        { prop: 'company_branch_name', label: '出单公司', width: '100' },
        {
          prop: 'insured',
          label: '投保人/被保人',
          width: '180',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policyholder}</label>
                  <br />
                  <small>{scoped.row.insured ? scoped.row.insured : ''}</small>
                </div>
              )
            }
          }
        },
        {
          label: '险类',
          prop: 'policy_offline.category.name',
          width: '150'
        },
        {
          label: '险种',
          prop: 'policy_offline.insurance.name'
        },
        {
          label: '保费',
          prop: 'premium'
        },
        { prop: 'user.name', label: '投保用户', width: '150' },
        {
          label: '投保时间/出单时间',
          width: 140,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.submitted_at}</label>
                  <br />
                  <small>{scoped.row.issued_at}</small>
                </div>
              )
            }
          }
        },
        {
          label: '状态',
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.status_text}</label>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          width: '200',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link onClick={() => this.handleTable('view', scoped.row)} class="text-blue">
                    详情
                  </el-link>
                  <el-link
                    onClick={() =>
                      scoped.row.status === 5
                        ? this.handleTable('download', scoped.row)
                        : this.$message.warning('暂未出单')
                    }
                    class="text-blue m-mini-x"
                  >
                    下载
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.fetchPolicies()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['user'])
  },
  created() {
    this.fetchPolicies()
  },
  methods: {
    fetchPolicies() {
      this.searchData.type = PRODUCT_TYPE

      getPolicies({
        filter: this.searchData,
        page: this.paging.page
      }).then((r) => {
        this.tableData = r.data
        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    handlePolicySearch(val) {
      this.searchData = Object.assign({}, this.searchData, val)

      this.fetchPolicies()
    },
    handleExport() {
      this.searchData.type = PRODUCT_TYPE

      window.open(
        buildExportHref({
          filter: this.searchData
        })
      )
    },
    handleTable(type, row) {
      switch (type) {
        case 'view':
          this.$router.push({ name: 'PoliciesOfflineDetails', params: { id: row.id } })
          break
        case 'download':
          window.open(row.policy_file)
          break
        default:
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-list {
  width: 100%;
  overflow: auto;
  /deep/ .table-wrap {
    flex: 1;
    overflow: hidden;
    .el-card__body {
      padding-top: 0;
      padding-bottom: 0;
      overflow: auto;
    }
  }
}
</style>
