<template>
  <div class="pay-info w-100">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '0' }">
      <p>应付保费： ¥{{ price }}元</p>
      <div class="d-flex">
        <span style="line-height: 30px; margin-right: 18px">支付凭证:</span>
        <upload-file v-model="fileList"></upload-file>
      </div>
    </el-card>
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '0' }">
      <div class="d-flex justify-content-center align-items-center">
        <el-button
          size="large"
          type="primary"
          icon="el-icon-refresh-right"
          @click="
            $router.push({
              name: 'InsureOtherForm',
              params: { id: $route.query.product_id },
              query: { policy_id: $route.params.id }
            })
          "
          >返回上一页</el-button
        >
        <el-button size="large" type="primary" @click="submitBtn">确认提交</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { uploadFile, otherInsuranceDetail } from '@/apis/otherInsurance'

export default {
  name: 'payInfoPage',
  data() {
    return {
      fileList: void 0,
      price: void 0
    }
  },
  mounted() {
    otherInsuranceDetail(this.$route.query.product_id).then((r) => {
      this.price = r.data.reference_price
    })
  },
  methods: {
    submitBtn() {
      if (!this.fileList) {
        return this.$message({
          type: 'error',
          message: '请上传支付凭证'
        })
      }

      uploadFile(this.$route.params.id, { proof: this.fileList })
        .then(() => {
          this.$message({
            type: 'successs',
            message: '提交成功'
          })

          this.$store.dispatch('auth/refreshUser')

          this.$router.push({ name: 'ManageOtherDetail', params: { id: this.$route.params.id } })
        })
        .catch((err) => {
          this.$message({
            type: 'error',
            message: err
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.pay-info {
  padding: 20px;
  .box-card-options {
    padding: 16px 20px;
  }
}

// 上传提示文字
.el-upload__tip {
  color: #f56c6c;
}
//上传间距
.upload-demo {
  margin-left: 15px;
}
//卡片间距
.el-card {
  margin-bottom: 20px;
}
</style>
