<template>
  <div class="payHistory w-100">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '0' }">
      <define-table :data="dataList" :cols="cols"></define-table>
    </el-card>
  </div>
</template>

<script>
import { recordpay } from '@/apis/otherInsurance'
import dayjs from 'dayjs'

export default {
  name: 'PayHistory',
  data() {
    return {
      dataList: [],
      cols: [
        { align: 'center', type: 'index' },
        { prop: 'transaction_no', label: '流水号' },
        { prop: 'amount', label: '金额' },
        { prop: 'created_at', label: '支付时间' },
        {
          prop: 'status',
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              return <div>{this.statusList[scoped.row.status - 1].label}</div>
            }
          }
        }
      ],
      statusList: [
        { label: '已支付', value: 1 },
        { label: '已到账', value: 2 },
        { label: '已退回', value: 3 }
      ]
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      recordpay(this.$route.params.id).then((r) => {
        r.data?.forEach((item) => {
          item.created_at = dayjs(item.created_at).format('YYYY-MM-DD HH:mm')
        })
        this.dataList = r.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.payHistory {
  padding: 0 20px;
}
</style>
