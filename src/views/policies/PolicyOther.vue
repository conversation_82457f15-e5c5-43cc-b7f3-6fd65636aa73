<template>
  <div class="policy-list p-extra-large-x p-extra-large-b w-100">
    <policy-search :statuses="statusList" :hide-cargo-fields="true" @search="handlePolicySearch" />
    <el-card class="m-extra-large-t table-wrap" shadow="never">
      <div class="d-flex w-100" slot="header">
        <el-button icon="fas fa-download" @click="handleExport">导出列表</el-button>
      </div>
      <DefineTable :data="tabelData" :cols="cols" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
  </div>
</template>

<script>
import PolicySearch from '@/components/policy/PolicySearch'
import { manageList } from '@/apis/otherInsurance'
import { buildDownloadHref, buildExportHref } from '@/apis/policy'

const PRODUCT_TYPE = 4

export default {
  name: 'policyOther',
  components: {
    PolicySearch
  },
  data() {
    return {
      searchData: {},
      statusList: [
        { value: 0, label: '暂存单' },
        { value: 1, label: '已提交' },
        { value: 2, label: '审核中' },
        { value: 3, label: '已支付' },
        { value: 5, label: '已出单' },
        { value: 6, label: '已退保' },
        { value: 8, label: '批改申请中' },
        { value: 9, label: '退保申请中' },
        { value: 10, label: '已退回' }
      ],

      // 表格表头设置
      cols: [
        {
          label: '保险公司',
          // width: '140',
          prop: 'company.name'
        },
        { prop: 'product.name', label: '保险产品', align: 'center' },
        {
          label: '保单号',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <div>{scoped.row?.order_no}</div>
                  <div>{scoped.row?.policy_no}</div>
                </div>
              )
            }
          }
        },
        { prop: 'user.name', label: '投保用户', width: '100' },
        {
          label: '被保人/投保人',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <div>{scoped.row?.policyholder}</div>
                  <div>{scoped.row?.insured}</div>
                </div>
              )
            }
          }
        },
        {
          label: '投保时间/起保时间',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <div>{scoped.row.created_at}</div>
                  <div>{scoped.row.start_at}</div>
                </div>
              )
            }
          }
        },
        {
          prop: 'status',
          label: '状态',
          align: 'center',
          fixed: 'right',
          width: '120',
          scopedSlots: {
            default: (scoped) => {
              return <div>{scoped.row.status_text}</div>
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          width: '180',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link onClick={() => this.handlerDetail(scoped.row)} plain size="small" class="text-blue m-mini-x">
                    查看
                  </el-link>
                  <el-link
                    onClick={() =>
                      scoped.row.status === 5 ? this.handleDownload(scoped.row) : this.$message.warning('暂未出单')
                    }
                    class="text-blue"
                  >
                    下载
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      // 表格数据
      tabelData: [],
      //  表格分页
      pagingAttrs: {
        align: 'left',
        page: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      //  分页事件
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.page = page

          this.getListData()
        }
      }
    }
  },
  mounted() {
    const _base = {
      type: 4
    }
    this.getListData({ filter: _base })
  },
  methods: {
    // 保单导出
    handleExport() {
      const link = buildExportHref(this.searchData)
      window.open(link, '_blank')
    },
    // 搜索列表
    handlePolicySearch(val) {
      this.searchData = Object.assign({}, this.searchData, val)

      this.getListData()
    },
    getListData() {
      this.searchData.type = PRODUCT_TYPE
      manageList({
        filter: this.searchData,
        page: this.pagingAttrs.page
      }).then((r) => {
        const _temp = r
        this.pagingAttrs.page = r.meta.current_page
        this.pagingAttrs.total = r.meta.total
        this.pagingAttrs.pageSize = r.meta.per_page
        this.tabelData = _temp.data
      })
    },
    handlerDetail(row) {
      this.$router.push({
        name: 'ManageOtherDetail',
        params: {
          id: row.id
        }
      })
    },
    handleDownload(row) {
      const link = buildDownloadHref(row.id)
      window.open(link, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-list {
  width: 100%;
  overflow: auto;
  /deep/ .table-wrap {
    flex: 1;
    overflow: hidden;
    .el-card__body {
      padding-top: 0;
      padding-bottom: 0;
      overflow: auto;
    }
  }
}
</style>
