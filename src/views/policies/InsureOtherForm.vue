<template>
  <div class="write-table w-100">
    <!--    投保联系人信息-->
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '0' }">
      <div slot="header" class="clearfix">
        <span>投保联系人</span>
      </div>
      <div class="main-box">
        <el-form
          ref="baseInfoOne"
          :model="baseInfoOne"
          label-width="140px"
          :inline="true"
          size="small"
          label-suffix="："
        >
          <el-form-item
            label="姓名"
            prop="applicant_contact_name"
            :rules="[{ required: true, message: '请输入投保联系人姓名', trigger: 'blur' }]"
          >
            <el-input v-model="baseInfoOne.applicant_contact_name" placeholder="请输入投保联系人姓名(必填)"></el-input>
          </el-form-item>
          <el-form-item
            label="电话"
            prop="applicant_contact_phone"
            :rules="[{ required: true, message: '请输入投保联系人电话', trigger: 'blur' }]"
          >
            <el-input v-model="baseInfoOne.applicant_contact_phone" placeholder="请输入投保联系人电话(必填)"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--    基本信息-->
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '0' }">
      <div slot="header" class="clearfix">
        <span>基本信息</span>
      </div>
      <div class="main-box">
        <el-form
          ref="baseInfoTwo"
          :model="baseInfoTwo"
          label-width="140px"
          :inline="true"
          size="small"
          label-suffix="："
        >
          <el-form-item
            label="投保人"
            prop="policyholder"
            :rules="[{ required: true, message: '请输入投保人姓名', trigger: 'blur' }]"
          >
            <el-input v-model="baseInfoTwo.policyholder" placeholder="请输入投保人姓名(必填)"></el-input>
          </el-form-item>
          <el-form-item
            label="被保人"
            prop="insured"
            :rules="[{ required: true, message: '请输入被保人', trigger: 'blur' }]"
          >
            <el-input v-model="baseInfoTwo.insured" placeholder="请输入被保人(必填)"></el-input>
          </el-form-item>
          <el-form-item
            label="起保日期"
            prop="start_at"
            :rules="[{ required: true, message: '请输入起保日期', trigger: 'blur' }]"
          >
            <el-date-picker
              v-model="baseInfoTwo.start_at"
              type="date"
              format="yyyy-MM-dd 00:00:00"
              value-format="yyyy-MM-dd 00:00:00"
              class="w-100"
              placeholder="请选择起保日期(必填)"
            >
            </el-date-picker>
            <!--            <el-input v-model="form.tel" placeholder="请选择起保日期(必填)"></el-input>-->
          </el-form-item>
          <el-form-item prop="end_at" label="终保日期" :rules="[{ required: true, message: '请输入终保日期' }]">
            <el-input class="w-100" v-model="baseInfoTwo.end_at" placeholder="请选择日期" readonly />
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--    保单相关信息-->
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '0' }">
      <div slot="header" class="clearfix">
        <span>保单相关信息<span style="color: #ff7f4c; font-size: 12px">(以下都为必填项)</span></span>
      </div>
      <div class="main-box">
        <el-form
          ref="baseInfoThree"
          :model="baseInfoThree"
          label-width="140px"
          :inline="true"
          size="small"
          label-suffix="："
        >
          <div v-if="model.length === 0">无保单相关信息需要填写~</div>
          <el-form-item :label="item.title" v-for="item in model" :key="item.id">
            <div slot="label">
              {{ item.title }}
              <span v-if="item.file"><a :href="item.file" download="">模板</a></span>
            </div>
            <el-input v-model="baseInfoThree[item.name]" v-if="item.type === 'text'"></el-input>
            <el-input v-model="baseInfoThree[item.name]" type="textarea" v-if="item.type === 'textarea'"></el-input>
            <template v-if="item.type === 'file' || item.type === '_file'">
              <upload-file
                v-model="baseInfoThree[item.name]"
                accept=".jpeg,.png,.pdf,.jpg,.zip"
                :limitSize="4"
              ></upload-file>
              <span class="text-danger"> .jpeg, .png, .pdf, .jpg, .zip 文件，大小不能超过 4M</span>
            </template>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--    发票相关信息-->
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '0' }">
      <div slot="header" class="clearfix">
        <span>发票相关信息</span>
      </div>
      <div class="main-box-type">
        <el-radio v-model="invoice_type" label="no">不需要发票</el-radio>
        <el-radio v-model="invoice_type" label="plain">普通发票</el-radio>
        <el-radio v-model="invoice_type" label="special">增值税专用发票</el-radio>
      </div>
      <el-form
        ref="baseInfo"
        :model="baseInfo"
        label-width="140px"
        :inline="true"
        size="small"
        label-suffix="："
        v-if="invoice_type === 'plain' || invoice_type === 'special'"
      >
        <el-form-item
          label="发票抬头"
          prop="title"
          :rules="[{ required: true, message: '请输入发票抬头', trigger: 'blur' }]"
        >
          <el-input v-model="baseInfo.title" placeholder="请输入发票抬头"></el-input>
        </el-form-item>
        <el-form-item
          label="纳税人识别号"
          prop="tax_no"
          :rules="[{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }]"
        >
          <el-input v-model="baseInfo.tax_no" placeholder="请输入纳税人识别号"></el-input>
        </el-form-item>
        <template v-if="invoice_type === 'special'">
          <el-form-item
            label="开户行"
            prop="bank"
            :rules="invoice_type === 'special' ? [{ required: true, message: '请输入开户行', trigger: 'blur' }] : []"
          >
            <el-input v-model="baseInfo.bank" placeholder="请输入开户行"></el-input>
          </el-form-item>
          <el-form-item
            label="账号"
            prop="card_no"
            :rules="invoice_type === 'special' ? [{ required: true, message: '请输入账号', trigger: 'blur' }] : []"
          >
            <el-input v-model="baseInfo.card_no" placeholder="请输入账号"></el-input>
          </el-form-item>
          <el-form-item
            label="公司地址"
            prop="company_address"
            :rules="invoice_type === 'special' ? [{ required: true, message: '请输入公司地址', trigger: 'blur' }] : []"
          >
            <el-input v-model="baseInfo.company_address" placeholder="请输入公司地址"></el-input>
          </el-form-item>
          <el-form-item
            label="公司电话"
            prop="company_phone"
            :rules="invoice_type === 'special' ? [{ required: true, message: '请输入公司电话', trigger: 'blur' }] : []"
          >
            <el-input v-model="baseInfo.company_phone" placeholder="请输入公司电话"></el-input>
          </el-form-item>
        </template>
      </el-form>
    </el-card>
    <!--    计算保费-->
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '0' }">
      <div class="main-box-type d-flex justify-content-center align-items-center">
        <el-button size="large" type="primary" icon="el-icon-check" @click="submitInfo">计算保费</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import UploadFile from '../../components/globals/UploadFile/UploadFile.vue'
import { otherInsuranceDetail, writeTable } from '@/apis/otherInsurance'
import { getPolicyDetails } from '@/apis/policy'
import dayjs from 'dayjs'

export default {
  components: { UploadFile },
  name: 'insureOtherForm',
  data() {
    return {
      // 保单相关自定义信息
      product: {},
      model: [],
      fileList: [],
      //投保人信息
      baseInfoOne: {
        applicant_contact_name: '',
        applicant_contact_phone: ''
      },
      //基本信息
      baseInfoTwo: {
        policyholder: '',
        insured: '',
        start_at: '',
        end_at: ''
      },
      //票据上传信息
      baseInfoThree: {},
      //发票类型
      invoice_type: 'no',
      baseInfo: {},
      policyData: {}
    }
  },
  watch: {
    'baseInfoTwo.start_at'(value) {
      let startDate = new Date(value)
      startDate.setDate(startDate.getDate() - 1)
      this.baseInfoTwo.end_at = dayjs(startDate)
        .add(this.product?.insurance_period, 'month')
        .format('YYYY-MM-DD 23:59:59')
    }
  },
  created() {
    this.getComponent().then(() => {
      if (this.$route.query.policy_id) {
        getPolicyDetails(this.$route.query.policy_id).then((r) => {
          this.policyData = r.data
          this.fillInputs()
        })
      }
    })
  },
  methods: {
    fillInputs() {
      this.baseInfoOne.applicant_contact_name = this.policyData?.detail?.applicant_contact_name
      this.baseInfoOne.applicant_contact_phone = this.policyData?.detail?.applicant_contact_phone

      this.baseInfoTwo.policyholder = this.policyData?.policyholder
      this.baseInfoTwo.insured = this.policyData?.insured
      this.baseInfoTwo.start_at = this.policyData?.detail?.start_at
      this.baseInfoTwo.end_at = this.policyData?.detail?.end_at

      this.fillFields()
    },
    fillFields() {
      if (this.$route.query.policy_id) {
        const values = JSON.parse(this.policyData?.detail?.addition || '{}')
        this.model.forEach((e) => {
          const el = values.find((v) => e.name === v.name)

          e.value = el.value

          this.baseInfoThree[e.name] = el.value
        })
      }
    },
    // 获取自定义组建内容
    getComponent() {
      return otherInsuranceDetail(this.$route.params.id).then((r) => {
        this.product = r.data
        this.model = r.data.model.fields
      })
    },
    // 提交保单判断
    submitInfo() {
      const is_pass = [false, false, false]
      this.$refs.baseInfoOne.validate((valid) => {
        if (valid) {
          return (is_pass[0] = true)
        } else {
          return false
        }
      })
      this.$refs.baseInfoTwo.validate((valid) => {
        if (valid) {
          return (is_pass[1] = true)
        } else {
          return false
        }
      })
      this.$refs?.baseInfoThree?.validate((valid) => {
        if (valid) {
          return (is_pass[2] = true)
        } else {
          return false
        }
      })
      this.$refs?.baseInfo?.validate((valid) => {
        if (valid) {
          return (is_pass[3] = true)
        } else {
          return false
        }
      })
      const r = is_pass.every((item) => {
        return item === true
      })
      if (r) {
        const _temp = Object.assign(
          {},
          this.baseInfoOne,
          this.baseInfoTwo,
          this.baseInfoThree,
          { invoice_type: this.invoice_type },
          this.baseInfo
        )

        let submitInfo = {
          type: 4,
          product_id: this.$route.params.id,
          applicant_contact_name: _temp.applicant_contact_name,
          applicant_contact_phone: _temp.applicant_contact_phone,
          policyholder: _temp.policyholder,
          insured: _temp.insured,
          start_at: _temp.start_at,
          end_at: _temp.end_at,
          invoice_type: this.invoice_type,
          ...this.baseInfoThree,
          ...this.baseInfo
        }

        if (this.$route.query.policy_id) {
          submitInfo.policy_id = this.$route.query.policy_id
        }

        // console.log('提交数据', submitInfo)
        writeTable(submitInfo).then((r) => {
          this.$message({
            type: 'success',
            message: '提交成功'
          })

          this.$router.push({
            name: 'PayInfoPage',
            params: { id: r.data.id },
            query: {
              product_id: this.$route.params.id
            }
          })
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.write-table {
  padding: 0 20px;

  .main-box {
    padding: 20px 20px 0;
  }
  .main-box-type {
    padding: 20px;
  }
}

//  灰色背景颜色
.bg-color {
  background-color: rgba(0, 0, 0, 0.03);
}
//文字字段颜色
.word-color {
  color: #6c757d;
}
//  覆盖原始card-header样式
/deep/ .el-card__header {
  background-color: rgba(0, 0, 0, 0.03) !important;
}
//    el-form样式修正
.el-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  flex-wrap: wrap;
}
/deep/ .el-form-item {
  //margin: 0 !important;
  width: 48% !important;
}
/deep/ .el-form-item__content {
  width: 60% !important;
}
.el-date-editor {
  width: 100%;
}
// 每个el-card间距
.el-card {
  margin-bottom: 20px;
}
//上传列表提示文字
.el-upload__tip {
  color: #f56c6c;
}
</style>
