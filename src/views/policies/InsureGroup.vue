<!--
 * @Descripttion:
 * @Author: Mr. z<PERSON>
 * @Date: 2021-01-10 00:05:29
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-11 11:10:14
-->
<template>
  <div class="policy-form p-extra-large-x w-100">
    <policy-progress-bar :active.sync="currentStep" :steps="steps" />

    <choose-product :data.sync="companies" v-if="currentStep === 0" @selected="handleSelectedProduct" />

    <group-form
      v-if="currentStep === 1"
      :data.sync="product"
      :policy.sync="policy"
      :product.sync="product"
      @back="currentStep--"
      @submit="handleSubmit"
    />
  </div>
</template>

<script>
import ChooseProduct from '@/components/policy/Group/ChooseProduct'
import PolicyProgressBar from '@/components/policy/PolicyProgressBar'
import GroupForm from '@/components/policy/Group/Form'
import { getGroupProducts } from '@/apis/groupProduct'
import { insureGroupPolicy } from '@/apis/groupProduct'
import { getGroupPolicyProduct } from '../../apis/groupProduct'

export default {
  name: 'InsureGroup',
  components: {
    ChooseProduct,
    PolicyProgressBar,
    GroupForm
  },
  data() {
    return {
      product: {},
      policy: {},
      data: {},
      policyGroupId: -1,
      steps: ['1.选择套餐', '2.填写投保信息', '3.添加保障人员', '4.计算保费', '5.提交成功'],
      currentStep: 0,
      companies: []
    }
  },
  created() {
    this.fetchProductList()
    this.handleStepBar()

    // let product = window.localStorage.getItem('_selected_product')
    // if (product) {
    //   this.product = JSON.parse(product)
    // }
    // this.currentStep = parseInt(this.$route.query.current_step ?? 1)
  },
  methods: {
    handleBack() {
      this.currentStep--
    },
    handleSelectedProduct(data) {
      this.currentStep++
      this.product = data

      // window.localStorage.setItem('_selected_product', JSON.stringify(data))
      // this.$router.replace(this.$router.currentRoute.path + '?current_step=' + this.currentStep)
    },
    handleSubmit(data) {
      let form = data.data
      form.id = this.policyGroupId
      form.product_id = this.product.product.id
      form.group_plan_id = this.product.plan.id

      insureGroupPolicy(form).then((r) => {
        this.currentStep++
        this.$router.push({
          name: 'PoliciesGroupEmployee',
          params: { id: r.data.policy_group.id }
        })

        this.$notify({
          title: '保单已暂存成功',
          type: 'success',
          duration: 0
        })
      })
    },
    fetchProductList() {
      getGroupProducts().then((r) => {
        this.companies = r.data
      })
    },
    handleStepBar() {
      let policyGroupId = this.$route.params.policyGroupId
      let step = this.$route.params.policyGroupId

      if (policyGroupId !== undefined && step !== undefined) {
        this.currentStep = 1

        this.policyGroupId = policyGroupId
        getGroupPolicyProduct(policyGroupId).then((r) => {
          this.product = r.data

          this.policy = this.product.form
        })
      }
    }
  }
}
</script>
