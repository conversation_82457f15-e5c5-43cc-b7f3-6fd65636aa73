<!--
 * @Descripttion:
 * @Author: Mr. <PERSON><PERSON>
 * @Date: 2021-01-08 11:07:40
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 16:03:43
-->
<template>
  <div class="policy-list p-extra-large-x p-extra-large-b">
    <policy-search @search="handlePolicySearch" :hide-cargo-fields="true" :statuses="statuses"></policy-search>
    <el-card class="m-extra-large-t" shadow="never">
      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import PolicySearch from '@/components/policy/PolicySearch'
import { buildExportHref } from '@/apis/policy'
import { getGroupPolicies } from '@/apis/groupProduct'

const POLICY_TYPE = 5

export default {
  name: 'PoliciesGroup',
  components: { PolicySearch },
  data() {
    return {
      statuses: [
        { value: 0, label: '暂存单' },
        { value: 1, label: '已提交' },
        { value: 2, label: '审核中' },
        { value: 4, label: '审核完成' },
        { value: 5, label: '已出单' },
        { value: 7, label: '已作废' },
        { value: 8, label: '批改中' }
      ],
      searchData: {},
      // 表格数据
      tableData: [],
      meta: {},
      // 表格列配置
      cols: [
        {
          label: '保单号/流水号',
          width: 180,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label class="text-blue">{scoped.row.policy_no ? scoped.row.policy_no : '尚未出单 ~'}</label>
                  <br />
                  <small>{scoped.row.order_no}</small>
                </div>
              )
            }
          }
        },
        {
          label: '出单公司',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.company_branch.name
            }
          }
        },
        { prop: 'product.name', label: '保险产品', width: 200 },
        { prop: 'user.name', label: '投保用户', width: 120 },
        {
          label: '被保单位/投保单位',
          width: 220,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.insured}</label>
                  <br />
                  <small>{scoped.row.policyholder}</small>
                </div>
              )
            }
          }
        },
        {
          label: '在保人数',
          width: 80,
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.insured_employee_count} 人</label>
                </div>
              )
            }
          }
        },
        { prop: 'submitted_at', label: '投保时间', width: 150 },
        {
          label: '状态',
          width: 80,
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.status_text}</label>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          width: '100',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    onClick={() =>
                      this.$router.push({ name: 'PoliciesGroupDetails', params: { id: scoped.row.policy_group_id } })
                    }
                    class="text-blue"
                  >
                    详情
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.fetchPolicies()
        }
      }
    }
  },
  created() {
    this.fetchPolicies()
  },
  methods: {
    fetchPolicies() {
      this.searchData.type = POLICY_TYPE

      getGroupPolicies({
        filter: this.searchData,
        page: this.paging.page
      }).then((r) => {
        this.tableData = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    handlePolicySearch(val) {
      this.searchData = Object.assign({}, this.searchData, val)

      this.fetchPolicies(this.searchData)
    },
    handleExport() {
      window.open(
        buildExportHref({
          filter: this.filter
        })
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-list {
  width: 100%;
  overflow: auto;
  /deep/ .table-wrap {
    flex: 1;
    overflow: hidden;
    .el-card__body {
      padding-top: 0;
      padding-bottom: 0;
      overflow: auto;
    }
  }
}
</style>
