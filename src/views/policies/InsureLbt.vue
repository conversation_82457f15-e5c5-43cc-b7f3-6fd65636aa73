<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-10 00:05:29
 * @LastEditors: yanb
 * @LastEditTime: 2022-11-11 14:56:06
-->
<template>
  <div class="policy-form p-extra-large-x w-100">
    <policy-progress-bar :active.sync="currentStep" :steps="steps" />
    <el-alert
      :title="'您正在复制保单 ' + orderNo"
      type="warning"
      style="margin-bottom: 10px"
      :closable="false"
      v-show="$route.query.from === 'copy'"
    />
    <lbt-form v-show="currentStep === 0" :policy.sync="policy" @on-save="handleSave" @on-preview="handlePreview" />
    <preview-lbt
      v-if="currentStep === 1"
      :data="data"
      :origin-data="originData"
      :product="product"
      @on-back="handleBack"
      @on-submit="handleSubmit"
    />
  </div>
</template>

<script>
import { getPolicyDetails, previewPolicy, insurePolicy, saveAsDraft, updatePolicy } from '@/apis/policy'
import LbtForm from '@/components/policy/LbtForm'
import PreviewLbt from '@/components/policy/PreviewLbt'
import PolicyProgressBar from '@/components/policy/PolicyProgressBar'
import { Loading } from 'element-ui'

const PRODUCT_TYPE = 3

export default {
  name: 'InsureLBT',
  components: {
    LbtForm,
    PreviewLbt,
    PolicyProgressBar
  },
  data() {
    return {
      orderNo: '',
      policy: {},
      product: {},
      data: {},
      originData: {},
      steps: ['1.填写保单', '2.计算保费', '3.提交审核', '4.保单生效'],
      currentStep: 0
    }
  },
  created() {
    if (this.$route.params.id) {
      getPolicyDetails(this.$route.params.id).then((r) => {
        const data = r.data
        this.orderNo = data.order_no

        const [departure, departureAddr] = data.detail?.departure?.split(':') || []
        const [destination, destinationAddr] = data.detail?.destination?.split(':') || []
        const [transmit, transmitAddr] = data.detail?.transmit?.split(':') || []

        let policy = {
          type: PRODUCT_TYPE,
          product_id: data.product.id,
          policyholder: data.policyholder,
          policyholder_address: data.policyholder_address,
          policyholder_phone_number: data.policyholder_phone_number,
          insured: data.insured,
          insured_address: data.insured_address,
          insured_phone_number: data.insured_phone_number,
          sticky_note: data.sticky_note,
          goods_type_id: data.detail?.goods_type?.id,
          loading_method_id: data.detail?.loading_method?.id,
          packing_method_id: data.detail?.packing_method?.id,
          goods_name: data.detail?.goods_name,
          goods_amount: data.detail?.goods_amount,
          transport_method_id: data.detail?.transport_method?.id,
          departure: departure?.split('-'),
          departure_port: data.detail?.departure_port,
          departure_addr: departureAddr,
          destination: destination?.split('-'),
          destination_port: data.detail?.destination_port,
          destination_addr: destinationAddr
        }

        // 编辑需要填充所有的
        if (['edit', 'continue'].includes(this.$route.query.from)) {
          policy = Object.assign(policy, {
            id: data.id,
            coverage: data.coverage,
            transport_no: data.detail?.transport_no,
            invoice_no: data.detail?.invoice_no,
            waybill_no: data.detail?.waybill_no,
            shipping_date: Date.parse(data.detail?.shipping_date),
            transmit: transmit?.split('-'),
            transmit_port: data.detail?.transmit_port,
            transmit_addr: transmitAddr,
            remark: data.remark,
            anti_dated_file: data.detail?.anti_dated_file,
            custom_file: data.detail?.custom_file
          })
        }

        const ticket = data.tickets.find((e) => e.status === 4)?.revision || {}
        if (ticket?.shipping_date !== undefined) {
          ticket.shipping_date = Date.parse(ticket.shipping_date)
        }

        policy = Object.assign({}, policy, ticket)

        this.policy = policy
      })
    }
  },
  methods: {
    handleBack() {
      this.currentStep--
    },
    async handlePreview(product, originData, form) {
      this.product = product
      const loading = Loading.service()

      if (Object.keys(originData).length > 0 && this.$route.query.from === 'edit') {
        await previewPolicy(Object.assign({}, originData)).then((r) => {
          if (originData.id !== undefined) {
            this.originData = Object.assign({}, r.data, { id: originData.id })
            this.originData.policy_id = originData.id
          } else {
            this.originData = Object.assign({}, r.data, originData)
          }
        })
      }

      await previewPolicy(form)
        .then((r) => {
          if (form.id !== undefined) {
            this.data = Object.assign({}, r.data, { id: form.id })
            this.data.policy_id = form.id
          } else {
            this.data = Object.assign({}, r.data, form)
          }

          this.data.anti_dated_file = form.anti_dated_file
          this.data.custom_file = form.custom_file
        })
        .finally(() => loading.close())

      this.currentStep++
    },
    handleSave(product, data) {
      if (this.$route.params.id) {
        if (['edit', 'continue'].includes(this.$route.query.from)) {
          data.policy_id = this.$route.params.id
        }
      }

      const loading = Loading.service()
      saveAsDraft(data)
        .then(() => {
          this.$message.success('保存成功')

          this.$router.push({ name: 'PoliciesLbt' })
        })
        .finally(() => loading.close())
    },
    cleanData(data) {
      delete data.user_rate
      delete data.user_premium
      delete data.packing_method
      delete data.loading_method
      delete data.transport_method
      delete data.goods_type
      delete data.subject
      delete data.clauses

      return data
    },
    handleSubmit() {
      const loading = Loading.service()
      if (this.data.id !== undefined && this.$route.query.from === 'edit') {
        if (this.$route.query.ticket_id !== undefined) {
          this.data.ticket_id = this.$route.query.ticket_id
        }
        updatePolicy(this.data.id, this.cleanData(this.data))
          .then(() => {
            this.$message.success('提交成功')

            this.$router.push({ name: 'PoliciesLbtDetails', params: { id: this.data.id } })
          })
          .finally(() => loading.close())
      } else {
        if (this.$route.query.from === 'copy') {
          delete this.data.id
          delete this.data.policy_id
        }

        insurePolicy(this.cleanData(this.data))
          .then(() => {
            this.$message.success('提交成功')
            this.$store.dispatch('auth/refreshUser')

            this.$router.push({ name: 'PoliciesLbt' })
          })
          .finally(() => loading.close())
      }
    }
  }
}
</script>
