<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 10:42:14
 * @LastEditors: yanb
 * @LastEditTime: 2024-04-28 15:08:26
-->
<template>
  <div class="policy-wrap">
    <site-breadcrumb>
      <div class="m-mini-x" v-if="user.id === policy?.user?.id">
        <template v-if="policy.status === 5">
          <el-button
            icon="el-icon-download"
            type="primary"
            @click="download"
            :disabled="!policy.downloadable"
            :loading="!policy.downloadable"
          >
            {{ policy.downloadable ? '下载保单' : '保单生成中' }}
          </el-button>
        </template>
      </div>
    </site-breadcrumb>
    <div class="m-extra-large-x p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <define-details :data="data"></define-details>
    </div>
  </div>
</template>

<script>
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import { getPolicyDetails, buildDownloadHref } from '@/apis/policy'
import { mapGetters } from 'vuex'

export default {
  name: 'PoliciesOfflineDetails',
  components: {
    SiteBreadcrumb
  },
  data() {
    return {
      surrender: {
        visible: false
      },
      policy: {}
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    data() {
      return {
        title: '保单详情 - ' + this.policy?.status_text,
        data: [
          {
            title: '投保信息',
            groups: [
              { label: '险类', value: this.policy?.detail?.insurance_name },

              { label: '险种', value: this.policy?.detail?.category_name },
              {
                label: '保单号',
                value: this.policy?.policy_no
              },
              {
                label: '保险公司',
                value: this.policy?.company?.name
              },
              { label: '出单公司', value: this.policy?.company_branch?.name },
              { label: '保单起始日期', value: this.policy?.detail?.start_at },
              { label: '保单终止日期', value: this.policy?.detail?.end_at },
              { label: '业务员', value: this.policy?.salesman?.name },
              { label: '保单状态', value: this.policy?.status_text },
              { label: '投保人', value: this.policy?.policyholder },
              { label: '被保人', value: this.policy?.insured },
              { label: '保费', value: this.policy?.premium },
              { label: '业务类型', value: this.policy?.detail?.business_type == 1 ? '会员业务' : '代理业务' }
            ]
          }
        ]
      }
    }
  },
  methods: {
    download() {
      window.open(this.policy?.policy_file)
    },
    fetchDetail() {
      getPolicyDetails(this.$route.params.id).then((r) => {
        this.policy = r.data
      })
    }
  },
  created() {
    this.fetchDetail()
  }
}
</script>

<style lang="scss" scoped>
.policy-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
