<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 11:07:40
 * @LastEditors: yanb
 * @LastEditTime: 2023-01-19 17:28:21
-->
<template>
  <div class="policy-list p-extra-large-x p-extra-large-b">
    <policy-search :statuses="statuses" @search="handlePolicySearch"></policy-search>
    <el-card class="m-extra-large-t table-wrap" shadow="never">
      <div class="d-flex w-100" slot="header">
        <el-button type="primary" icon="fas fa-check" v-if="searchData?.status === 0" @click="handleBatchSubmit">
          批量投保
        </el-button>
        <el-button type="danger" icon="fas fa-trash" v-if="searchData?.status === 0" @click="handleBatchDestroy">
          批量作废
        </el-button>
        <el-button type="primary" icon="fas fa-download" @click="handleExport">导出列表</el-button>
        <el-button icon="fas fa-file-download" @click="handleDownloadFiles">下载保单</el-button>
      </div>
      <define-table
        ref="tableWithoutSelection"
        key="no-selection"
        v-if="searchData?.status !== 0"
        :row-key="(data) => data.id"
        :data="tableData"
        :cols="cols"
        :paging="paging"
        :paging-events="pagingEvents"
      />
      <define-table
        ref="tableWithSelection"
        key="with-selection"
        v-else
        :events="tableEvents"
        :row-key="(data) => data.id"
        :data="tableData"
        :cols="selectionCols"
        :paging="paging"
        :paging-events="pagingEvents"
      />
    </el-card>
  </div>
</template>

<script>
import PolicySearch from '@/components/policy/PolicySearch'
import {
  getPolicies,
  buildDownloadHref,
  buildExportHref,
  buildDownloadFilesHref,
  batchSubmit,
  batchDestroy
} from '@/apis/policy'
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui'

const PRODUCT_TYPE = 7

export default {
  name: 'PoliciesCbec',
  components: { PolicySearch },
  data() {
    return {
      searchData: {},
      statuses: [
        { value: 0, label: '暂存单' },
        { value: 1, label: '已提交' },
        { value: 2, label: '审核中' },
        { value: 5, label: '已出单' },
        { value: 6, label: '已退保' },
        { value: 7, label: '已作废' },
        { value: 8, label: '批改中' },
        { value: 9, label: '退保申请中' },
        { value: 10, label: '已退回' },
        { value: 11, label: '待确认' },
        { value: 13, label: '保单退回(补充资料)' },
        { value: 14, label: '工单退回(补充资料)' }
      ],
      // 表格数据
      tableData: [],
      meta: {},
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      cols: [
        {
          label: '保单号/流水号',
          width: 180,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label class="text-blue">{scoped.row.policy_no ? scoped.row.policy_no : '尚未出单 ~'}</label>
                  <br />
                  <small>{scoped.row.order_no}</small>
                </div>
              )
            }
          }
        },
        { prop: 'company_branch_name', label: '出单公司', width: '100' },
        {
          prop: 'insured',
          label: '投保人/被保人',
          width: '180',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policyholder}</label>
                  <br />
                  <small>{scoped.row.insured ? scoped.row.insured : ''}</small>
                </div>
              )
            }
          }
        },
        {
          label: '起运地/目的地',
          width: '180',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.departure_port}</label>
                  <br />
                  <small>{scoped.row.destination_port}</small>
                </div>
              )
            }
          }
        },
        {
          label: '保费/保险金额',
          width: '180',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.premium} 人民币</label>
                  <br />
                  <small>
                    {scoped.row?.coverage} {scoped.row?.coverage_currency?.name}
                  </small>
                </div>
              )
            }
          }
        },
        {
          label: '提单号/发票号',
          width: '150',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row?.waybill_no || '无'}</label>
                  <br />
                  <small>{scoped.row?.invoice_no || '无'}</small>
                </div>
              )
            }
          }
        },
        { prop: 'user.name', label: '投保用户', width: '150' },
        { prop: 'submitted_at', width: '180', label: '投保时间' },
        {
          label: '状态',
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.status_text}</label>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          width: '200',
          scopedSlots: {
            default: (scoped) => {
              let copyBtn = ''
              if (scoped.row.user.id === this.user.id) {
                copyBtn = (
                  <el-link onClick={() => this.handleTable('copy', scoped.row)} class="text-blue">
                    复制
                  </el-link>
                )
              }
              return (
                <div>
                  <el-link onClick={() => this.handleTable('view', scoped.row)} class="text-blue">
                    详情
                  </el-link>
                  <el-link
                    onClick={() =>
                      scoped.row.status === 5
                        ? this.handleTable('download', scoped.row)
                        : this.$message.warning('暂未出单')
                    }
                    class="text-blue m-mini-x"
                  >
                    下载
                  </el-link>
                  {copyBtn}
                </div>
              )
            }
          }
        }
      ],
      tableEvents: {
        'selection-change': (data) => {
          this.selectedData = data
        }
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.fetchPolicies()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    selectionCols() {
      return [{ align: 'center', type: 'selection', fixed: 'left', reserveSelection: true }, ...this.cols]
    }
  },
  created() {
    this.fetchPolicies()
  },
  methods: {
    fetchPolicies() {
      this.searchData.type = PRODUCT_TYPE

      getPolicies({
        filter: this.searchData,
        page: this.paging.page
      }).then((r) => {
        this.tableData = r.data
        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    handlePolicySearch(val) {
      this.searchData = Object.assign({}, this.searchData, val)
      if (this.searchData?.status === 0) {
        this.$refs?.tableWithSelection?.$refs?.dataTable?.clearSelection(this.selectedData)
        this.selectedData = []
      }

      this.fetchPolicies()
    },
    handleBatchSubmit() {
      let message = '确认提交全部保单？'
      if (this.selectedData.length > 0) {
        message = `确认提交选中的 ${this.selectedData.length} 条保单？`
      }
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const ids = this.selectedData.map((item) => item.id)
          const loading = Loading.service()
          batchSubmit({
            type: PRODUCT_TYPE,
            ids
          })
            .then(() => {
              this.$message.success('提交成功')
              this.fetchPolicies()
              this.$refs?.tableWithSelection?.$refs?.dataTable?.clearSelection(this.selectedData)
              this.selectedData = []
            })
            .finally(() => {
              loading.close()
            })
        })
        .catch(() => {})
    },
    handleBatchDestroy() {
      let message = '确认作废全部保单？'
      if (this.selectedData.length > 0) {
        message = `确认作废选中的 ${this.selectedData.length} 条保单？`
      }
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const ids = this.selectedData.map((item) => item.id)
          const loading = Loading.service()
          batchDestroy({
            type: PRODUCT_TYPE,
            ids
          })
            .then(() => {
              this.$message.success('提交成功')
              this.fetchPolicies()
              this.$refs?.tableWithSelection?.$refs?.dataTable?.clearSelection(this.selectedData)
              this.selectedData = []
            })
            .finally(() => {
              loading.close()
            })
        })
        .catch(() => {})
    },
    handleExport() {
      this.searchData.type = PRODUCT_TYPE

      window.open(
        buildExportHref({
          filter: this.searchData
        })
      )
    },
    handleDownloadFiles() {
      if (this.paging.total > 100) {
        return this.$message.error('单次最多只能下载 100 条数据')
      }

      this.searchData.type = PRODUCT_TYPE

      window.open(
        buildDownloadFilesHref({
          filter: this.searchData
        })
      )
    },
    handleTable(type, row) {
      switch (type) {
        case 'view':
          this.$router.push({ name: 'PoliciesCbecDetails', params: { id: row.id } })
          break
        case 'download':
          window.open(buildDownloadHref(row.id))
          break
        case 'copy':
          this.$router.push({ name: 'PoliciesInsureCbec', params: { id: row.id }, query: { from: 'copy' } })
          break
        default:
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-list {
  width: 100%;
  overflow: auto;
  /deep/ .table-wrap {
    flex: 1;
    overflow: hidden;
    .el-card__body {
      padding-top: 0;
      padding-bottom: 0;
      overflow: auto;
    }
  }
}
</style>
