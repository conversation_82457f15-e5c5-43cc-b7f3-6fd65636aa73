<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 10:42:14
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-08 14:50:46
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100">
    <el-card class="table-wrap" shadow="never">
      <define-table :data="data" :cols="cols" />
    </el-card>
    <el-dialog title="银行支付信息" :visible.sync="dialogVisible" width="50%">
      <el-row :gutter="48">
        <el-col :span="8">
          <p>银行卡号</p>
          <p>开户行</p>
          <p>账户名称</p>
          <p>银行预留手机号</p>
        </el-col>
        <el-col :span="16" v-if="bankInfo">
          <p>{{ bankInfo.bank_card_no }}</p>
          <p>{{ bankInfo.bank_name }}</p>
          <p>{{ bankInfo.bank_card_holder }}</p>
          <p>{{ bankInfo.phone_number }}</p>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getGroupPayments } from '@/apis/groupProduct'

export default {
  name: 'PoliciesGroupPayments',
  data() {
    return {
      data: [],
      dialogVisible: false,
      bankInfo: null,
      cols: [
        { label: '支付流水号', prop: 'transaction_no' },
        { label: '金额', prop: 'amount' },
        { label: '支付方式', prop: 'remark' },
        { label: '创建时间', prop: 'created_at' },
        {
          label: '支付凭证',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <div>
                    <el-link type="primary" href={scoped.row.proof} download="" target="_blank">
                      点击查看
                    </el-link>
                  </div>
                )
              } else {
                return <span>-</span>
              }
            }
          }
        },
        {
          label: '状态',
          prop: 'status',
          scopedSlots: {
            default: (scoped) => {
              const text = {
                1: '已支付',
                2: '已到账',
                3: '已拒绝',
                4: '已提交'
              }
              return <span>{text[scoped.row.status]}</span>
            }
          }
        }
      ]
    }
  },
  methods: {
    fetchPayments() {
      getGroupPayments(this.$route.params.id).then((r) => {
        this.data = r.data
      })
    },
    showBankInfo(item) {
      this.dialogVisible = true
      this.bankInfo = item.bank_info
    }
  },
  created() {
    this.fetchPayments()
  }
}
</script>

<style lang="scss" scoped>
.policy-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
