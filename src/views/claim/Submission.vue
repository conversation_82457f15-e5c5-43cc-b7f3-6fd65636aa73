<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <search-panel
      size="small"
      :custom="searchFields"
      @change="(data) => (searchData = data)"
      @command="handleSearch"
    ></search-panel>

    <el-card class="m-extra-large-t table-wrap" shadow="never">
      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
    <submission-form :visible.sync="submissionForm.show" :policy="submissionForm.policy" @submit="handleSubmit" />
  </div>
</template>

<script>
import SubmissionForm from '@/components/claim/SubmissionForm'
import * as claimApi from '@/apis/claim'
import * as policyApi from '@/apis/policy'
import { Loading } from 'element-ui'

export default {
  components: {
    SubmissionForm
  },
  data() {
    return {
      submissionForm: {
        show: false,
        policy: {}
      },
      tableData: [],
      cols: [
        {
          label: '险种',
          width: 100,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                7: '跨境电商货运险'
              }

              return <div>{types[scoped.row.type]}</div>
            }
          }
        },
        {
          label: '保单号/流水号',
          width: 180,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label class="text-blue">{scoped.row.policy_no}</label>
                  <br />
                  <small>{scoped.row.order_no}</small>
                </div>
              )
            }
          }
        },
        { prop: 'company_branch_name', label: '出单公司', width: '100' },
        {
          prop: 'insured',
          label: '投保人/被保人',
          width: 300,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policyholder}</label>
                  <br />
                  <small>{scoped.row.insured ? scoped.row.insured : ''}</small>
                </div>
              )
            }
          }
        },
        {
          label: '保费/保险金额',
          width: '100',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.premium} 人民币</label>
                  <br />
                  <small>
                    {scoped.row?.coverage} {scoped.row?.premium_currency}
                  </small>
                </div>
              )
            }
          }
        },
        { prop: 'submitted_at', width: '150', label: '投保时间' },
        { prop: 'claims_count', width: 80, label: '已报案次数' },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          width: '100',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    class="text-blue"
                    onClick={() =>
                      (this.submissionForm = {
                        show: true,
                        policy: scoped.row
                      })
                    }
                  >
                    报案
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchClaimableList()
        }
      },
      searchFields: [
        {
          type: 'select',
          valKey: 'type',
          hintText: '险种',
          options: [
            { value: 1, label: '国内货运险' },
            { value: 2, label: '国际货运险' },
            { value: 3, label: '单车责任险' },
            { value: 4, label: '其他险种' },
            { value: 5, label: '雇主责任险' },
            { value: 7, label: '跨境电商货运险' }
          ]
        },
        {
          type: 'input',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保险人'
        },
        {
          type: 'select',
          valKey: 'submitted_count',
          hintText: '报案次数',
          options: [
            { value: 0, label: '0' },
            { value: 1, label: '>=1' }
          ]
        },
        {
          type: 'daterange',
          valKey: 'issued_at_range',
          hintText: '出单时间'
        }
      ],
      searchData: {}
    }
  },
  created() {
    this.fetchClaimableList()
  },
  methods: {
    async handleSubmit(data, fileList) {
      const loading = Loading.service()
      try {
        data.policy_id = this.submissionForm?.policy?.id
        const caseData = await claimApi.createClaim(data)
        for (const f of fileList) {
          await claimApi.uploadAttachment(caseData.data.id, {
            primary_category: f.category.primary,
            secondary_category: f.category.secondary,
            name: f.name,
            file: f.raw
          })
        }
        this.$message.success('报案成功')
      } catch {
        this.$message.error('报案失败')
      }
      loading.close()

      await this.fetchClaimableList()
    },
    async handleSearch(command, data) {
      this.searchData = data
      this.paging.page = 1

      await this.fetchClaimableList()
    },
    async fetchClaimableList() {
      const data = await policyApi.fetchClaimableList({
        page: this.paging.page,
        filter: this.searchData
      })

      this.tableData = data.data
      this.paging.currentPage = data.meta.current_page
      this.paging.pageSize = data.meta.per_page
      this.paging.total = data.meta.total
    }
  }
}
</script>
