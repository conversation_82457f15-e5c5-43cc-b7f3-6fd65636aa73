<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <search-panel
      size="small"
      :custom="searchFields"
      @change="(data) => (searchData = data)"
      @command="handleSearch"
    ></search-panel>
    <el-card class="m-extra-large-t table-wrap" shadow="never">
      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>

    <case-detail :visible.sync="caseDetailVisible" :data="caseDetailData" @updated="fetchSubmittedClaims" />
  </div>
</template>

<script>
import * as claimApi from '@/apis/claim'
import CaseDetail from '@/components/claim/CaseDetail'
import { Loading } from 'element-ui'

export default {
  components: {
    CaseDetail
  },
  data() {
    return {
      caseDetailVisible: false,
      caseDetailData: {},
      tableData: [],
      cols: [
        { prop: 'case_no', label: '理赔编号', fixed: 'left', width: 150 },
        {
          label: '险种',
          width: 100,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                7: '跨境电商货运险'
              }

              return <div>{types[scoped.row.policy_type]}</div>
            }
          }
        },
        { prop: 'company_branch.name', label: '出单公司', width: '100' },
        {
          label: '保单号/保司报案号',
          width: 180,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label class="text-blue">{scoped.row.policy_no}</label>
                  <br />
                  <small>{scoped.row.external_case_no || '-'}</small>
                </div>
              )
            }
          }
        },
        { prop: 'subject', label: '标的', width: 150 },
        {
          prop: 'insured',
          label: '投保人/被保人',
          width: 300,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policyholder}</label>
                  <br />
                  <small>{scoped.row.insured ? scoped.row.insured : ''}</small>
                </div>
              )
            }
          }
        },
        {
          prop: 'created_at',
          width: 150,
          label: '报案时间/报案时长',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.created_at}</label>
                  <br />
                  <small>{scoped.row.duration} 天</small>
                </div>
              )
            }
          }
        },
        {
          prop: 'status',
          width: 100,
          label: '理赔进度',
          scopedSlots: {
            default: (scoped) => {
              const lastStatus = scoped.row.status_text.slice(scoped.row.status_text.lastIndexOf('>') + 1)
              return (
                <div>
                  <span class="text-danger">{lastStatus}</span>
                  <el-popover placement="bottom" title="当前进度" trigger="hover" content={scoped.row.status_text}>
                    <i slot="reference" class="el-icon-question text-primary"></i>
                  </el-popover>
                </div>
              )
            }
          }
        },
        {
          width: 100,
          label: '获赔金额',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.settlement_payment_amount}</label>
                  <br />
                  <small>{scoped.row.settlement_payment_amount_currency}</small>
                </div>
              )
            }
          }
        },
        {
          prop: 'operator',
          width: 200,
          label: '理赔员',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row?.operator?.name}</label>
                  <br />
                  <small>{scoped.row?.operator?.email}</small>
                  <br />
                  <small>{scoped.row?.operator?.phone_number}</small>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          width: 110,
          scopedSlots: {
            default: (scoped) => {
              let hurryUpButton = (
                <el-link class="text-blue" onClick={() => this.hurryUp(scoped.row)}>
                  催办
                </el-link>
              )

              if ([0, 4, 5, 7].includes(scoped.row.status)) {
                hurryUpButton = ''
              }

              if (scoped.row.hurry_up_at) {
                hurryUpButton = (
                  <el-link disabled class="text-danger">
                    已催办
                  </el-link>
                )
              }

              return (
                <div>
                  {hurryUpButton}
                  <el-link class="text-blue m-mini-x" onClick={() => this.showDetail(scoped.row)}>
                    详情
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchSubmittedClaims()
        }
      },
      searchFields: [
        {
          type: 'input',
          valKey: 'series_no',
          hintText: '保单号/理赔编号/保司报案号'
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保险人'
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '理赔进度',
          options: []
        },
        {
          type: 'daterange',
          valKey: 'created_at_range',
          hintText: '报案'
        }
      ],
      searchData: {}
    }
  },
  created() {
    this.fetchSubmittedClaims()
  },
  methods: {
    async hurryUp(claimCase) {
      const loading = Loading.service()
      try {
        await claimApi.hurryUp(claimCase.id)
        this.$message.success('催办成功')
        this.fetchSubmittedClaims()
      } catch (error) {
        //
      }

      loading.close()
    },
    async showDetail(claimCase) {
      const loading = Loading.service()
      try {
        const data = await claimApi.fetchCaseDetail(claimCase?.id)
        this.caseDetailData = data.data
        this.caseDetailVisible = true
      } catch (error) {
        //
      }

      loading.close()
    },
    async handleSearch(command, data) {
      this.searchData = data
      this.paging.page = 1

      await this.fetchSubmittedClaims()
    },
    async fetchSubmittedClaims() {
      const data = await claimApi.fetchSubmittedClaims({
        page: this.paging.page,
        filter: this.searchData
      })

      this.searchFields[this.searchFields.findIndex((item) => item.valKey === 'status')].options = data.statuses.map(
        (item) => {
          return {
            label: item.label.slice(item.label.lastIndexOf('>') + 1),
            value: item.value
          }
        }
      )

      this.tableData = data.data
      this.paging.currentPage = data.meta.current_page
      this.paging.pageSize = data.meta.per_page
      this.paging.total = data.meta.total
    }
  }
}
</script>
