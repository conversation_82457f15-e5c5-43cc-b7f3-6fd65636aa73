<template>
  <div class="account-setting p-extra-large-t p-extra-large-x p-extra-large-b w-100">
    <header class="header-row">
      <el-row>
        <el-col :span="12">
          <span>账户设置</span>
          <div class="short-line"></div>
        </el-col>
        <el-col :span="12" style="text-align: right">
          <el-button-group>
            <el-button type="primary" icon="fas fa-user" @click="profileFormVisible = true">资料修改</el-button>
            <el-button type="primary" icon="fas fa-lock" @click="passwordFormVisible = true">密码修改</el-button>
          </el-button-group>
        </el-col>
      </el-row>
    </header>
    <el-row :gutter="30">
      <el-col :span="18">
        <el-card shadow="never" class="no-padding">
          <header slot="header" class="clearfix">
            <span>用户资料</span>
          </header>
          <main class="list">
            <ul>
              <li>用户名: {{ user.username }}</li>
              <li>{{ user.type === 1 ? '联系人' : '企业名称' }}: {{ user.name }}</li>
              <li>{{ user.type === 1 ? '身份证号' : '纳税人识别码' }}: {{ user.idcard_no }}</li>
              <li>联系地址: {{ user.address }}</li>
            </ul>
            <ul>
              <li>角色: {{ user.is_agent ? '代理' : '普通用户' }}</li>
              <li>手机: {{ user.phone_number }}</li>
              <li>邮箱: {{ user.email }}</li>
            </ul>
          </main>
        </el-card>

        <el-card shadow="never" class="no-padding m-extra-large-t">
          <header slot="header" class="clearfix">
            <span>银行卡</span>
            <el-button
              type="text"
              style="float: right; padding: 3px 0"
              @click="
                () => {
                  bankcardFormVisible = true
                  bankcardForm = {}
                }
              "
            >
              添加
            </el-button>
          </header>
          <main>
            <define-table :cols="bankcardCols" :data="bankcards" />
          </main>
        </el-card>

        <el-card shadow="never" class="no-padding m-extra-large-t m-extra-large-b">
          <header slot="header" class="clearfix">
            <span>发票信息</span>
            <el-button type="text" style="float: right; padding: 3px 0" @click="invoiceFormVisible = true">
              修改
            </el-button>
          </header>
          <main class="list">
            <ul>
              <li>单位名称: {{ invoiceInformation.company_name }}</li>
              <li>纳税人识别号: {{ invoiceInformation.tax_no }}</li>
              <li>注册地址: {{ invoiceInformation.registered_addr }}</li>
              <li>注册电话: {{ invoiceInformation.registered_phone_number }}</li>
            </ul>
            <ul>
              <li>开户行: {{ invoiceInformation.bank_name }}</li>
              <li>银行账号: {{ invoiceInformation.bankcard_no }}</li>
              <li>收件人: {{ invoiceInformation.recipient }} / {{ invoiceInformation.recipient_phone_number }}</li>
              <li>寄送地址: {{ invoiceInformation.delivery_address }}</li>
            </ul>
          </main>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="never">
          <header slot="header" class="clearfix balance_header">
            <span>账户余额</span>
            <div class="balance_header_buttons">
              <el-button v-if="showOnlineRecharge" type="primary" @click="onlineRecharge"> 充值 </el-button>
              <el-button style="padding: 3px 0" type="text" @click="showPayments"> 充值记录 </el-button>
            </div>
          </header>
          <main>
            <span style="font-size: 24px">¥ {{ user.balance }}</span>
          </main>
        </el-card>
      </el-col>
    </el-row>

    <password-form :visible.sync="passwordFormVisible" @submit="handleUpdatePassword" />
    <profile-form :visible.sync="profileFormVisible" :data="user" @submit="handleupdateUser" />
    <bankcard-form :visible.sync="bankcardFormVisible" :data="bankcardForm" @submit="handleSubmitBankcard" />
    <invoice-form :visible.sync="invoiceFormVisible" :data="invoiceInformation" @submit="handleSubmitInoviceInfo" />
    <el-dialog :visible.sync="paymentsDialog.visible" title="充值记录" destroy-on-close width="1000px">
      <define-table :cols="paymentCols" :data="payments" :paging="paymentPaging" :paging-events="paymentPageEvents" />
    </el-dialog>
  </div>
</template>

<script>
import PasswordForm from '@/components/account/PasswordForm'
import ProfileForm from '@/components/account/ProfileForm'
import BankcardForm from '@/components/account/BankcardForm'
import InvoiceForm from '@/components/account/InvoiceForm'
import { mapGetters } from 'vuex'
import {
  updateUser,
  updatePassword,
  getBankcards,
  createBankcard,
  updateBankcard,
  deleteBankcard,
  getInvoiceInformation,
  updateInvoiceInformation,
  getPayments,
  onlineRecharge,
  cancelRecharge
} from '@/apis/user'

export default {
  name: 'Accounts',
  components: { PasswordForm, ProfileForm, BankcardForm, InvoiceForm },
  data() {
    return {
      passwordFormVisible: false,
      profileFormVisible: false,
      invoiceFormVisible: false,
      bankcardFormVisible: false,
      paymentsDialog: {
        model: {},
        visible: false
      },
      paymentPageEvents: {
        currentChange: (page) => {
          this.paymentPaging.page = page

          this.fetchPayments()
        }
      },
      paymentPaging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      bankcardForm: {},
      profile: {
        title: '用户资料',
        data: []
      },
      bankcardCols: [
        { label: '银行卡号', prop: 'bankcard_no' },
        { label: '开户行', prop: 'bank_name' },
        { label: '姓名', prop: 'cardholder' },
        { label: '电话', prop: 'phone_number' },
        {
          label: '操作',
          prop: 'operation',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    size="small"
                    type="text"
                    onClick={() => {
                      this.$nextTick(() => {
                        this.bankcardFormVisible = true
                        this.bankcardForm = scoped.row
                      })
                    }}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleRemoveBankcard(scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ],
      paymentCols: [
        { label: '交易流水号', prop: 'order_no' },
        { label: '充值金额', prop: 'amount' },
        {
          label: '充值类型',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.charge_type) {
                case 1:
                  return '真实充值'
                case 2:
                  return '虚拟充值'
                case 3:
                  return '补缴欠款'
                case 4:
                  return '支付保险公司'
                case 5:
                  return '系统扣费'
                case 6:
                  return '代理充值'
                case 7:
                  return '代理扣费'
                case 8:
                  return '在线充值'
                default:
                  return '未知'
              }
            }
          }
        },
        {
          label: '状态',
          width: 160,
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.status) {
                case 1:
                  return '已提交'
                case 2:
                  return '已充值'
                case 3:
                  return '已退回'
                case 4:
                  return '待支付'
                default:
                  return '未知'
              }
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          width: '200',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.status === 4 && scoped.row.callback_data?.payment_url) {
                return (
                  <div>
                    <el-link
                      style="margin-right: 10px"
                      type="primary"
                      onClick={() => this.handleContinuePayment(scoped.row)}
                    >
                      继续支付
                    </el-link>
                    <el-link type="primary" onClick={() => this.handleCancelPayment(scoped.row)}>
                      取消支付
                    </el-link>
                  </div>
                )
              } else {
                return '-'
              }
            }
          }
        },
        { label: '退回原因', prop: 'reason', width: '150' },
        { label: '交易时间', prop: 'operated_at', width: '150' }
      ],
      invoiceInformation: {},
      bankcards: [],
      paymentCurrentPage: 1,
      paymentTotal: 0,
      payments: []
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    ...mapGetters('platform', ['online_payment_is_enabled']),
    showOnlineRecharge() {
      return this.online_payment_is_enabled == 1 && this.user.is_online_payment == 1
    }
  },
  created() {
    this.fetchInvoiceInformation()
    this.fetchBankcards()
  },
  methods: {
    fetchPayments() {
      getPayments('me', {
        page: this.paymentPaging.page
      }).then((r) => {
        this.payments = r.data

        this.paymentPaging.currentPage = r.meta.current_page
        this.paymentPaging.total = r.meta.total
        this.paymentPaging.pageSize = r.meta.per_page
      })
    },
    showPayments() {
      this.paymentsDialog.visible = true

      this.fetchPayments()
    },
    fetchInvoiceInformation() {
      getInvoiceInformation().then((r) => (this.invoiceInformation = r.data))
    },
    fetchBankcards() {
      getBankcards().then((r) => (this.bankcards = r.data))
    },
    handleUpdatePassword(data) {
      updatePassword('me', data).then(() => {
        this.$message.success('更新用户密码成功')
      })
    },
    handleupdateUser(data) {
      updateUser('me', data).then(() => {
        this.$message.success('更新用户资料成功')

        this.$store.dispatch('auth/refreshUser')
      })
    },
    handleSubmitBankcard(data) {
      const action = data.id !== undefined ? updateBankcard(data.id, data) : createBankcard(data)

      action.then(() => {
        this.$message.success('添加银行卡成功')

        this.fetchBankcards()
      })
    },
    handleRemoveBankcard(data) {
      deleteBankcard(data.id).then(() => {
        this.$message.success('删除银行卡成功')
        this.fetchBankcards()
      })
    },
    handleSubmitInoviceInfo(data) {
      updateInvoiceInformation(data).then(() => {
        this.$message.success('更新票务信息成功')

        this.fetchInvoiceInformation()
      })
    },
    onlineRecharge() {
      this.$prompt('', '请输入充值金额', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'number'
      })
        .then(({ value }) => {
          onlineRecharge({
            amount: value
          }).then((r) => {
            if (r?.payment_url) {
              window.open(r.payment_url, '_blank')
            }
          })
        })
        .catch(() => {})
    },
    handleContinuePayment(row) {
      if (row.callback_data) {
        window.open(row.callback_data?.payment_url, '_blank')
      } else {
        this.$message.warning('没有找到有效的支付链接')
      }
    },
    handleCancelPayment(row) {
      cancelRecharge(row.id).then(() => {
        this.$message.success('取消支付成功')
        this.fetchPayments()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header-row {
  margin-bottom: 20px;
}

.no-padding {
  /deep/ .el-card__body {
    padding: 0;
  }
}

.balance_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .balance_header_buttons {
    display: flex;
  }
}

.list {
  display: flex;

  ul {
    flex: 1;
    padding: 0;

    li {
      list-style: none;
      height: 45px;
      line-height: 45px;
      border-bottom: 1px solid #eee;
      box-sizing: border-box;
      padding-left: 20px;
    }
  }
}
</style>
