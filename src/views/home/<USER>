<template>
  <div class="home p-extra-large-x p-extra-large-b w-100">
    <el-radio-group v-model="activeKind">
      <el-radio-button label="domestic">国内货运险</el-radio-button>
      <el-radio-button label="international">国际货运险</el-radio-button>
    </el-radio-group>
    <div class="m-extra-large-t">
      <domestic-product
        :subjects="subjects"
        :subject.sync="subjectId"
        @subject-change="handleSubjectChange"
        :products="products || {}"
        @product-change="(p) => (product = p)"
        v-if="activeKind === 'domestic'"
      />
      <intl-product
        :subjects="subjects"
        :subject.sync="subjectId"
        @subject-change="handleSubjectChange"
        :products="products || []"
        @product-change="(p) => (product = p)"
        v-else
      />
    </div>

    <el-card class="m-extra-large-t table-wrap" shadow="never" v-if="product?.additional?.notice">
      <el-collapse accordion>
        <el-collapse-item title="投保须知" name="notice">
          <div style="text-align: left" v-html="product.additional.notice"></div>
        </el-collapse-item>
      </el-collapse>
    </el-card>

    <el-card class="m-extra-large-t table-wrap" shadow="never">
      <el-form ref="form" :model="form" :rules="rules" :disabled="product?.id === undefined">
        <el-row :gutter="24">
          <template v-if="activeKind === 'international'">
            <el-col :span="6">
              <el-form-item prop="invoice_amount" label="发票金额">
                <el-input clearable v-model="form.invoice_amount" placeholder="请输入发票金额"> </el-input>
                <span>{{ form.invoice_amount | toChineseNumber }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="currency_id" label="发票币种">
                <currencies v-model="form.currency_id" :currency.sync="currency" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="bonus" label="加成比例">
                <el-input clearable v-model="form.bonus" placeholder="请输入加成比例"> </el-input>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="6">
            <el-form-item prop="insured_amount" label="保险金额">
              <el-input clearable v-model="form.insured_amount" placeholder="请输入保险金额"> </el-input>
              <span>{{ form.insured_amount | toChineseNumber }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item prop="rate" label="费率(‱)">
              <el-input clearable v-model="form.rate" placeholder="请输入费率"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="18" v-if="premium > 0">
            <p>当前费率下保费为(<span class="text-danger">计算结果仅供参考，实际保费请按投保时为准。</span>)：</p>
            <p>{{ premium | toLocaleCurrency }} {{ premium | toChineseNumber }}</p>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import DomesticProduct from '@/components/policy/PolicyDomesticProduct'
import IntlProduct from '@/components/policy/PolicyIntlProduct'
import { getSubjects } from '@/apis/subject'
import { getProducts } from '@/apis/product'
import { digitUppercase } from '@/utils'
import Currencies from '@/components/policy/Currencies'

export default {
  name: 'PremiumCalculator',
  components: {
    DomesticProduct,
    IntlProduct,
    Currencies
  },
  data() {
    return {
      activeKind: 'domestic',
      subjectId: 2,
      product: {},
      products: null,
      subjects: [],
      currency: {},
      form: {
        insured_amount: '',
        invoice_amount: '',
        currency_id: '',
        bonus: 10,
        rate: 0
      }
    }
  },
  filters: {
    toLocaleCurrency(currency) {
      return parseFloat(currency).toLocaleString('zh-CN', {
        style: 'currency',
        currency: 'CNY'
      })
    },
    toChineseNumber(value) {
      if (!value) return ''
      return digitUppercase(value)
    }
  },
  computed: {
    rules() {
      let validationRules = {
        insured_amount: [{ required: true, message: '请输入保险金额', trigger: 'blur' }],
        rate: [{ required: true, message: '请输入费率', trigger: 'blur' }]
      }

      if (this.activeKind === 'international') {
        validationRules = {
          ...validationRules,
          invoice_amount: [{ required: true, message: '请输入发票金额', trigger: 'blur' }],
          currency_id: [{ required: true, message: '请选择发票币种', trigger: 'change' }],
          bonus: [{ required: true, message: '请输入加成比例', trigger: 'blur' }]
        }
      }

      return validationRules
    },
    premium() {
      let premium = (this.form.insured_amount * this.form.rate) / 10000
      if (this.activeKind === 'international') {
        premium = parseFloat(this.currency?.rate) * premium
      }
      if (premium < parseFloat(this.product?.minimum_premium)) {
        return parseFloat(this.product?.minimum_premium)?.toFixed(2)
      }
      return premium.toFixed(2)
    }
  },
  watch: {
    activeKind() {
      this.resetProduct()
      this.handleSubjectChange(2)
    },
    'form.invoice_amount'() {
      this.form.insured_amount = (this.form.invoice_amount * (1 + this.form.bonus / 100)).toFixed(2)
      this.$refs?.form?.clearValidate('insured_amount')
    },
    // hack: 不知道为啥切回国内会把 subjectId 改为 -1，先临时处理
    subjectId(value) {
      if (value === -1) {
        this.subjectId = 2
      }
    },
    product(value) {
      this.form.rate = value?.user_rate
      this.$refs?.form?.clearValidate('rate')
    }
  },
  async created() {
    await this.getSubjects()
    await this.getProducts()
  },
  methods: {
    handleSubjectChange(subjectId) {
      this.resetProduct()

      this.subjectId = subjectId
      this.getProducts()
    },
    resetProduct() {
      this.products = null
      this.product = {}

      this.$refs?.form?.resetFields()
      this.$refs?.form?.clearValidate()
    },
    async getSubjects() {
      const { data } = await getSubjects()
      this.subjects = data
    },
    async getProducts() {
      if (this.subjectId == -1) {
        return
      }

      const { data } = await getProducts({
        filter: {
          type: this.activeKind === 'domestic' ? 1 : 2,
          subject_id: this.subjectId
        }
      })
      this.products = data
    }
  }
}
</script>
