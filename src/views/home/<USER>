<template>
  <div class="home p-extra-large-t p-extra-large-x p-extra-large-b o-hidden-x w-100">
    <section id="recommendations">
      <el-row>
        <el-col :span="24">
          <div class="header-row">
            <h3>产品推荐</h3>
            <div class="short-line"></div>
          </div>
          <el-card shadow="never" class="m-extra-large-t w-100 no-padding h-260">
            <el-carousel :interval="5000">
              <el-carousel-item v-for="(recommendation, idx) in data.recommendations || []" :key="idx">
                <el-row>
                  <el-col :span="12">
                    <el-image :src="recommendation.image">
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </el-col>
                  <el-col :span="12" class="p-mini-x">
                    <div v-html="recommendation.content"></div>
                  </el-col>
                </el-row>
              </el-carousel-item>
            </el-carousel>
          </el-card>
        </el-col>
      </el-row>
    </section>
    <section id="messages">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="header-row">
            <h3>会员面板</h3>
            <div class="short-line"></div>
          </div>
          <el-card shadow="never" class="m-extra-large-t w-100" style="height: 235px">
            <el-row>
              <el-col :span="24">
                <h3>欢迎使用: {{ user.name }}</h3>
              </el-col>
            </el-row>
            <el-row class="user-card text-center hp-stats-text with-top-bottom-border">
              <div>
                <p>累计保费(元)</p>
                <p>{{ data.policy?.user_premium }}</p>
              </div>
              <div>
                <p>累计投保(单)</p>
                <p>{{ data.policy?.count }}</p>
              </div>
              <div>
                <p>出险案件(件)</p>
                <p>{{ data.claim?.count }}</p>
              </div>
              <div>
                <p>结案(件)</p>
                <p>{{ data.claim?.finished_count }}</p>
              </div>
              <div>
                <p>获赔金额(元)</p>
                <p>{{ data.claim?.amount }}</p>
              </div>
            </el-row>
            <el-row class="m-extra-large-t">
              <el-col :span="16" class="hp-balance">
                <span class="text-gray">账户余额:&nbsp;</span>
                <span>
                  ¥ <b>{{ user.balance }}</b> 元
                </span>
              </el-col>
              <el-col :span="8" v-if="showBankAccount">
                <el-button v-if="showOnlineRecharge" @click="recharge" type="primary" size="mini" icon="fas fa-yen-sign"
                  >充值</el-button
                >
                <el-popover v-else placement="bottom" title="充值信息 - 保费专收账户" width="400" trigger="click">
                  <el-button type="primary" size="mini" icon="fas fa-yen-sign" slot="reference">充值</el-button>
                  <el-row :gutter="48">
                    <el-col :span="6">
                      <p>账户名称</p>
                      <p>开户行</p>
                      <p>账号</p>
                    </el-col>
                    <el-col :span="18">
                      <p>全联保险经纪有限公司</p>
                      <p>招商银行五角场支行</p>
                      <p>*****************</p>
                    </el-col>
                  </el-row>
                </el-popover>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="header-row">
            <h3>通知面板</h3>
            <div class="short-line"></div>
          </div>
          <el-card shadow="never" class="m-extra-large-t" style="height: 235px">
            <div class="notifications">
              <ul>
                <template v-if="data.messages.length > 0">
                  <li v-for="(message, idx) in data.messages.slice(0, 5)" :key="idx">
                    <el-tooltip class="item" effect="dark" :content="message.message" placement="top-start">
                      <div class="message-content">
                        <span class="text-ellipsis" :class="`text-${message.level}`">{{ message.message }}</span>
                        <el-button
                          class="read-btn"
                          type="text"
                          size="mini"
                          circle
                          icon="fas fa-check-circle"
                          @click="handleReadMessage(message)"
                        >
                          已读
                        </el-button>
                      </div>
                    </el-tooltip>
                  </li>
                </template>
                <template v-else>
                  <li class="message-content">暂无消息</li>
                </template>
              </ul>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </section>
    <section id="shortcut">
      <el-row>
        <el-col>
          <div class="header-row">
            <h3>快速投保</h3>
            <div class="short-line"></div>
          </div>
          <el-card shadow="never" class="m-extra-large-t" style="height: 235px">
            <div class="shortcuts">
              <div class="shortcut">
                <p class="icon">
                  <i class="fas fa-truck"></i>
                </p>
                <p class="desc">国内货运险</p>
                <h3 class="count">{{ data.policy.count_type | filterProduct(1) }} 单</h3>
                <h3 class="amount">{{ data.policy.count_type | filterProduct(1, 'premium') }}</h3>
                <el-button type="primary" class="shadow" @click="$router.push({ name: 'PoliciesInsureDomestic' })">
                  立即投保
                </el-button>
              </div>
              <div class="shortcut">
                <p class="icon">
                  <i class="fas fa-ship"></i>
                </p>
                <p class="desc">国际货运险</p>
                <h3 class="count">{{ data.policy.count_type | filterProduct(2) }} 单</h3>
                <h3 class="amount">{{ data.policy.count_type | filterProduct(2, 'premium') }}</h3>
                <el-button type="primary" class="shadow" @click="$router.push({ name: 'PoliciesInsureIntl' })">
                  立即投保
                </el-button>
              </div>
              <div class="shortcut">
                <p class="icon">
                  <i class="fas fa-truck-pickup"></i>
                </p>
                <p class="desc">单车责任险</p>
                <h3 class="count">{{ data.policy.count_type | filterProduct(3) }} 单</h3>
                <h3 class="amount">{{ data.policy.count_type | filterProduct(3, 'premium') }}</h3>
                <el-button type="primary" class="shadow" @click="$router.push({ name: 'PoliciesInsureLbt' })">
                  立即投保
                </el-button>
              </div>
              <div class="shortcut">
                <p class="icon">
                  <i class="fas fa-people-carry"></i>
                </p>
                <p class="desc">雇主责任险</p>
                <h3 class="count">{{ data.policy.count_type | filterProduct(5) }} 单</h3>
                <h3 class="amount">{{ data.policy.count_type | filterProduct(5, 'premium') }}</h3>
                <el-button type="primary" class="shadow" @click="$router.push({ name: 'PoliciesInsureGroup' })">
                  立即投保
                </el-button>
              </div>
              <div class="shortcut">
                <p class="icon">
                  <i class="fas fa-car-crash"></i>
                </p>
                <p class="desc">其他险</p>
                <h3 class="count">{{ data.policy.count_type | filterProduct(4) }} 单</h3>
                <h3 class="amount">{{ data.policy.count_type | filterProduct(4, 'premium') }}</h3>
                <el-button type="primary" class="shadow" @click="$router.push({ name: 'PoliciesInsureOther' })">
                  立即投保
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </section>
    <section id="profile-and-recommend">
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="header-row">
            <h3>待处理</h3>
            <div class="short-line"></div>
          </div>
          <el-card shadow="never" class="m-extra-large-t">
            <define-table :cols="cols" :data="data.policy.pending" />
          </el-card>
        </el-col>
      </el-row>
    </section>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { getHomeData } from '@/apis/home'
import { getUnreadMessages, markMessageAsRead, onlineRecharge } from '@/apis/user'

export default {
  name: 'HomeIndex',
  computed: {
    ...mapGetters('auth', ['user']),
    ...mapGetters('platform', ['online_payment_is_enabled']),
    showBankAccount() {
      // 只有保呀和运吉宝显示
      return [1, 2].includes(this.user.platform_id)
    },
    showOnlineRecharge() {
      return this.online_payment_is_enabled == 1 && this.user.is_online_payment == 1
    }
  },
  data() {
    return {
      cols: [
        { label: '保险产品', prop: 'product', width: 250 },
        { label: '流水号', prop: 'order_no', width: 200 },
        { label: '投保人', prop: 'policyholder' },
        { label: '被保险人', prop: 'insured' },
        { label: '更新时间', prop: 'updated_at', width: 150 },
        { label: '状态', prop: 'status_text', width: 80 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link onClick={() => this.showDetail(scoped.row)} class="text-primary">
                    详情
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      data: {
        messages: [],
        policy: {
          count: 0,
          count_type: {},
          coverage: 0,
          pending: [],
          user_premium: 0
        },
        recommandations: [],
        user: {}
      }
    }
  },
  filters: {
    filterProduct(data, type, column = 'total') {
      return data.length > 0 ? data.find((e) => e.type == type)?.[column] || 0 : 0
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      getHomeData().then((r) => {
        this.data = r.data
      })
    },
    handleReadMessage(message) {
      markMessageAsRead(message.id).then(() => {
        getUnreadMessages().then((r) => (this.data.messages = r.data))
      })
    },
    showDetail(row) {
      const to = {
        1: 'PoliciesDomesticDetails',
        2: 'PoliciesIntlDetails',
        3: 'PoliciesLbtDetails',
        4: 'PoliciesGroupDetails',
        5: 'ManageOtherDetail'
      }

      this.$router.push({ name: to[row.type], params: { id: row.id } })
    },
    recharge() {
      this.$confirm('', '请选择充值类型', {
        distinguishCancelAndClose: true,
        confirmButtonText: '余额充值',
        cancelButtonText: '在线充值'
      })
        .then(() => {
          // 余额充值 展示全联账户信息
          this.$alert(
            `<el-row :gutter="48">
                    <el-col :span="6">
                      <p>账户名称: 全联保险经纪有限公司</p>
                      <p>开户行: 招商银行五角场支行</p>
                      <p>账号: *****************</p>
                    </el-col>
                    </el-col>
                  </el-row>`,
            '充值信息 - 保费专收账户',
            {
              dangerouslyUseHTMLString: true
            }
          )
        })
        .catch((action) => {
          // 在线充值 弹出充值金额填写框
          if (action === 'cancel') {
            this.$prompt('', '请输入充值金额', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              inputType: 'number'
            })
              .then(({ value }) => {
                onlineRecharge({
                  amount: value
                }).then((r) => {
                  if (r?.payment_url) {
                    window.open(r.payment_url, '_blank')
                  }
                })
              })
              .catch(() => {})
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.h-260 {
  height: 260px;
}

.user-card {
  display: flex;
  justify-content: space-between;
}

.with-top-bottom-border {
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}

.hp-stats-text {
  font-size: 14px;
  font-weight: 800;

  p {
    margin: 10px 0px 10px 0px;
  }
}

.hp-balance {
  font-size: 16px;
  line-height: 28px;
  .text-gray {
    color: #888;
  }
}

/deep/ .no-padding .el-card__body {
  padding: 0;
}

/deep/ .el-carousel__container {
  height: 260px !important;
  .el-image img {
    height: 260px !important;
  }
}

.notifications {
  ul {
    padding-left: 0;
    margin: 0;
  }
  li {
    list-style: none;
    height: 39px;
    line-height: 39px;
    box-sizing: border-box;
    font-size: 12px;
    border-bottom: 1px solid #ebeef5;

    &:last-child {
      border-bottom: none;
    }

    .message-content {
      display: flex;
      align-items: center;
      padding-left: 10px;

      .text-ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 90%;
        max-width: 90%;
      }
    }

    .read-btn {
      float: right;
      height: 39px;
      line-height: 20px;
    }
  }
}
.shortcuts {
  display: flex;
  flex-direction: row;
  align-content: center;
  align-items: center;

  .shortcut {
    flex: 1;
    align-content: center;
    text-align: center;

    .icon {
      margin: 0;
      font-size: 32px;
    }
    .desc {
      font-size: 14px;
      padding: 0;
      margin: 10px;
      color: #333;
    }

    .count {
      font-size: 20px;
      margin: 0;
    }

    .amount {
      font-size: 14px;
      color: #333;
    }
  }
}
</style>
