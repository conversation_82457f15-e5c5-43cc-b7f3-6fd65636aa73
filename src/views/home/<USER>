<template>
  <div class="home p-extra-large-x p-extra-large-b w-100">
    <el-card class="table-wrap" shadow="never">
      <define-table :data="data" :cols="cols"></define-table>
    </el-card>
  </div>
</template>

<script>
import { announcements } from '@/apis/announcement'

export default {
  name: 'Announcements',
  data() {
    return {
      data: [],
      cols: [
        {
          label: '展示区域',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              const displayAreas = {
                1: '全站',
                2: '保单打印'
              }
              return <span>{displayAreas[scoped.row.display_area]}</span>
            }
          }
        },
        {
          label: '通知内容',
          prop: 'content'
        },
        {
          label: '时间',
          width: 150,
          prop: 'created_at'
        }
      ]
    }
  },
  created() {
    announcements().then((r) => (this.data = r.data))
  }
}
</script>
