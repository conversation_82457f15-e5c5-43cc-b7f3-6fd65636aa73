<template>
  <div class="inner-box p-extra-large-x m-extra-large-b w-100">
    <el-tabs v-model="currentType">
      <search-panel
        size="small"
        :custom="searchFields"
        @change="(data) => (searchData = data)"
        @command="handleSearch"
      />
      <el-tab-pane v-for="(name, type) in tabs" :key="type" :name="type" :label="name">
        <el-card shadow="never" class="m-extra-large-t">
          <define-table :attrs="{ border: false }" :cols="tableCols" :data="products"></define-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>
    <configure-product :visible.sync="configure.visible" :data="configure.model" @submit="handleSubmit" />
  </div>
</template>
<script>
import { getProductsCompanies } from '@/apis/product'
import { getSubjects } from '@/apis/subject'
import { getUserProducts, assignProduct, updateProductStatus } from '@/apis/user'
import ConfigureProduct from '@/components/user/ConfigureProduct'
import { Loading } from 'element-ui'

export default {
  name: 'UsersProducts',
  components: {
    ConfigureProduct
  },
  data() {
    return {
      baseSearchFields: [
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'select',
          valKey: 'subject_id',
          hintText: '标的',
          options: []
        },
        {
          type: 'select',
          valKey: 'is_enabled',
          hintText: '是否配置',
          options: [
            { value: 1, label: '是' },
            { value: 0, label: '否' }
          ]
        }
      ],
      rawCompanies: [],
      searchData: {},
      configure: {
        visible: false,
        model: {}
      },
      currentType: '1',
      tabs: {
        1: '国内货运险',
        2: '国际货运险',
        3: '单车责任险',
        4: '其他险种',
        5: '雇主责任险',
        7: '跨境电商货运险'
      },
      types: {
        1: '国内货运险',
        2: '国际货运险',
        3: '单车责任险',
        4: '其他险种',
        5: '雇主责任险',
        7: '跨境电商货运险'
      },
      rawProducts: [],
      cols: [
        { label: '产品名称', prop: 'product.name', width: 200 },
        { label: '产品代码', prop: 'product.code', width: 100 },
        { label: '主险', prop: 'product.main_clause' },
        { label: '附加险', prop: 'product.additional_clauses' },
        { label: '标的', prop: 'product.subject' },
        { label: '出单公司', prop: 'product.company_branch' },
        { label: '成本费率(‱)', prop: 'agent_user_rate' },
        { label: '最低保费', prop: 'agent_minimum_premium' },
        { label: '用户费率(‱)', prop: 'user_rate' },
        { label: '用户最低保费', prop: 'minimum_premium' },
        {
          label: '是否启用',
          prop: 'is_enabled',
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-switch
                  value={scoped?.row?.is_enabled}
                  active-value={1}
                  inactive-value={0}
                  disabled={scoped.row.user_rate <= 0 || [3, 4, 5].includes(scoped.row.product.type)}
                  onChange={() => {
                    if (scoped?.row?.is_enabled === 1) {
                      scoped.row.is_enabled = 0
                    } else {
                      scoped.row.is_enabled = 1
                    }

                    this.updateStatus(scoped.row.id, scoped.row.is_enabled)
                  }}
                />
              )
            }
          }
        },
        {
          label: '操作',
          prop: 'op',
          fixed: 'right',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    type="text"
                    disabled={[3, 4, 5].includes(scoped.row.product.type)}
                    onClick={() => {
                      this.configure.visible = true
                      this.configure.model = {
                        id: scoped.row.id,
                        name: scoped.row.product.name,
                        user_rate: scoped.row.user_rate,
                        minimum_premium: scoped.row.minimum_premium,
                        is_enabled: scoped.row.is_enabled,
                        is_allowed_invoice: scoped.row.is_allowed_invoice,
                        disabled_invoice_checkbox: scoped.row.disabled_invoice_checkbox
                      }
                    }}
                  >
                    配置
                  </el-button>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  computed: {
    products() {
      return this.rawProducts.filter((item) => item.product.type === parseInt(this.currentType))
    },
    searchFields() {
      let fields = JSON.parse(JSON.stringify(this.baseSearchFields))
      if ([3, 4, 5, 7].includes(parseInt(this.currentType, 10))) {
        delete fields[fields.findIndex((item) => item.valKey === 'subject_id')]
      }

      return fields
    },
    tableCols() {
      let fields = this.cols
      if ([3, 4, 5].includes(parseInt(this.currentType, 10))) {
        fields = fields.filter(
          (item) => !['product.subject', 'product.main_clause', 'product.additional_clauses'].includes(item.prop)
        )
      }

      if ([4, 5].includes(parseInt(this.currentType, 10))) {
        fields = fields.filter(
          (item) =>
            !['agent_minimum_premium', 'agent_user_rate', 'minimum_premium', 'user_rate', 'op'].includes(item.prop)
        )

        fields = fields.map((f) => {
          // if (f.prop === 'product.name') f.width = 400
          if (f.prop === 'product.code') f.width = 150
          if (f.prop === 'is_enabled') f.width = 100

          return f
        })
      }

      return fields
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  created() {
    getProductsCompanies().then((r) => {
      this.rawCompanies = r.data
      this.loadCompanies()
      this.loadCompanyBranches()
    })

    getSubjects().then((r) => {
      this.assignSelectOptions(
        'subject_id',
        r.data.map((item) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      )
    })

    this.fetchProducts()
  },
  methods: {
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '') {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.baseSearchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.baseSearchFields[targetIdx].options = options
      }
    },
    handleSearch(command, data) {
      this.searchData = data

      this.fetchProducts()
    },
    updateStatus(id, status) {
      updateProductStatus(this.$route.params.id, id, status).then(() => {
        this.$message.success('更新状态成功')
      })
    },
    fetchProducts() {
      const loading = Loading.service()
      getUserProducts(this.$route.params.id, this.searchData)
        .then((r) => {
          this.rawProducts = r.data
          getUserProducts('me').then((r) => {
            this.rawProducts = this.rawProducts.map((p) => {
              const agentProduct = r.data.find((m) => m.product.id === p.product.id)
              p.agent_user_rate = agentProduct.user_rate
              p.agent_minimum_premium = agentProduct.minimum_premium

              return p
            })
          })
        })
        .finally(() => {
          loading.close()
        })
    },
    handleSubmit(data) {
      assignProduct(this.$route.params.id, this.configure.model.id, data).then(() => {
        this.$message.success('配置成功')

        this.fetchProducts()
      })
    }
  }
}
</script>
