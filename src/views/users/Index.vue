<template>
  <div class="users inner-box p-extra-large-t p-extra-large-x p-extra-large-b w-100">
    <header class="header-row">
      <el-row>
        <el-col :span="12">
          <span>账户设置</span>
          <div class="short-line"></div>
        </el-col>
        <el-col :span="12" style="text-align: right">
          <div class="flex inline-btn-group">
            <el-button type="primary" icon="fas fa-user" @click="() => (userForm = { visible: true, model: {} })">
              添加客户
            </el-button>
            <el-input placeholder="请输入用户名/联系人搜索" class="inline-input" v-model="filter.keywords"> </el-input>
            <el-button type="primary" slot="append" icon="el-icon-search" class="m-mini-l" @click="fetchUsers">
              查询
            </el-button>
          </div>
        </el-col>
      </el-row>
    </header>
    <el-card shadow="never" class="m-extra-large-t">
      <define-table :cols="cols" :data="users" :paging-events="pageEvents" :paging="paging" />
    </el-card>

    <user-form :visible.sync="userForm.visible" :data.sync="userForm.model" @submit="handleSubmit" />

    <el-dialog
      :visible.sync="chargeDialog.visible"
      title="充值"
      width="520px"
      destroy-on-close
      :before-close="handleCloseChargeDialog"
      class="no-padding"
    >
      <el-form ref="chargeForm" :model="chargeDialog.form" :rules="chargeDialog.rules">
        <el-form-item prop="amount" label="充值金额">
          <el-input v-model="chargeDialog.form.amount" placeholder="请输入充值金额">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <el-button @click="handleCloseChargeDialog" icon="fas fa-times">取 消</el-button>
        <el-button @click="handleCharge" icon="fas fa-check" type="primary">充值</el-button>
      </template>
    </el-dialog>

    <el-dialog
      :visible.sync="payDialog.visible"
      title="扣费"
      width="520px"
      destroy-on-close
      :before-close="handleClosePayDialog"
      class="no-padding"
    >
      <el-form ref="payForm" :model="payDialog.form" :rules="payDialog.rules">
        <el-form-item prop="amount" label="扣费金额">
          <el-input v-model="payDialog.form.amount" placeholder="请输入扣费金额">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <el-button @click="handleClosePayDialog" icon="fas fa-times">取 消</el-button>
        <el-button @click="handlePay" icon="fas fa-check" type="primary">扣费</el-button>
      </template>
    </el-dialog>

    <el-dialog :visible.sync="paymentsDialog.visible" title="充值记录" destroy-on-close width="1000px">
      <define-table :cols="paymentCols" :data="payments" :paging="paymentPaging" :paging-events="paymentPageEvents" />
    </el-dialog>
  </div>
</template>

<script>
import UserForm from '@/components/user/UserForm'
import { getUsers, createUser, updateUser, charge, pay, getUserPayments } from '@/apis/user'
import { Loading } from 'element-ui'

export default {
  name: 'Users',
  components: {
    UserForm
  },
  data() {
    return {
      userForm: {
        visible: false,
        model: {}
      },
      chargeDialog: {
        visible: false,
        model: {},
        form: {
          amount: ''
        },
        rules: {
          amount: [
            { required: true, message: '请输入充值金额', trigger: ['blur', 'change'] },
            { min: 1, message: '最小金额不低于 1 元', trigger: ['blur', 'change'] }
          ]
        }
      },
      payDialog: {
        visible: false,
        model: {},
        form: {
          amount: ''
        },
        rules: {
          amount: [
            { required: true, message: '请输入扣费金额', trigger: ['blur', 'change'] },
            { min: 1, message: '最小金额不低于 1 元', trigger: ['blur', 'change'] }
          ]
        }
      },
      paymentsDialog: {
        model: {},
        visible: false
      },
      pageEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchUsers()
        }
      },
      paymentPageEvents: {
        currentChange: (page) => {
          this.paymentPaging.page = page

          this.getPayments()
        }
      },
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      paymentPaging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      users: [],
      cols: [
        {
          label: '用户类型',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.type === 1 ? '个人用户' : '企业用户'
            }
          }
        },
        {
          label: '用户名/联系人',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.username}</label>
                  <br />
                  <small>{scoped.row.name}</small>
                </div>
              )
            }
          }
        },
        {
          label: '联系方式',
          prop: 'phone_number'
        },
        {
          label: '联系地址',
          prop: 'address'
        },
        {
          label: '账户余额',
          prop: 'balance'
        },
        {
          label: '注册时间',
          prop: 'created_at'
        },
        {
          label: '操作',
          width: 120,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    size="small"
                    type="text"
                    onClick={() => {
                      this.$nextTick(() => {
                        this.userForm.visible = true
                        this.userForm.model = scoped.row
                      })
                    }}
                  >
                    编辑
                  </el-button>
                  <el-dropdown class="m-mini-l">
                    <el-button size="small" type="text">
                      更多操作
                      <i class="el-icon-arrow-down el-icon--down"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item>
                        <el-button
                          size="small"
                          type="text"
                          class="w-100"
                          onClick={() => {
                            this.chargeDialog.visible = true
                            this.chargeDialog.model = scoped.row
                          }}
                        >
                          充值
                        </el-button>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button
                          size="small"
                          type="text"
                          class="w-100"
                          onClick={() => {
                            this.payDialog.visible = true
                            this.payDialog.model = scoped.row
                          }}
                        >
                          扣费
                        </el-button>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button
                          size="small"
                          type="text"
                          class="w-100"
                          onClick={() => {
                            this.paymentsDialog = {
                              visible: true,
                              model: scoped.row
                            }
                            this.paymentPaging.page = 1

                            this.getPayments()
                          }}
                        >
                          充值记录
                        </el-button>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button
                          size="small"
                          type="text"
                          class="w-100"
                          onClick={() => this.$router.push({ name: 'UsersProducts', params: { id: scoped.row.id } })}
                        >
                          配置产品
                        </el-button>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              )
            }
          }
        }
      ],
      paymentCols: [
        { label: '交易流水号', prop: 'order_no' },
        { label: '充值金额', prop: 'amount' },
        {
          label: '充值类型',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.charge_type) {
                case 1:
                  return '真实充值'
                case 2:
                  return '虚拟充值'
                case 3:
                  return '补缴欠款'
                case 4:
                  return '支付保险公司'
                case 5:
                  return '系统扣费'
                case 6:
                  return '代理充值'
                case 7:
                  return '代理扣费'
                default:
                  return '未知'
              }
            }
          }
        },
        {
          label: '状态',
          width: 160,
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.status) {
                case 1:
                  return '已提交'
                case 2:
                  return '已充值'
                case 3:
                  return '已退回'
                default:
                  return '未知'
              }
            }
          }
        },
        { label: '交易时间', prop: 'operated_at', width: 150 }
      ],
      filter: {
        keywords: ''
      },
      payments: []
    }
  },
  created() {
    this.fetchUsers()
  },
  methods: {
    handleCharge() {
      this.$refs.chargeForm.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          charge(this.chargeDialog.model.id, this.chargeDialog.form)
            .then(() => {
              this.handleCloseChargeDialog()

              this.$message.success('充值成功')

              this.fetchUsers()
            })
            .finally(() => loading.close())
        }
      })
    },
    handlePay() {
      this.$refs.payForm.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          pay(this.payDialog.model.id, this.payDialog.form)
            .then(() => {
              this.handleClosePayDialog()

              this.$message.success('扣款成功')

              this.fetchUsers()
            })
            .finally(() => loading.close())
        }
      })
    },
    handleCloseChargeDialog() {
      this.chargeDialog.visible = false
      this.chargeDialog.form = {
        amount: ''
      }
      this.chargeDialog.model = {}
    },
    handleClosePayDialog() {
      this.payDialog.visible = false
      this.payDialog.form = {
        amount: ''
      }
      this.payDialog.model = {}
    },
    fetchUsers() {
      getUsers({
        page: this.paging.page,
        filter: this.filter
      }).then((r) => {
        this.users = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.total = r.meta.total
        this.paging.pageSize = r.meta.per_page
      })
    },
    handleSubmit(data) {
      const action = data.id === undefined ? createUser(data) : updateUser(data.id, data)

      action.then(() => {
        this.$message.success('保存成功')

        this.fetchUsers()
      })
    },
    getPayments() {
      getUserPayments(this.paymentsDialog.model.id, {
        page: this.paymentPaging.page
      }).then((r) => {
        this.payments = r.data

        this.paymentPaging.currentPage = r.meta.current_page
        this.paymentPaging.total = r.meta.total
        this.paymentPaging.pageSize = r.meta.per_page
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.no-padding {
  /deep/ .el-dialog__body {
    padding: 0 20px;
  }
}

.inline-btn-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;

  .inline-input {
    margin-left: 20px;
    width: 250px;
  }
}
</style>
