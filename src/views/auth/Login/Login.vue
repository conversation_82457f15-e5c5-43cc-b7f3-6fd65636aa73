<template>
  <el-container class="login" :style="'background-image: url(' + bgImage + ')'">
    <el-row class="main">
      <el-col :span="14" class="left">
        <div>
          <el-col>
            <img :src="logoWhite" class="logo" alt="LOGO" />
          </el-col>
          <el-col class="hero">
            <h3>{{ slogan }}</h3>
            <p>{{ description }}</p>
          </el-col>
        </div>
        <el-col class="copyright">
          <p>© 2019 - {{ new Date().getFullYear() }} Copyright {{ title }}.</p>
          <p class="custom-footer">
            <span v-for="(f, idx) in customFooter" :key="idx">
              <a v-if="f.type === 'link'" :href="f.href" target="_blank">{{ f.text }}</a>
              <template v-else>{{ f.text }}</template>
            </span>
          </p>
        </el-col>
      </el-col>
      <el-col :span="10" class="right">
        <div class="form">
          <div class="form-main">
            <p class="form-title">
              登录
              <span class="short-line"></span>
            </p>
            <el-form ref="form" :model="form" :rules="rules">
              <el-form-item prop="username">
                <el-input v-model="form.username" placeholder="请输入用户名" prefix-icon="el-icon-user"></el-input>
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  type="password"
                  v-model="form.password"
                  placeholder="请输入密码"
                  prefix-icon="el-icon-lock"
                  show-password
                ></el-input>
              </el-form-item>
              <el-form-item prop="captcha">
                <el-row :gutter="10">
                  <el-col :span="16">
                    <el-input
                      type="captcha"
                      v-model="form.captcha"
                      placeholder="请输入验证码"
                      prefix-icon="el-icon-s-check"
                    ></el-input>
                  </el-col>
                  <el-col :span="6" class="captcha">
                    <img :src="captchaSrc" alt="" @click="fetchCaptchaSrc" />
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" style="width: 100%" :loading="loading" @click="handleSubmit">登 录</el-button>
                <el-row class="m-mini-t">
                  <el-col :span="12">
                    <a class="text-link" @click="$router.push({ name: 'Register' })"> 申请开户 </a>
                  </el-col>
                  <el-col :span="12" class="text-right">
                    <a class="text-link" @click="$router.push({ name: 'ForgotPassword' })"> 忘记密码？ </a>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-form>
          </div>
          <div class="back-home">
            <a :href="officialSite"> 返回首页 </a>
          </div>
        </div>
      </el-col>
    </el-row>
  </el-container>
</template>

<script>
import { login, captchaSrc } from '@/apis/auth'
import { mapGetters } from 'vuex'

export default {
  name: 'Login',
  data() {
    return {
      loading: false,
      captchaSrc: '',
      form: {
        username: '',
        password: '',
        captcha_key: '',
        captcha: ''
      },
      rules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters('platform', [
      'logoWhite',
      'title',
      'slogan',
      'description',
      'bgImage',
      'customFooter',
      'styles',
      'officialSite'
    ])
  },
  created() {
    this.fetchCaptchaSrc()

    const style = document.createElement('style')
    style.textContent = this.styles
    document.head.appendChild(style)
  },
  methods: {
    fetchCaptchaSrc() {
      captchaSrc().then((r) => {
        this.captchaSrc = r.data.img
        this.form.captcha_key = r.data.key
      })
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          login(this.form)
            .then(async (r) => {
              await this.$store.dispatch('auth/setToken', r)
              this.$router.push({ name: 'HomeIndex' })
            })
            .catch((e) => {
              if (e.response?.data?.message?.captcha?.indexOf('验证码') !== -1) {
                this.fetchCaptchaSrc()
              }
            })
            .finally(() => (this.loading = false))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login {
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;

  .main {
    width: 1100px;

    .left {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .logo {
      height: 48px;
    }

    .hero {
      margin-top: 50px;
      color: #fff;

      h3 {
        font-size: 62px;
        font-weight: 400;
        margin: 0;
      }

      p {
        margin: 10px 0 0 0;
        font-size: 24px;
      }
    }

    .copyright {
      color: #fff;
      bottom: 0px;
      .custom-footer {
        span {
          color: #fff;
          &:not(:first-child) {
            margin-left: 10px;
          }
        }
        a {
          color: #fff;
          text-decoration: none;
        }
      }
    }

    .form {
      float: right;
      width: 380px;
      background: #fff;
      border-radius: 5px;
      border-top: 5px solid #33435e;
      .form-main {
        box-sizing: border-box;
        padding: 40px 30px 0px 30px;

        .form-title {
          height: 40px;
          font-size: 18px;
          margin: 0 0 20px 0;

          .short-line {
            display: block;
            margin-top: 5px;
            width: 30px;
            height: 3px;
            background: #ff7f4c;
            box-shadow: 2px 1px 3px 0px rgba(0, 0, 0, 0.05);
            border-radius: 2px;
          }
        }

        .captcha {
          padding-top: 0;
          padding-bottom: 0;
          text-align: right;
          max-height: 32px;

          img {
            height: 32px;
            cursor: pointer;
          }
        }

        .text-link {
          text-decoration: none;
          color: #999999;
          cursor: pointer;
          &:hover,
          &:focus {
            color: #999999;
          }
        }
      }

      .back-home {
        a {
          display: block;
          text-decoration: none;
          text-align: center;
          width: 100%;
          margin-top: 10px;
          height: 50px;
          line-height: 50px;
          background-color: #f9f9f9;
          border-radius: 0 0 5px 5px;
          font-size: 14px;
          color: #999999;
        }
      }
    }
  }
}
</style>
