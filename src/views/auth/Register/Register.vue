<template>
  <el-container class="login" :style="'background-image: url(' + bgImage + ')'">
    <el-row class="main">
      <el-col :span="14" class="left">
        <div>
          <el-col>
            <img :src="logoWhite" class="logo" alt="LOGO" />
          </el-col>
          <el-col class="hero">
            <h3>{{ slogan }}</h3>
            <p>{{ description }}</p>
          </el-col>
        </div>
        <el-col class="copyright">
          <p>© 2019 - {{ new Date().getFullYear() }} Copyright {{ title }}.</p>
          <p class="custom-footer">
            <span v-for="(f, idx) in customFooter" :key="idx">
              <a v-if="f.type === 'link'" :href="f.href" target="_blank">{{ f.text }}</a>
              <template v-else>{{ f.text }}</template>
            </span>
          </p>
        </el-col>
      </el-col>
      <el-col :span="10" class="right">
        <div class="form">
          <div class="form-main">
            <p class="form-title">
              注册
              <span class="short-line"></span>
            </p>
            <el-form ref="form" :model="form" :rules="rules">
              <el-form-item prop="name">
                <el-input v-model="form.name" placeholder="请输入姓名/企业名称" prefix-icon="el-icon-user"></el-input>
              </el-form-item>
              <el-form-item prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱" prefix-icon="el-icon-message"></el-input>
              </el-form-item>
              <el-form-item prop="phone_number">
                <el-row :gutter="10">
                  <el-col :span="16">
                    <el-input
                      type="phone_number"
                      v-model="form.phone_number"
                      maxlength="11"
                      placeholder="请输入手机号"
                      prefix-icon="el-icon-mobile-phone"
                    ></el-input>
                  </el-col>
                  <el-col :span="6" class="captcha">
                    <el-button
                      type="primary"
                      style="width: 100px"
                      :disabled="!verifiable"
                      @click="handleGetVerificationCode"
                    >
                      {{ verificationBtnText }}
                    </el-button>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item prop="verification_code">
                <el-input
                  type="text"
                  maxlength="6"
                  v-model="form.verification_code"
                  placeholder="请输入验证码"
                  prefix-icon="el-icon-lock"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" style="width: 100%" :loading="loading" @click="handleSubmit">
                  提交申请
                </el-button>
                <el-row class="m-mini-t">
                  <el-col :span="12">
                    <a class="text-link" @click="$router.push({ name: 'Login' })"> 已有账号？ </a>
                  </el-col>
                  <el-col :span="12" class="text-right">
                    <a class="text-link" href="/"> 忘记密码？ </a>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-form>
          </div>
          <div class="back-home">
            <a :href="officialSite"> 返回首页 </a>
          </div>
        </div>
      </el-col>
    </el-row>
  </el-container>
</template>

<script>
import { register, getPhoneNumberVerificationCode } from '@/apis/auth'
import { mapGetters } from 'vuex'

export default {
  name: 'Register',
  data() {
    return {
      loading: false,
      countdown: 120,
      verifiable: false,
      form: {
        name: '',
        email: '',
        phone_number: '',
        country_code: 86,
        verification_code: ''
      },
      rules: {
        name: [{ required: true, message: '请输入姓名/企业名称', trigger: 'blur' }],
        email: [{ required: true, type: 'email', message: '请输入邮箱', trigger: 'blur' }],
        phone_number: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        verification_code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters('platform', [
      'logoWhite',
      'title',
      'slogan',
      'description',
      'bgImage',
      'customFooter',
      'officialSite'
    ]),
    verificationBtnText() {
      if (this.countdown === 120 || this.verifiable) {
        return '发送验证码'
      }

      return this.countdown
    }
  },
  watch: {
    'form.phone_number'(value) {
      this.verifiable = value.length >= 11
    }
  },
  methods: {
    handleGetVerificationCode() {
      getPhoneNumberVerificationCode({
        country_code: '86',
        phone_number: this.form.phone_number
      }).then(() => {
        this.verifiable = false
        this.$message.success('短信已发送')

        const timer = setInterval(() => {
          if (this.countdown <= 0) {
            clearInterval(timer)
            this.verifiable = true
          }

          this.countdown--
        }, 1000)
      })
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          register(this.form)
            .then(() => {
              this.$message.success('提交申请成功，请等待审核')
              this.$router.push({ name: 'Login' })
            })
            .finally(() => (this.loading = false))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login {
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;

  .main {
    width: 1100px;

    .left {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .logo {
      height: 48px;
    }

    .hero {
      margin-top: 50px;
      color: #fff;

      h3 {
        font-size: 62px;
        font-weight: 400;
        margin: 0;
      }

      p {
        margin: 10px 0 0 0;
        font-size: 24px;
      }
    }

    .copyright {
      color: #fff;
      bottom: 0px;
      .custom-footer {
        span {
          color: #fff;
          &:not(:first-child) {
            margin-left: 10px;
          }
        }
        a {
          color: #fff;
          text-decoration: none;
        }
      }
    }

    .form {
      float: right;
      width: 380px;
      background: #fff;
      border-radius: 5px;
      border-top: 5px solid #33435e;
      .form-main {
        box-sizing: border-box;
        padding: 40px 30px 0px 30px;

        .form-title {
          height: 40px;
          font-size: 18px;
          margin: 0 0 20px 0;

          .short-line {
            display: block;
            margin-top: 5px;
            width: 30px;
            height: 3px;
            background: #ff7f4c;
            box-shadow: 2px 1px 3px 0px rgba(0, 0, 0, 0.05);
            border-radius: 2px;
          }
        }

        .text-link {
          text-decoration: none;
          color: #999999;
          cursor: pointer;
          &:hover,
          &:focus {
            color: #999999;
          }
        }
      }

      .back-home {
        a {
          display: block;
          text-decoration: none;
          text-align: center;
          width: 100%;
          margin-top: 10px;
          height: 50px;
          line-height: 50px;
          background-color: #f9f9f9;
          border-radius: 0 0 5px 5px;
          font-size: 14px;
          color: #999999;
        }
      }
    }
  }
}
</style>
