<template>
  <div class="p-extra-large-t p-extra-large-x p-extra-large-b w-100">
    <header class="header-row">
      <el-row>
        <el-col :span="24">
          <span>资料中心</span>
          <div class="short-line"></div>
        </el-col>
      </el-row>
    </header>

    <el-row class="m-extra-large-t">
      <el-col :span="24">
        <div class="companies">
          <div
            class="company"
            v-for="com in categories"
            :key="com.id"
            @click="handleSwitchCategory(com)"
            :class="{ active: com.id === activeCategory?.id }"
          >
            <img class="logo" :src="com.logo" :alt="com.name" />
          </div>
        </div>
      </el-col>
    </el-row>

    <ul class="documents">
      <li v-if="documents.length <= 0">暂无资料</li>
      <li v-for="doc in documents" :key="doc.id">
        <div>{{ doc.name }}</div>
        <div class="right">
          <span class="publish_date">发布日期：{{ doc.created_at }}</span>
          <el-button
            class="download-btn"
            type="primary"
            size="mini"
            icon="fas fa-download"
            @click="handleDownload(doc)"
          >
            下载
          </el-button>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { getDocumentCategories, getDocuments } from '@/apis/document'

export default {
  data() {
    return {
      categories: [],
      activeCategory: {},
      documents: []
    }
  },
  async created() {
    const categories = await getDocumentCategories()
    this.categories = categories.data
    this.activeCategory = categories.data[0]

    this.fetchDocuments()
  },
  methods: {
    handleSwitchCategory(category) {
      this.activeCategory = category
      this.fetchDocuments()
    },
    handleDownload(doc) {
      window.open(doc.url, '_blank')
    },
    async fetchDocuments() {
      const documents = await getDocuments({
        category_id: this.activeCategory.id
      })
      this.documents = documents.data
    }
  }
}
</script>

<style scoped lang="scss">
.documents {
  list-style: none;
  padding: 0px;

  li {
    padding: 10px;
    background: #ffffff;
    border-radius: 5px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;

    .right {
      margin-left: auto;
      display: flex;
      align-items: center;

      .publish_date {
        color: #888888;
      }

      .download-btn {
        margin-left: 20px;
      }
    }

    &:hover {
      background: #f0f0f0;
    }
  }
}
.companies {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  column-gap: 20px;
  row-gap: 20px;

  .company {
    padding: 10px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #f0f0f0;

    &:hover {
      background: #ffffff;
      box-shadow: 0 0 10px 0 rgba(100, 100, 100, 0.1);
    }

    .logo {
      max-height: 30px;
      max-width: 120px;
    }
  }

  .active {
    background: #ffffff;
    box-shadow: 0 0 10px 0 rgba(100, 100, 100, 0.1);
    border: 1px solid #e6a23c;
  }
}
</style>
