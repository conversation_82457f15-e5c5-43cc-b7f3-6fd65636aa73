<template>
  <div class="finance-income p-extra-large-x p-extra-large-b w-100 o-hidden">
    <el-card shadow="never" class="m-extra-large-t">
      <div class="d-flex w-100" slot="header">
        <div class="flex-fill m-mini-r"></div>
        <el-select v-model="filter.status" placeholder="请选择状态">
          <el-option value="0" label="全部"></el-option>
          <el-option value="1" label="已提交"></el-option>
          <el-option value="2" label="已到账"></el-option>
        </el-select>
        <el-date-picker v-model="filter.starts_at" placeholder="起始时间" class="m-mini-l" />
        <el-date-picker v-model="filter.ends_at" placeholder="截止时间" class="m-mini-l" />
        <el-input v-model="filter.serial_no" placeholder="流水号" class="m-mini-l" style="width: 200px" />
        <el-button type="primary" icon="fas fa-search" class="m-mini-l"></el-button>
      </div>
      <define-table :cols="cols" :data="[]" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'FinanceIncomeWithdraw',
  data() {
    return {
      filter: {
        status: '',
        starts_at: '',
        ends_at: '',
        serial_no: ''
      },
      cols: [
        { label: '流水号', prop: 'source' },
        { label: '金额', prop: 'source' },
        { label: '开户行', prop: 'source' },
        { label: '卡号', prop: 'source' },
        { label: '账户名', prop: 'source' },
        { label: '回单号', prop: 'source' },
        { label: '回单', prop: 'source' },
        { label: '时间', prop: 'source' },
        { label: '状态', prop: 'source' }
      ],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      pagingEvents: {
        currentChange: (page) => {
          //
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.text-amount {
  font-size: 2rem;
  margin: 20px 0 20px 0;
}
</style>
