<template>
  <div class="finance-income p-extra-large-x p-extra-large-b w-100">
    <div class="my-income">
      <search-panel
        :custom="searchFields"
        size="small"
        @change="(data) => (searchData = data)"
        @command="handleSearchPanel"
      ></search-panel>
      <el-card shadow="never" class="m-extra-large-t">
        <div class="d-flex w-100" slot="header">
          <el-button icon="fas fa-download" @click="handleExport">导出列表</el-button>
        </div>
        <define-table :cols="cols" :data="data" :paging="paging" :paging-events="pagingEvents" />
      </el-card>
    </div>
  </div>
</template>

<script>
import { getProductsCompanies } from '@/apis/product'
import { getIncomes, buildIncomesExportHref } from '@/apis/user'

export default {
  name: 'FinanceIncome',
  data() {
    return {
      searchFields: [
        {
          type: 'select',
          valKey: 'type',
          hintText: '收益来源',
          options: [
            { label: '全部收益', value: 0 },
            { label: '国内货运险', value: 1 },
            { label: '国际货运险', value: 2 },
            { label: '单车责任险', value: 3 },
            { label: '其他险种', value: 4 },
            { label: '雇主责任险', value: 5 },
            { label: '跨境电商货运险', value: 7 }
          ]
        },
        {
          type: 'daterange',
          valKey: 'submitted_at_range'
        },
        {
          type: 'input',
          valKey: 'order_no',
          hintText: '流水号'
        },
        {
          type: 'input',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '结算状态',
          options: [
            { label: '未结算', value: 0 },
            { label: '已结算', value: 1 }
          ]
        }
      ],
      cols: [
        { label: '险种', prop: 'source', fixed: 'left' },
        { label: '流水号', prop: 'order_no', fixed: 'left', width: 200 },
        { label: '保单号', prop: 'policy_no' },
        { label: '出单公司', prop: 'company_branch' },
        { label: '投保人', prop: 'policyholder' },
        { label: '投保用户', prop: 'user' },
        { label: '保费', prop: 'premium' },
        { label: '佣金', prop: 'amount' },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_done ? (
                <span style="color: green;">已结算</span>
              ) : (
                <span style="color: red;">未结算</span>
              )
            }
          }
        },
        { label: '时间', prop: 'created_at', width: 200 }
      ],
      data: [],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        page: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.fetchIncomes()
        }
      },
      rawCompanies: [],
      searchData: []
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  created() {
    this.fetchIncomes()

    getProductsCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchData = data
      this.paging.page = 1
      if (command === 'export') {
        //
      }

      this.fetchIncomes()
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleExport() {
      window.open(
        buildIncomesExportHref({
          filter: this.searchData
        })
      )
    },
    fetchIncomes() {
      getIncomes({
        page: this.paging.page,
        filter: this.searchData
      }).then((r) => {
        this.data = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.text-amount {
  font-size: 2rem;
  margin: 20px 0 20px 0;
}
</style>
