<!--
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-06-21 09:57:50
 * @LastEditors: yanb
 * @LastEditTime: 2021-07-08 15:13:27
-->
<template>
  <div class="finance-invoices inner-box p-extra-large-x p-extra-large-b w-100">
    <el-card class="m-extra-large-t table-wrap" shadow="never">
      <define-table :data="data" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import { getInvoiceRecords } from '@/apis/invoice'

export default {
  name: 'FinanceInvoiceRecords',
  data() {
    return {
      cols: [
        { label: '发票号', prop: 'invoice_no' },
        { label: '抬头', prop: 'company_name' },
        { label: '纳税人识别码', prop: 'tax_no' },
        { label: '金额(元)', prop: 'amount' },
        { label: '申请时间', prop: 'created_at' },
        {
          label: '状态',
          prop: 'status',
          scopedSlots: {
            default: (scoped) => {
              const statuses = {
                '-1': '已提交',
                1: '已提交',
                2: '审核中',
                3: '已完成',
                4: '已退回'
              }

              return <span>{statuses[scoped.row.status]}</span>
            }
          }
        },
        {
          label: '操作',
          width: '50',
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-button
                  type="text"
                  size="small"
                  onClick={() => this.$router.push({ name: 'FinanceInvoiceDetail', params: { id: scoped.row.id } })}
                >
                  查看
                </el-button>
              )
            }
          }
        }
      ],
      data: [],
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchInvoicesHisotries()
        }
      },
      // 分页器 不传递 -> 没有分页
      paging: {
        page: 1,
        pageSize: 10,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      }
    }
  },
  created() {
    this.fetchInvoicesHisotries()
  },
  methods: {
    fetchInvoicesHisotries() {
      getInvoiceRecords({
        page: this.paging.page
      }).then((r) => {
        this.data = r.data

        this.paging.page = r.meta.current_page
        this.paging.total = r.meta.total
      })
    }
  }
}
</script>
