<template>
  <div class="finance-income p-extra-large-x p-extra-large-b w-100 o-hidden">
    <el-card shadow="never" class="m-extra-large-t">
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form ref="form" :model="form" :rules="rules" label-width="100">
            <h3>申请提现</h3>
            <el-form-item prop="amount">
              <template slot>
                提现金额
                <span class="text-blue">(可提现: ¥{{ withdrawable }}元)</span>
              </template>
              <div class="d-flex">
                <el-input type="number" v-model="form.amount" placeholder="请输入金额"></el-input>
                <el-button class="m-mini-l" type="primary" @click="form.amount = withdrawable">全部提现</el-button>
              </div>
            </el-form-item>
            <h3>银行信息</h3>
            <el-form-item prop="bank" label="银行">
              <el-input v-model="form.bank" placeholder="请输入银行名称"></el-input>
            </el-form-item>
            <el-form-item prop="bank_branch" label="开户行">
              <el-input v-model="form.bank_branch" placeholder="请输入开户行名称"></el-input>
            </el-form-item>
            <el-form-item prop="bankcard_no" label="银行卡号">
              <el-input v-model="form.bankcard_no" placeholder="请输入银行卡号名称"></el-input>
            </el-form-item>
            <el-form-item prop="phone_number" label="请输入联系方式">
              <el-input v-model="form.phone_number" placeholder="请输入请输入联系方式"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="fas fa-check" @click="handleSubmit">申请提现</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <el-alert type="warning" title="提现须知" :closable="false">
            <template slot>
              <p>1. 提现金额不得小于2000.00元。</p>
              <p>2. 提现时间为收到提现发票后的2个工作日，如遇节假日顺延。</p>
              <p>3. 投保生效15日后方可提现</p>
            </template>
          </el-alert>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'FinanceIncomeApplyWithdraw',
  data() {
    return {
      withdrawable: 1000,
      form: {
        amount: '',
        bank: '',
        bank_branch: '',
        bankcard_no: '',
        phone_number: ''
      },
      rules: {
        amount: [{ required: true, message: '请输入金额', trigger: 'blur' }],
        bank: [{ required: true, message: '请输入银行名称', trigger: 'blur' }],
        bank_branch: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
        bankcard_no: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
        phone_number: [{ required: true, message: '请输入联系方式', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          //
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.text-amount {
  font-size: 2rem;
  margin: 20px 0 20px 0;
}
</style>
