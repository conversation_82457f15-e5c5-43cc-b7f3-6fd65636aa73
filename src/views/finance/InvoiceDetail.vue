<template>
  <div class="w-100 billdetail">
    <div class="m-extra-large-x p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <el-alert
        style="margin-bottom: 10px"
        v-if="[-1, 1, 2].includes(invoice.status)"
        type="success"
        title="正在处理"
        show-icon
        description="您的开票申请正在处理中"
        :closable="false"
      />
      <el-alert
        style="margin-bottom: 10px"
        v-if="invoice.status === 4"
        type="warning"
        title="您的开票申请已被退回"
        show-icon
        :description="'退回原因：' + invoice.reason"
        :closable="false"
      />
      <div>
        <el-row>
          <el-col>
            <el-button-group style="float: right">
              <el-button type="primary" @click="exportExcel"> 导出保单列表 </el-button>
              <el-button type="primary" @click="download" v-if="!(invoice?.is_zy && invoice?.type === 1)">
                下载
              </el-button>
            </el-button-group>
          </el-col>
        </el-row>
      </div>

      <define-details :data="dataList"></define-details>
      <define-table :attrs="{ border: true }" :data="invoice.policies" :cols="policyCols" v-if="invoice" />
    </div>
  </div>
</template>

<script>
import { getInvoice, exportPolicy } from '@/apis/invoice'
import { mapGetters } from 'vuex'

export default {
  name: 'InvoiceDetail',
  data() {
    return {
      policyCols: [
        { align: '', type: 'index' },
        // { prop: '', label: '流水号' },
        { prop: 'policy_no', label: '保单号' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                7: '跨境电商货运险'
              }

              return types[scoped.row.type]
            }
          }
        },
        {
          label: '金额(元)',
          scopedSlots: {
            default: (scoped) => {
              // 雇主责任险
              if (scoped.row.type === 5) {
                return (
                  <div>
                    ¥ {scoped.row.amount}
                    <el-popover placement="left" width="680" trigger="hover">
                      <el-table data={scoped.row.group_endorses}>
                        <el-table-column width="80" property="id" label="#"></el-table-column>
                        <el-table-column width="150" property="batch_no" label="批次号"></el-table-column>
                        <el-table-column width="200" property="endorse_no" label="批单号"></el-table-column>
                        <el-table-column width="100" property="total_fee" label="金额"></el-table-column>
                        <el-table-column width="150" property="created_at" label="时间"></el-table-column>
                      </el-table>
                      <span class="text-primary" style="cursor: pointer;" slot="reference">
                        &nbsp;&nbsp;详情
                      </span>
                    </el-popover>
                  </div>
                )
              } else {
                return <span>¥ {scoped.row.amount}</span>
              }
            }
          }
        },
        { prop: 'company_branch_name', label: '出单公司' },
        { prop: 'submitted_at', label: '投保时间' }
      ],
      invoice: {}
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    dataList() {
      let dataList = {
        title: '发票详情',
        data: [
          {
            title: '账户信息',
            groups: [
              { label: '流水号', value: this.invoice?.order_no ?? '' },
              { label: '发票类型', value: this.invoice?.type === 1 ? '普票' : '专票' },
              { label: '申请人', value: this.invoice?.apply_name ?? '' },
              { label: '申请时间', value: this.invoice?.apply_at ?? '' }
            ]
          },
          {
            title: '开票信息',
            groups: [
              { label: '发票抬头', value: this.invoice?.company_name ?? '' },
              { label: '纳税识别号', value: this.invoice?.tax_no ?? '' },
              { label: '开票金额', value: this.invoice?.amount + '元' ?? '' },
              { label: '发票号', value: this.invoice?.invoice_no }
            ]
          },
          {
            title: '专票信息',
            groups: [
              { label: '注册地址', value: this.invoice?.registered_addr ?? '' },
              { label: '注册电话', value: this.invoice?.registered_phone_number ?? '' },
              { label: '开户银行', value: this.invoice?.bank_name ?? '' },
              { label: '银行账号', value: this.invoice?.bankcard_no ?? '' }
            ]
          },
          {
            title: '发票文件',
            isHide: !(this.invoice?.type === 1 && this.invoice?.is_zy),
            groups: [{ label: '发票文件', value: this.invoiceFiles ?? '', row: true }]
          },
          {
            title: '邮寄信息',
            groups: [
              { label: '是否邮寄', value: this.invoice?.is_mail ? '是' : '否' },
              { label: '收件人', value: this.invoice?.recipient ?? '' },
              { label: '收件人电话', value: this.invoice?.recipient_phone_number ?? '' },
              { label: '发票寄送地址', value: this.invoice?.delivery_address ?? '' }
            ]
          },
          {
            title: '开票保单',
            groups: []
          }
        ]
      }
      dataList.data = dataList.data.filter((e) => e.isHide === undefined || e.isHide === false)
      return dataList
    },
    invoiceFiles() {
      if ((this.invoice?.file ?? '').indexOf(';') != -1) {
        var data = ''
        this.invoice?.file.split(';').forEach((item, index) => {
          if (item) {
            data +=
              '<a style="text-decoration:none; margin-right: 10px" href="' +
              item +
              '" target="_blank">发票文件' +
              (Number(index) + 1) +
              '</a>'
          }
        })
        return data
      }
      return '<a style="text-decoration:none;" href="' + this.invoice?.file + '" target="_blank">发票文件</a>'
    }
  },
  created() {
    getInvoice(this.$route.params.id).then((r) => {
      this.invoice = r.data
    })
  },
  methods: {
    download() {
      window.open(this.invoice.file, '_blank')
    },
    exportExcel() {
      window.open(exportPolicy(this.$route.params.id), '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.header-row {
  margin-bottom: 20px;
}

.no-padding {
  /deep/ .el-card__body {
    padding: 0;
  }
}

.list {
  display: flex;

  ul {
    flex: 1;
    padding: 0;

    li {
      list-style: none;
      height: 45px;
      line-height: 45px;
      border-bottom: 1px solid #eee;
      box-sizing: border-box;
      padding-left: 20px;
    }
  }
}
</style>
