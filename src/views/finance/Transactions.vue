<template>
  <div class="finance-transactions inner-box p-extra-large-x p-extra-large-b w-100">
    <search-panel :custom="searchFields" :hide-export="true" size="small" @command="handleSearchPanel" />

    <el-card class="m-extra-large-t" shadow="never">
      <div class="d-flex w-100" slot="header">
        <el-button icon="fas fa-download" @click="handleExport">导出列表</el-button>
      </div>
      <define-table :data="data" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import { getTransactions, buildTransactionsExportHref } from '@/apis/user'

export default {
  name: 'FinanceTransactions',
  data() {
    return {
      searchFields: [
        {
          type: 'select',
          valKey: 'type',
          hintText: '类型',
          options: [
            { label: '收入', value: 1 },
            { label: '支出', value: 2 }
          ]
        },
        {
          type: 'daterange',
          valKey: 'submitted_at_range'
        },
        {
          type: 'input',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'remark',
          hintText: '搜索备注'
        }
      ],
      data: [],
      cols: [
        {
          label: '类型',
          prop: 'type',
          width: 50,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.type) {
                case 1:
                  return <span class="text-primary">充值</span>
                case 2:
                  return <span class="text-danger">扣费</span>
                case 3:
                  return <span class="text-blue">退款</span>
                default:
                  break
              }
            }
          }
        },
        {
          label: '交易流水号/保单号',
          fixed: 'left',
          width: 200,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label class="text-blue">{scoped.row.transaction_no}</label>
                  <br />
                  <small>{scoped.row.policy_no || '-'}</small>
                </div>
              )
            }
          }
        },
        {
          label: '险种',
          prop: 'transaction_type',
          width: 100
        },
        { label: '用户', prop: 'actual_user', width: 150 },
        {
          label: '金额',
          prop: 'amount',
          width: 120,
          scopedSlots: {
            default: (scoped) => {
              return '¥' + scoped.row.amount + ' 元'
            }
          }
        },
        {
          label: '余额',
          prop: 'balance',
          width: 120,
          scopedSlots: {
            default: (scoped) => {
              return '¥' + scoped.row.balance + ' 元'
            }
          }
        },
        {
          label: '投保用户',
          prop: 'actual_user',
          width: 150
        },
        { label: '备注', prop: 'remark', width: 150 },
        { label: '交易时间', prop: 'created_at', width: 150, fixed: 'right' }
      ],
      searchData: {},
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchTransactions()
        }
      },
      // 分页器 不传递 -> 没有分页
      paging: {
        page: 1,
        pageSize: 10,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      }
    }
  },
  created() {
    this.fetchTransactions()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchData = data
      this.paging.page = 1

      this.fetchTransactions()
    },
    handleExport() {
      window.open(
        buildTransactionsExportHref('me', {
          filter: this.searchData
        })
      )
    },
    fetchTransactions() {
      getTransactions('me', {
        filter: this.searchData,
        page: this.paging.page
      }).then((r) => {
        this.data = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    }
  }
}
</script>
