<template>
  <div class="finance-invoices p-extra-large-x p-extra-large-b w-100">
    <search-panel
      :custom="searchFields"
      size="small"
      :hide-export="true"
      @command="
        (command, data) => {
          this.searchData = data
          this.$refs.table.$refs.dataTable.clearSelection(this.selectedData)
          this.selectedData = []
          fetchInvoices()
        }
      "
    />

    <el-alert
      style="margin-top: 20px !important"
      type="warning"
      title="如需雇主责任险开票请先按险种筛选"
      show-icon
      :closable="false"
    ></el-alert>

    <el-card class="m-extra-large-t table-wrap" shadow="never">
      <p>
        有 <b class="text-danger">{{ data.length }}</b> 个订单可开票，总共可开票金额：
        <b class="text-danger"> ¥ {{ pageAmount }} 元 </b>
      </p>
      <p>
        已选 <b class="text-danger">{{ selectedData.length }}</b> 个订单，开票金额：
        <b class="text-danger">¥ {{ selectedAmount }} 元</b>
      </p>
      <el-button type="primary" icon="fas fa-receipt" @click="showRequestInvoiceForm">立即开票</el-button>
      <el-button type="primary" icon="fas fa-receipt" @click="applyInvoiceAll">全部开票</el-button>
      <el-button type="primary" icon="fas fa-list" @click="$router.push({ name: 'FinanceInvoiceRecords' })">
        开票记录
      </el-button>
      <el-switch
        style="margin: 5px 0 0 10px"
        active-text="按被保人开票"
        inactive-text="按投保人开票"
        :active-value="2"
        :inactive-value="1"
        v-model="clientType"
      ></el-switch>
    </el-card>

    <el-card class="m-extra-large-t table-wrap" shadow="never">
      <define-table
        ref="table"
        :events="tableEvents"
        :data="data"
        :cols="cols"
        :paging="paging"
        :paging-events="pagingEvents"
        :rowKey="getRowKeys"
      />
    </el-card>

    <request-invoice
      :fill-data.sync="userInvoiceInfo"
      :visible.sync="invoiceFormVisible"
      :amount="selectedAmount"
      :isZhongyi.sync="isZhongyi"
      :isAllApply.sync="isAllApply"
      @submit="handleSubmit"
      @all-apply-submit="allApplyInvoice"
    />
  </div>
</template>

<script>
import { getCompanyBranches } from '@/apis/company_branch'
import { getInovices, makeInvoice, fetchSuggestion, makeInvoiceAll } from '@/apis/invoice'
import RequestInvoice from '@/components/finance/RequestInvoice'

export default {
  name: 'FinanceInvoices',
  components: { RequestInvoice },
  data() {
    return {
      isAllApply: false,
      searchFields: [
        {
          type: 'daterange',
          valKey: 'submitted_at_range'
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保险人'
        },
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'select',
          valKey: 'type',
          hintText: '险种',
          options: [
            { value: 1, label: '国内货运险' },
            { value: 2, label: '国际货运险' },
            { value: 3, label: '单车责任险' },
            { value: 4, label: '其他险' },
            { value: 5, label: '雇主责任险' },
            { value: 7, label: '跨境电商货运险' }
          ]
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        }
      ],
      cols: [
        { align: 'center', type: 'selection', fixed: 'left', reserveSelection: true },
        {
          label: '保单号',
          width: '200',
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.type === 5 ? scoped.row.endorse_no : scoped.row.policy_no
            }
          }
        },
        { label: '投保人', prop: 'policyholder' },
        { label: '被保险人', prop: 'insured' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险'
              }

              return types[scoped.row.type]
            }
          }
        },
        { label: '投保产品', prop: 'product.name' },
        { label: '出单公司', prop: 'company_branch.name' },
        {
          label: '金额(元)',
          scopedSlots: {
            default: (scoped) => {
              // 雇主责任险
              return <span>¥ {scoped.row.amount}</span>
            }
          }
        },
        { label: '投保时间', prop: 'submitted_at', fixed: 'right', width: '150' }
      ],
      data: [],
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchInvoices()
        }
      },
      // 分页器 不传递 -> 没有分页
      paging: {
        page: 1,
        pageSize: 10,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      selectedData: [],
      tableEvents: {
        'selection-change': (data) => {
          this.selectedData = data
        }
      },
      invoiceFormVisible: false,
      userInvoiceInfo: {},
      searchData: {},
      suggestions: [],
      clientType: 1
    }
  },
  computed: {
    pageAmount() {
      let sum = 0

      this.data.forEach((e) => (sum += parseFloat(e.amount)))

      return ((sum * 100) / 100).toFixed(2)
    },
    selectedAmount() {
      let sum = 0

      this.selectedData.forEach((e) => (sum += parseFloat(e.amount)))

      return ((sum * 100) / 100).toFixed(2)
    },
    isSameCompany() {
      const companyBranchIds = Array.from(new Set(this.selectedData.map((e) => e.company_branch.id)))

      return companyBranchIds.length === 1
    },
    isSamePolicyholder() {
      const policyholder = Array.from(new Set(this.selectedData.map((e) => e.policyholder)))

      return policyholder.length === 1
    },
    isSameInsured() {
      const insured = Array.from(new Set(this.selectedData.map((e) => e.insured)))

      return insured.length === 1
    },
    isSamePolicy() {
      const policyIds = Array.from(new Set(this.selectedData.map((e) => e.policy_id)))

      return policyIds.length === 1
    },
    companyBranchId() {
      if (this.isSameCompany) {
        return Array.from(new Set(this.selectedData.map((e) => e.company_branch.id)))[0]
      } else {
        return this.searchData?.company_branch_id
      }
    },
    isZhongyi() {
      return this.selectedData?.[0]?.is_zhongyi
    }
  },
  watch: {
    searchData: {
      deep: true,
      handler(value) {
        const label = value.type === 5 ? '批单号' : '保单号'

        this.cols[1].label = label
      }
    }
  },
  created() {
    this.loadCompanyBranches()

    this.fetchInvoices()
  },
  methods: {
    getRowKeys(row) {
      return row.id
    },
    handleSubmit(data) {
      data.ids = []
      this.selectedData.forEach((e) => data.ids.push(e.id))
      data.filter = Object.assign({}, this.searchData)
      data.filter.company_branch_id = this.companyBranchId
      data.company_branch_id = this.companyBranchId
      data.client_type = this.clientType

      makeInvoice(data).then(() => {
        this.$message.success('申请成功')

        this.fetchInvoices()
      })
    },
    showRequestInvoiceForm() {
      if (this.selectedAmount <= 0) {
        return this.$message.error('请选择需要开票的保单')
      }

      if (!this.isSameCompany && !this.searchData?.company_branch_id) {
        return this.$message.error('单次只能开一家出单公司发票，请先按照出单公司筛选')
      }

      if (!this.isSamePolicyholder && this.clientType == 1) {
        return this.$message.error('只能选择同一投保人开票')
      }

      if (!this.isSameInsured && this.clientType == 2) {
        return this.$message.error('只能选择同一被保人开票')
      }

      if (!this.isSamePolicy && this.searchData?.type == 5) {
        return this.$message.error('请选择同一保单的批单')
      }

      this.$nextTick(() => {
        this.invoiceFormVisible = true
        this.userInvoiceInfo = {
          customer_role_type: this.clientType,
          company_name: this.clientType == 1 ? this.selectedData[0].policyholder : this.selectedData[0].insured
        }

        fetchSuggestion({
          name: this.userInvoiceInfo.company_name
        }).then((r) => {
          this.userInvoiceInfo = Object.assign({}, this.userInvoiceInfo, r.data)
        })
      })
    },
    loadCompanyBranches() {
      getCompanyBranches().then((r) => {
        this.searchFields[this.searchFields.length - 1].options = r.data.map((e) => {
          return {
            label: e.name,
            value: e.id
          }
        })
      })
    },
    fetchInvoices() {
      getInovices({
        page: this.paging.page,
        filter: this.searchData
      }).then((r) => {
        this.data = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    applyInvoiceAll() {
      this.isAllApply = true
      this.invoiceFormVisible = true
      this.userInvoiceInfo = {
        customer_role_type: this.clientType,
        company_name: this.clientType == 1 ? this.data[0].policyholder : this.data[0].insured
      }
    },
    allApplyInvoice(data) {
      data.filter = Object.assign({}, this.searchData)
      data.filter.company_branch_id = this.companyBranchId
      data.company_branch_id = this.companyBranchId
      data.client_type = this.clientType

      makeInvoiceAll(data).then(() => {
        this.$message.success('申请成功')

        this.fetchInvoices()
      })
    }
  }
}
</script>
