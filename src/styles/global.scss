:root {}

body {
  margin: 0;
  font-family: Robot<PERSON>,
    'Helvetica Neue,Helvetica,PingFang SC,Hiragino Sans GB,Microsoft YaHei,"\5FAE\8F6F\96C5\9ED1",<PERSON><PERSON>,sans-serif',
    sans-serif;
  font-size: 14px;
  color: #212529;
  text-align: left;
  background-color: #f8f9fa;
}

.clearfix::after {
  content: '.';
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
  font-size: 0;
}

.clearfix {
  *zoom: 1;
}

*[align='center'] {
  text-align: center;
}

.d-flex {
  display: flex !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  -ms-flex-item-align: stretch !important;
  align-self: stretch !important;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.o-hidden {
  overflow: hidden;
}

.o-hidden-y {
  overflow-y: hidden;
}

.o-hidden-x {
  overflow-x: hidden;
}

.o-y-auto {
  overflow-y: auto;
}

.o-x-auto {
  overflow-x: auto;
}

.font-20 {
  font-size: $app-size-extra-large;
}

.text-primary {
  color: $app-color-primary;
}

.text-blue {
  color: $app-color-blue !important;
}

.text-success {
  color: $app-color-success !important;
}

.text-warning {
  color: $app-color-warning !important;
}

.text-danger {
  color: $app-color-danger !important;
}

.text-info {
  color: $app-color-info !important;
}

.text-ellipsis {
  overflow: hidden; // 超出的文本隐藏
  text-overflow: ellipsis; // 溢出用省略号显示
  white-space: nowrap; // 溢出不换行
}

.text-center {
  text-align: center;
}

/// bg
.bg-white {
  background-color: #fff;
}

.el-button svg[data-fa-i2svg]+span {
  margin-left: 5px;
}

.short-line {
  display: block;
  width: 25px;
  height: 5px;
  border-radius: 3px;
  background: $app-color-danger;
  margin-top: 10px;
}

.inner-box {
  box-sizing: border-box;
}

.text-right {
  text-align: right;
}

.dialog-footer {
  margin-top: -40px;
}
