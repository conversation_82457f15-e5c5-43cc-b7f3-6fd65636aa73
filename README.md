## 目录说明

> `+`: 目录
>
> `-`: 文件

```basic
+	apis										统一封装Api请求
+ assets									静态资源目录(子目录`js`,`css`,`images`,`...`)
+ components							App组件目录
	+ 	global							components的子目录，该目录下的所有组件自动注册为全局组件
	+ 	...									global同级的其他组件目录是业务组件, 所有组件以文件夹形式创建(方便后续扩展)
	-   index.js  					此文件用于注册全局 组件
+ 	config								存放App配置信息
	-		index.js						导出所有配置信息
+ layouts									App主要布局视图目录
	- AsideMenu.js					构建动态菜单
  - Index.vue							布局主视图
  - SiteBreadcrumb.vue		面包屑视图
  - SiteHeader.vue			  头部布局
  - style.scss					  布局样式
+ router								  路由目录
  - routes.js						 	组装路由
  - index.js						 	路由出口
+ store								 		Vuex
  + modules								Vuex模块(该目录下的文件自动注册为vuex modules)
  - state.js							vuex 全局 State
  - getters.js						vuex 全局 Getters
  - mutations.js					vuex 全局 Mutations
  - actions.js						vuex 全局 Actions
+ styles									全局样式存储目录
+ utils										工具方法存储目录
+ views										视图目录
- plugins.js							插件引入目录
```
