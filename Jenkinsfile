pipeline {
  agent {
    docker {
      reuseNode 'true'
      image 'node:16.18.0-alpine'
      args '-v /root/.cache/:/root/.cache/ -v /root/.npm/:/root/.npm/'
    }
  }

  stages {
    stage('Checkout') {
      steps {
        checkout([
          $class: 'GitSCM',
          branches: [[name: env.GIT_BUILD_REF]],
          userRemoteConfigs: [[
            url: env.GIT_REPO_URL,
            credentialsId: env.CREDENTIALS_ID
          ]]])
        }
      }

      stage('Install Dependencies') {
        steps {
          sh "node --version"
          sh "npm --version"
          sh 'SASS_BINARY_SITE=https://npmmirror.com/mirrors/node-sass/ npm install --registry https://registry.npmmirror.com'
        }
      }

      stage('Deploy: Staging') {
        when {
            branch "develop"
        }
        steps {
          script {
            def remoteConfig = [:]
            remoteConfig.allowAnyHosts = true
            remoteConfig.name = "Staging"
            remoteConfig.host = "${SSH_STAGING_IP}"

            echo "deploy to testing server"
            sh "npm run build-staging"

            withCredentials([sshUserPrivateKey(
              credentialsId: "${SSH_STAGING_PRIVATE_KEY}",
              keyFileVariable: "privateKeyFilePath"
            )]) {
              // SSH 登陆用户名
              remoteConfig.user = "${SSH_STAGING_USER}"
              // SSH 私钥文件地址
              remoteConfig.identityFile = privateKeyFilePath

              sshPut(
                remote: remoteConfig,
                // 本地文件或文件夹
                from: './dist/index.html',
                // 远端文件或文件夹
                into: '/var/www/new_51baoya/user/web'
              )
            }
          }
        }
      }

      stage('Deploy: Production') {
        when {
          branch "master"
        }
        steps {
          script {
            timeout(time: 120, unit: 'SECONDS') {
              input '是否部署代码到正式环境？请谨慎操作！！！'
            }
            def remoteConfig = [:]
            remoteConfig.allowAnyHosts = true
            remoteConfig.name = "Production"
            remoteConfig.host = "${SSH_PROD_IP}"

            echo "deploy to production server"
            sh "npm run build"

            withCredentials([sshUserPrivateKey(
              credentialsId: "${SSH_PROD_PRIVATE_KEY}",
              keyFileVariable: "privateKeyFilePath"
            )]) {
              // SSH 登陆用户名
              remoteConfig.user = "${SSH_PROD_USER}"
              // SSH 私钥文件地址
              remoteConfig.identityFile = privateKeyFilePath

              sshPut(
                remote: remoteConfig,
                // 本地文件或文件夹
                from: './dist/index.html',
                // 远端文件或文件夹
                into: '/home/<USER>/app/baoya/user/web'
              )
            }
          }
        }
      }
  }
}
